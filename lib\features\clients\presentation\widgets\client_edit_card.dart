import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_create/sections/unified_attachments_section.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/file_display/files_grid_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_versions_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_entity_controller_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_entity_controller_state.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_version_selector.dart';
import 'package:balansoved_enterprise/injection_container.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/main_info_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_sections.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/tax_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/payment_schedules_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/extended_info_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_utils.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_validation_utils.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_edit_controllers.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_edit_state.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/yearly_tasks_calendar.dart';

class ClientEditCard extends StatefulWidget {
  final ClientEntity client;
  final VoidCallback onCancel;
  final void Function(ClientEntity) onSaved;

  const ClientEditCard({
    super.key,
    required this.client,
    required this.onCancel,
    required this.onSaved,
  });

  @override
  State<ClientEditCard> createState() => ClientEditCardState();
}

class ClientEditCardState extends State<ClientEditCard> {
  final _formKey = GlobalKey<FormState>();

  // Утилитные классы для управления состоянием
  late final ClientEditControllers _controllers;
  late final ClientEditState _state;

  // Файловые вложения (облачные)
  final List<FileAttachmentItem> _cloudFiles = [];
  bool _wasAutoSaved = false;

  // Флаг для отслеживания несохраненных изменений
  bool _hasUnsavedChanges = false;

  // Кубит для контроля entity клиента
  late final ClientEntityControllerCubit _clientEntityController;

  int _selectedYear = DateTime.now().year;

  /// Геттер для доступа к флагу несохраненных изменений извне
  bool get hasUnsavedChanges => _hasUnsavedChanges;

  bool get _hasActiveFileOperations => _cloudFiles.any(
    (f) =>
        f.status == FileAttachmentStatus.uploading ||
        f.status == FileAttachmentStatus.deleting ||
        f.status == FileAttachmentStatus.pending,
  );

  @override
  void initState() {
    super.initState();
    _controllers = ClientEditControllers();
    _state = ClientEditState();
    _clientEntityController = ClientEntityControllerCubit();
    _clientEntityController.loadClient(widget.client);

    // Инициализация из клиента
    _controllers.initializeFromClient(widget.client);
    _state.initializeFromClient(widget.client);

    // Версии клиента будут загружены в ClientVersionSelector

    // Инициализируем вложения из существующего клиента
    if (widget.client.attachments.isNotEmpty) {
      _cloudFiles.addAll(
        widget.client.attachments
            .map((map) => FileAttachmentItem.fromMap(map))
            .toList(),
      );
    }

    _setupFocusListeners();
    _setupChangeListeners();
  }

  /// Метод для отметки о наличии несохраненных изменений
  void _markAsChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
  }

  /// Метод для обновления данных при выборе новой версии клиента
  void _onVersionSelected(ClientEntity selectedVersion) {
    setState(() {
      _clientEntityController.updateClient(selectedVersion);
      _hasUnsavedChanges = false;
    });

    // Обновляем контроллеры и состояние с данными выбранной версии
    _controllers.initializeFromClient(selectedVersion);
    _state.initializeFromClient(selectedVersion);

    // Обновляем файловые вложения
    _cloudFiles.clear();
    if (selectedVersion.attachments.isNotEmpty) {
      _cloudFiles.addAll(
        selectedVersion.attachments
            .map((map) => FileAttachmentItem.fromMap(map))
            .toList(),
      );
    }
  }

  /// Настройка слушателей изменений для отслеживания несохраненных данных
  void _setupChangeListeners() {
    _controllers.nameCtrl.addListener(_markAsChanged);
    _controllers.shortNameCtrl.addListener(_markAsChanged);
    _controllers.innCtrl.addListener(_markAsChanged);
    _controllers.commentCtrl.addListener(_markAsChanged);
    _controllers.creationDateCtrl.addListener(_markAsChanged);
    _controllers.fixedContributionsDateCtrl.addListener(_markAsChanged);
    _controllers.contributionsIP1PercentDateCtrl.addListener(_markAsChanged);
    _controllers.salaryDateCtrl.addListener(_markAsChanged);
    _controllers.advanceDateCtrl.addListener(_markAsChanged);
    _controllers.ndflDate1Ctrl.addListener(_markAsChanged);
    _controllers.ndflDate2Ctrl.addListener(_markAsChanged);
  }

  void _setupFocusListeners() {
    // Smart parsing listeners for date fields
    _controllers.creationDateFN.addListener(() {
      ClientUtils.parseAndFormatDateField(
        _controllers.creationDateFN,
        _controllers.creationDateCtrl,
        (date) => setState(() => _state.creationDate = date),
      );
    });

    _controllers.fixedContributionsDateFN.addListener(() {
      ClientUtils.parseAndFormatFixedContributionsField(
        _controllers.fixedContributionsDateFN,
        _controllers.fixedContributionsDateCtrl,
        _state.fixedContributionsIP,
        (dates) => setState(() => _state.fixedContributionsPaymentDate = dates),
      );
    });

    _controllers.contributionsIP1PercentDateFN.addListener(() {
      ClientUtils.parseAndFormatFixedContributionsField(
        _controllers.contributionsIP1PercentDateFN,
        _controllers.contributionsIP1PercentDateCtrl,
        _state.contributionsIP1Percent,
        (dates) =>
            setState(() => _state.contributionsIP1PercentPaymentDate = dates),
      );
    });

    _controllers.salaryDateFN.addListener(() {
      ClientUtils.parseAndFormatDayField(
        _controllers.salaryDateFN,
        _controllers.salaryDateCtrl,
        (day) => setState(
          () =>
              _state.salaryPayment =
                  _state.salaryPayment?.copyWith(paymentDate: day) ??
                  PaymentSchedule(paymentDate: day, transferDate: 1),
        ),
      );
    });

    _controllers.advanceDateFN.addListener(() {
      ClientUtils.parseAndFormatDayField(
        _controllers.advanceDateFN,
        _controllers.advanceDateCtrl,
        (day) => setState(
          () =>
              _state.advancePayment =
                  _state.advancePayment?.copyWith(paymentDate: day) ??
                  PaymentSchedule(paymentDate: day, transferDate: 1),
        ),
      );
    });

    _controllers.ndflDate1FN.addListener(() {
      ClientUtils.parseAndFormatDayField(
        _controllers.ndflDate1FN,
        _controllers.ndflDate1Ctrl,
        (day) => setState(
          () =>
              _state.ndflPayment =
                  _state.ndflPayment?.copyWith(paymentDate: day) ??
                  PaymentSchedule(
                    paymentDate: day,
                    transferDate: _state.ndflPayment?.transferDate ?? 1,
                  ),
        ),
      );
    });

    _controllers.ndflDate2FN.addListener(() {
      ClientUtils.parseAndFormatDayField(
        _controllers.ndflDate2FN,
        _controllers.ndflDate2Ctrl,
        (day) => setState(
          () =>
              _state.ndflPayment =
                  _state.ndflPayment?.copyWith(transferDate: day) ??
                  PaymentSchedule(
                    paymentDate: _state.ndflPayment?.paymentDate ?? 1,
                    transferDate: day,
                  ),
        ),
      );
    });
  }

  @override
  void dispose() {
    _clientEntityController.close();
    _controllers.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      color: ClientConstants.cardColor(context),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Text(
                          'Редактировать клиента',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 24),
                        BlocProvider(
                          create: (context) => sl<ClientVersionsCubit>(),
                          child: Row(
                            children: [
                              ClientVersionSelector(
                                firmId:
                                    context
                                        .read<ActiveFirmCubit>()
                                        .state
                                        .selectedFirm
                                        ?.id ??
                                    '',
                                clientName: widget.client.name,
                                onVersionSelected: _onVersionSelected,
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                tooltip:
                                    'Перенести отметки задач в актуальную версию',
                                icon: const Icon(Icons.task_alt),
                                onPressed: () {
                                  final firm =
                                      context
                                          .read<ActiveFirmCubit>()
                                          .state
                                          .selectedFirm;
                                  if (firm == null) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Сначала выберите фирму'),
                                      ),
                                    );
                                    return;
                                  }
                                  context
                                      .read<ClientVersionsCubit>()
                                      .transferSystemTaskMarksToActual(firm.id);
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    MainInfoSection(
                      isEditing: _state.isEditing,
                      nameCtrl: _controllers.nameCtrl,
                      shortNameCtrl: _controllers.shortNameCtrl,
                      innCtrl: _controllers.innCtrl,
                      commentCtrl: _controllers.commentCtrl,
                      creationDate: _state.creationDate,
                      ownershipForm: _state.ownershipForm,
                      directorType: _state.directorType,
                      directorName: _state.directorName,
                      directorStartDate: _state.directorStartDate,
                      digitalSignatureExpiryDate:
                          _state.digitalSignatureExpiryDate,
                      kppInfo: _state.kppInfo, // Добавляем информацию о КПП
                      digitalSignatureExpiryDateCtrl:
                          _controllers.digitalSignatureExpiryDateCtrl,
                      digitalSignatureExpiryDateKey:
                          _controllers.digitalSignatureExpiryDateKey,
                      digitalSignatureExpiryDateFN:
                          _controllers.digitalSignatureExpiryDateFN,
                      creationDateCtrl: _controllers.creationDateCtrl,
                      creationDateKey: _controllers.creationDateKey,
                      creationDateFN: _controllers.creationDateFN,
                      directorNameCtrl: _controllers.directorNameCtrl,
                      directorStartDateCtrl: _controllers.directorStartDateCtrl,
                      directorStartDateKey: _controllers.directorStartDateKey,
                      directorStartDateFN: _controllers.directorStartDateFN,
                      onDateChanged: (date) {
                        setState(() {
                          _state.creationDate = date;
                          _controllers.creationDateCtrl.text =
                              date != null
                                  ? DateFormat('dd.MM.yyyy').format(date)
                                  : '';
                        });
                      },
                      onOwnershipFormChanged:
                          (form) => setState(() => _state.ownershipForm = form),
                      onDirectorTypeChanged:
                          (type) => setState(() => _state.directorType = type),
                      onDirectorNameChanged:
                          (name) => setState(() => _state.directorName = name),
                      onDirectorStartDateChanged: (date) {
                        setState(() {
                          _state.directorStartDate = date;
                          _controllers.directorStartDateCtrl.text =
                              date != null
                                  ? DateFormat('dd.MM.yyyy').format(date)
                                  : '';
                        });
                      },
                      onDigitalSignatureExpiryDateChanged: (date) {
                        setState(() {
                          _state.digitalSignatureExpiryDate = date;
                          _controllers.digitalSignatureExpiryDateCtrl.text =
                              date != null
                                  ? DateFormat('dd.MM.yyyy').format(date)
                                  : '';
                        });
                      },
                      onCreateSystemTask: () async {
                        final activeFirm =
                            context.read<ActiveFirmCubit>().state.selectedFirm;
                        if (activeFirm == null) return;

                        final currentClient =
                            _clientEntityController.getCurrentClient() ??
                            widget.client;
                        final result = await context
                            .read<TasksCubit>()
                            .createSystemTaskForClient(
                              activeFirm.id,
                              currentClient,
                            );

                        if (!mounted) return;

                        if (result.taskAlreadyExists) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(result.message),
                              backgroundColor: Colors.orange,
                              duration: const Duration(seconds: 3),
                            ),
                          );
                        } else if (result.success) {
                          // Обновляем локальное состояние клиента
                          final clientsCubit = context.read<ClientsCubit>();
                          final messenger = ScaffoldMessenger.of(context);

                          await clientsCubit.fetchClients(activeFirm.id);
                          final clientsState = clientsCubit.state;
                          if (clientsState.clients.isNotEmpty) {
                            final updatedClient = clientsState.clients
                                .firstWhere(
                                  (client) => client.id == currentClient.id,
                                  orElse: () => currentClient,
                                );
                            setState(() {
                              _clientEntityController.updateClient(
                                updatedClient,
                              );
                            });
                          }

                          messenger.showSnackBar(
                            SnackBar(
                              content: Text(result.message),
                              backgroundColor: Colors.green,
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    ContactsSection(
                      isEditing: _state.isEditing,
                      contacts: _state.contacts,
                      onRemoveContact:
                          (index) =>
                              setState(() => _state.contacts.removeAt(index)),
                      onUpdateContact:
                          (index, contact) =>
                              setState(() => _state.contacts[index] = contact),
                      onAddContact:
                          () => setState(() {
                            _state.contacts.add(
                              const ContactEntity(
                                fullName: '',
                                phone: '',
                                emails: [],
                                communicationMethods: {},
                              ),
                            );
                          }),
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    TaxSection(
                      isEditing: _state.isEditing,
                      taxSystems: _state.taxSystems,
                      profitTaxTypes: _state.profitTaxTypes,
                      vatTypes: _state.vatTypes,
                      propertyTypes: _state.propertyTypes,
                      reportingType: _state.reportingType,
                      reportingOperator: _state.reportingOperator,
                      exciseGoods: _state.exciseGoods,
                      excisePaymentTerms: _state.excisePaymentTerms,
                      edoOperators: _state.edoOperators,
                      enpType: _state.enpType,
                      ndflType: _state.ndflType,
                      onTaxSystemAdd:
                          (item) => setState(() => _state.taxSystems.add(item)),
                      onTaxSystemRemove:
                          (item) =>
                              setState(() => _state.taxSystems.remove(item)),
                      onProfitTaxTypeAdd:
                          (item) =>
                              setState(() => _state.profitTaxTypes.add(item)),
                      onProfitTaxTypeRemove:
                          (item) => setState(
                            () => _state.profitTaxTypes.remove(item),
                          ),
                      onVatTypeAdd:
                          (item) => setState(() => _state.vatTypes.add(item)),
                      onVatTypeRemove:
                          (item) =>
                              setState(() => _state.vatTypes.remove(item)),
                      onPropertyTypeAdd:
                          (item) =>
                              setState(() => _state.propertyTypes.add(item)),
                      onPropertyTypeRemove:
                          (item) =>
                              setState(() => _state.propertyTypes.remove(item)),
                      onReportingTypeChanged:
                          (value) =>
                              setState(() => _state.reportingType = value),
                      onReportingOperatorChanged:
                          (value) =>
                              setState(() => _state.reportingOperator = value),
                      onExciseGoodAdd:
                          (item) =>
                              setState(() => _state.exciseGoods.add(item)),
                      onExciseGoodRemove:
                          (item) =>
                              setState(() => _state.exciseGoods.remove(item)),
                      onExcisePaymentTermAdd:
                          (item) => setState(
                            () => _state.excisePaymentTerms.add(item),
                          ),
                      onExcisePaymentTermRemove:
                          (item) => setState(
                            () => _state.excisePaymentTerms.remove(item),
                          ),
                      onEdoOperatorAdd:
                          (item) =>
                              setState(() => _state.edoOperators.add(item)),
                      onEdoOperatorRemove:
                          (item) =>
                              setState(() => _state.edoOperators.remove(item)),
                      onEnpTypeChanged:
                          (value) => setState(() => _state.enpType = value),
                      onNdflTypeChanged:
                          (value) => setState(() => _state.ndflType = value),
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                      highlightEnp: false,
                      enpKey: _controllers.enpFieldKey,
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    PaymentSchedulesSection(
                      isEditing: _state.isEditing,
                      salaryPayment: _state.salaryPayment,
                      salaryPaymentEnabled: _state.salaryPaymentEnabled,
                      onSalaryPaymentEnabledChanged:
                          (v) =>
                              setState(() => _state.salaryPaymentEnabled = v),
                      cashOrBank: _state.cashOrBank,
                      onCashOrBankChanged:
                          (cashOrBank) =>
                              setState(() => _state.cashOrBank = cashOrBank),
                      cashPayment: _state.cashPayment,
                      onCashPaymentChanged:
                          (value) => setState(() => _state.cashPayment = value),
                      bankPayment: _state.bankPayment,
                      onBankPaymentChanged:
                          (value) => setState(() => _state.bankPayment = value),
                      hasEmployees: _state.hasEmployees,
                      onHasEmployeesChanged:
                          (value) =>
                              setState(() => _state.hasEmployees = value),
                      isSoleFounderDirector: _state.isSoleFounderDirector,
                      onIsSoleFounderDirectorChanged:
                          (value) => setState(
                            () => _state.isSoleFounderDirector = value,
                          ),
                      advancePayment: _state.advancePayment,
                      ndflPayment: _state.ndflPayment,
                      onSalaryChanged:
                          (sch) => setState(() => _state.salaryPayment = sch),
                      onAdvanceChanged:
                          (sch) => setState(() => _state.advancePayment = sch),
                      onNdflChanged:
                          (sch) => setState(() => _state.ndflPayment = sch),
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                      salaryDateCtrl: _controllers.salaryDateCtrl,
                      salaryDateFN: _controllers.salaryDateFN,
                      advanceDateCtrl: _controllers.advanceDateCtrl,
                      advanceDateFN: _controllers.advanceDateFN,
                      ndflDate1Ctrl: _controllers.ndflDate1Ctrl,
                      ndflDate1FN: _controllers.ndflDate1FN,
                      ndflDate2Ctrl: _controllers.ndflDate2Ctrl,
                      ndflDate2FN: _controllers.ndflDate2FN,
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    AdditionalSection(
                      isEditing: _state.isEditing,
                      additionalTags: _state.additionalTags,
                      activityTypes: _state.activityTypes,
                      fixedContributionsIP: _state.fixedContributionsIP,
                      fixedContributionsPaymentDate:
                          _state.fixedContributionsPaymentDate,
                      contributionsIP1Percent: _state.contributionsIP1Percent,
                      contributionsIP1PercentPaymentDate:
                          _state.contributionsIP1PercentPaymentDate,
                      fixedContributionsDateCtrl:
                          _controllers.fixedContributionsDateCtrl,
                      fixedContributionsDateKey:
                          _controllers.fixedContributionsDateKey,
                      fixedContributionsDateFN:
                          _controllers.fixedContributionsDateFN,
                      contributionsIP1PercentDateCtrl:
                          _controllers.contributionsIP1PercentDateCtrl,
                      contributionsIP1PercentDateKey:
                          _controllers.contributionsIP1PercentDateKey,
                      contributionsIP1PercentDateFN:
                          _controllers.contributionsIP1PercentDateFN,
                      onAddTag:
                          (tag) =>
                              setState(() => _state.additionalTags.add(tag)),
                      onRemoveTag:
                          (tag) =>
                              setState(() => _state.additionalTags.remove(tag)),
                      onActivityTypeAdd:
                          (type) =>
                              setState(() => _state.activityTypes.add(type)),
                      onActivityTypeRemove:
                          (type) =>
                              setState(() => _state.activityTypes.remove(type)),
                      onFixedContributionAdd:
                          (type) => setState(
                            () => _state.fixedContributionsIP.add(type),
                          ),
                      onFixedContributionRemove: (type) {
                        setState(() {
                          _state.fixedContributionsIP.remove(type);
                          _state.fixedContributionsPaymentDate.clear();
                          _controllers.fixedContributionsDateCtrl.clear();
                        });
                      },
                      onFixedContributionDateChanged: (dates) {
                        setState(() {
                          _state.fixedContributionsPaymentDate = dates;
                          _controllers
                              .fixedContributionsDateCtrl
                              .text = ClientConstants.formatPaymentDates(dates);
                        });
                      },
                      onContributionsIP1PercentAdd:
                          (type) => setState(
                            () => _state.contributionsIP1Percent.add(type),
                          ),
                      onContributionsIP1PercentRemove: (type) {
                        setState(() {
                          _state.contributionsIP1Percent.remove(type);
                          _state.contributionsIP1PercentPaymentDate.clear();
                          _controllers.contributionsIP1PercentDateCtrl.clear();
                        });
                      },
                      onContributionsIP1PercentDateChanged: (dates) {
                        setState(() {
                          _state.contributionsIP1PercentPaymentDate = dates;
                          _controllers
                              .contributionsIP1PercentDateCtrl
                              .text = ClientConstants.formatPaymentDates(dates);
                        });
                      },
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    ExtendedInfoSection(
                      isEditing: _state.isEditing,
                      ownershipForm: _state.ownershipForm,
                      fnsInfo: _state.fnsInfo,
                      kppInfo: _state.kppInfo,
                      sfrInfo: _state.sfrInfo,
                      okpo: _state.okpo,
                      ogrn: _state.ogrn,
                      legalAddress: _state.legalAddress,
                      actualAddress: _state.actualAddress,
                      actualAddressSameAsLegal: _state.actualAddressSameAsLegal,
                      onFnsInfoChanged:
                          (fnsInfo) => setState(() {
                            _state.fnsInfo.clear();
                            _state.fnsInfo.addAll(fnsInfo);
                          }),
                      onKppInfoChanged:
                          (kppInfo) => setState(() {
                            _state.kppInfo.clear();
                            _state.kppInfo.addAll(kppInfo);
                          }),
                      onSfrInfoChanged:
                          (sfrInfo) => setState(() => _state.sfrInfo = sfrInfo),
                      onOkpoChanged:
                          (okpo) => setState(() => _state.okpo = okpo),
                      onOgrnChanged:
                          (ogrn) => setState(() => _state.ogrn = ogrn),
                      onLegalAddressChanged:
                          (legalAddress) => setState(
                            () => _state.legalAddress = legalAddress,
                          ),
                      onActualAddressChanged:
                          (actualAddress) => setState(
                            () => _state.actualAddress = actualAddress,
                          ),
                      onActualAddressSameAsLegalChanged:
                          (same) => setState(
                            () => _state.actualAddressSameAsLegal = same,
                          ),
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    // Файловые вложения
                    if (_state.isEditing)
                      UnifiedAttachmentsSection(
                        cloudFiles: _cloudFiles,
                        onAddCloudFile: _addCloudFile,
                        onRemoveCloudFile: _removeCloudFile,
                        onUpdateCloudFile: _updateCloudFile,
                        onTaskAutoSave: _autoSaveClient,
                      )
                    else
                      FilesGridSection(
                        files: _state.attachments,
                        title: 'Файловые вложения',
                      ),

                    const SizedBox(height: ClientConstants.sectionSpacing),

                    // Комментарии к вложению
                    if (_state.isEditing)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Комментарии к вложению',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.w600),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _controllers.attachmentCommentsCtrl,
                            maxLines: 3,
                            decoration: const InputDecoration(
                              hintText: 'Введите комментарии к вложениям...',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) {
                              setState(() {
                                _state.attachmentComments =
                                    value.isEmpty ? null : value;
                              });
                              _autoSaveClient();
                            },
                          ),
                        ],
                      )
                    else if (_state.attachmentComments != null &&
                        _state.attachmentComments!.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Комментарии к вложению',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.w600),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _state.attachmentComments!,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),

                    const SizedBox(height: ClientConstants.sectionSpacing),

                    PatentsSection(
                      isEditing: _state.isEditing,
                      patents: _state.patents,
                      onUpdatePatent:
                          (index, patent) =>
                              setState(() => _state.patents[index] = patent),
                      onRemovePatent:
                          (index) =>
                              setState(() => _state.patents.removeAt(index)),
                      onAddPatent:
                          () => setState(() {
                            final startDate = DateTime.now();
                            _state.patents.add(
                              PatentEntity(
                                startDate: startDate,
                                endDate: DateTime(
                                  startDate.year + 1,
                                  startDate.month,
                                  startDate.day,
                                ),
                                issueDate: startDate,
                                patentAmount: 0,
                                patentNumber:
                                    'Новый патент ${DateTime.now().millisecondsSinceEpoch}',
                                patentTitle: 'Новый патент',
                              ),
                            );
                          }),
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),
                    BlocBuilder<
                      ClientEntityControllerCubit,
                      ClientEntityControllerState
                    >(
                      bloc: _clientEntityController,
                      builder: (context, state) {
                        if (state is! ClientEntityControllerLoaded) {
                          return const SizedBox.shrink();
                        }
                        return YearlyTasksCalendar(
                          client: state.client,
                          selectedYear: _selectedYear,
                          onYearChanged: (year) {
                            setState(() {
                              _selectedYear = year;
                            });
                          },
                          onTaskTap: _onSystemTaskTap,
                          isEditing: _state.isEditing,
                        );
                      },
                    ),
                    const SizedBox(height: 16),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (_state.isEditing) ...[
                          TextButton(
                            onPressed:
                                _hasActiveFileOperations
                                    ? null
                                    : () => setState(
                                      () => _state.isEditing = false,
                                    ),
                            child: const Text('Отмена'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed:
                                _hasActiveFileOperations ? null : _saveClient,
                            child: const Text('Сохранить'),
                          ),
                        ] else ...[
                          TextButton(
                            onPressed: widget.onCancel,
                            child: const Text('Закрыть'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed:
                                () => setState(() => _state.isEditing = true),
                            child: const Text('Редактировать'),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            right: 8,
            top: 8,
            child: IconButton(
              icon: const Icon(Icons.close),
              onPressed: widget.onCancel,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveClient() async {
    // Parse dates from text fields before validation
    if (_controllers.creationDateCtrl.text.isNotEmpty) {
      try {
        _state.creationDate = DateFormat(
          'dd.MM.yyyy',
        ).parseStrict(_controllers.creationDateCtrl.text);
      } catch (_) {
        _state.creationDate = ClientUtils.tryParseDate(
          _controllers.creationDateCtrl.text,
        );
        if (_state.creationDate != null) {
          _controllers.creationDateCtrl.text = DateFormat(
            'dd.MM.yyyy',
          ).format(_state.creationDate!);
        }
      }
    } else {
      _state.creationDate = null;
    }

    if (_state.fixedContributionsIP.isNotEmpty &&
        _controllers.fixedContributionsDateCtrl.text.isNotEmpty) {
      try {
        _state
            .fixedContributionsPaymentDate = ClientConstants.parsePaymentDates(
          _controllers.fixedContributionsDateCtrl.text,
          _state.fixedContributionsIP.first,
        );
      } catch (e) {
        // Validator will show the error
      }
    }

    if (_state.contributionsIP1Percent.isNotEmpty &&
        _controllers.contributionsIP1PercentDateCtrl.text.isNotEmpty) {
      try {
        _state.contributionsIP1PercentPaymentDate =
            ClientConstants.parsePaymentDates(
              _controllers.contributionsIP1PercentDateCtrl.text,
              _state.contributionsIP1Percent.first,
            );
      } catch (e) {
        // Validator will show the error
      }
    }

    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Пожалуйста, исправьте ошибки в форме')),
      );

      final fieldsToScroll = [
        _controllers.creationDateKey,
        _controllers.fixedContributionsDateKey,
        _controllers.contributionsIP1PercentDateKey,
        ClientValidationUtils.getInvalidPaymentScheduleKey(
          _state.salaryPayment,
          _state.advancePayment,
          _state.ndflPayment,
          _controllers.salaryPaymentKey,
          _controllers.advancePaymentKey,
          _controllers.ndflPaymentKey,
        ),
        _controllers.enpFieldKey,
      ];

      ClientValidationUtils.scrollToFirstInvalidField(fieldsToScroll);
      return;
    }

    final activeFirm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (activeFirm == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Сначала выберите фирму')));
      return;
    }

    // Используем текущую версию клиента для сохранения
    final clientToSave =
        _clientEntityController.getCurrentClient() ?? widget.client;
    final newClient = _state.createClientEntity(
      clientToSave.id,
      _controllers.nameCtrl.text.trim(),
      _controllers.innCtrl.text.trim().isEmpty
          ? null
          : _controllers.innCtrl.text.trim(),
      _controllers.shortNameCtrl.text.trim().isEmpty
          ? null
          : _controllers.shortNameCtrl.text.trim(),
      _controllers.commentCtrl.text.trim().isEmpty
          ? null
          : _controllers.commentCtrl.text.trim(),
    );

    final cubit = context.read<ClientsCubit>();
    final savedClient = await cubit.saveClient(activeFirm.id, newClient);
    if (savedClient != null) {
      widget.onSaved(savedClient);
      if (mounted) {
        setState(() {
          _state.isEditing = false;
          _hasUnsavedChanges =
              false; // Сбрасываем флаг после успешного сохранения
        });
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Не удалось сохранить клиента')),
        );
      }
    }
  }

  // === Работа с файлами ===
  void _addCloudFile(FileAttachmentItem file) {
    setState(() => _cloudFiles.add(file));
  }

  void _removeCloudFile(FileAttachmentItem file) {
    setState(() {
      _cloudFiles.remove(file);
      _state.attachments =
          _cloudFiles
              .where(
                (f) =>
                    f.status == FileAttachmentStatus.uploaded &&
                    f.fileKey != null,
              )
              .map((f) => f.toMap())
              .toList();
    });
  }

  void _updateCloudFile(FileAttachmentItem updated) {
    setState(() {
      final idx = _cloudFiles.indexWhere((f) => f.name == updated.name);
      if (idx != -1) _cloudFiles[idx] = updated;

      // Обновляем список вложений в состоянии
      _state.attachments =
          _cloudFiles
              .where(
                (f) =>
                    f.status == FileAttachmentStatus.uploaded &&
                    f.fileKey != null,
              )
              .map((f) => f.toMap())
              .toList();
    });
  }

  Future<void> _autoSaveClient() async {
    if (_hasActiveFileOperations) return;

    final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (firm == null) return;

    final cubit = context.read<ClientsCubit>();
    final client = _buildClientEntity();

    ClientEntity? savedClient;
    if (_wasAutoSaved) {
      savedClient = await cubit.saveClient(firm.id, client);
    } else if (_cloudFiles.any(
      (f) => f.status == FileAttachmentStatus.uploaded,
    )) {
      savedClient = await cubit.saveClient(firm.id, client);
      _wasAutoSaved = true;

      if (mounted && savedClient != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Изменения клиента сохранены при загрузке файла',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }

    if (savedClient != null) {
      // тихое обновление, чтобы не сбрасывать фокус и т.д.
      _state.updateFromEntity(savedClient);
    }
  }

  ClientEntity _buildClientEntity() {
    // Используем _state для создания сущности и добавляем вложения
    final clientToSave =
        _clientEntityController.getCurrentClient() ?? widget.client;
    return _state.createClientEntity(
      clientToSave.id,
      _controllers.nameCtrl.text.trim(),
      _controllers.innCtrl.text.trim().isEmpty
          ? null
          : _controllers.innCtrl.text.trim(),
      _controllers.shortNameCtrl.text.trim().isEmpty
          ? null
          : _controllers.shortNameCtrl.text.trim(),
      _controllers.commentCtrl.text.trim().isEmpty
          ? null
          : _controllers.commentCtrl.text.trim(),
    );
  }

  // Построение сетки 4x3 календарей
  Widget _buildYearlyCalendars(ClientEntity client) {
    final execDates = client.systemTaskUids.map((e) => e.dueDate).toList();

    Widget buildMonthCalendar(String name, int index) {
      return Expanded(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).dividerColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Text(
                  name,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(4),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 7,
                    childAspectRatio: 1,
                    crossAxisSpacing: 2,
                    mainAxisSpacing: 2,
                  ),
                  itemCount: 31,
                  itemBuilder: (ctx, i) {
                    final day = i + 1;
                    final taskOnDate = client.systemTaskUids.firstWhereOrNull(
                      (uidInfo) =>
                          uidInfo.dueDate?.month == index + 1 &&
                          uidInfo.dueDate?.day == day,
                    );
                    final selected = taskOnDate != null;

                    final tooltipMessage =
                        selected ? 'UID: ${taskOnDate.uid}' : '';

                    return Tooltip(
                      message: tooltipMessage,
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              selected ? Theme.of(context).primaryColor : null,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Center(
                          child: Text(
                            '$day',
                            style: TextStyle(
                              fontSize: 10,
                              color: selected ? Colors.white : null,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    }

    final monthNames = [
      'Янв',
      'Фев',
      'Мар',
      'Апр',
      'Май',
      'Июн',
      'Июл',
      'Авг',
      'Сен',
      'Окт',
      'Ноя',
      'Дек',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Системные задачи',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        const SizedBox(height: 12),
        Row(
          children: List.generate(
            4,
            (i) => buildMonthCalendar(monthNames[i], i),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(
            4,
            (i) => buildMonthCalendar(monthNames[i + 4], i + 4),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(
            4,
            (i) => buildMonthCalendar(monthNames[i + 8], i + 8),
          ),
        ),
      ],
    );
  }

  /// Обработчик клика на системную задачу
  void _onSystemTaskTap(String taskId) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: const Text('Системная задача'),
          content: const Text('Вы хотите удалить эту системную задачу?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Отмена'),
            ),
            ElevatedButton.icon(
              icon: const Icon(Icons.delete_forever),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              label: const Text('Удалить'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _deleteSystemTask(taskId);
              },
            ),
          ],
        );
      },
    );
  }

  /// Удаление системной задачи
  Future<void> _deleteSystemTask(String taskId) async {
    final firmId = context.read<ActiveFirmCubit>().state.selectedFirm!.id;
    final client = _clientEntityController.getCurrentClient() ?? widget.client;

    final result = await context.read<TasksCubit>().deleteSystemTask(
      firmId,
      taskId,
      client,
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.message),
          backgroundColor: result.success ? Colors.green : Colors.red,
        ),
      );

      if (result.success) {
        // Обновляем локальное состояние клиента
        final updatedSystemTaskUids =
            client.systemTaskUids
                .where((systemTask) => systemTask.uid != taskId)
                .toList();

        final updatedClient = client.copyWith(
          systemTaskUids: updatedSystemTaskUids,
        );

        setState(() {
          _clientEntityController.updateClient(updatedClient);
        });
      }
    }
  }
}
