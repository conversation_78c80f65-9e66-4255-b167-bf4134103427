
Идентификатор - d4eh9o1sau3l2mv3g09n
Описание - ⏰ Управляет запланированными событиями: создание, обновление, удаление и получение.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> **Аутентификация**: Предполагается, что доступ к этой функции ограничен на уровне API Gateway и она не вызывается напрямую клиентом. Аутентификация через JWT токен пользователя не требуется.
	-> Тело запроса:
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
		- `event_id` (string, необязательно): ID события.
			- Обязателен для `DELETE`.
			- Обязателен для обновления при `UPSERT`.
			- Приоритетен для `GET`.
		- `custom_identifier` (string, необязательно): Кастомный идентификатор для поиска событий при `GET`. Используется, если `event_id` не указан.
		- `payload` (object, необязательно): Объект с данными события. Обязателен для `UPSERT`.

Внутренняя работа:
    -> **Парсинг запроса**: Используется `utils.request_parser` для извлечения данных из тела запроса, включая `action`, `event_id`, `custom_identifier` и `payload`.
    -> **Подключение к БД**: Инициализируется драйвер YDB для `scheduler-database` с использованием переменных окружения `YDB_ENDPOINT_SCHEDULER` и `YDB_DATABASE_SCHEDULER`.
    -> **Маршрутизация по `action`** в транзакции:
        -> **`GET`**:
            -> Если указан `event_id`, выполняется запрос на выборку события по ID; если не найдено, ошибка 404.
            -> Иначе, если указан `custom_identifier`, выборка событий по нему.
            -> Иначе, выборка всех активных событий (`is_active = true`).
            -> Результаты форматируются, парсятся JSON-поля и возвращаются в `data`.
        -> **`UPSERT`**:
            -> Если цель - `endpoints-manager`, модифицируется `request_body_json` для упрощения payload.
            -> Если `event_id` указан (обновление): обновляются поля в таблице, включая `updated_at`.
            -> Иначе (создание): генерируется новый `event_id`, устанавливается `created_at`, `is_active=true` (если не указано), обновляется в таблице с помощью UPSERT.
            -> Возвращается 201 для создания или 200 для обновления.
        -> **`DELETE`**: Удаление записи по `event_id` из таблицы.
    -> **Обработка ошибок**: Логирование, возврат 400 для логики, 404 для не найденного, 500 для внутренних ошибок.

На выходе:
	-> `200 OK` (GET): `{"data": [{...}]}` или `{"data": {...}}`
	-> `201 Created` (UPSERT/create): `{"message": "Scheduled event created", "event_id": "..."}`
	-> `200 OK` (UPSERT/update): `{"message": "Scheduled event updated"}`
	-> `200 OK` (DELETE): `{"message": "Scheduled event deleted"}`
	-> `400 Bad Request`: Неверные или отсутствующие параметры.
	-> `404 Not Found`: Указанный `event_id` не найден.
	-> `500 Internal Server Error`: Ошибка на стороне сервера или БД.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/ydb_utils.py` ([[📄 utils - ydb_utils.md]]), `utils/request_parser.py` ([[📄 utils - request_parser.md]])
- **Переменные окружения**:
	- `YDB_ENDPOINT_SCHEDULER` - Эндпоинт для [[💾 scheduler-database - База данных YandexDatabase.md]].
	- `YDB_DATABASE_SCHEDULER` - Путь к [[💾 scheduler-database - База данных YandexDatabase.md]].
	- `SA_KEY_FILE` ([[ydb_sa_key.json]])