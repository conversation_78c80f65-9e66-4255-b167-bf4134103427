import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/refresh_token_usecase.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

part 'refresh_token_state.dart';

class RefreshTokenCubit extends Cubit<RefreshTokenState> {
  final RefreshTokenUseCase _refreshTokenUseCase;

  RefreshTokenCubit({
    required RefreshTokenUseCase refreshTokenUseCase,
  }) : _refreshTokenUseCase = refreshTokenUseCase,
       super(RefreshTokenInitial());

  Future<void> refreshUserToken({
    required String email,
    required String password,
  }) async {
    NetworkLogger.printInfo('REFRESH_TOKEN_CUBIT: Начинаем обновление токена для: $email');
    emit(RefreshTokenLoading());
    
    final result = await _refreshTokenUseCase(
      RefreshTokenParams(email: email, password: password),
    );
    
    result.fold(
      (failure) {
        NetworkLogger.printError('REFRESH_TOKEN_CUBIT: Ошибка обновления токена:', failure);
        emit(RefreshTokenError(_mapFailure(failure)));
      },
      (_) {
        NetworkLogger.printSuccess('REFRESH_TOKEN_CUBIT: Токен успешно обновлен');
        emit(RefreshTokenSuccess());
      },
    );
  }

  void resetState() {
    emit(RefreshTokenInitial());
  }

  String _mapFailure(Failure failure) {
    if (failure is ServerFailure &&
        failure.details != null &&
        failure.details!.isNotEmpty) {
      return '${failure.message}\n\nДетали: ${failure.details}';
    }
    return failure.message;
  }
}