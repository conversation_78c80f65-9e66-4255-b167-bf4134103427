import 'dart:async';
import 'dart:html' as html;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dropzone/flutter_dropzone.dart';
import 'package:flutter/services.dart';
import 'package:collection/collection.dart';

import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_state.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';

import './seamless/description_models.dart';
import './seamless/attachment_image_selector.dart';
import './seamless/block_widgets.dart';
import './seamless/description_state_manager.dart';

class SeamlessDescriptionField extends StatefulWidget {
  final String? initialText;
  final void Function(String description) onChanged;
  final List<FileAttachmentItem> attachments;
  final VoidCallback? onTaskAutoSave;
  final void Function(FileAttachmentItem)? onFileAdded;
  final void Function(FileAttachmentItem)? onFileUpdated;

  const SeamlessDescriptionField({
    super.key,
    required this.initialText,
    required this.onChanged,
    required this.attachments,
    this.onTaskAutoSave,
    this.onFileAdded,
    this.onFileUpdated,
  });

  @override
  State<SeamlessDescriptionField> createState() =>
      _SeamlessDescriptionFieldState();
}

class _SeamlessDescriptionFieldState extends State<SeamlessDescriptionField> {
  late final DescriptionStateManager _stateManager;
  DropzoneViewController? _dzController;
  bool _hover = false;
  StreamSubscription? _storageSubscription;

  @override
  void initState() {
    super.initState();
    _stateManager = DescriptionStateManager(
      initialText: widget.initialText,
      attachments: widget.attachments,
      onUpdate: () => setState(() {}),
      onContentChanged: widget.onChanged,
    );
    _storageSubscription = context.read<TariffsAndStorageCubit>().stream.listen(
      _handleStorageState,
    );
  }

  void _handleStorageState(TariffsAndStorageState state) {
    if (state is FileUploadCompleted) {
      final fileName = state.fileName;
      for (final b in _stateManager.blocks.where(
        (e) => e.type == BlockType.image,
      )) {
        if (b.fileKey != null &&
            b.fileKey!.startsWith('local/') &&
            b.fileName == fileName) {
          setState(() {
            b.fileKey = state.fileKey;
          });
          if (widget.onFileUpdated != null) {
            final existingFile = widget.attachments.firstWhereOrNull(
              (f) => f.name == fileName,
            );
            if (existingFile != null) {
              widget.onFileUpdated!(
                existingFile.copyWith(
                  fileKey: state.fileKey,
                  status: FileAttachmentStatus.uploaded,
                ),
              );
            }
          }
          break;
        }
      }
    }
  }

  @override
  void dispose() {
    _stateManager.dispose();
    _storageSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Описание', style: Theme.of(context).textTheme.labelLarge),
        const SizedBox(height: 8),
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.image),
              tooltip: 'Вставить изображение из вложений',
              onPressed: () async {
                _stateManager.cacheFocus();
                final selectedAttachment = await showAttachmentImageSelector(
                  context,
                  widget.attachments,
                );
                if (selectedAttachment != null) {
                  _stateManager.insertImageFromAttachment(selectedAttachment);
                }
              },
            ),
            const Spacer(),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          constraints: const BoxConstraints(minHeight: 300, maxHeight: 500),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.05),
            border: Border.all(color: Colors.red, width: 2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _buildBlocks(),
                  ),
                ),
              ),
              Positioned.fill(
                child: IgnorePointer(
                  ignoring: true,
                  child: DropzoneView(
                    onCreated: (c) => _dzController = c,
                    onHover: () => setState(() => _hover = true),
                    onLeave: () => setState(() => _hover = false),
                    onDropFile: _onDrop,
                  ),
                ),
              ),
              if (_hover)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      border: Border.all(color: Colors.red, width: 2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Text(
                        'Отпустите для загрузки изображения',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildBlocks() {
    final widgets = <Widget>[];
    for (int i = 0; i < _stateManager.blocks.length; i++) {
      final block = _stateManager.blocks[i];
      if (block.type == BlockType.text) {
        widgets.add(
          TextBlockWidget(
            block: block,
            index: i,
            onTextChanged: () => _stateManager.onContentChanged,
            onBackspaceAtStart: _stateManager.handleBackspaceAtStart,
            onMoveFocusVertically: _stateManager.moveFocusVertically,
          ),
        );
      } else {
        widgets.add(
          ImageBlockWidget(
            block: block,
            index: i,
            onRemove: _stateManager.removeImageBlock,
          ),
        );
      }
    }
    return widgets;
  }

  Future<void> _onDrop(dynamic ev) async {
    setState(() => _hover = false);

    if (ev is! html.File) return;
    final file = ev;

    final ext = file.name.split('.').last.toLowerCase();
    const allowedExtensions = [
      'jpg',
      'jpeg',
      'png',
      'webp',
      'jpe',
      'gif',
      'bmp',
    ];
    if (!allowedExtensions.contains(ext)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Неподдерживаемый формат файла: .$ext'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Файл слишком большой. Максимум 10MB'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final reader = html.FileReader();
    reader.readAsArrayBuffer(file);
    await reader.onLoadEnd.first;
    final bytes = reader.result as Uint8List;

    const w = 300;
    const h = 200;
    final localKey = 'local/${file.name}';

    _stateManager.insertImageBlock(localKey, file.name, w, h);

    final firmState = context.read<ActiveFirmCubit>().state;
    final firmId = firmState.selectedFirm?.id;
    if (firmId == null) return;

    try {
      if (mounted) {
        context.read<TariffsAndStorageCubit>().uploadFile(
          firmId: firmId,
          fileName: file.name,
          fileBytes: bytes,
        );

        if (widget.onFileAdded != null) {
          widget.onFileAdded!(
            FileAttachmentItem(
              name: file.name,
              fileKey: null,
              status: FileAttachmentStatus.uploading,
            ),
          );
        }
        if (widget.onTaskAutoSave != null) {
          widget.onTaskAutoSave!();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка загрузки файла: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
