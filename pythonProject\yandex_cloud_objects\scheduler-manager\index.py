import json
import os
import logging
import ydb
from utils import ydb_utils, request_parser

from get import get_events
from upsert import upsert_event
from delete import delete_event
from custom_errors import LogicError, NotFoundError

logging.basicConfig(level=logging.INFO)

def handler(event, context):
    try:
        # Аутентификация пользователя здесь не требуется, т.к. функция внутренняя
        data = request_parser.parse_request_body(event)
        action = data.get('action')
        
        if not action:
            raise LogicError("Action is a required parameter.")

        driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_SCHEDULER"],
            os.environ["YDB_DATABASE_SCHEDULER"]
        )
        pool = ydb.SessionPool(driver)
        table_name = "ScheduledEvents"

        def transaction_router(session):
            event_id = data.get('event_id')
            payload = data.get('payload', {})

            if action == "GET":
                return get_events(session, table_name, event_id)
            
            elif action == "UPSERT":
                return upsert_event(session, table_name, payload, event_id)
            
            elif action == "DELETE":
                return delete_event(session, table_name, event_id)
            
            else:
                raise LogicError(f"Invalid action '{action}'.")

        return pool.retry_operation_sync(transaction_router)

    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing scheduled event request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}