import 'package:flutter/material.dart';

import 'doc_preview_widget.dart';
import 'file_preview_widget.dart';
import 'image_preview_widget.dart';
import 'xml_preview_widget.dart';

class FilePreviewDialog extends StatelessWidget {
  final String fileKey;
  final String fileName;

  const FilePreviewDialog({
    super.key,
    required this.fileKey,
    required this.fileName,
  });

  static void show(BuildContext context, String fileKey, String fileName) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'Close',
      barrierColor: Colors.black.withOpacity(0.85),
      pageBuilder: (ctx, anim1, anim2) {
        return FilePreviewDialog(fileKey: fileKey, fileName: fileName);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final extension = fileName.split('.').last.toLowerCase();
    Widget viewer;

    if (extension == 'pdf') {
      viewer = PdfPreviewWidget(fileKey: fileKey, fileName: fileName);
    } else if (extension == 'doc' ||
        extension == 'docx' ||
        extension == 'xls' ||
        extension == 'xlsx') {
      viewer = DocPreviewWidget(
        fileKey: fileKey,
        fileName: fileName,
        footerText: 'Нажмите, для дополнительных параметров предпросмотра ▼',
      );
    } else if (['png', 'jpeg', 'jpg', 'jpe', 'webp'].contains(extension)) {
      viewer = ImagePreviewWidget(fileKey: fileKey, fileName: fileName);
    } else if (extension == 'xml') {
      viewer = XmlPreviewWidget(fileKey: fileKey, fileName: fileName);
    } else {
      viewer = Center(
        child: Text(
          'Предпросмотр для файлов .$extension не поддерживается.',
          style: const TextStyle(color: Colors.white, fontSize: 16),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(
                40,
                60,
                40,
                40,
              ), // Отступы для контента (больше сверху под крестик)
              child: viewer,
            ),
          ),
          Positioned(
            top: 16,
            left: 16,
            child: Text(
              fileName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Positioned(
            top: 16,
            right: 16,
            child: IconButton(
              icon: const Icon(Icons.close, color: Colors.white, size: 30),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
        ],
      ),
    );
  }
}
