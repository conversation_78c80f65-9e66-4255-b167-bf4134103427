import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/file_preview/inline_image_widget.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import './description_models.dart';

class TextBlockWidget extends StatelessWidget {
  final Block block;
  final int index;
  final VoidCallback onTextChanged;
  final Function(int) onBackspaceAtStart;
  final Function(int, {required bool directionUp}) onMoveFocusVertically;

  const TextBlockWidget({
    super.key,
    required this.block,
    required this.index,
    required this.onTextChanged,
    required this.onBackspaceAtStart,
    required this.onMoveFocusVertically,
  });

  @override
  Widget build(BuildContext context) {
    block.focusNode!.onKeyEvent = (node, event) {
      if (event is KeyDownEvent) {
        final ctrl = block.controller!;
        // Backspace at start
        if (event.logicalKey == LogicalKeyboardKey.backspace &&
            ctrl.selection.baseOffset == 0) {
          onBackspaceAtStart(index);
          return KeyEventResult.handled;
        }
        // Arrow navigation
        if (event.logicalKey == LogicalKeyboardKey.arrowUp &&
            ctrl.selection.baseOffset == 0) {
          onMoveFocusVertically(index, directionUp: true);
          return KeyEventResult.handled;
        }
        if (event.logicalKey == LogicalKeyboardKey.arrowDown &&
            ctrl.selection.baseOffset == ctrl.text.length) {
          onMoveFocusVertically(index, directionUp: false);
          return KeyEventResult.handled;
        }
      }
      return KeyEventResult.ignored;
    };

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: TextField(
        controller: block.controller,
        focusNode: block.focusNode,
        maxLines: null,
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: index == 0 ? 'Введите описание...' : '',
          contentPadding: const EdgeInsets.symmetric(vertical: 4),
        ),
        onChanged: (_) => onTextChanged(),
      ),
    );
  }
}

class ImageBlockWidget extends StatelessWidget {
  final Block block;
  final int index;
  final Function(int) onRemove;

  const ImageBlockWidget({
    super.key,
    required this.block,
    required this.index,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    // 1. Если изображение ещё загружается локально – показываем только placeholder
    if (block.fileKey?.startsWith('local/') == true) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Stack(
          children: [
            _buildLoadingTile(),
            Positioned(top: 4, right: 4, child: _buildRemoveButton()),
          ],
        ),
      );
    }

    // 2. Если изображение уже имеет fileKey из API – накладываем изображение на placeholder,
    //    чтобы placeholder не исчезал до полной отрисовки картинки
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Нижний слой – всегда placeholder
          _buildLoadingTile(),
          // Верхний слой – сама картинка (InlineImageWidget уже имеет свои placeholder'ы при загрузке)
          SizedBox(
            width: double.infinity,
            child: InlineImageWidget(
              fileKey: block.fileKey!,
              fileName: block.fileName ?? 'Изображение',
              originalWidth: block.width,
              originalHeight: block.height,
            ),
          ),
          Positioned(top: 4, right: 4, child: _buildRemoveButton()),
        ],
      ),
    );
  }

  Widget _buildRemoveButton() {
    return GestureDetector(
      onTap: () => onRemove(index),
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(Icons.close, color: Colors.white, size: 16),
      ),
    );
  }

  // Единый placeholder на основе LoadingTile (48px высотой)
  Widget _buildLoadingTile() {
    return const SizedBox(
      width: double.infinity,
      height: 48,
      child: LoadingTile(height: 48),
    );
  }
}
