class TasksTableHelpers {
  static String translateStatus(String status) {
    switch (status) {
      case 'in_progress':
      case 'ongoing':
        return 'Активна';
      case 'completed':
      case 'done':
        return 'Завершена';
      case 'cancelled':
        return 'Отменена';
      case 'pending':
        return 'Новая';
      case 'testing':
        return 'Тестирование';
      case 'blocked':
        return 'Заблокирована';
      default:
        return status;
    }
  }

  static String translatePriority(String priority) {
    switch (priority) {
      case 'low':
        return 'Низкий';
      case 'medium':
        return 'Средний';
      case 'high':
        return 'Высокий';
      case 'critical':
        return 'Критический';
      default:
        return priority;
    }
  }
}