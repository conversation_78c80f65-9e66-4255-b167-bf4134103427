import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_input_field.dart';

/// Универсальный компонент мультиселекта
class MultiSelectWidget extends StatelessWidget {
  final String label;
  final List<String> selected;
  final List<String> options;
  final bool isEditing;
  final Function(String) onAdd;
  final Function(String) onRemove;
  final Function(String, String) copyToClipboard;
  final int? maxSelection;
  final IconData? icon;

  const MultiSelectWidget({
    super.key,
    required this.label,
    required this.selected,
    required this.options,
    required this.isEditing,
    required this.onAdd,
    required this.onRemove,
    required this.copyToClipboard,
    this.maxSelection,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    if (!isEditing) {
      // В режиме просмотра скрываем пустые поля
      if (selected.isEmpty) return const SizedBox.shrink();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label.isNotEmpty) ...[
            Row(
              children: [
                if (icon != null) ...[
                  Icon(icon, size: 18, color: theme.textTheme.bodySmall?.color),
                  const SizedBox(width: 8),
                ],
                Text(label),
              ],
            ),
            const SizedBox(height: 4),
          ],
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                selected.map((item) {
                  return GestureDetector(
                    onTap: () => copyToClipboard(item, label),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        border: Border.all(
                          color: Theme.of(context).primaryColor,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(item),
                          const SizedBox(width: 4),
                          const Icon(Icons.copy, size: 14),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ],
      );
    }

    // Режим редактирования - обычное поведение
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label.isNotEmpty) ...[
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 18, color: theme.textTheme.bodySmall?.color),
                const SizedBox(width: 8),
              ],
              Text(label),
            ],
          ),
          const SizedBox(height: 4),
        ],
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              options.map((option) {
                final isSelected = selected.contains(option);
                return FilterChip(
                  label: Text(option),
                  selected: isSelected,
                  onSelected: (bool value) {
                    if (value) {
                      onAdd(option);
                    } else {
                      // For single-select mode, don't allow deselection by re-clicking.
                      // To change, user must select another option.
                      if (maxSelection != 1) {
                        onRemove(option);
                      }
                    }
                  },
                );
              }).toList(),
        ),
      ],
    );
  }
}

/// Компонент для выпадающих списков с поддержкой копирования
class DropdownFieldWidget extends StatelessWidget {
  final String label;
  final String? value;
  final List<String> options;
  final bool isEditing;
  final Function(String?) onChange;
  final Function(String, String) copyToClipboard;
  final IconData? icon;

  const DropdownFieldWidget({
    super.key,
    required this.label,
    required this.value,
    required this.options,
    required this.isEditing,
    required this.onChange,
    required this.copyToClipboard,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    if (isEditing) {
      return DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: icon != null ? Icon(icon, size: 20) : null,
        ),
        items:
            options
                .map((item) => DropdownMenuItem(value: item, child: Text(item)))
                .toList(),
        onChanged: onChange,
      );
    }

    if (value?.isEmpty ?? true) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label.isNotEmpty) ...[
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 18, color: theme.textTheme.bodySmall?.color),
                const SizedBox(width: 8),
              ],
              Text(label),
            ],
          ),
          const SizedBox(height: 4),
        ],
        GestureDetector(
          onTap: () => copyToClipboard(value!, label),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                if (icon != null) ...[
                  Icon(icon, size: 20, color: theme.textTheme.bodySmall?.color),
                  const SizedBox(width: 12),
                ],
                Expanded(child: Text(value!)),
                const Icon(Icons.copy, size: 16, color: Colors.grey),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// Компонент для редактирования тегов
class TagsFieldWidget extends StatelessWidget {
  final String label;
  final List<String> tags;
  final bool isEditing;
  final Function(String) onAddTag;
  final Function(String) onRemoveTag;
  final Function(String, String) copyToClipboard;
  final IconData? icon;

  const TagsFieldWidget({
    super.key,
    required this.label,
    required this.tags,
    required this.isEditing,
    required this.onAddTag,
    required this.onRemoveTag,
    required this.copyToClipboard,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    if (!isEditing) {
      // Режим просмотра
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 18, color: theme.textTheme.bodySmall?.color),
                const SizedBox(width: 8),
              ],
              Text(label),
            ],
          ),
          const SizedBox(height: 4),
          if (tags.isEmpty)
            GestureDetector(
              onTap: () => copyToClipboard('', label),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 12,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Нет данных',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                    Icon(Icons.copy, size: 16, color: Colors.grey),
                  ],
                ),
              ),
            )
          else
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  tags.map((tag) {
                    return GestureDetector(
                      onTap: () => copyToClipboard(tag, label),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).primaryColor.withOpacity(0.1),
                          border: Border.all(
                            color: Theme.of(context).primaryColor,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(tag),
                            const SizedBox(width: 4),
                            const Icon(Icons.copy, size: 14),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
            ),
        ],
      );
    }

    // Режим редактирования
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (icon != null) ...[
              Icon(icon, size: 18, color: theme.textTheme.bodySmall?.color),
              const SizedBox(width: 8),
            ],
            Text(label),
          ],
        ),
        const SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ...tags.map(
              (tag) =>
                  Chip(label: Text(tag), onDeleted: () => onRemoveTag(tag)),
            ),
            ActionChip(
              label: const Text('+ Добавить'),
              onPressed: () => _showAddTagDialog(context),
            ),
          ],
        ),
      ],
    );
  }

  void _showAddTagDialog(BuildContext context) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text('Добавить тег'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(labelText: 'Название тега'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx),
                child: const Text('Отмена'),
              ),
              TextButton(
                onPressed: () {
                  if (controller.text.trim().isNotEmpty) {
                    onAddTag(controller.text.trim());
                    Navigator.pop(ctx);
                  }
                },
                child: const Text('Добавить'),
              ),
            ],
          ),
    );
  }
}

class DateInputFormField extends StatelessWidget {
  final GlobalKey? fieldKey;
  final TextEditingController controller;
  final FocusNode? focusNode;
  final String labelText;
  final IconData prefixIcon;
  final VoidCallback onIconTap;
  final String? Function(String?)? validator;
  final bool isEditing;
  final bool disableAutoFormatting;
  final Function(DateTime?)? onDateChanged;

  const DateInputFormField({
    super.key,
    this.fieldKey,
    required this.controller,
    this.focusNode,
    required this.labelText,
    required this.prefixIcon,
    required this.onIconTap,
    this.validator,
    required this.isEditing,
    this.disableAutoFormatting = false,
    this.onDateChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: fieldKey,
      child: SmartDateInputField(
        controller: controller,
        focusNode: focusNode,
        labelText: labelText,
        prefixIcon: prefixIcon,
        readOnly: !isEditing,
        enabled: isEditing,
        validator: validator,
        onDateChanged: onDateChanged,
        onCalendarTap:
            isEditing
                ? () async {
                  onIconTap();
                  return null; // Обработка календаря через onIconTap
                }
                : null,
        onCopyToClipboard:
            !isEditing
                ? (text) {
                  // Копирование в буфер обмена для режима просмотра
                  if (text.isNotEmpty) {
                    // Добавим сюда логику копирования, если нужно
                  }
                }
                : null,
        fieldName: labelText,
        disableAutoFormatting: disableAutoFormatting,
      ),
    );
  }
}
