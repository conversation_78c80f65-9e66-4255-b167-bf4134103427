import 'package:balansoved_enterprise/features/clients/domain/entities/client_payment_entity.dart';

/// Модель платежа клиента, соответствующая JSON-формату API.
class ClientPaymentModel {
  final String clientId;
  final DateTime period; // первый день месяца
  final double? actualAmountPaid; // рубли
  final double? tariffAnnualAmount; // рубли
  final DateTime? paymentDate; // точная дата оплаты
  final DateTime createdAt;
  final DateTime updatedAt;

  const ClientPaymentModel({
    required this.clientId,
    required this.period,
    required this.actualAmountPaid,
    required this.tariffAnnualAmount,
    this.paymentDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ClientPaymentModel.fromJson(Map<String, dynamic> json) {
    // period приходит в формате YYYY-MM
    final periodString = json['period'] as String;
    final dateParts = periodString.split('-');
    final year = int.parse(dateParts[0]);
    final month = int.parse(dateParts[1]);
    final periodDate = DateTime.utc(year, month, 1);

    DateTime parseTs(dynamic v) {
      if (v == null) return DateTime.fromMillisecondsSinceEpoch(0);
      if (v is int) {
        // сервер возвращает микросекунды
        return DateTime.fromMicrosecondsSinceEpoch(v);
      }
      if (v is String) {
        // может быть числовая строка микросекунды или ISO
        final numVal = int.tryParse(v);
        if (numVal != null) {
          return DateTime.fromMicrosecondsSinceEpoch(numVal);
        }
        return DateTime.parse(v);
      }
      return DateTime.fromMillisecondsSinceEpoch(0);
    }

    DateTime? parsePaymentDate(dynamic v) {
      if (v == null) return null;
      if (v is String) {
        try {
          // Пробуем парсить как ISO дату
          return DateTime.parse(v);
        } catch (e) {
          // Если не получилось, пробуем как микросекунды
          final numVal = int.tryParse(v);
          if (numVal != null) {
            return DateTime.fromMicrosecondsSinceEpoch(numVal);
          }
        }
      }
      if (v is int) {
        return DateTime.fromMicrosecondsSinceEpoch(v);
      }
      return null;
    }

    return ClientPaymentModel(
      clientId: json['client_id'] as String,
      period: periodDate,
      actualAmountPaid: (json['actual_amount_paid'] as num?)?.toDouble(),
      tariffAnnualAmount: (json['tariff_annual_amount'] as num?)?.toDouble(),
      paymentDate: parsePaymentDate(json['actual_payment_date']),
      createdAt: parseTs(json['created_at']),
      updatedAt: parseTs(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() => {
    'client_id': clientId,
    'period':
        '${period.year.toString().padLeft(4, '0')}-${period.month.toString().padLeft(2, '0')}',
    'actual_amount_paid': actualAmountPaid,
    'tariff_annual_amount': tariffAnnualAmount,
    'actual_payment_date': paymentDate?.toIso8601String(),
    'created_at': createdAt.toIso8601String(),
    'updated_at': updatedAt.toIso8601String(),
  };

  ClientPaymentEntity toEntity() => ClientPaymentEntity(
    clientId: clientId,
    period: period,
    actualAmountPaid: actualAmountPaid,
    tariffAnnualAmount: tariffAnnualAmount,
    paymentDate: paymentDate,
    createdAt: createdAt,
    updatedAt: updatedAt,
  );

  factory ClientPaymentModel.fromEntity(ClientPaymentEntity entity) {
    return ClientPaymentModel(
      clientId: entity.clientId,
      period: entity.period,
      actualAmountPaid: entity.actualAmountPaid,
      tariffAnnualAmount: entity.tariffAnnualAmount,
      paymentDate: entity.paymentDate,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
