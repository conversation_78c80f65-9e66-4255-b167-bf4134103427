import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../repositories/tariffs_and_storage_repository.dart';

/// Use case для проверки доступности места в хранилище
class CheckSpaceAvailableUseCase {
  final ITariffsAndStorageRepository repository;

  const CheckSpaceAvailableUseCase({required this.repository});

  /// Проверить, хватит ли места для загрузки файла указанного размера
  Future<Either<Failure, bool>> call({
    required String firmId,
    required int fileSize,
  }) async {
    return await repository.checkSpaceAvailable(
      firmId: firmId,
      fileSize: fileSize,
    );
  }
}
