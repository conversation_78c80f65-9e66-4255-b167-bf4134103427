import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Кастомный виджет для текстовых полей с правильным управлением фокусом
/// и предотвращением нежелательного выделения текста
class CustomTextField extends StatefulWidget {
  final String? initialValue;
  final String labelText;
  final Icon? prefixIcon;
  final bool readOnly;
  final bool isEditing;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final String? Function(String?)? validator;
  final int? maxLength;

  const CustomTextField({
    super.key,
    this.initialValue,
    required this.labelText,
    this.prefixIcon,
    this.readOnly = false,
    this.isEditing = true,
    this.keyboardType,
    this.inputFormatters,
    this.maxLines = 1,
    this.onChanged,
    this.onTap,
    this.validator,
    this.maxLength,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _hasBeenFocused = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _focusNode = FocusNode();
    
    // Добавляем listener для контроллера
    _controller.addListener(_onTextChanged);
    
    // Добавляем listener для фокуса
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void didUpdateWidget(CustomTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Обновляем текст только если он изменился извне
    if (oldWidget.initialValue != widget.initialValue) {
      final newText = widget.initialValue ?? '';
      if (_controller.text != newText) {
        // Сохраняем позицию курсора
        final selection = _controller.selection;
        _controller.text = newText;
        
        // Восстанавливаем позицию курсора, если поле в фокусе
        if (_focusNode.hasFocus && selection.isValid) {
          _controller.selection = selection;
        }
      }
    }
  }

  void _onTextChanged() {
    if (widget.isEditing && widget.onChanged != null) {
      widget.onChanged!(_controller.text);
    }
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus && !_hasBeenFocused) {
      // При первом получении фокуса выделяем весь текст
      _hasBeenFocused = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_focusNode.hasFocus && _controller.text.isNotEmpty) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
    } else if (!_focusNode.hasFocus) {
      // Сбрасываем флаг при потере фокуса
      _hasBeenFocused = false;
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _focusNode.removeListener(_onFocusChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      decoration: InputDecoration(
        labelText: widget.labelText,
        prefixIcon: widget.prefixIcon,
      ),
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      maxLines: widget.maxLines,
      maxLength: widget.maxLength,
      readOnly: widget.readOnly || !widget.isEditing,
      validator: widget.validator,
      onTap: widget.isEditing ? null : widget.onTap,
    );
  }
}