# SmartDateInputField

Универсальный виджет для "умного" ввода дат с поддержкой различных форматов ввода и автоматическим форматированием.

## Особенности

- ✅ **Умный парсинг**: Автоматически распознает различные форматы дат
- ✅ **Автоформатирование**: Приводит введенную дату к заданному формату
- ✅ **Гибкая настройка**: Множество параметров для кастомизации
- ✅ **Встроенная валидация**: Проверка корректности введенной даты
- ✅ **Интеграция с календарем**: Возможность выбора даты через DatePicker
- ✅ **Режим чтения**: Поддержка копирования в буфер обмена
- ✅ **Кастомные парсеры**: Возможность добавления собственной логики парсинга

## Поддерживаемые форматы ввода

### Стандартные форматы
- `dd.MM.yyyy` (например: 25.12.2024)
- `dd/MM/yyyy` (например: 25/12/2024)
- `dd-MM-yyyy` (например: 25-12-2024)
- `dd.MM.yy` (например: 25.12.24)
- `dd/MM/yy` (например: 25/12/24)
- `dd-MM-yy` (например: 25-12-24)
- `dd.MM` (например: 25.12 - год текущий)
- `dd/MM` (например: 25/12 - год текущий)
- `dd-MM` (например: 25-12 - год текущий)

### Числовые форматы без разделителей
- `ddmmyyyy` (например: 25122024)
- `ddmmyy` (например: 251224)

### Произвольный текст
Виджет извлекает числа из любого текста и интерпретирует их как день, месяц, год.

## Базовое использование

```dart
SmartDateInputField(
  labelText: 'Дата рождения',
  onDateChanged: (DateTime? date) {
    print('Выбрана дата: $date');
  },
  validator: (String? value) {
    if (value == null || value.isEmpty) {
      return 'Введите дату';
    }
    return null;
  },
)
```

## Основные параметры

### Контроллеры и управление
- `controller` - TextEditingController (опционально)
- `focusNode` - FocusNode (опционально)
- `initialDate` - Начальное значение даты

### Отображение
- `labelText` - Текст подсказки
- `hintText` - Placeholder текст
- `helperText` - Текст помощи
- `prefixIcon` - Иконка префикса
- `suffixIcon` - Иконка суффикса (по умолчанию календарь)
- `decoration` - Кастомная декорация поля
- `style` - Стиль текста

### Форматирование
- `dateFormat` - Формат отображения (по умолчанию 'dd.MM.yyyy')
- `customDateParser` - Кастомная функция парсинга
- `customDateFormatter` - Кастомная функция форматирования

### Поведение
- `readOnly` - Режим только для чтения
- `enabled` - Включен ли виджет
- `required` - Обязательное поле
- `autofocus` - Автофокус

### Функции обратного вызова
- `onDateChanged` - При изменении даты
- `onTextChanged` - При изменении текста
- `onCalendarTap` - При клике на календарь
- `onTap` - При клике на поле
- `onFieldSubmitted` - При отправке формы (Enter)
- `onFocusChanged` - При изменении фокуса
- `onCopyToClipboard` - Для режима чтения

### Валидация
- `validator` - Функция валидации
- `autovalidateMode` - Режим автовалидации

### Календарь
- `firstDate` - Минимальная дата в календаре
- `lastDate` - Максимальная дата в календаре

## Примеры использования

### 1. Базовый пример

```dart
SmartDateInputField(
  labelText: 'Дата создания',
  prefixIcon: Icons.calendar_today,
  onDateChanged: (date) => print('Дата: $date'),
)
```

### 2. С кастомным форматом

```dart
SmartDateInputField(
  labelText: 'Дата в ISO формате',
  dateFormat: 'yyyy-MM-dd',
  hintText: 'гггг-мм-дд',
  onDateChanged: (date) => _handleDateChange(date),
)
```

### 3. Режим только для чтения

```dart
SmartDateInputField(
  controller: TextEditingController(text: '01.01.2024'),
  labelText: 'Дата создания записи',
  readOnly: true,
  onCopyToClipboard: (text) => _copyToClipboard(text),
  fieldName: 'Дата создания',
)
```

### 4. С кастомной валидацией

```dart
SmartDateInputField(
  labelText: 'Дата не ранее 2020 года',
  validator: (value) {
    if (value?.isEmpty ?? true) return 'Введите дату';
    
    final date = DateFormat('dd.MM.yyyy').tryParse(value!);
    if (date == null) return 'Неверный формат';
    if (date.year < 2020) return 'Дата должна быть не ранее 2020';
    
    return null;
  },
  autovalidateMode: AutovalidateMode.onUserInteraction,
)
```

### 5. С кастомным парсером

```dart
SmartDateInputField(
  labelText: 'Только 8 цифр (ддммгггг)',
  customDateParser: (input) {
    if (RegExp(r'^\d{8}$').hasMatch(input)) {
      final day = int.parse(input.substring(0, 2));
      final month = int.parse(input.substring(2, 4));
      final year = int.parse(input.substring(4, 8));
      
      if (day >= 1 && day <= 31 && month >= 1 && month <= 12) {
        return DateTime(year, month, day);
      }
    }
    return null;
  },
  customDateFormatter: (date) =>
      '${date.day.toString().padLeft(2, '0')}'
      '${date.month.toString().padLeft(2, '0')}'
      '${date.year}',
)
```

### 6. С внешними контроллерами

```dart
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  final _controller = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SmartDateInputField(
      controller: _controller,
      focusNode: _focusNode,
      labelText: 'Управляемое поле',
      onDateChanged: (date) {
        // Обработка изменения даты
      },
    );
  }
}
```

## Интеграция с существующими формами

Виджет полностью совместим с `Form` и `FormField`:

```dart
Form(
  key: _formKey,
  child: Column(
    children: [
      SmartDateInputField(
        labelText: 'Дата рождения',
        validator: (value) => value?.isEmpty == true ? 'Обязательное поле' : null,
      ),
      // другие поля...
      ElevatedButton(
        onPressed: () {
          if (_formKey.currentState!.validate()) {
            // Форма валидна
          }
        },
        child: Text('Сохранить'),
      ),
    ],
  ),
)
```

## Автоматическое форматирование

При потере фокуса или нажатии Enter виджет автоматически:

1. Парсит введенный текст
2. Определяет корректную дату
3. Форматирует её согласно `dateFormat`
4. Обновляет отображение
5. Вызывает `onDateChanged` с новой датой

## Совместимость

- ✅ Flutter 3.0+
- ✅ Web поддержка
- ✅ Mobile (iOS/Android)
- ✅ Desktop (Windows/macOS/Linux)

## Производительность

Виджет оптимизирован для:
- Минимального количества перестроений
- Эффективного управления памятью
- Быстрого парсинга дат
- Плавного пользовательского опыта 