
Идентификатор - d5d... (будет присвоен при создании)
Имя - storage-api
Служебный домен - https://d5d...laqt4bj7.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: File Storage Management API
  version: 1.0.0
servers:
  - url: https://d5d...laqt4bj7.apigw.yandexcloud.net

x-yc-apigateway:
  cors:
    origin: "*"
    methods:
      - "POST"
      - "OPTIONS"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    allowCredentials: true
    maxAge: 3600

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - bearerAuth: []

paths:
  /manage:
    post:
      summary: Универсальный метод для управления файлами
      operationId: manageFiles
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e... (ID новой функции)
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action:
                  type: string
                  enum: [GET_UPLOAD_URL, DELETE]
                firm_id:
                  type: string
                # Для GET_UPLOAD_URL
                filename:
                  type: string
                filesize:
                  type: integer
                # Для DELETE
                file_key:
                  type: string
              required:
                - action
                - firm_id
      responses:
        '200':
          description: Успешное выполнение.
        '400':
          description: Неверные параметры.
        '403':
          description: Ошибка авторизации.
        '413':
          description: Квота на хранилище превышена.
        '500':
          description: Внутренняя ошибка.
```