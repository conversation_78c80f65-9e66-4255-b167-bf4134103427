import json
import uuid
import datetime
import pytz
import ydb
from custom_errors import LogicError

def _get_declare_for_event(payload):
    """Генерирует DECLARE и параметры для YQL-запроса из словаря."""
    declare_clauses, params = "", {}
    type_map = {
        'event_id': ydb.PrimitiveType.Utf8,
        'function_id': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'custom_identifier': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'is_annual': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'execution_dates_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'request_body_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'is_active': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'created_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
        'updated_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
    }

    for key, value in payload.items():
        if key in type_map:
            params[f"${key}"] = value
            type_name_str = str(type_map[key])
            type_name = type_name_str.replace("Optional[", "").replace("]", "") if "Optional" in type_name_str else type_name_str
            declare_clauses += f"DECLARE ${key} AS {type_name}; "
            
    return declare_clauses, params

def upsert_event(session, table_name, payload, event_id=None):
    if not payload:
        raise LogicError("Payload is required for UPSERT action.")

    tx = session.transaction(ydb.SerializableReadWrite())
    now = datetime.datetime.now(pytz.utc)
    payload['updated_at'] = now

    if event_id:  # Update
        payload['event_id'] = event_id
        declare_clauses, params = _get_declare_for_event(payload)
        set_clauses = ", ".join([f"`{k}` = ${k}" for k in payload if k != 'event_id'])
        
        query_text = f"{declare_clauses} UPDATE `{table_name}` SET {set_clauses} WHERE event_id = $event_id;"
        tx.execute(session.prepare(query_text), params)
        tx.commit()
        return {"statusCode": 200, "body": json.dumps({"message": "Scheduled event updated"})}
    
    else:  # Create
        new_event_id = str(uuid.uuid4())
        payload['event_id'] = new_event_id
        payload['created_at'] = now
        if 'is_active' not in payload:
            payload['is_active'] = True # По умолчанию событие активно

        declare_clauses, params = _get_declare_for_event(payload)
        columns = ", ".join([f"`{k}`" for k in payload.keys()])
        placeholders = ", ".join([f"${k}" for k in payload.keys()])
        
        query_text = f"{declare_clauses} UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"
        tx.execute(session.prepare(query_text), params)
        tx.commit()
        return {"statusCode": 201, "body": json.dumps({"message": "Scheduled event created", "event_id": new_event_id})}