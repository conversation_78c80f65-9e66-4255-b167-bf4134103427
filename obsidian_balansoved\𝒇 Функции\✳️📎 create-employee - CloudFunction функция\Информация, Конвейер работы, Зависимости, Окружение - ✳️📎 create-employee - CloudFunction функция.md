
Идентификатор - d4e5ctmd3olk0kcj9hv0
Описание - 𐀪 Добавить существующего пользователя в указанную фирму как сотрудника
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя с правами `OWNER` или `ADMIN`.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы, в которую добавляется сотрудник.
		- `email` (string, **обязательно**): Email пользователя для добавления.
		- `roles` (list, необязательно): Список ролей, по умолчанию `["EMPLOYEE"]`.
Внутренняя работа:
	-> Логирование: Установка уровня логирования на INFO.
	-> Авторизация:
		-> Получение заголовка Authorization.
		-> Проверка наличия 'Bearer ' и извлечение токена.
		-> Верификация JWT с помощью auth_utils.verify_jwt.
		-> Извлечение admin_user_id из payload, иначе ошибка авторизации.
	-> Парсинг запроса:
		-> Использование request_parser.parse_request_body для получения данных.
		-> Извлечение firm_id, email, roles (по умолчанию ["EMPLOYEE"]).
		-> Проверка наличия firm_id и email, иначе LogicError.
	-> Поиск пользователя в jwt-database:
		-> Инициализация auth_driver с помощью ydb_utils.get_ydb_driver.
		-> Создание пула сессий.
		-> Запрос на поиск user_id и user_name по email с is_active=true.
		-> Если не найдено, NotFoundError.
	-> Добавление в firms-database:
		-> Инициализация firms_driver с помощью ydb_utils.get_driver_for_db.
		-> Создание пула сессий.
		-> Транзакция SerializableReadWrite:
			-> Запрос на получение roles админа для firm_id.
			-> Если нет записи, AuthError (не член фирмы).
			-> Проверка наличия "ADMIN" или "OWNER" в roles, иначе AuthError.
			-> Проверка существования записи для target_user_id и firm_id, если да - LogicError.
			-> Подготовка и выполнение INSERT в Users с данными: user_id, firm_id, email, null password_hash, full_name, json roles, is_active true, created_at CurrentUtcTimestamp.
			-> Коммит транзакции.
	-> Возврат 201 с сообщением и user_id.
	-> Обработка исключений:
		-> AuthError -> 403
		-> LogicError -> 409
		-> NotFoundError -> 404
		-> Другие -> 500 с логированием ошибки.
На выходе:
	-> `201 Created`: {"message": "Employee added successfully", "user_id": "..."}
	-> `403 Forbidden`: Для AuthError (Unauthorized, Invalid token, Not member, Insufficient permissions).
	-> `409 Conflict`: Для LogicError (missing params, already member).
	-> `404 Not Found`: Для NotFoundError (user not found or not active).
	-> `500 Internal Server Error`: Для других исключений.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT`, `YDB_DATABASE` (для `jwt-database`)
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`