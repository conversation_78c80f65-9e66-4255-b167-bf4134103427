import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/employees_repository.dart';

class InviteEmployeeUseCase {
  final IEmployeesRepository repository;

  InviteEmployeeUseCase(this.repository);

  Future<Either<Failure, void>> call(String firmId, String email) async {
    return await repository.inviteEmployee(firmId, email);
  }
}
