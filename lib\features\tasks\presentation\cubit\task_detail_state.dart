import 'package:equatable/equatable.dart';
import '../../domain/entities/task_entity.dart';
import '../../../employees_and_firms/domain/entities/employee_entity.dart';
import '../../../clients/domain/entities/client_entity.dart';

abstract class TaskDetailState extends Equatable {
  const TaskDetailState();

  @override
  List<Object?> get props => [];
}

class TaskDetailInitial extends TaskDetailState {}

class TaskDetailLoading extends TaskDetailState {}

class TaskDetailLoaded extends TaskDetailState {
  final TaskEntity task;
  final List<EmployeeEntity> employees;
  final List<ClientEntity> clients;
  final bool isExpanded;
  final bool showFullDescription;

  const TaskDetailLoaded({
    required this.task,
    this.employees = const [],
    this.clients = const [],
    this.isExpanded = false,
    this.showFullDescription = false,
  });

  TaskDetailLoaded copyWith({
    TaskEntity? task,
    List<EmployeeEntity>? employees,
    List<ClientEntity>? clients,
    bool? isExpanded,
    bool? showFullDescription,
  }) {
    return TaskDetailLoaded(
      task: task ?? this.task,
      employees: employees ?? this.employees,
      clients: clients ?? this.clients,
      isExpanded: isExpanded ?? this.isExpanded,
      showFullDescription: showFullDescription ?? this.showFullDescription,
    );
  }

  @override
  List<Object?> get props => [
    task,
    employees,
    clients,
    isExpanded,
    showFullDescription,
  ];
}

class TaskDetailError extends TaskDetailState {
  final String message;

  const TaskDetailError({required this.message});

  @override
  List<Object?> get props => [message];
}