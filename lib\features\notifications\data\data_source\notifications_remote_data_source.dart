import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:balansoved_enterprise/core/constants/constants.dart';
import '../models/notification_model.dart';

abstract class NotificationsRemoteDataSource {
  Future<List<NotificationModel>> getNotifications({
    required int page,
    bool getArchived = false,
    required String token,
  });
  
  Future<NotificationModel> getNotificationById(String noticeId, String token);
  
  Future<void> archiveNotification(String noticeId, String token);
  
  Future<void> markAsDelivered(List<String> noticeIds, String token);
}

class NotificationsRemoteDataSourceImpl implements NotificationsRemoteDataSource {
  final http.Client client;

  NotificationsRemoteDataSourceImpl({required this.client});

  @override
  Future<List<NotificationModel>> getNotifications({
    required int page,
    bool getArchived = false,
    required String token,
  }) async {
    final uri = Uri.parse(NotificationsApiUrls.getNotices()).replace(
      queryParameters: {
        'page': page.toString(),
        'get_archived': getArchived.toString(),
      },
    );

    final response = await client.get(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonResponse = json.decode(utf8.decode(response.bodyBytes));
      final List<dynamic> noticesJson = jsonResponse['data'] ?? [];
      
      return noticesJson
          .map((json) => NotificationModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } else {
      throw Exception('Failed to load notifications: ${response.statusCode}');
    }
  }

  @override
  Future<NotificationModel> getNotificationById(String noticeId, String token) async {
    final response = await client.get(
      Uri.parse(NotificationsApiUrls.getNoticeById(noticeId)),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> jsonResponse = json.decode(response.body);
      return NotificationModel.fromJson(jsonResponse);
    } else {
      throw Exception('Failed to load notification: ${response.statusCode}');
    }
  }

  @override
  Future<void> archiveNotification(String noticeId, String token) async {
    final response = await client.post(
      Uri.parse(NotificationsApiUrls.archiveNotice()),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: json.encode({
        'notice_id': noticeId,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to archive notification: ${response.statusCode}');
    }
  }

  @override
  Future<void> markAsDelivered(List<String> noticeIds, String token) async {
    final response = await client.post(
      Uri.parse(NotificationsApiUrls.markAsDelivered()),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: json.encode({
        'notice_ids': noticeIds,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to mark notifications as delivered: ${response.statusCode}');
    }
  }
}