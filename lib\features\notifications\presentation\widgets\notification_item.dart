import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/notification_entity.dart';
import '../cubit/notifications_cubit.dart';

class NotificationItem extends StatelessWidget {
  final NotificationEntity notification;

  const NotificationItem({
    super.key,
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final dateFormat = DateFormat('dd.MM.yyyy HH:mm');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: notification.isDelivered 
            ? colorScheme.surface
            : colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.isDelivered 
              ? colorScheme.outline.withValues(alpha: 0.3)
              : colorScheme.primary.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          if (!notification.isDelivered) {
            _markAsRead(context);
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Заголовок и время
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Иконка уведомления
                  Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.only(top: 6, right: 8),
                    decoration: BoxDecoration(
                      color: notification.isDelivered 
                          ? colorScheme.outline.withValues(alpha: 0.5)
                          : colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  
                  // Заголовок
                  Expanded(
                    child: Text(
                      notification.title ?? '',
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: notification.isDelivered 
                            ? FontWeight.normal 
                            : FontWeight.bold,
                      ),
                    ),
                  ),
                  
                  // Время
                  Text(
                    dateFormat.format(notification.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Текст уведомления
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Text(
                  notification.body ?? '',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
              ),
              
              // Кнопка "Отметить как прочитанное" для непрочитанных
              if (!notification.isDelivered)
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 8),
                  child: TextButton(
                    onPressed: () => _markAsRead(context),
                    style: TextButton.styleFrom(
                      foregroundColor: colorScheme.primary,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                    ),
                    child: const Text(
                      'Отметить как прочитанное',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _markAsRead(BuildContext context) {
    context.read<NotificationsCubit>().markNotificationsAsDelivered([notification.id]);
  }
}