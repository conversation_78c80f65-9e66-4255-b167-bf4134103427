
Идентификатор - d4ep4vks5f5ahnbb6i45
Описание - 𐀪 Создать новую фирму и все связанные с ней таблицы
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен любого зарегистрированного пользователя.
	-> `firm_name`: Название новой фирмы в теле запроса.
Внутренняя работа:
	-> Логирование: Установка уровня логирования на INFO.
	-> Авторизация:
		-> Проверка наличия и формата заголовка Authorization.
		-> Верификация JWT токена и извлечение user_id и email.
	-> Парсинг запроса:
		-> Извлечение firm_name из тела запроса с помощью request_parser.
	-> Генерация ID: Создание уникального firm_id с помощью UUID.
	-> Получение имени пользователя:
		-> Подключение к основной БД (jwt-database) для получения user_name по user_id.
		-> Если не удалось получить, установка значения по умолчанию "Unknown".
	-> Транзакция в firms-database:
		-> Проверка, является ли пользователь уже владельцем (роль "OWNER") какой-либо фирмы.
		-> Вставка записи в таблицу Firms с firm_id, firm_name, owner_user_id, integrations_json={}, created_at, is_active=True.
		-> Вставка записи в таблицу Users с user_id, firm_id, email, full_name, roles=["OWNER"], is_active=True, created_at.
	-> Создание таблицы клиентов:
		-> Подключение к clients-database.
		-> Создание таблицы clients_[firm_id] с колонками: client_id, manual_creation_date (primary keys), is_actual, client_name, short_name, contacts_json, tax_and_legal_info_json, payment_schedule_json, patents_json, tags_json, comment, is_active, created_at, updated_at.
	-> Создание таблицы задач:
		-> Подключение к tasks-database.
		-> Создание таблицы tasks_[firm_id] с колонками: task_id (primary key), title, description, client_ids_json, assignee_ids_json, observer_ids_json, creator_ids_json, status, priority, due_date, completed_at, attachments_json, checklist_json, reminders_json, recurrence_json, options_json, holiday_transfer_rule, origin_task_id, is_system_task, created_at, updated_at.
	-> Обработка исключений: Логирование ошибок и возврат соответствующих статусов.
На выходе:
	-> `201 Created`: {"message": "Firm created successfully", "firm_id": "..."}
	-> `400 Bad Request`: Если firm_name отсутствует или ошибка парсинга тела запроса.
	-> `401 Unauthorized`: Если токен отсутствует или невалиден.
	-> `409 Conflict`: Если пользователь уже является владельцем другой фирмы.
	-> `500 Internal Server Error`: В случае ошибок при работе с базами данных или создании таблиц.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `YDB_ENDPOINT_CLIENTS`, `YDB_DATABASE_CLIENTS`
	- `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`