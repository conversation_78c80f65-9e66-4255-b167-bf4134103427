
Идентификатор - d4ek9ojgjnoibdsi1qut
Описание - 💰 Управляет тарифами, квотами и файлами в Object Storage.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя.
	-> Тело запроса:
		- `action` (string, **обязательно**): Тип операции.
		- `firm_id` (string, **обязательно**): ID фирмы.
		- **Для `GET_RECORD`**:
			- Никаких доп. полей.
		- **Для `UPDATE_JSON`**:
			- `target_json_field` (string): Имя JSON-поля для обновления (`subscription_info_json`, `storage_info_json`, `confidential_data_json`).
			- `updates` (object): Словарь с ключами и значениями для обновления.
		- **Для `CLEAR_JSON`**:
			- `fields_to_clear` (list): Список JSON-полей для очистки.
		- **Для `GET_UPLOAD_URL`**:
			- `filename` (string): Имя файла.
			- `filesize` (integer): Размер файла в байтах.
		- **Для `DELETE_FILE`**:
			- `file_key` (string): Ключ файла в S3.

Внутренняя работа:
    -> Логирование начала вызова и очистка кэша драйверов YDB.
    -> Авторизация:
        -> Извлечение и проверка JWT-токена из заголовков.
        -> Верификация токена и получение user_id.
    -> Парсинг тела запроса с помощью request_parser.
    -> Проверка обязательных параметров: firm_id и action.
    -> Инициализация подключения к базам данных firms и tariffs.
    -> Проверка членства пользователя в фирме и прав (OWNER/ADMIN для определенных действий).
    -> Маршрутизация по action:
        -> GET_RECORD (требует ADMIN/OWNER):
            -> Вызов get_or_create_record: поиск записи, если нет - создание с дефолтными значениями (quota 100MB).
        -> UPDATE_JSON (требует ADMIN/OWNER):
            -> Вызов update_json_fields: чтение текущего JSON, обновление ключей, запись обратно с обновлением timestamps.
        -> CLEAR_JSON (требует ADMIN/OWNER):
            -> Вызов clear_json_fields: установка указанных JSON-полей в {} с обновлением timestamps.
        -> GET_UPLOAD_URL:
            -> Вызов handle_get_upload_url: расчет актуального использования из S3, проверка квоты, синхронизация БД если нужно, генерация pre-signed PUT URL.
        -> GET_DOWNLOAD_URL:
            -> Вызов handle_get_download_url: генерация pre-signed GET URL для файла.
        -> CONFIRM_UPLOAD:
            -> Вызов handle_confirm_upload: получение размера файла из S3, атомарное обновление used_bytes в storage_info_json.
        -> DELETE_FILE:
            -> Вызов handle_delete_file: получение размера, удаление файла из S3, атомарное уменьшение used_bytes в storage_info_json.
    -> Обработка исключений: возврат соответствующих статусов (400, 403, 404, 413, 500).

На выходе:
-   `200 OK` (GET_RECORD): `{"data": {...}}`
-   `200 OK` (UPDATE_JSON/CLEAR_JSON): `{"message": "..."}`
-   `200 OK` (GET_UPLOAD_URL): `{"upload_url": "...", "file_key": "..."}`
-   `200 OK` (DELETE_FILE): `{"message": "File deleted"}`
-   `400 Bad Request`, `403 Forbidden`, `404 Not Found`, `413 Payload Too Large`.

---
#### Зависимости и окружение
-   **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `utils/storage_utils.py`
-   **Переменные окружения**:
    -   `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
    -   `YDB_ENDPOINT_TARIFFS`, `YDB_DATABASE_TARIFFS` ([[💾 tariffs-and-storage-database - База данных YandexDatabase]])
    -   `SA_KEY_FILE` ([[ydb_sa_key.json]])
    -   `JWT_SECRET`
    -   `STORAGE_BUCKET_NAME`
    -   `STORAGE_ENDPOINT` ("https://storage.yandexcloud.net")
    -   `STORAGE_REGION` ("ru-central1")
    -   `STORAGE_ACCESS_KEY` ([[🗝️ auth-service-acc - Статический ключ доступа]])
    -   `STORAGE_SECRET_KEY` ([[🗝️ auth-service-acc - Статический ключ доступа]])