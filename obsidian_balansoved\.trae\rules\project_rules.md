
Общее ->
1. Ты находишься в базе знаний нашего проекта YandexCloud, где содержится полная информация и функциях / базах данных / АПИ / сервисных аккаунтов.
2. "🔗 gateway-api" содержит md, с нашими АПИ (yandex cloud, gateway-api).
3. "🗄️ Базы данных" содержит md, с нашими Базами Данных (yandex cloud, YDB).
4. "𝒇 Функции" содержит md, с нашими Функциями (yandex cloud, cloud functions).
4. "𝒇 Функции\💉 requirements.txt (общий)" содержит ПАКЕТЫ дял python, которые используют все текущие/будущие python скрипты, cloud functions.

---
Правила редактирования .md ->
1. Тебе нужно ЧЕТКО соблюдать СТРУКТУРУ ТЕКСТА (для опоры прочитай файлы того же типа, в той-же папке)
2. Изменения в любых функциях / база данных, могут СЛОМАТЬ, что-то стороннее. ЛЮБОЕ ИЗМЕНЕНИЕ нужно ОБСУДИТЬ С ЮЗЕРОМ. ЛЮБОЕ ИЗМЕНЕНИЕ ДОЛЖНО БЫТЬ БЕЗОПАСНЫМ, чтобы СЛУЧАЙНО НЕ СЛОМАТЬ что-то внешнее.
3. Перед редактированием прочти ВЕСЬ ФАЙЛ MD!

---
Редактирование MD для АПИ, "🔗 gateway-api" ->
1. MD файлы АПИ, состоят из следующих, обязательных секций: 
    Общее (Идентификатор, Имя, Служебный домен), Спецификация (Конфигурация в ```...```)

---
Редактирование MD для Cloud Functions, "𝒇 Функции"  ->
1. MD файлы Cloud Functions, состоят из следующих, обязательных секций: 
    Общее (Идентификатор, Описание, Точка входа, Таймаут), Конвеер работы (`На входе`, `Внутренняя работа`, `На выходе`), Зависимости и окружение (`Необходимые утилиты`, `Переменные окружения`), Код функции (обязателньый `index.py`, и дополнительный файлы питон для реализации конкретных ФИЧЕЙ).
2. В разделе `Переменные окружения`, оставляй ссылки [[...]], на файлы MD, где можно найти указанную переменную окружения.
3. Процесс редактирование Cloud Function, должен обязательно включить, редактирование `Конвеер работы`

---
Редактирование MD для Баз Данных, "🗄️ Базы данных" ->
1. MD файлы Баз Данных, состоят из следующих, обязательных секций: 
    Общее (Идентификатор, Путь к базе, Эндпоинт. Имя базы содержится в имени MD), Таблицы (Для каждой таблицы, баз данных, "# / Имя / Ключ / Тип / Описание")

---
Редактирование MD для Utils, "𝒇 Функции\⚙️ utils" ->
1. MD файлы Utils, состоят из следующих, обязательных секций: 
    Список ФИЧЕЙ/МЕТОДОВ (По аналогии с "функциями" - `На входе`, `Внутренняя работа`, `На выходе`, для кажлого метода/фичи), Код (только 1 файл, с указанием имени .py)
2. Утилиты используются, "Cloud Функциями", поэтому их редактирование должно быть максимально безопасным. Редактирование утилиты, должно быть, только путём их РАСШИРЕНИЯ. Без удаления методов (только если мы абсолюнто уверены, что метод нигде больше не используется).