
```python
import json
import datetime as dt
import runtime_utils

CLIENT_NAME_CACHE, EMPLOYEE_NAME_CACHE = {}, {}
PAGE_SIZE = 100

def populate_employee_cache(user_jwt: str, firm_id: str):
    """(ИСПРАВЛЕНО) Заполняет кеш сотрудников из двух источников для полноты данных."""
    runtime_utils.log_message("Заполнение кеша сотрудников...")
    
    # 1. Вызов get-user-data для получения общего списка
    user_data = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_GET_USER_DATA, {}, user_jwt)
    if user_data:
        # Ищем список сотрудников в разных возможных ключах
        employees = []
        for key in ("employees", "users", "team", "staff"):
             if key in user_data and isinstance(user_data[key], list):
                employees = user_data[key]
                break
        for emp in employees:
            uid = emp.get("user_id") or emp.get("id") or emp.get("uid")
            name = emp.get("full_name") or emp.get("fullName") or emp.get("name")
            if uid and name:
                EMPLOYEE_NAME_CACHE[uid] = name
        runtime_utils.log_message(f"Кеш сотрудников пополнен из get-user-data: {len(employees)} записей.")

    # 2. Вызов edit-employee для получения полного списка по фирме
    emp_data = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_EMPLOYEE, {"firm_id": firm_id, "action": "GET_INFO"}, user_jwt)
    if emp_data and isinstance(emp_data.get('data'), list):
        employees = emp_data['data']
        for emp in employees:
            uid = emp.get("user_id")
            name = emp.get("full_name")
            if uid and name:
                EMPLOYEE_NAME_CACHE[uid] = name
        runtime_utils.log_message(f"Кеш сотрудников дополнен из edit-employee: {len(employees)} записей.")
    
    runtime_utils.log_message(f"Итоговый размер кеша сотрудников: {len(EMPLOYEE_NAME_CACHE)}.")

def get_client_name(client_id: str, user_jwt: str, firm_id: str) -> str:
    if client_id in CLIENT_NAME_CACHE: return CLIENT_NAME_CACHE[client_id]
    # Запрашиваем только актуальную версию клиента
    payload = {"firm_id": firm_id, "action": "GET", "client_id": client_id, "is_actual": True}
    response = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_CLIENT, payload, user_jwt)
    if response:
        # Поскольку is_actual=true, мы ожидаем список из одного элемента
        client_data = response.get('data')
        if isinstance(client_data, list) and client_data:
            name = client_data[0].get('client_name', client_id)
            CLIENT_NAME_CACHE[client_id] = name
            return name
        # Фоллбэк для старых версий или ошибок
        elif isinstance(client_data, dict):
            name = client_data.get('client_name', client_id)
            CLIENT_NAME_CACHE[client_id] = name
            return name

    CLIENT_NAME_CACHE[client_id] = client_id
    return client_id

def get_employee_name(employee_id: str, user_jwt: str, firm_id: str) -> str:
    """(ИСПРАВЛЕНО) Получает имя сотрудника из кеша, с фоллбэком на API-запрос."""
    if employee_id in EMPLOYEE_NAME_CACHE:
        return EMPLOYEE_NAME_CACHE[employee_id]
    
    runtime_utils.log_message(f"Имя для {employee_id} не найдено в кеше. Выполняю доп. запрос...", "WARN")
    payload = {"firm_id": firm_id, "action": "GET_INFO", "user_id_to_edit": employee_id}
    response = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_EMPLOYEE, payload, user_jwt)
    
    if response and isinstance(response.get('data'), dict):
        name = response['data'].get('full_name', employee_id)
        EMPLOYEE_NAME_CACHE[employee_id] = name
        return name
        
    EMPLOYEE_NAME_CACHE[employee_id] = employee_id
    return employee_id

def get_client_folder_name(client_ids_json: str, user_jwt: str, firm_id: str) -> str:
    from yandex_disk_uploader import sanitize_filename
    try:
        client_ids = json.loads(client_ids_json or '[]')
        if not client_ids: return "unassigned"
        folder_parts = [f"{sanitize_filename(get_client_name(cid, user_jwt, firm_id))} ({cid})" for cid in client_ids]
        return "-".join(sorted(folder_parts))
    except (json.JSONDecodeError, TypeError):
        return "invalid_client_id_format"

def get_all_tasks(user_jwt: str, firm_id: str) -> list:
    tasks_to_process = []
    runtime_utils.log_message("Получение списка задач...")
    page = 0
    while True:
        resp = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_TASK, {"firm_id": firm_id, "action": "GET", "page": page}, user_jwt)
        if not resp or not resp.get('data'): break
        tasks = resp['data']
        tasks_to_process.extend([{'type': 'timeless', 'data': t} for t in tasks])
        if len(tasks) < PAGE_SIZE: break
        page += 1

    current_year = dt.datetime.now().year
    for month in range(1, 13):
        resp = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_TASK, {"firm_id": firm_id, "action": "GET", "get_dated_tasks": True, "year": current_year, "month": month}, user_jwt)
        if resp and resp.get('data'):
            tasks_to_process.extend([{'type': 'dated', 'data': t, 'year': current_year, 'month': month} for t in resp['data']])
    
    return tasks_to_process
```