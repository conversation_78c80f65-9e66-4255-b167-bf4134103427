
Идентификатор - d4e0nco8ka4c1me3qdrt
Описание - 📇 Создать, обновить, удалить или получить информацию о клиенте фирмы
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: То<PERSON><PERSON>н любого сотрудника фирмы.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы.
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
		- `client_id` (string, необязательно): ID клиента.
		- `creation_date` (string): Дата версии в формате `YYYY-MM-DD`. Обязательно для обновления в UPSERT и для DELETE, опционально для GET.
		- `payload` (object, необязательно): Данные клиента для `UPSERT`.

Внутренняя работа:
    -> Логирование входных данных
    -> Авторизация и парсинг запроса
        -> Извлечение auth header (предпочтительно x-forwarded-authorization)
        -> Верификация JWT токена для получения user_id
        -> Парсинг тела запроса для получения firm_id, action, client_id, creation_date, payload
    -> Проверка обязательных параметров (firm_id, action)
    -> Парсинг creation_date, если предоставлена
    -> Инициализация YDB подключений
        -> Для firms-database (проверка членства)
        -> Для clients-database (основная логика)
    -> Проверка членства пользователя в фирме и получение ролей (в firms-database)
    -> Роутинг по action в транзакции clients-database (таблица clients_{firm_id})
        -> Для GET:
            -> Если client_id и creation_date: Получить конкретную версию клиента
            -> Если только client_id: Получить все версии клиента, отсортированные по дате DESC
            -> Иначе: Получить все актуальные клиенты (is_actual = true)
            -> Сериализация результатов в JSON
        -> Для UPSERT (требует ролей OWNER или ADMIN):
            -> Добавление updated_at в payload
            -> Если client_id и creation_date: Обновление существующей версии (только предоставленные поля)
            -> Если client_id и manual_creation_date в payload: Создание новой версии
                -> Поиск и деактивация старой актуальной версии (is_actual = false)
                -> Вставка новой версии с is_actual = true и created_at
            -> Если нет client_id и manual_creation_date в payload: Создание нового клиента
                -> Генерация client_id
                -> Установка is_actual = true, is_active = true, created_at
            -> Валидация и подготовка параметров для YDB запросов
        -> Для DELETE (требует ролей OWNER или ADMIN):
            -> Проверка наличия client_id и creation_date
            -> Проверка существования версии
            -> Если версия актуальна: Проверка, не является ли она последней (запрет удаления)
            -> Удаление версии
            -> Если удалена актуальная: Поиск и установка самой поздней оставшейся версии как актуальной (is_actual = true)
    -> Обработка результатов и исключений (AuthError -> 403, LogicError -> 400, NotFoundError -> 404, прочие -> 500)
    -> Накопление и вывод логов в конце обработки

На выходе:
	-> `200 OK` (GET): `{"data": [{...}]}` или `{"data": {...}}` или `{"data": []}` (все даты в строковом формате YYYY-MM-DD)
	-> `201 Created` (UPSERT/create): `{"message": "Client created", "client_id": "...", "creation_date": "..."}` (дата в формате YYYY-MM-DD)
	-> `200 OK` (UPSERT/update): `{"message": "Client version updated", "client_id": "...", "creation_date": "..."}` (дата в формате YYYY-MM-DD)
	-> `200 OK` (UPSERT/new-version): `{"message": "New client version created", "client_id": "...", "creation_date": "..."}` (дата в формате YYYY-MM-DD)
	-> `200 OK` (DELETE): `{"message": "Client version deleted"}`
	-> `400 Bad Request`: Неверные или отсутствующие параметры.
	-> `403 Forbidden`: Недостаточно прав для `UPSERT` или `DELETE`.
	-> `404 Not Found`: Указанный `client_id` не найден.
	-> `500 Internal Server Error`: Ошибка на стороне сервера или БД.

---
#### Зависимости и окружение
- **Необходимые утилиты**: [[utils/auth_utils.py]], [[utils/ydb_utils.py]], [[utils/request_parser.py]]
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS` ([[💾 firms-database - База данных YandexDatabase]]), `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
	- `YDB_ENDPOINT_CLIENTS` ([[💾 clients-database - База данных YandexDatabase]]), `YDB_DATABASE_CLIENTS` ([[💾 clients-database - База данных YandexDatabase]])
	- `SA_KEY_FILE` ([[ydb_sa_key.json]])
	- `JWT_SECRET`