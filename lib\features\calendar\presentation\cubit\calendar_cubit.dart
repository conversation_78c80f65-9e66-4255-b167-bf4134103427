import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/get_tasks_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/calendar/presentation/cubit/calendar_state.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';

class CalendarCubit extends Cubit<CalendarState> {
  final GetTasksUseCase getTasksUseCase;

  // Сохраняем последние параметры загрузки для обновления
  String? _lastFirmId;
  int? _lastMonth;
  int? _lastYear;

  CalendarCubit({required this.getTasksUseCase}) : super(CalendarInitial());

  Future<void> fetchDatedTasks(
    String firmId, {
    required int month,
    required int year,
  }) async {
    // Сохраняем параметры для возможного обновления
    _lastFirmId = firmId;
    _lastMonth = month;
    _lastYear = year;

    emit(CalendarLoading());

    final params = TaskRequestParams.dated(month: month, year: year);
    final result = await getTasksUseCase(firmId, params);

    result.fold((failure) {
      if (failure is AccessDeniedFailure) {
        emit(CalendarNoAccess(message: failure.message));
      } else {
        emit(CalendarError(message: failure.message));
      }
    }, (tasksResult) => emit(CalendarLoaded(tasks: tasksResult.tasks)));
  }

  /// Обновляет текущие загруженные данные календаря
  /// Используется когда данные задач изменились в другом месте приложения
  Future<void> refreshCurrentData() async {
    if (_lastFirmId != null && _lastMonth != null && _lastYear != null) {
      await fetchDatedTasks(_lastFirmId!, month: _lastMonth!, year: _lastYear!);
    }
  }

  /// Обновляет конкретную задачу в текущем состоянии без полной перезагрузки
  void updateTaskInCurrentState(TaskEntity updatedTask) {
    final currentState = state;
    if (currentState is CalendarLoaded) {
      final updatedTasks =
          currentState.tasks.map((task) {
            return task.id == updatedTask.id ? updatedTask : task;
          }).toList();

      emit(CalendarLoaded(tasks: updatedTasks));
    }
  }
}
