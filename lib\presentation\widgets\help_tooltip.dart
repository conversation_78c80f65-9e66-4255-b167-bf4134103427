import 'package:flutter/material.dart';

/// Универсальный виджет для отображения подсказок
class HelpTooltip extends StatelessWidget {
  final String message;
  final Color? iconColor;
  final double iconSize;

  const HelpTooltip({
    super.key,
    required this.message,
    this.iconColor,
    this.iconSize = 16,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Tooltip(
      message: message,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.inverseSurface,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      textStyle: TextStyle(
        color: theme.colorScheme.onInverseSurface,
        fontSize: 14,
      ),
      waitDuration: const Duration(milliseconds: 500),
      showDuration: const Duration(seconds: 3),
      child: Icon(
        Icons.help_outline,
        size: iconSize,
        color: iconColor ?? theme.colorScheme.primary.withOpacity(0.7),
      ),
    );
  }
}
