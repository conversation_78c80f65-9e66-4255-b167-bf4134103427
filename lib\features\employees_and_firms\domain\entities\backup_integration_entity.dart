import 'package:equatable/equatable.dart';

/// Сущность, описывающая состояние резервного копирования на Яндекс Диск
class BackupIntegrationEntity extends Equatable {
  /// Включена ли интеграция резервного копирования
  final bool enabled;

  /// Дата/время последней успешной синхронизации в UTC
  final DateTime? lastSyncUtc;

  /// Токен Яндекс Диска (может быть скрыт в UI)
  final String? token;

  const BackupIntegrationEntity({
    required this.enabled,
    this.lastSyncUtc,
    this.token,
  });

  /// Расчёт следующей запланированной синхронизации (каждые 7 дней)
  DateTime? get nextSyncUtc => lastSyncUtc?.add(const Duration(days: 7));

  @override
  List<Object?> get props => [enabled, lastSyncUtc, token];
}
