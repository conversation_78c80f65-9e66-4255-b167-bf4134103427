import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';

import '../../domain/entities/tariffs_and_storage_entity.dart';
import '../cubit/tariffs_and_storage_cubit.dart';
import '../cubit/tariffs_and_storage_state.dart';

/// Виджет для отображения использования хранилища с круговым прогресс-баром
class StorageUsageWidget extends StatelessWidget {
  final String firmId;
  final double? size;
  final TextStyle? textStyle;

  const StorageUsageWidget({
    super.key,
    required this.firmId,
    this.size = 80.0,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TariffsAndStorageCubit, TariffsAndStorageState>(
      builder: (context, state) {
        if (state is TariffsAndStorageLoading) {
          return _buildLoadingWidget();
        }

        if (state is TariffsAndStorageError) {
          return _buildErrorWidget(context, state.message);
        }

        TariffsAndStorageEntity? data;
        if (state is TariffsAndStorageLoaded) {
          data = state.data;
        } else if (state is FileUploadCompleted) {
          data = state.updatedData;
        } else if (state is FileDeleted) {
          data = state.updatedData;
        }

        if (data != null) {
          return _buildStorageUsageDisplay(context, data);
        }

        // Если данных нет, загружаем их
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.read<TariffsAndStorageCubit>().loadStorageInfo(firmId);
        });

        return _buildLoadingWidget();
      },
    );
  }

  Widget _buildLoadingWidget() {
    // Используем универсальную заглушку LoadingTile с ограниченной шириной.
    return LoadingTile(height: size, width: 250);
  }

  Widget _buildErrorWidget(BuildContext context, String errorMessage) {
    return SizedBox(
      width: size,
      height: size,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.error,
            size: size! * 0.6,
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              'Ошибка',
              style:
                  textStyle?.copyWith(fontSize: 12) ??
                  TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.error,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageUsageDisplay(
    BuildContext context,
    TariffsAndStorageEntity data,
  ) {
    final progress = data.usageProgress;
    final formattedUsage = data.formattedUsage;

    return SizedBox(
      width: 250,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Строка с кружком слева и текстом справа
          Row(
            children: [
              // Круговой прогресс-бар
              SizedBox(
                width: size,
                height: size,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      width: size,
                      height: size,
                      child: CircularProgressIndicator(
                        value: progress,
                        strokeWidth: 6.0,
                        backgroundColor:
                            Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getProgressColor(progress),
                        ),
                      ),
                    ),
                    // Центральный текст с процентами
                    Text(
                      '${(progress * 100).toStringAsFixed(0)}%',
                      style:
                          textStyle?.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: (size! * 0.15).clamp(12.0, 16.0),
                          ) ??
                          TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: (size! * 0.15).clamp(12.0, 16.0),
                            color: _getProgressColor(progress),
                          ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              // Текст с информацией об использовании справа
              Expanded(
                child: Text(
                  formattedUsage,
                  style:
                      textStyle?.copyWith(fontSize: 14) ??
                      const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
          // Статус подписки (если не бесплатная)
          if (data.subscriptionInfo.planId != 'free') ...[
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color:
                      data.subscriptionInfo.isActive
                          ? Theme.of(
                            context,
                          ).colorScheme.primaryContainer.withOpacity(0.3)
                          : Theme.of(
                            context,
                          ).colorScheme.errorContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        data.subscriptionInfo.isActive
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.error,
                  ),
                ),
                child: Text(
                  data.subscriptionInfo.planId.toUpperCase(),
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color:
                        data.subscriptionInfo.isActive
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.error,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getProgressColor(double progress) {
    if (progress < 0.5) {
      return Colors.green;
    } else if (progress < 0.8) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}

/// Простой виджет только с круговым прогресс-баром (без текста снизу)
class StorageUsageCompactWidget extends StatelessWidget {
  final String firmId;
  final double size;

  const StorageUsageCompactWidget({
    super.key,
    required this.firmId,
    this.size = 40.0,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TariffsAndStorageCubit, TariffsAndStorageState>(
      builder: (context, state) {
        if (state is TariffsAndStorageLoading) {
          return SizedBox(
            width: size,
            height: size,
            child: const CircularProgressIndicator(),
          );
        }

        TariffsAndStorageEntity? data;
        if (state is TariffsAndStorageLoaded) {
          data = state.data;
        } else if (state is FileUploadCompleted) {
          data = state.updatedData;
        } else if (state is FileDeleted) {
          data = state.updatedData;
        }

        if (data != null) {
          final progress = data.usageProgress;

          return SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              value: progress,
              strokeWidth: 3.0,
              backgroundColor:
                  Theme.of(context).colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getProgressColor(progress),
              ),
            ),
          );
        }

        // Если данных нет, загружаем их
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.read<TariffsAndStorageCubit>().loadStorageInfo(firmId);
        });

        return SizedBox(
          width: size,
          height: size,
          child: Icon(
            Icons.storage,
            size: size * 0.8,
            color: Theme.of(context).colorScheme.outline,
          ),
        );
      },
    );
  }

  Color _getProgressColor(double progress) {
    if (progress < 0.5) {
      return Colors.green;
    } else if (progress < 0.8) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
