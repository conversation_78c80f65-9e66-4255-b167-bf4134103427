import 'package:balansoved_enterprise/features/profile/domain/entities/profile_entity.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/firm_entity.dart';

class ProfileModel extends ProfileEntity {
  const ProfileModel({
    required super.id,
    super.email,
    super.userName,
    required super.firms,
    required super.tasksCount,
  });

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    final user = json['user_info'] ?? json['user'] ?? {};
    final firmsJson = json['firms'] as List? ?? [];
    final tasks = json['tasks'] as List? ?? [];

    final firms =
        firmsJson
            .map(
              (f) => FirmEntity(
                id: f['firm_id'] ?? '',
                name: f['firm_name'] ?? '',
                ownerUserId: f['owner_user_id'] ?? '',
                roles: List<String>.from(f['user_roles'] ?? const []),
              ),
            )
            .toList();

    return ProfileModel(
      id: user['user_id'] ?? user['id'] ?? '',
      email: user['email'],
      userName: user['user_name'] ?? user['name'],
      firms: firms,
      tasksCount: tasks.length,
    );
  }

  Map<String, dynamic> toJson() => {
    'user': {'user_id': id, 'email': email, 'user_name': userName},
    'firms_count': firms.length,
    'tasks_count': tasksCount,
  };

  ProfileEntity toEntity() => ProfileEntity(
    id: id,
    email: email,
    userName: userName,
    firms: firms,
    tasksCount: tasksCount,
  );
}
