import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/form_widgets.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_utils.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';

class PatentMainInfo extends StatelessWidget {
  final DateTime startDate;
  final TextEditingController startDateCtrl;
  final GlobalKey startDateKey;
  final DateTime issueDate;
  final TextEditingController issueDateCtrl;
  final GlobalKey issueDateKey;
  final DateTime endDate;
  final TextEditingController endDateCtrl;
  final GlobalKey endDateKey;
  final DateTime? newPatentApplicationDate;
  final TextEditingController newPatentApplicationDateCtrl;
  final GlobalKey newPatentApplicationDateKey;
  final TextEditingController patentNumberCtrl;
  final TextEditingController patentTitleCtrl;
  final TextEditingController amountCtrl;
  final TextEditingController commentCtrl;
  final bool isEditing;
  final Function(DateTime) onStartDateChanged;
  final Function(DateTime) onIssueDateChanged;
  final Function(DateTime) onEndDateChanged;
  final Function(DateTime?) onNewPatentApplicationDateChanged;
  final VoidCallback onChanged;
  final FocusNode startDateFN;
  final FocusNode issueDateFN;
  final FocusNode endDateFN;
  final FocusNode newPatentApplicationDateFN;

  const PatentMainInfo({
    super.key,
    required this.startDate,
    required this.issueDate,
    required this.endDate,
    required this.newPatentApplicationDate,
    required this.patentNumberCtrl,
    required this.patentTitleCtrl,
    required this.amountCtrl,
    required this.commentCtrl,
    required this.isEditing,
    required this.onStartDateChanged,
    required this.onIssueDateChanged,
    required this.onEndDateChanged,
    required this.onNewPatentApplicationDateChanged,
    required this.onChanged,
    required this.startDateCtrl,
    required this.startDateKey,
    required this.issueDateCtrl,
    required this.issueDateKey,
    required this.endDateCtrl,
    required this.endDateKey,
    required this.newPatentApplicationDateCtrl,
    required this.newPatentApplicationDateKey,
    required this.startDateFN,
    required this.issueDateFN,
    required this.endDateFN,
    required this.newPatentApplicationDateFN,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: DateInputFormField(
                fieldKey: startDateKey,
                controller: startDateCtrl,
                labelText: 'Дата начала',
                prefixIcon: Icons.calendar_today_outlined,
                isEditing: isEditing,
                onIconTap:
                    () => _selectDate(context, startDate, onStartDateChanged),
                validator: (v) => _validateDate(v, isRequired: true),
                focusNode: startDateFN,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DateInputFormField(
                fieldKey: issueDateKey,
                controller: issueDateCtrl,
                labelText: 'Дата выдачи',
                prefixIcon: Icons.calendar_today_outlined,
                isEditing: isEditing,
                onIconTap:
                    () => _selectDate(context, issueDate, onIssueDateChanged),
                validator: (v) => _validateDate(v, isRequired: true),
                focusNode: issueDateFN,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: DateInputFormField(
                fieldKey: endDateKey,
                controller: endDateCtrl,
                labelText: 'Дата конца',
                prefixIcon: Icons.calendar_today_outlined,
                isEditing: isEditing,
                onIconTap:
                    () => _selectDate(context, endDate, onEndDateChanged),
                validator: (v) => _validateDate(v, isRequired: true),
                focusNode: endDateFN,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DateInputFormField(
                fieldKey: newPatentApplicationDateKey,
                controller: newPatentApplicationDateCtrl,
                labelText: 'Заявление на новый патент',
                prefixIcon: Icons.calendar_today_outlined,
                isEditing: isEditing,
                onIconTap:
                    () =>
                        _selectDate(context, newPatentApplicationDate, (date) {
                          onNewPatentApplicationDateChanged(date);
                        }),
                validator: (v) => _validateDate(v, isRequired: false),
                focusNode: newPatentApplicationDateFN,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        UniversalNumberField(
          controller: patentNumberCtrl,
          labelText: 'Номер патента',
          prefixIcon: Icons.numbers_outlined,
          fieldType: NumberFieldType.integer,
          isRequired: true,
          readOnly: !isEditing,
          maxLength: 200,
          onTap:
              isEditing
                  ? null
                  : () => ClientUtils.copyToClipboard(
                    context,
                    patentNumberCtrl.text,
                    'Номер патента',
                  ),
          onChanged: isEditing ? (_) => onChanged() : null,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: patentTitleCtrl,
          decoration: const InputDecoration(labelText: 'Название патента'),
          readOnly: !isEditing,
          onTap:
              isEditing
                  ? null
                  : () => ClientUtils.copyToClipboard(
                    context,
                    patentTitleCtrl.text,
                    'Название патента',
                  ),
          onChanged: isEditing ? (_) => onChanged() : null,
        ),
        const SizedBox(height: 16),
        UniversalNumberField(
          controller: amountCtrl,
          labelText: 'Сумма патента',
          prefixIcon: Icons.attach_money_outlined,
          fieldType: NumberFieldType.decimal,
          suffixText: '₽',
          isRequired: true,
          minValue: 0.01,
          readOnly: !isEditing,
          onTap:
              isEditing
                  ? null
                  : () => ClientUtils.copyToClipboard(
                    context,
                    amountCtrl.text,
                    'Сумма патента',
                  ),
          onChanged: isEditing ? (_) => onChanged() : null,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: commentCtrl,
          decoration: const InputDecoration(labelText: 'Комментарий'),
          maxLines: 2,
          readOnly: !isEditing,
          onTap:
              isEditing
                  ? null
                  : () => ClientUtils.copyToClipboard(
                    context,
                    commentCtrl.text,
                    'Комментарий',
                  ),
          onChanged: isEditing ? (_) => onChanged() : null,
        ),
      ],
    );
  }

  Future<void> _selectDate(
    BuildContext context,
    DateTime? initialDate,
    ValueChanged<DateTime> onDateSelected,
  ) async {
    final newDate = await SmartDatePickerDialog.show(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      helpText: 'Выберите дату',
      allowClear: false,
    );
    if (newDate != null) {
      onDateSelected(newDate);
    }
  }

  String? _validateDate(String? value, {required bool isRequired}) {
    if (value == null || value.isEmpty) {
      return isRequired ? 'Обязательное поле' : null;
    }
    try {
      DateFormat('dd.MM.yyyy').parseStrict(value);
      return null;
    } catch (e) {
      return 'Формат: дд.мм.гггг';
    }
  }
}
