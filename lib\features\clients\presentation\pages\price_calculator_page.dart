import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/price_calculator_cubit.dart';
import '../cubit/zero_reporting_cubit.dart';
import '../cubit/one_time_services_cubit.dart';
import '../widgets/price_calculator/calculator_body.dart';
import '../widgets/zero_reporting/zero_reporting_body.dart';
import '../widgets/one_time_services/one_time_services_body.dart';

/// Типы калькуляторов
enum CalculatorType {
  main('Основной калькулятор'),
  zeroReporting('Нулевая отчетность'),
  oneTimeServices('Разовые услуги');

  const CalculatorType(this.label);
  final String label;
}

/// Страница «Калькулятор цен»
@RoutePage()
class PriceCalculatorPage extends StatefulWidget {
  const PriceCalculatorPage({super.key});

  @override
  State<PriceCalculatorPage> createState() => _PriceCalculatorPageState();
}

class _PriceCalculatorPageState extends State<PriceCalculatorPage> {
  CalculatorType _selectedCalculator = CalculatorType.main;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => PriceCalculatorCubit()..loadFromPrefs()),
        BlocProvider(create: (_) => ZeroReportingCubit()),
        BlocProvider(create: (_) => OneTimeServicesCubit()),
      ],
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerLowest,
        appBar: AppBar(
          title: const Text('Калькулятор бухгалтерских услуг'),
          backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(60),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: SegmentedButton<CalculatorType>(
                  segments:
                      CalculatorType.values
                          .map(
                            (e) => ButtonSegment<CalculatorType>(
                              value: e,
                              label: Text(e.label),
                            ),
                          )
                          .toList(),
                  selected: {_selectedCalculator},
                  onSelectionChanged: (newSelection) {
                    setState(() {
                      _selectedCalculator = newSelection.first;
                    });
                  },
                ),
              ),
            ),
          ),
        ),
        body: _getSelectedBody(),
      ),
    );
  }

  /// Получение виджета для выбранной вкладки
  Widget _getSelectedBody() {
    switch (_selectedCalculator) {
      case CalculatorType.main:
        return const PriceCalculatorBody();
      case CalculatorType.zeroReporting:
        return const ZeroReportingBody();
      case CalculatorType.oneTimeServices:
        return const OneTimeServicesBody();
    }
  }
}
