
Идентификатор - d4er1guc7vbu00j2lksd
Описание - ✉️ Пригласить нового или существующего пользователя в фирму по email.
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя с правами `OWNER` или `ADMIN`.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы, в которую отправляется приглашение.
		- `email` (string, **обязательно**): Email приглашаемого пользователя.

 Внутренняя работа:
-> Авторизация и парсинг запроса:
  -> Проверяет наличие и формат заголовка Authorization (Bearer token).
  -> Верифицирует JWT, извлекает admin_user_id; при ошибке - AuthError (403).
  -> Парсит тело запроса для firm_id и invitee_email (в нижний регистр); при отсутствии - LogicError (400).

-> Поиск кандидата в глобальной базе (auth-data):
  -> Инициализирует YDB driver и pool для auth-database.
  -> Выполняет запрос на user_id и user_name по email; сохраняет candidate_data.

-> Основная транзакция в firms-database:
  -> Инициализирует YDB driver и pool для firms-database.
  -> В транзакции:
    -> Проверяет права администратора: SELECT roles из Users по admin_user_id и firm_id; если нет или роли не OWNER/ADMIN - AuthError (403).
    -> Получает firm_name из Firms по firm_id; если не найдено - LogicError (400).
    -> Проверяет существование пользователя в Users по email и firm_id: SELECT user_id, is_active, invitation_sent_at.
    -> Генерирует invitation_key.
    -> Если пользователь существует:
      -> Если is_active=True - LogicError (409, уже активен).
      -> Если is_active=False: проверяет время с invitation_sent_at; если <3 мин - LogicError (409); иначе UPDATE invitation_key и invitation_sent_at.
    -> Если не существует:
      -> Создает новую запись: UPSERT в Users с user_id (из candidate_data или новый), email, full_name, roles=["EMPLOYEE"], is_active=False, invitation_key, created_at, invitation_sent_at.
  -> Коммитит транзакцию, возвращает invitation_key и firm_name.

-> Отправка email:
  -> Получает admin_name из payload.
  -> Вызывает email_utils.send_invitation с invitee_email, invitation_key, firm_name, admin_name.
  -> Если не удалось - логирует ошибку и возвращает 500.
  -> Если успешно - логирует и возвращает 201.

-> Обработка исключений:
  -> AuthError -> 403.
  -> LogicError -> 409 (или 400 для некоторых случаев).
  -> Другие ошибки -> 500.

На выходе:
	-> `201 Created`: {"message": "Invitation sent successfully."}
	-> `400 Bad Request`: Ошибка в теле запроса.
	-> `403 Forbidden`: Недостаточно прав для выполнения операции.
	-> `409 Conflict`: Пользователь уже является активным участником фирмы, или повторная отправка происходит слишком часто.
	-> `500 Internal Server Error`: Ошибка отправки email или работы с БД.

---
#### Зависимости и окружение
- **Необходимые утилиты**: [[𝒇 Функции/utils/auth_utils - утилита.md|auth_utils.py]], [[𝒇 Функции/utils/ydb_utils - утилита.md|ydb_utils.py]], [[𝒇 Функции/utils/request_parser - утилита.md|request_parser.py]], [[𝒇 Функции/utils/email_utils - утилита.md|email_utils.py]]
- **Переменные окружения**:
	- `YDB_ENDPOINT`, `YDB_DATABASE` ([[💾 jwt-database - База данных YandexDatabase]])
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
	- SA_KEY_FILE ([[ydb_sa_key.json]])
	- `JWT_SECRET`
    - `UNISENDER_API_KEY`, `UNISENDER_SENDER_EMAIL`, `UNISENDER_SENDER_NAME`, `UNISENDER_LIST_ID` (для отправки email)