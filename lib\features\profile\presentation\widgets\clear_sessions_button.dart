import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/refresh_token_cubit.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';

class ClearSessionsButton extends StatefulWidget {
  const ClearSessionsButton({super.key});

  @override
  State<ClearSessionsButton> createState() => _ClearSessionsButtonState();
}

class _ClearSessionsButtonState extends State<ClearSessionsButton> {
  bool _showPassword = false;
  final _passwordController = TextEditingController();
  bool _isDialogOpen = false;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  void _showPasswordDialog() {
    if (_isDialogOpen) return;
    _isDialogOpen = true;
    _passwordController.clear();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) {
        return BlocProvider.value(
          value: context.read<RefreshTokenCubit>(),
          child: AlertDialog(
            title: const Text('Очистить сеансы аккаунта'),
            content: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Это действие обновит ваш JWT токен и завершит все активные сеансы на других устройствах.',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Для подтверждения введите ваш пароль:',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _passwordController,
                      obscureText: !_showPassword,
                      decoration: InputDecoration(
                        labelText: 'Пароль',
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _showPassword
                                ? Icons.visibility_off
                                : Icons.visibility,
                          ),
                          onPressed: () {
                            setState(() {
                              _showPassword = !_showPassword;
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  _isDialogOpen = false;
                },
                child: const Text('Отмена'),
              ),
              BlocConsumer<RefreshTokenCubit, RefreshTokenState>(
                listener: (context, state) {
                  if (state is RefreshTokenSuccess) {
                    Navigator.of(dialogContext).pop();
                    _isDialogOpen = false;
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Сеансы успешно очищены'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    // Обновляем профиль после успешного обновления токена
                    context.read<ProfileCubit>().fetchProfile();
                  } else if (state is RefreshTokenError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Ошибка: ${state.message}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                builder: (context, state) {
                  final isLoading = state is RefreshTokenLoading;
                  return ElevatedButton(
                    onPressed:
                        isLoading
                            ? null
                            : () {
                              if (_passwordController.text.trim().isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Введите пароль'),
                                    backgroundColor: Colors.orange,
                                  ),
                                );
                                return;
                              }
                              // Получаем email из ProfileCubit
                              final profileState =
                                  context.read<ProfileCubit>().state;
                              if (profileState is ProfileLoaded) {
                                context
                                    .read<RefreshTokenCubit>()
                                    .refreshUserToken(
                                      email: profileState.profile.email ?? '',
                                      password: _passwordController.text.trim(),
                                    );
                              }
                            },
                    child:
                        isLoading
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text('Очистить сеансы'),
                  );
                },
              ),
            ],
          ),
        );
      },
    ).then((_) {
      _isDialogOpen = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: _showPasswordDialog,
      icon: const Icon(Icons.security),
      label: const Text('Очистить сеансы аккаунта'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.errorContainer,
        foregroundColor: Theme.of(context).colorScheme.onErrorContainer,
      ),
    );
  }
}
