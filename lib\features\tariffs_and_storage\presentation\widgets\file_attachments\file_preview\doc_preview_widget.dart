import 'dart:async';
import 'dart:html' as html; // ignore: avoid_web_libraries_in_flutter
import 'dart:ui_web' as ui; // ignore: uri_does_not_exist
// ignore: avoid_web_libraries_in_flutter

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class DocPreviewWidget extends StatefulWidget {
  final String fileKey;
  final String fileName;
  final String? footerText;

  const DocPreviewWidget({
    super.key,
    required this.fileKey,
    required this.fileName,
    this.footerText,
  });

  @override
  State<DocPreviewWidget> createState() => _DocPreviewWidgetState();
}

class _DocPreviewWidgetState extends State<DocPreviewWidget> {
  String? _documentUrl;
  bool _isLoading = true;
  String? _errorMessage;

  html.IFrameElement? _iframeElement;
  double _currentScale = 1.0;

  @override
  void initState() {
    super.initState();
    _loadDocumentUrl();
  }

  Future<void> _loadDocumentUrl() async {
    try {
      final downloadUrl = await _getDownloadUrl();
      if (downloadUrl == null) {
        if (mounted) {
          setState(() {
            _errorMessage = 'Не удалось получить ссылку для предпросмотра';
            _isLoading = false;
          });
        }
        return;
      }

      final officeUrl =
          'https://view.officeapps.live.com/op/embed.aspx?src=${Uri.encodeComponent(downloadUrl)}';

      ui.platformViewRegistry.registerViewFactory(
        'office-viewer-${widget.fileKey}',
        (int viewId) {
          _iframeElement =
              html.IFrameElement()
                ..src = officeUrl
                ..style.border = 'none'
                ..style.width = '100%'
                ..style.height = '100%'
                ..style.transformOrigin = 'top left';
          _applyScale();
          return _iframeElement!;
        },
      );

      if (mounted) {
        setState(() {
          _documentUrl = officeUrl;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Ошибка загрузки документа: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<String?> _getDownloadUrl() async {
    final storageCubit = context.read<TariffsAndStorageCubit>();
    final firmState = context.read<ActiveFirmCubit>().state;

    if (firmState.selectedFirm == null) return null;

    final firmId = firmState.selectedFirm!.id;
    final completer = Completer<String?>();
    late StreamSubscription sub;

    sub = storageCubit.stream.listen((state) {
      if (state is FileDownloadUrlReady && state.fileKey == widget.fileKey) {
        if (!completer.isCompleted) completer.complete(state.downloadUrl);
        sub.cancel();
      } else if (state is TariffsAndStorageError) {
        if (!completer.isCompleted) completer.complete(null);
        sub.cancel();
      }
    });

    storageCubit.getDownloadUrl(firmId: firmId, fileKey: widget.fileKey);

    Future.delayed(const Duration(seconds: 15), () {
      if (!completer.isCompleted) {
        completer.complete(null);
        sub.cancel();
      }
    });

    return completer.future;
  }

  void _applyScale() {
    if (_iframeElement != null) {
      _iframeElement!.style.transform = 'scale($_currentScale)';
    }
  }

  void _zoom(double factor) {
    _currentScale = (_currentScale * factor).clamp(0.5, 3.0);
    _applyScale();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Text(
          _errorMessage!,
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      );
    }

    if (_documentUrl == null) {
      return const Center(child: Text('Не удалось загрузить документ.'));
    }

    return Stack(
      children: [
        Positioned.fill(
          child: HtmlElementView(viewType: 'office-viewer-${widget.fileKey}'),
        ),
        Positioned(
          right: 8,
          bottom: 32,
          child: Text(
            widget.footerText ?? 'Выберите масштаб документа',
            style: TextStyle(color: Colors.black, fontSize: 12),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
}
