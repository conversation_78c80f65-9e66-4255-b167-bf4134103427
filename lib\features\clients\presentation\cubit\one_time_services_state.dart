import 'package:equatable/equatable.dart';

/// Типы разовых услуг
enum OneTimeServiceType {
  // Отчетность и налоги
  additionalReportForms(
    'additional_report_forms',
    'Составление доп. форм отчетности',
    1000,
    true,
    'от 1000 руб.',
  ),
  szvReports('szv_reports', 'Подготовка СЗВ-М, СЗВ-ТД', 250, false, '250 руб.'),
  ipReceipts(
    'ip_receipts',
    'Формирование квитанций для ИП',
    500,
    false,
    '500 руб.',
  ),
  taxSystemChange(
    'tax_system_change',
    'Смена режима налогообложения',
    1000,
    false,
    '1000 руб.',
  ),

  // Проверки и взаимодействие с ФНС
  auditSupport(
    'audit_support',
    'Сопровождение камеральной проверки',
    3000,
    true,
    'от 3000 руб.',
  ),
  fieldAuditSupport(
    'field_audit_support',
    'Сопровождение выездной проверки',
    0,
    false,
    'по договоренности',
  ),
  certificateWithEcp(
    'certificate_with_ecp',
    'Справка в МИФНС с ЭЦП',
    1000,
    false,
    '1000 руб.',
  ),
  certificateWithoutEcp(
    'certificate_without_ecp',
    'Справка в МИФНС без ЭЦП',
    500,
    false,
    '500 руб.',
  ),
  taxReconciliation(
    'tax_reconciliation',
    'Сверка по налогам (за 1 год)',
    3000,
    false,
    '3000 руб.',
  ),
  interrogationPrep(
    'interrogation_prep',
    'Подготовка к допросу в ИФНС',
    5000,
    false,
    '5000 руб.',
  ),

  // Восстановление учета
  fullAccountingRestoration(
    'full_accounting_restoration',
    'Полное восстановление учета',
    7500,
    true,
    'от 7500 руб.',
  ),
  specialistVisit(
    'specialist_visit',
    'Выезд специалиста к клиенту',
    5000,
    false,
    '5000 руб.',
  ),

  // Кадровый учет
  hrDocuments(
    'hr_documents',
    'Составление кадровых документов',
    100,
    false,
    '100 руб.',
  ),
  laborContract(
    'labor_contract',
    'Составление трудового договора',
    1000,
    true,
    'от 1000 руб.',
  ),
  fssCompensation(
    'fss_compensation',
    'Возмещение пособия из ФСС',
    3000,
    false,
    '3000 руб.',
  ),

  // Юридические услуги
  oooLiquidation(
    'ooo_liquidation',
    'Ликвидация ООО',
    80000,
    true,
    'от 80000 руб.',
  ),
  oooReorganization(
    'ooo_reorganization',
    'Реорганизация ООО',
    50000,
    true,
    'от 50000 руб.',
  ),
  founderChange(
    'founder_change',
    'Смена учредителя',
    15000,
    true,
    'от 15000 руб.',
  ),
  directorChange(
    'director_change',
    'Смена директора',
    5000,
    false,
    '5000 руб.',
  ),
  addressChange('address_change', 'Смена адреса', 5000, false, '5000 руб.'),
  activityChange(
    'activity_change',
    'Смена видов деятельности',
    3000,
    false,
    '3000 руб.',
  ),
  charterChange(
    'charter_change',
    'Изменение устава',
    10000,
    true,
    'от 10000 руб.',
  ),

  // Прочие разовые услуги
  accountingPolicyDevelopment(
    'accounting_policy_development',
    'Разработка учетной политики',
    5000,
    true,
    'от 5000 руб.',
  ),
  consultation(
    'consultation',
    'Консультации (час)',
    1500,
    false,
    '1500 руб./час',
  ),
  training1C('training_1c', 'Обучение 1С (час)', 2500, false, '2500 руб./час'),
  courierDelivery('courier_delivery', 'Выезд курьера', 500, false, '500 руб.'),
  courierSubmission(
    'courier_submission',
    'Сдача с курьером',
    1000,
    false,
    '1000 руб.',
  ),
  counterpartyCheck(
    'counterparty_check',
    'Проверка контрагента',
    1000,
    false,
    '1000 руб.',
  ),
  bankAccountAssistance(
    'bank_account_assistance',
    'Помощь в открытии р/с',
    750,
    false,
    '750 руб.',
  ),

  // Неактивные услуги (по договоренности)
  auditServices(
    'audit_services',
    'Аудиторские услуги',
    0,
    false,
    'по договоренности',
  ),
  legalServices(
    'legal_services',
    'Юридические услуги',
    0,
    false,
    'по договоренности',
  );

  const OneTimeServiceType(
    this.id,
    this.title,
    this.basePrice,
    this.isFromPrice,
    this.priceLabel,
  );

  final String id;
  final String title;
  final int basePrice;
  final bool isFromPrice; // true если цена "от X руб."
  final String priceLabel;

  /// Является ли услуга активной (участвует в расчете)
  bool get isActive => basePrice > 0;
}

/// Категории разовых услуг для группировки
enum OneTimeServiceCategory {
  reporting('Отчетность и налоги'),
  inspections('Проверки и взаимодействие с ФНС'),
  restoration('Восстановление учета'),
  hr('Кадровый учет'),
  legal('Юридические услуги'),
  other('Прочие разовые услуги'),
  inactive('Услуги по договоренности');

  const OneTimeServiceCategory(this.title);
  final String title;
}

/// Маппинг услуг по категориям
const Map<OneTimeServiceCategory, List<OneTimeServiceType>> servicesByCategory =
    {
      OneTimeServiceCategory.reporting: [
        OneTimeServiceType.additionalReportForms,
        OneTimeServiceType.szvReports,
        OneTimeServiceType.ipReceipts,
        OneTimeServiceType.taxSystemChange,
      ],
      OneTimeServiceCategory.inspections: [
        OneTimeServiceType.auditSupport,
        OneTimeServiceType.fieldAuditSupport,
        OneTimeServiceType.certificateWithEcp,
        OneTimeServiceType.certificateWithoutEcp,
        OneTimeServiceType.taxReconciliation,
        OneTimeServiceType.interrogationPrep,
      ],
      OneTimeServiceCategory.restoration: [
        OneTimeServiceType.fullAccountingRestoration,
        OneTimeServiceType.specialistVisit,
      ],
      OneTimeServiceCategory.hr: [
        OneTimeServiceType.hrDocuments,
        OneTimeServiceType.laborContract,
        OneTimeServiceType.fssCompensation,
      ],
      OneTimeServiceCategory.legal: [
        OneTimeServiceType.oooLiquidation,
        OneTimeServiceType.oooReorganization,
        OneTimeServiceType.founderChange,
        OneTimeServiceType.directorChange,
        OneTimeServiceType.addressChange,
        OneTimeServiceType.activityChange,
        OneTimeServiceType.charterChange,
      ],
      OneTimeServiceCategory.other: [
        OneTimeServiceType.accountingPolicyDevelopment,
        OneTimeServiceType.consultation,
        OneTimeServiceType.training1C,
        OneTimeServiceType.courierDelivery,
        OneTimeServiceType.courierSubmission,
        OneTimeServiceType.counterpartyCheck,
        OneTimeServiceType.bankAccountAssistance,
      ],
      OneTimeServiceCategory.inactive: [
        OneTimeServiceType.auditServices,
        OneTimeServiceType.legalServices,
      ],
    };

/// Состояние калькулятора разовых услуг
class OneTimeServicesState extends Equatable {
  const OneTimeServicesState({
    this.selectedServices = const {},
    this.total = 0,
  });

  /// Выбранные услуги: ID услуги -> количество
  final Map<String, int> selectedServices;

  /// Итоговая стоимость
  final int total;

  OneTimeServicesState copyWith({
    Map<String, int>? selectedServices,
    int? total,
  }) {
    return OneTimeServicesState(
      selectedServices: selectedServices ?? this.selectedServices,
      total: total ?? this.total,
    );
  }

  @override
  List<Object?> get props => [selectedServices, total];
}
