
Идентификатор - d4ea448kvn3t4tceoj7i
Описание - 🔔 Управляет уведомлениями пользователя: получение, архивация.
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: То<PERSON><PERSON>н аутентифицированного пользователя.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).
	-> Тело запроса:
		- `action` (string, **обязательно**): Тип операции. Допустимые значения:
			- `GET`: Получить уведомления.
			- `ARCHIVE`: Архивировать уведомление.
			- `MARK_AS_DELIVERED`: Пометить уведомления как доставленные/прочитанные.
		- `notice_id` (string, необязательно): ID уведомления. Обязателен для `ARCHIVE` и для получения одного конкретного уведомления через `GET`.
		- `notice_ids` (list of strings, необязательно): Список ID уведомлений. Обязателен для `MARK_AS_DELIVERED`.
		- **Параметры для `action: GET`**:
			- `page` (integer, необязательно): Номер страницы для пагинации (начиная с 0). По умолчанию `0`.
			- `get_archived` (boolean, необязательно): Если `true`, возвращает список архивированных уведомлений. По умолчанию `false`.

Внутренняя работа:
	-> Авторизация и извлечение user_id:
		-> Проверка наличия и валидности JWT токена из заголовков (Authorization или X-Forwarded-Authorization).
		-> Верификация токена с использованием JWT_SECRET, извлечение user_id.
	-> Подробное логирование:
		-> Вывод в лог полного содержимого event и context для отладки.
	-> Получение action из requestContext.apiGateway.operationContext.
	-> Создание подключения к YDB:
		-> Получение драйвера и пула сессий для notices-database с использованием переменных окружения.
	-> Маршрутизация по action в транзакции:
		-> Для GET:
			-> Извлечение параметров: notice_id из pathParams, page и get_archived из queryStringParameters.
			-> Если notice_id указан: выборка одной записи по ID, форматирование json-полей, возврат данных.
			-> Иначе: подсчет общего количества неархивированных уведомлений, расчет пагинации, выборка с ORDER BY created_at DESC, LIMIT и OFFSET, форматирование и возврат с метаданными.
			-> Обработка случая отсутствия таблицы (новый пользователь): возврат пустого списка.
		-> Для ARCHIVE и MARK_AS_DELIVERED:
			-> Парсинг тела запроса для получения notice_id или notice_ids.
			-> Для ARCHIVE: проверка существования, обновление is_archived = true.
			-> Для MARK_AS_DELIVERED: проверка списка, обновление is_delivered = true и delivered_at = текущему времени для всех IDs.
	-> Обработка ошибок:
		-> AuthError: 403 Forbidden.
		-> LogicError: 400 Bad Request.
		-> NotFoundError: 404 Not Found.
		-> Общие исключения: 500 Internal Server Error.

На выходе:
	-> `200 OK` (GET, список): `{"metadata": {"total": int, "page": int, "pages": int}, "data": [array of notices]}` с форматированными json-полями.
	-> `200 OK` (GET, одно уведомление): `{"data": {notice_object}}` с форматированными json-полями.
	-> `200 OK` (ARCHIVE): `{"message": "Notice archived successfully."}`
	-> `200 OK` (MARK_AS_DELIVERED): `{"message": "{count} notices marked as delivered."}`
	-> `400 Bad Request` для логических ошибок (например, invalid action, missing params).
	-> `403 Forbidden` для ошибок авторизации.
	-> `404 Not Found` для несуществующих уведомлений или страниц.
	-> `500 Internal Server Error` для внутренних ошибок.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_NOTICES` - Эндпоинт для `notices-database`.
	- `YDB_DATABASE_NOTICES` - Путь к `notices-database`.
	- `SA_KEY_FILE`
	- `JWT_SECRET`
	- `STATIC_ACCESS_KEY_ID` [[🗝️ auth-service-acc - Статический ключ доступа.md]]
	- `STATIC_SECRET_ACCESS_KEY` [[🗝️ auth-service-acc - Статический ключ доступа.md]]

---
#### index.py
```python
import json
import os
import ydb
from utils import auth_utils, ydb_utils, request_parser

from get import get_notices
from archive import archive_notice
from mark_as_delivered import mark_notices_as_delivered
from custom_errors import AuthError, LogicError, NotFoundError

def _log_event_context(event, context):
    """Выводит в лог подробную информацию о входящем событии и контексте вызова."""
    try:
        print("RAW EVENT: %s" % json.dumps(event, default=str, ensure_ascii=False)[:10000])
    except Exception:
        print("RAW EVENT (non-json serialisable): %s" % event)
    
    if context is not None:
        print(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s" %
            (getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None))
        )

def handler(event, context):
    _log_event_context(event, context)
    
    user_id = "unknown"
    action = "unknown"
    try:
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing Bearer token")

        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired token")
        
        user_id = user_payload['user_id']
        print(f"Authorized request for user_id: {user_id}")

        action = event.get('requestContext', {}).get('apiGateway', {}).get('operationContext', {}).get('action')
        
        if not action:
            raise LogicError("Action is a required parameter (must be set in API Gateway operation context).")

        print(f"Processing action: {action} for user_id: {user_id}")

        table_name = f"notices_{user_id}"
        notices_driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_NOTICES"],
            os.environ["YDB_DATABASE_NOTICES"]
        )
        notices_pool = ydb.SessionPool(notices_driver)
        
        print(f"Routing action '{action}' to table '{table_name}'")

        def notices_transaction_router(session):
            if action == "GET":
                notice_id = event.get('pathParams', {}).get('noticeId')
                page_str = event.get('queryStringParameters', {}).get('page', '0')
                get_archived_str = event.get('queryStringParameters', {}).get('get_archived', 'false')
                
                print(f"GET params: notice_id={notice_id}, page={page_str}, get_archived={get_archived_str}")
                
                return get_notices(
                    session, table_name, notice_id,
                    int(page_str), get_archived_str.lower() == 'true'
                )
            
            data = request_parser.parse_request_body(event)
            print(f"Parsed request body for {action}: {data}")

            if action == "ARCHIVE":
                notice_id = data.get('notice_id')
                return archive_notice(session, table_name, notice_id)
            
            elif action == "MARK_AS_DELIVERED":
                notice_ids = data.get('notice_ids')
                return mark_notices_as_delivered(session, table_name, notice_ids)
            
            else:
                raise LogicError(f"Invalid action specified: '{action}'.")

        response = notices_pool.retry_operation_sync(notices_transaction_router)
        
        if response.get('statusCode') == 200:
            print(f"Action '{action}' for user {user_id} completed successfully.")
        
        print(f"Response to be returned: {response}")
        return response

    except AuthError as e:
        print(f"[AUTH ERROR] {e}")
        return {"statusCode": 403, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": str(e)}, ensure_ascii=False)}
    except LogicError as e:
        print(f"[LOGIC ERROR] {e}")
        return {"statusCode": 400, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": str(e)}, ensure_ascii=False)}
    except NotFoundError as e:
        print(f"[NOT FOUND ERROR] {e}")
        return {"statusCode": 404, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": str(e)}, ensure_ascii=False)}
    except Exception as e:
        print(f"[CRITICAL ERROR] Error processing notice request for user {user_id} on action {action}: {e}")
        return {"statusCode": 500, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": "Internal Server Error"}, ensure_ascii=False)}
```
#### get.py
```python
import json
import math
import ydb
from custom_errors import NotFoundError

PAGE_SIZE = 100

def _format_notice(row, columns):
    data = {}
    for c in columns:
        value = row[c.name]
        if 'json' in c.name and value:
            data[c.name] = json.loads(value)
        else:
            data[c.name] = value
    return data

def get_notices(session, table_name, notice_id=None, page=0, get_archived=False):
    try:
        tx = session.transaction(ydb.SerializableReadWrite())

        if notice_id:
            query = session.prepare(f"DECLARE $id AS Utf8; SELECT * FROM `{table_name}` WHERE notice_id = $id;")
            print(f"Executing YQL for single notice fetch: NOTICE_ID={notice_id}")
            res = tx.execute(query, {"$id": notice_id})
            if not res[0].rows:
                raise NotFoundError(f"Notice with id {notice_id} not found.")
            
            data = _format_notice(res[0].rows[0], res[0].columns)
            tx.commit()
            print(f"Successfully fetched single notice {notice_id} from table {table_name}.")
            return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"data": data}, default=str, ensure_ascii=False)}

        page = int(page or 0)
        if page < 0: page = 0
        offset = page * PAGE_SIZE

        where_clause = "WHERE (is_archived IS NULL OR is_archived = false)"

        count_query = session.prepare(f"SELECT COUNT(notice_id) AS total FROM `{table_name}` {where_clause};")
        print(f"Executing YQL count query with clause: {where_clause}")
        count_res = tx.execute(count_query)
        total_items = count_res[0].rows[0].total if count_res[0].rows else 0
        total_pages = math.ceil(total_items / PAGE_SIZE) if total_items > 0 else 0
        
        print(f"Found {total_items} notices in table {table_name} matching criteria.")

        if total_items == 0:
            tx.commit()
            return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"metadata": {"total": 0, "page": 0, "pages": 0}, "data": []}, ensure_ascii=False)}

        if page >= total_pages and total_pages > 0:
            raise NotFoundError(f"Page {page} does not exist. Total pages: {total_pages}.")

        select_data_query = session.prepare(f"SELECT * FROM `{table_name}` {where_clause} ORDER BY created_at DESC LIMIT {PAGE_SIZE} OFFSET {offset};")
        print("Executing YQL select data query with pagination")
        data_res = tx.execute(select_data_query)
        
        data = [_format_notice(row, data_res[0].columns) for row in data_res[0].rows]
        metadata = {"total": total_items, "page": page, "pages": total_pages}
        
        print(f"Successfully fetched {len(data)} notices from table {table_name} on page {page}. Total items: {total_items}.")
        tx.commit()
        return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"metadata": metadata, "data": data}, default=str, ensure_ascii=False)}

    except ydb.SchemeError:
        print(f"Table '{table_name}' not found. Returning empty list as it's a valid case for a new user.")
        return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"metadata": {"total": 0, "page": 0, "pages": 0}, "data": []}, ensure_ascii=False)}
```
#### archive.py
```python
import json
import ydb
from custom_errors import LogicError, NotFoundError

def archive_notice(session, table_name, notice_id):
    if not notice_id:
        raise LogicError("notice_id is required for ARCHIVE action.")
    
    tx = session.transaction(ydb.SerializableReadWrite())

    check_query = session.prepare(f"DECLARE $id AS Utf8; SELECT 1 FROM `{table_name}` WHERE notice_id = $id;")
    print(f"Executing YQL existence check for archive on notice_id: {notice_id}")
    check_res = tx.execute(check_query, {"$id": notice_id})
    if not check_res[0].rows:
        raise NotFoundError(f"Notice with id {notice_id} not found.")

    update_query = session.prepare(f"UPDATE `{table_name}` SET is_archived = true WHERE notice_id = $id;")
    print("Executing YQL update to set is_archived=true")
    tx.execute(update_query, {"$id": notice_id})
    
    tx.commit()
    print(f"Successfully archived notice_id: {notice_id} in table {table_name}")
    
    return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": "Notice archived successfully."}, ensure_ascii=False)}
```
#### mark_as_delivered.py
```python
import json
import datetime
import pytz
import ydb
from custom_errors import LogicError

def mark_notices_as_delivered(session, table_name, notice_ids):
    if not isinstance(notice_ids, list) or not notice_ids:
        raise LogicError("notice_ids must be a non-empty list.")
    
    tx = session.transaction(ydb.SerializableReadWrite())

    update_query = session.prepare(f"DECLARE $ids AS List<Utf8>; DECLARE $now AS Timestamp; UPDATE `{table_name}` SET is_delivered = true, delivered_at = $now WHERE notice_id IN $ids;")
    print(f"Executing YQL update to mark {len(notice_ids)} notices as delivered")
    tx.execute(update_query, {
        "$ids": notice_ids,
        "$now": datetime.datetime.now(pytz.utc)
    })
    
    tx.commit()
    print(f"Marked {len(notice_ids)} notices as delivered in table {table_name}.")
    
    return {
        "statusCode": 200,
        "headers": {"Content-Type": "application/json; charset=utf-8"},
        "body": json.dumps({"message": f"{len(notice_ids)} notices marked as delivered."}, ensure_ascii=False)
    }
```
#### custom_errors.py
```python
class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass
```