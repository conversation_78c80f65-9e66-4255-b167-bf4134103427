import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_create_card.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/router.dart';

@RoutePage()
class TasksEditPage extends StatelessWidget {
  final String taskId;

  const TasksEditPage({super.key, required this.taskId});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TasksCubit, TasksState>(
      builder: (context, state) {
        TaskEntity? task;

        if (state is TasksLoaded || state is TasksLoadingMore) {
          final tasks = context.read<TasksCubit>().tasks;
          try {
            task = tasks.firstWhere((t) => t.id == taskId);
          } catch (e) {
            // Задача не найдена в списке, возможно она загружена отдельно
          }
        } else if (state is TaskLoaded) {
          if (state.task.id == taskId) {
            task = state.task;
          }
        }

        if (task == null) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    IconButton(
                      onPressed: () => context.router.navigate(TasksRoute()),
                      icon: const Icon(Icons.arrow_back),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Редактирование задачи',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Expanded(child: Center(child: Text('Задача не найдена'))),
              ],
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  IconButton(
                    onPressed: () => context.router.navigate(TasksRoute()),
                    icon: const Icon(Icons.arrow_back),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Редактирование задачи',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: [
                    TaskCreateCard(
                      initialTask: task,
                      onCancel: () => context.router.navigate(TasksRoute()),
                      onCreated: (updatedTask) {
                        final firm =
                            context.read<ActiveFirmCubit>().state.selectedFirm;
                        if (firm != null) {
                          context.read<TasksCubit>().saveTask(
                            firm.id,
                            updatedTask,
                          );
                        }
                        context.router.navigate(TasksRoute());
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
