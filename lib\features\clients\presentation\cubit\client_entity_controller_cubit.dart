import 'package:bloc/bloc.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/system_task_uid_entity.dart';
import 'client_entity_controller_state.dart';

/// КУБИТ КОНТРОЛЯ ENTITY КЛИЕНТА
/// 
/// ⚠️ НЕ УДАЛЯТЬ ЭТО ПОЯСНЕНИЕ!
/// 
/// Этот кубит создан для решения проблемы с версиями client entity.
/// Он обеспечивает хранение ЕДИНСТВЕННОГО и ВСЕГДА АКТУАЛЬНОГО экземпляра
/// entity клиента в ЕДИНОМ МЕСТЕ.
/// 
/// Проблема, которую решает:
/// - Избегает конфликтов между "локальным измененным entity", "реальным" и "отправляемым для сохранения"
/// - Предотвращает ситуации, когда системная задача создается, но потом пропадает из-за сохранения старой версии entity
/// - Обеспечивает синхронизацию состояния клиента между различными компонентами интерфейса
/// 
/// Использование:
/// - Всегда используйте этот кубит для получения актуального состояния клиента
/// - Обновляйте состояние через методы этого кубита после любых изменений
/// - НЕ храните копии client entity в локальных переменных виджетов
class ClientEntityControllerCubit extends Cubit<ClientEntityControllerState> {
  ClientEntityControllerCubit() : super(ClientEntityControllerInitial());

  /// Загружает клиента и устанавливает его как активный
  void loadClient(ClientEntity client) {
    emit(ClientEntityControllerLoaded(client: client));
  }

  /// Обновляет текущего клиента
  void updateClient(ClientEntity updatedClient) {
    if (state is ClientEntityControllerLoaded) {
      emit(ClientEntityControllerLoaded(client: updatedClient));
    }
  }

  /// Обновляет системные задачи клиента
  void updateClientSystemTasks(List<SystemTaskUidEntity> systemTaskUids) {
    if (state is ClientEntityControllerLoaded) {
      final currentClient = (state as ClientEntityControllerLoaded).client;
      final updatedClient = currentClient.copyWith(systemTaskUids: systemTaskUids);
      emit(ClientEntityControllerLoaded(client: updatedClient));
    }
  }

  /// Добавляет новую системную задачу к клиенту
  void addSystemTask(SystemTaskUidEntity systemTask) {
    if (state is ClientEntityControllerLoaded) {
      final currentClient = (state as ClientEntityControllerLoaded).client;
      final updatedSystemTasks = List<SystemTaskUidEntity>.from(currentClient.systemTaskUids)
        ..add(systemTask);
      final updatedClient = currentClient.copyWith(systemTaskUids: updatedSystemTasks);
      emit(ClientEntityControllerLoaded(client: updatedClient));
    }
  }

  /// Удаляет системную задачу у клиента
  void removeSystemTask(String systemTaskUid) {
    if (state is ClientEntityControllerLoaded) {
      final currentClient = (state as ClientEntityControllerLoaded).client;
      final updatedSystemTasks = List<SystemTaskUidEntity>.from(currentClient.systemTaskUids)
        ..removeWhere((task) => task.uid == systemTaskUid);
      final updatedClient = currentClient.copyWith(systemTaskUids: updatedSystemTasks);
      emit(ClientEntityControllerLoaded(client: updatedClient));
    }
  }

  /// Получает текущего клиента (null если не загружен)
  ClientEntity? getCurrentClient() {
    if (state is ClientEntityControllerLoaded) {
      return (state as ClientEntityControllerLoaded).client;
    }
    return null;
  }

  /// Очищает состояние
  void clearClient() {
    emit(ClientEntityControllerInitial());
  }
}