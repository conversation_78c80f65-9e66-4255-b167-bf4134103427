
Идентификатор - d4e7poo87ijl346vpetg
Описание - ⛔️ (Высоко рисковая операция) Выполняет полное и необратимое удаление фирмы и всех связанных с ней данных на основе строгих предусловий
Точка входа - index.handler
Таймаут - 15 минут

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: **(Обязательно)** JWT токен пользователя, инициирующего удаление.
	-> Тело запроса:
	    - `firm_id` (string, **обязательно**): ID фирмы, подлежащей удалению.
Внутренняя работа:
	-> Логирует начало процесса удаления для фирмы {firm_id} владельцем {user_id}.
	-> Авторизация и парсинг запроса:
	    -> Извлекает заголовок Authorization, проверяет наличие Bearer токена, иначе AuthError.
	    -> Верифицирует JWT, извлекает user_id, иначе AuthError.
	    -> Парсит тело запроса, извлекает firm_id (обязательно), иначе LogicError.
	-> Подключение к YDB (firms-database), создание пула сессий.
	-> Фаза 1: Проверка предусловий (run_all_checks):
	    -> Проверяет права владельца (_check_owner_permissions): Запрос к Users, проверка наличия записи и roles == ["OWNER"], иначе AuthError.
	    -> Проверяет отсутствие активных интеграций (_check_integrations): Запрос к Firms, парсинг integrations_json, для каждого проверка enabled != true, иначе PreconditionFailedError.
	    -> Проверяет отсутствие вложений в задачах (_check_task_attachments): Подключение к tasks DB, запрос COUNT в tasks_{firm_id} где attachments_json содержит '[{', если >0 - PreconditionFailedError; если таблицы нет - пропуск.
	    -> Проверяет отсутствие вложений у клиентов (_check_client_attachments): Вызов edit-client с action=GET, для каждого клиента парсинг tax_and_legal_info_json, проверка attached_files пустой, иначе PreconditionFailedError.
	    -> Логирует успешное прохождение проверок.
	-> Фаза 2: Поэтапное удаление (run_all_deletions):
	    -> Удаление всех задач (_delete_all_tasks): Подключение к tasks DB, получение всех task_id из tasks_{firm_id}, цикл invoke edit-task с action=DELETE; логирует прогресс.
	    -> Удаление всех версий клиентов (_delete_all_clients): Подключение к clients DB, получение всех (client_id, manual_creation_date) из clients_{firm_id}, цикл invoke edit-client с action=DELETE и creation_date; логирует прогресс, пропускает ошибки.
	    -> Удаление всех сотрудников кроме владельца (_delete_all_employees): Вызов edit-employee с action=GET_INFO, фильтр user_id != owner_id, цикл invoke delete-employee; логирует прогресс.
	    -> Удаление персональных таблиц (_drop_personal_tables): Для tasks_{firm_id}, clients_{firm_id}, client_payments_{firm_id} - подключение к соответствующей DB, drop_table, если не существует - предупреждение; логирует.
	    -> Удаление записи тарифов (_delete_tariffs_and_storage_record): Подключение к tariffs DB, DELETE FROM tariffs_and_storage WHERE firm_id; логирует.
	    -> Финальное удаление записей фирмы (_delete_firm_records): Подключение к firms DB, в транзакции DELETE FROM Firms WHERE firm_id, DELETE FROM Users WHERE user_id=owner_id AND firm_id; логирует.
	-> Логирует успешное завершение удаления.
	-> Обработка исключений: Логирует ошибки, возвращает соответствующий статус (403 для AuthError, 400 для LogicError, 412 для PreconditionFailedError, 500 для других).
На выходе:
	-> `200 OK`: `{"message": "Firm and all associated data successfully deleted."}`
	-> `400 Bad Request`: Отсутствует или некорректен `firm_id` в теле запроса.
	-> `403 Forbidden`: Токен недействителен или у пользователя нет прав `OWNER`.
	-> `412 Precondition Failed`: Одно из предусловий (по интеграциям или вложениям) не выполнено.
	-> `500 Internal Server Error`: Внутренняя ошибка сервера в процессе выполнения.

---

Зависимости и окружение

Необходимые утилиты: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `invoke_utils.py`
Переменные окружения:
    *   `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
    *   `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS`
    *   `YDB_ENDPOINT_CLIENTS`, `YDB_DATABASE_CLIENTS`
    *   `YDB_ENDPOINT_CLIENT_PAYMENTS`, `YDB_DATABASE_CLIENT_PAYMENTS`
    *   `YDB_ENDPOINT_TARIFFS_AND_STORAGE`, `YDB_DATABASE_TARIFFS_AND_STORAGE`
    *   `SA_KEY_FILE`
    *   `JWT_SECRET`
    *   `FUNCTION_ID_EDIT_TASK`
    *   `FUNCTION_ID_EDIT_CLIENT`
    *   `FUNCTION_ID_EDIT_EMPLOYEE`
    *   `FUNCTION_ID_DELETE_EMPLOYEE`

---
#### index.py
```python
import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

# Импорт логических модулей
import precondition_checks
import deletion_logic
from custom_errors import AuthError, LogicError, PreconditionFailedError

# Настройка логирования
logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    try:
        # 1. Авторизация и парсинг
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing Bearer token.")
        
        user_jwt = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(user_jwt)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired token.")
        
        user_id = user_payload['user_id']
        
        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        if not firm_id:
            raise LogicError("`firm_id` is required in the request body.")

        logging.info(f"--- STARTING DELETION PROCESS FOR FIRM {firm_id} BY OWNER {user_id} ---")

        # 2. Подключение к основной БД для транзакций
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)

        # 3. Фаза 1: Проверка всех предусловий
        logging.info("--- PHASE 1: PRECONDITION CHECKS ---")
        precondition_checks.run_all_checks(firms_pool, user_jwt, user_id, firm_id)
        logging.info("All precondition checks passed successfully.")

        # 4. Фаза 2: Поэтапное удаление
        logging.info("--- PHASE 2: STAGED DELETION ---")
        deletion_logic.run_all_deletions(firms_pool, user_jwt, user_id, firm_id)
        
        logging.info(f"--- FIRM {firm_id} DELETED SUCCESSFULLY ---")
        return {"statusCode": 200, "body": json.dumps({"message": "Firm and all associated data successfully deleted."})}

    except AuthError as e:
        logging.error(f"Authorization error: {e}", exc_info=True)
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        logging.error(f"Logic error: {e}", exc_info=True)
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except PreconditionFailedError as e:
        logging.error(f"Precondition failed: {e}", exc_info=True)
        return {"statusCode": 412, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical unhandled error: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}

```

#### precondition_checks.py
```python
import json
import logging
import ydb
import invoke_utils
from custom_errors import AuthError, PreconditionFailedError
from utils import ydb_utils

def _check_owner_permissions(session, user_id, firm_id):
    """Проверяет, что пользователь является владельцем фирмы."""
    logging.info("Checking owner permissions...")
    query = session.prepare("DECLARE $uid AS Utf8; DECLARE $fid AS Utf8; SELECT roles FROM Users WHERE user_id = $uid AND firm_id = $fid;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id, "$fid": firm_id}, commit_tx=True)
    
    if not res[0].rows:
        raise AuthError("User is not a member of the specified firm.")
    
    roles = json.loads(res[0].rows[0].roles or '[]')
    if roles != ["OWNER"]:
        raise AuthError("Access denied. Only the firm owner can perform this operation.")
    logging.info("Owner permissions confirmed.")

def _check_integrations(session, firm_id):
    """Проверяет, что нет активных интеграций."""
    logging.info("Checking for active integrations...")
    query = session.prepare("DECLARE $fid AS Utf8; SELECT integrations_json FROM Firms WHERE firm_id = $fid;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$fid": firm_id}, commit_tx=True)
    
    if not res[0].rows: return # Фирма уже удалена, проверка пройдена
    
    integrations = json.loads(res[0].rows[0].integrations_json or '{}')
    for name, details in integrations.items():
        if isinstance(details, dict) and details.get("enabled") is True:
            raise PreconditionFailedError(f"Cannot delete firm: active integration '{name}' found.")
    logging.info("No active integrations found.")

def _check_task_attachments(session, firm_id):
    """Проверяет, что ни у одной задачи нет вложений."""
    logging.info("Checking for task attachments...")
    table_name = f"tasks_{firm_id}"
    
    # ИСПРАВЛЕНО: Правильное сравнение для JSON-поля
    # Проверяем, что attachments_json не NULL И что он не равен пустому JSON-массиву.
    # Если столбец Optional<Json>, то сравнение его с CAST('[]' AS Json)
    # будет работать, даже если значение NULL.
    # Более надежный способ: CAST в строку и проверка, что не содержит "[{", что означает не пустой массив объектов
    query_text = f"""
        SELECT COUNT(task_id) AS count FROM `{table_name}`
        WHERE attachments_json IS NOT NULL AND String::Contains(CAST(attachments_json AS String), '[{{');
    """
    
    try:
        res = session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(query_text), commit_tx=True)
        if res[0].rows and res[0].rows[0].count > 0:
            raise PreconditionFailedError(f"Cannot delete firm: found {res[0].rows[0].count} tasks with attachments.")
    except ydb.SchemeError: # <-- ИСПРАВЛЕНО: заменено ydb.BadQuery на ydb.SchemeError
        logging.warning(f"Table {table_name} not found. Assuming no tasks with attachments.")
        return # Если таблицы нет, то и задач с вложениями нет
    except Exception as e: # Отлавливаем другие ошибки YQL, которые не являются SchemeError
        # Для отладки можем логировать полную ошибку YDB
        if isinstance(e, ydb.issues.GenericError):
             logging.error(f"YDB Query Error in _check_task_attachments: {e.issues}", exc_info=True)
        else:
            logging.error(f"Unexpected error in _check_task_attachments: {e}", exc_info=True)
        raise # Пробрасываем ошибку дальше, так как это не ожидаемое отсутствие таблицы
    logging.info("No tasks with attachments found.")

def _check_client_attachments(user_jwt, firm_id):
    """Проверяет, что ни у одного клиента нет вложений."""
    logging.info("Checking for client attachments...")
    clients_response = invoke_utils.invoke_function(
        invoke_utils.FUNCTION_ID_EDIT_CLIENT,
        {"firm_id": firm_id, "action": "GET"},
        user_jwt
    )
    if not clients_response or 'data' not in clients_response:
        logging.warning("Could not retrieve client list to check for attachments.")
        return

    clients = clients_response['data']
    for client in clients:
        legal_info = client.get('tax_and_legal_info_json', '{}')
        if isinstance(legal_info, str): legal_info = json.loads(legal_info)
        
        # Проверяем наличие файла по ключу 'attached_files' и что он не пустой список
        if legal_info.get("attached_files") and len(legal_info["attached_files"]) > 0:
            raise PreconditionFailedError(f"Cannot delete firm: client '{client.get('client_name')}' has attached files.")
    logging.info("No clients with attachments found.")

def run_all_checks(pool, user_jwt, user_id, firm_id):
    """Запускает все проверки предусловий."""
    # Проверки, требующие доступ к firms-database
    pool.retry_operation_sync(lambda s: _check_owner_permissions(s, user_id, firm_id))
    pool.retry_operation_sync(lambda s: _check_integrations(s, firm_id))
    
    # Проверки, требующие доступ к другим БД или функциям
    tasks_driver = ydb_utils.get_driver_for_db(invoke_utils.YDB_ENDPOINT_TASKS, invoke_utils.YDB_DATABASE_TASKS)
    tasks_pool = ydb.SessionPool(tasks_driver)
    tasks_pool.retry_operation_sync(lambda s: _check_task_attachments(s, firm_id))

    _check_client_attachments(user_jwt, firm_id)
```

#### deletion_logic.py
```python
import logging
import os
import ydb
import invoke_utils
from utils import ydb_utils

def _delete_all_tasks(user_jwt, firm_id):
    """Удаляет все задачи фирмы."""
    logging.info("Starting deletion of all tasks...")
    tasks_driver = ydb_utils.get_driver_for_db(invoke_utils.YDB_ENDPOINT_TASKS, invoke_utils.YDB_DATABASE_TASKS)
    tasks_pool = ydb.SessionPool(tasks_driver)
    table_name = f"tasks_{firm_id}"

    def get_task_ids(session):
        try:
            res = session.transaction(ydb.SerializableReadWrite()).execute(f"SELECT task_id FROM `{table_name}`;", commit_tx=True)
            return [row.task_id for row in res[0].rows]
        except ydb.BadQuery:
            return [] # Таблицы нет - задач нет

    task_ids = tasks_pool.retry_operation_sync(get_task_ids)
    if not task_ids:
        logging.info("No tasks found to delete.")
        return

    logging.info(f"Found {len(task_ids)} tasks to delete.")
    for i, task_id in enumerate(task_ids):
        invoke_utils.invoke_function(
            invoke_utils.FUNCTION_ID_EDIT_TASK,
            {"firm_id": firm_id, "action": "DELETE", "task_id": task_id},
            user_jwt
        )
        logging.info(f"  - Deleted task {i+1}/{len(task_ids)} ({task_id})")
    logging.info("All tasks deleted.")

def _delete_all_clients(user_jwt, firm_id):
    """Удаляет все версии всех клиентов фирмы."""
    logging.info("Starting deletion of all client versions...")
    clients_driver = ydb_utils.get_driver_for_db(invoke_utils.YDB_ENDPOINT_CLIENTS, invoke_utils.YDB_DATABASE_CLIENTS)
    clients_pool = ydb.SessionPool(clients_driver)
    table_name = f"clients_{firm_id}"

    def get_all_client_versions(session):
        """Получает все версии (client_id, manual_creation_date) всех клиентов."""
        try:
            query = f"SELECT client_id, manual_creation_date FROM `{table_name}`"
            res = session.transaction(ydb.SerializableReadWrite()).execute(query, commit_tx=True)
            return [(row.client_id, row.manual_creation_date) for row in res[0].rows]
        except ydb.SchemeError:
            logging.warning(f"Table {table_name} not found. Assuming no clients to delete.")
            return []

    client_versions = clients_pool.retry_operation_sync(get_all_client_versions)

    if not client_versions:
        logging.info("No client versions found to delete.")
        return

    logging.info(f"Found {len(client_versions)} client versions to delete.")
    for i, (client_id, creation_date) in enumerate(client_versions):
        try:
            invoke_utils.invoke_function(
                invoke_utils.FUNCTION_ID_EDIT_CLIENT,
                {
                    "firm_id": firm_id,
                    "action": "DELETE",
                    "client_id": client_id,
                    "creation_date": creation_date.isoformat()
                },
                user_jwt
            )
            logging.info(f"  - Deleted client version {i+1}/{len(client_versions)} ({client_id}, {creation_date.isoformat()})")
        except Exception as e:
            logging.error(f"Failed to delete version for client {client_id} (date: {creation_date.isoformat()}): {e}", exc_info=True)
            continue

    logging.info("All client versions deleted.")

def _delete_all_employees(user_jwt, firm_id, owner_id):
    """Удаляет всех сотрудников, кроме владельца."""
    logging.info("Starting deletion of all employees...")
    employees_response = invoke_utils.invoke_function(
        invoke_utils.FUNCTION_ID_EDIT_EMPLOYEE,
        {"firm_id": firm_id, "action": "GET_INFO"},
        user_jwt
    )
    if not employees_response or not employees_response.get('data'):
        logging.warning("Could not retrieve employee list.")
        return

    employees_to_delete = [emp for emp in employees_response['data'] if emp['user_id'] != owner_id]
    if not employees_to_delete:
        logging.info("No other employees found to delete.")
        return
    
    logging.info(f"Found {len(employees_to_delete)} employees to delete.")
    for i, emp in enumerate(employees_to_delete):
        invoke_utils.invoke_function(
            invoke_utils.FUNCTION_ID_DELETE_EMPLOYEE,
            {"firm_id": firm_id, "user_id_to_delete": emp['user_id']},
            user_jwt
        )
        logging.info(f"  - Deleted employee {i+1}/{len(employees_to_delete)} ({emp['user_id']})")
    logging.info("All employees deleted.")

def _drop_personal_tables(firm_id):
    """Удаляет персональные таблицы фирмы из всех баз данных."""
    logging.info("Starting deletion of personal YDB tables...")
    databases = {
        "tasks": (invoke_utils.YDB_ENDPOINT_TASKS, invoke_utils.YDB_DATABASE_TASKS),
        "clients": (invoke_utils.YDB_ENDPOINT_CLIENTS, invoke_utils.YDB_DATABASE_CLIENTS),
        "client_payments": (invoke_utils.YDB_ENDPOINT_CLIENT_PAYMENTS, invoke_utils.YDB_DATABASE_CLIENT_PAYMENTS)
    }

    for db_alias, (endpoint, db_path) in databases.items():
        table_name = f"{db_alias}_{firm_id}" if db_alias != "client_payments" else f"client_payments_{firm_id}"
        full_table_path = os.path.join(db_path, table_name)
        try:
            driver = ydb_utils.get_driver_for_db(endpoint, db_path)
            session = driver.table_client.session().create()
            session.drop_table(full_table_path)
            logging.info(f"  - Dropped table: {full_table_path}")
        except ydb.SchemeError:
            logging.warning(f"  - Table not found, skipping drop: {full_table_path}")
        except Exception as e:
            logging.error(f"  - FAILED to drop table {full_table_path}: {e}")
    logging.info("Personal YDB tables deleted.")

def _delete_tariffs_and_storage_record(firm_id):
    """Удаляет запись о тарифах и хранилище фирмы."""
    logging.info("Starting deletion of tariffs and storage record...")
    try:
        tariffs_driver = ydb_utils.get_driver_for_db(invoke_utils.YDB_ENDPOINT_TARIFFS_AND_STORAGE, invoke_utils.YDB_DATABASE_TARIFFS_AND_STORAGE)
        tariffs_pool = ydb.SessionPool(tariffs_driver)
        
        def delete_record(session):
            query = session.prepare("DECLARE $fid AS Utf8; DELETE FROM tariffs_and_storage WHERE firm_id = $fid;")
            session.transaction(ydb.SerializableReadWrite()).execute(query, {"$fid": firm_id}, commit_tx=True)
        
        tariffs_pool.retry_operation_sync(delete_record)
        logging.info("Tariffs and storage record deleted successfully.")
    except Exception as e:
        logging.error(f"Failed to delete tariffs and storage record: {e}")
        # Не прерываем процесс удаления, так как это не критично

def _delete_firm_records(session, firm_id, owner_id):
    """Удаляет финальные записи о фирме и владении."""
    logging.info("Starting final cleanup of Firms and Users tables...")
    tx = session.transaction(ydb.SerializableReadWrite())
    
    # Удаляем запись о фирме
    tx.execute(session.prepare("DECLARE $fid AS Utf8; DELETE FROM Firms WHERE firm_id = $fid;"), {"$fid": firm_id})
    # Удаляем запись о владении
    tx.execute(session.prepare("DECLARE $uid AS Utf8; DECLARE $fid AS Utf8; DELETE FROM Users WHERE user_id = $uid AND firm_id = $fid;"), {"$uid": owner_id, "$fid": firm_id})
    
    tx.commit()
    logging.info("Final records from Firms and Users tables deleted.")

def run_all_deletions(pool, user_jwt, owner_id, firm_id):
    """Запускает все шаги по удалению."""
    _delete_all_tasks(user_jwt, firm_id)
    _delete_all_clients(user_jwt, firm_id)
    _delete_all_employees(user_jwt, firm_id, owner_id)
    _drop_personal_tables(firm_id)
    _delete_tariffs_and_storage_record(firm_id)
    pool.retry_operation_sync(lambda s: _delete_firm_records(s, firm_id, owner_id))
```

#### invoke_utils.py
```python
import os
import json
import logging
import time
import requests
import ydb.iam

# Переменные окружения для YDB
YDB_ENDPOINT_FIRMS = os.environ.get("YDB_ENDPOINT_FIRMS")
YDB_DATABASE_FIRMS = os.environ.get("YDB_DATABASE_FIRMS")
YDB_ENDPOINT_TASKS = os.environ.get("YDB_ENDPOINT_TASKS")
YDB_DATABASE_TASKS = os.environ.get("YDB_DATABASE_TASKS")
YDB_ENDPOINT_CLIENTS = os.environ.get("YDB_ENDPOINT_CLIENTS")
YDB_DATABASE_CLIENTS = os.environ.get("YDB_DATABASE_CLIENTS")
YDB_ENDPOINT_CLIENT_PAYMENTS = os.environ.get("YDB_ENDPOINT_CLIENT_PAYMENTS")
YDB_DATABASE_CLIENT_PAYMENTS = os.environ.get("YDB_DATABASE_CLIENT_PAYMENTS")
YDB_ENDPOINT_TARIFFS_AND_STORAGE = os.environ.get("YDB_ENDPOINT_TARIFFS_AND_STORAGE")
YDB_DATABASE_TARIFFS_AND_STORAGE = os.environ.get("YDB_DATABASE_TARIFFS_AND_STORAGE")

# ID целевых функций
FUNCTION_ID_EDIT_TASK = os.environ.get("FUNCTION_ID_EDIT_TASK")
FUNCTION_ID_EDIT_CLIENT = os.environ.get("FUNCTION_ID_EDIT_CLIENT")
FUNCTION_ID_EDIT_EMPLOYEE = os.environ.get("FUNCTION_ID_EDIT_EMPLOYEE")
FUNCTION_ID_DELETE_EMPLOYEE = os.environ.get("FUNCTION_ID_DELETE_EMPLOYEE")

# Кеш для IAM-токена
_IAM_TOKEN_CACHE = {"token": None, "expires_at": 0}

def _get_iam_token():
    """Получает и кеширует IAM-токен."""
    now = time.time()
    if _IAM_TOKEN_CACHE["token"] and now < _IAM_TOKEN_CACHE["expires_at"]:
        return _IAM_TOKEN_CACHE["token"]
    
    try:
        creds = ydb.iam.MetadataUrlCredentials()
        token, expires_at = creds.token
        _IAM_TOKEN_CACHE["token"] = token
        _IAM_TOKEN_CACHE["expires_at"] = expires_at - 60 # Запас в 60 секунд
        return token
    except Exception as e:
        logging.error(f"Failed to get IAM token: {e}", exc_info=True)
        return None

def invoke_function(function_id: str, payload: dict, user_jwt: str) -> dict | None:
    """Вызывает облачную функцию с авторизацией."""
    if not function_id:
        logging.error(f"Function ID is not specified for payload: {payload}")
        return None

    iam_token = _get_iam_token()
    if not iam_token:
        logging.error("Cannot invoke function, failed to get IAM token.")
        return None

    function_url = f"https://functions.yandexcloud.net/{function_id}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {iam_token}",
        "X-Forwarded-Authorization": f"Bearer {user_jwt}"
    }

    try:
        response = requests.post(function_url, headers=headers, json=payload, timeout=20)
        response.raise_for_status()
        return response.json() if response.content else {}
    except requests.exceptions.RequestException as e:
        logging.error(f"Error invoking function {function_id}: {e}", exc_info=True)
        return None
```

#### custom_errors.py
```python
class AuthError(Exception):
    """Ошибка аутентификации или прав доступа."""
    pass

class LogicError(Exception):
    """Ошибка в логике запроса (неверные параметры)."""
    pass

class PreconditionFailedError(Exception):
    """Ошибка, когда одно из предусловий для выполнения операции не выполнено."""
    pass
```
