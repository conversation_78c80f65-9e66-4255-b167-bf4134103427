import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';

/// Конфигурация для полей взносов (унификация)
class ContributionFieldConfig {
  final String key;
  final String label;
  final List<String> availableTypes;
  final TextEditingController dateController;
  final FocusNode dateFocusNode;
  final GlobalKey dateKey;
  final List<String> Function(ClientEntity) getTypesFromEntity;
  final List<int> Function(ClientEntity) getDatesFromEntity;
  final void Function(List<String>) updateTypes;
  final void Function(List<int>) updateDates;

  ContributionFieldConfig({
    required this.key,
    required this.label,
    required this.availableTypes,
    required this.dateController,
    required this.dateFocusNode,
    required this.dateKey,
    required this.getTypesFromEntity,
    required this.getDatesFromEntity,
    required this.updateTypes,
    required this.updateDates,
  });
}

/// Класс для управления всеми контроллерами и focus nodes в ClientEditCard
class ClientEditControllers {
  // Text Controllers
  final nameCtrl = TextEditingController();
  final shortNameCtrl = TextEditingController();
  final innCtrl = TextEditingController();
  final commentCtrl = TextEditingController();
  final creationDateCtrl = TextEditingController();
  final fixedContributionsDateCtrl = TextEditingController();
  final contributionsIP1PercentDateCtrl = TextEditingController();
  final salaryDateCtrl = TextEditingController();
  final advanceDateCtrl = TextEditingController();
  final ndflDate1Ctrl = TextEditingController();
  final ndflDate2Ctrl = TextEditingController();
  final directorNameCtrl = TextEditingController();
  final directorStartDateCtrl = TextEditingController();
  final digitalSignatureExpiryDateCtrl = TextEditingController();
  final attachmentCommentsCtrl = TextEditingController();

  // Focus Nodes
  final salaryDateFN = FocusNode();
  final advanceDateFN = FocusNode();
  final ndflDate1FN = FocusNode();
  final ndflDate2FN = FocusNode();
  final creationDateFN = FocusNode();
  final fixedContributionsDateFN = FocusNode();
  final contributionsIP1PercentDateFN = FocusNode();
  final directorStartDateFN = FocusNode();
  final digitalSignatureExpiryDateFN = FocusNode();

  // Keys for validation scrolling
  final enpFieldKey = GlobalKey();
  final creationDateKey = GlobalKey();
  final fixedContributionsDateKey = GlobalKey();
  final contributionsIP1PercentDateKey = GlobalKey();
  final salaryPaymentKey = GlobalKey();
  final advancePaymentKey = GlobalKey();
  final ndflPaymentKey = GlobalKey();
  final directorStartDateKey = GlobalKey();
  final digitalSignatureExpiryDateKey = GlobalKey();

  /// Инициализирует контроллеры данными из клиента
  void initializeFromClient(ClientEntity client) {
    nameCtrl.text = client.name;
    shortNameCtrl.text = client.shortName ?? '';
    innCtrl.text = client.inn ?? '';
    commentCtrl.text = client.comment ?? '';
    attachmentCommentsCtrl.text = client.attachmentComments ?? '';

    if (client.creationDate != null) {
      creationDateCtrl.text = DateFormat(
        'dd.MM.yyyy',
      ).format(client.creationDate!);
    }

    fixedContributionsDateCtrl.text = ClientConstants.formatPaymentDates(
      client.fixedContributionsPaymentDate,
    );

    contributionsIP1PercentDateCtrl.text = ClientConstants.formatPaymentDates(
      client.contributionsIP1PercentPaymentDate,
    );

    _initializePaymentScheduleTexts(client);

    // Инициализация полей руководителя
    directorNameCtrl.text = client.directorName ?? '';
    if (client.directorStartDate != null) {
      directorStartDateCtrl.text = DateFormat(
        'dd.MM.yyyy',
      ).format(client.directorStartDate!);
    }

    // Инициализация поля ЭЦП
    if (client.digitalSignatureExpiryDate != null) {
      digitalSignatureExpiryDateCtrl.text = DateFormat(
        'dd.MM.yyyy',
      ).format(client.digitalSignatureExpiryDate!);
    }
  }

  /// Инициализирует текстовые поля для графиков платежей
  void _initializePaymentScheduleTexts(ClientEntity client) {
    salaryDateCtrl.text =
        client.salaryPayment?.paymentDate != null
            ? '${client.salaryPayment!.paymentDate} число'
            : '';

    advanceDateCtrl.text =
        client.advancePayment?.paymentDate != null
            ? '${client.advancePayment!.paymentDate} число'
            : '';

    ndflDate1Ctrl.text =
        client.ndflPayment?.paymentDate != null
            ? '${client.ndflPayment!.paymentDate} число'
            : '';

    ndflDate2Ctrl.text =
        client.ndflPayment?.transferDate != null
            ? '${client.ndflPayment!.transferDate} число'
            : '';
  }

  /// Возвращает конфигурации для всех полей взносов (унификация)
  List<ContributionFieldConfig> getContributionConfigs(
    void Function(void Function()) setState,
    dynamic state,
  ) {
    return [
      ContributionFieldConfig(
        key: 'fixedContributionsIP',
        label: 'Фиксированные взносы ИП',
        availableTypes: ClientConstants.fixedContributionsIP,
        dateController: fixedContributionsDateCtrl,
        dateFocusNode: fixedContributionsDateFN,
        dateKey: fixedContributionsDateKey,
        getTypesFromEntity: (client) => client.fixedContributionsIP,
        getDatesFromEntity: (client) => client.fixedContributionsPaymentDate,
        updateTypes:
            (types) => setState(() => state.fixedContributionsIP = types),
        updateDates:
            (dates) =>
                setState(() => state.fixedContributionsPaymentDate = dates),
      ),
      ContributionFieldConfig(
        key: 'contributionsIP1Percent',
        label: 'Взносы ИП 1%',
        availableTypes: ClientConstants.contributionsIP1Percent,
        dateController: contributionsIP1PercentDateCtrl,
        dateFocusNode: contributionsIP1PercentDateFN,
        dateKey: contributionsIP1PercentDateKey,
        getTypesFromEntity: (client) => client.contributionsIP1Percent,
        getDatesFromEntity:
            (client) => client.contributionsIP1PercentPaymentDate,
        updateTypes:
            (types) => setState(() => state.contributionsIP1Percent = types),
        updateDates:
            (dates) => setState(
              () => state.contributionsIP1PercentPaymentDate = dates,
            ),
      ),
    ];
  }

  /// Освобождает все ресурсы
  void dispose() {
    // Dispose controllers
    nameCtrl.dispose();
    shortNameCtrl.dispose();
    innCtrl.dispose();
    commentCtrl.dispose();
    creationDateCtrl.dispose();
    fixedContributionsDateCtrl.dispose();
    contributionsIP1PercentDateCtrl.dispose();
    salaryDateCtrl.dispose();
    advanceDateCtrl.dispose();
    ndflDate1Ctrl.dispose();
    ndflDate2Ctrl.dispose();
    directorNameCtrl.dispose();
    directorStartDateCtrl.dispose();
    digitalSignatureExpiryDateCtrl.dispose();
    attachmentCommentsCtrl.dispose();

    // Dispose focus nodes
    salaryDateFN.dispose();
    advanceDateFN.dispose();
    ndflDate1FN.dispose();
    ndflDate2FN.dispose();
    creationDateFN.dispose();
    fixedContributionsDateFN.dispose();
    contributionsIP1PercentDateFN.dispose();
    directorStartDateFN.dispose();
    digitalSignatureExpiryDateFN.dispose();
  }
}
