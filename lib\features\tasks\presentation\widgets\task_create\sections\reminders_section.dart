import 'package:flutter/material.dart';
import '../models.dart';

// ------------------ Напоминания ------------------
class RemindersSection extends StatelessWidget {
  final List<ReminderItem> reminders;
  final VoidCallback onAddReminder;
  final Function(ReminderItem) onRemoveReminder;

  const RemindersSection({
    super.key,
    required this.reminders,
    required this.onAddReminder,
    required this.onRemoveReminder,
  });

  @override
  Widget build(BuildContext context) {
    String roleName(String r) {
      switch (r) {
        case 'assignee':
          return 'Исполнитель';
        case 'observer':
          return 'Наблюдатель';
        case 'creator':
          return 'Постановщик';
        default:
          return r;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Напоминания',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: onAddReminder,
              icon: const Icon(Icons.add),
              label: const Text('Добавить'),
            ),
          ],
        ),
        ...reminders.map(
          (reminder) => Card(
            child: ListTile(
              title: Text(
                '${reminder.dateTime.day}.${reminder.dateTime.month}.${reminder.dateTime.year} '
                '${reminder.dateTime.hour}:${reminder.dateTime.minute.toString().padLeft(2, '0')}',
              ),
              subtitle: Text('Роль: ${roleName(reminder.role)}'),
              trailing: IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => onRemoveReminder(reminder),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
