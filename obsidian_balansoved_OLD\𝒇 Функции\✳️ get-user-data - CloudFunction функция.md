Идентификатор - d4e4c3r7udrhh476a309
Описание - 📥 Получить все данные пользователя (инфо, фирмы, задачи) по JWT токену.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен аутентифицированного пользователя.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).

Внутренняя работа:
	1. **Авторизация**: Проверяется JWT токен с помощью [[📄 utils - auth_utils.md]], извлекается `user_id`. Логируется начало процесса авторизации.
	2. **Сбор информации о пользователе**: Выполняется запрос к [[💾 jwt-database - База данных YandexDatabase.md]] для получения основной информации о пользователе (`user_id`, `email`, `user_name`). Логируется email обрабатываемого пользователя.
	3. **Сбор информации о фирмах**:
		- Выполняется запрос к [[💾 firms-database - База данных YandexDatabase.md]] для поиска всех **АКТИВНЫХ** записей в таблице `Users` (`is_active = true`), где `user_id` совпадает с `user_id` из токена.
		- Из полученных записей формируется список `firm_ids`.
		- Выполняется второй запрос к таблице `Firms` для получения полной информации по каждому `firm_id` из списка.
		- Логируются найденные фирмы и их данные.
	4. **Сбор информации о задачах**:
		- Для каждой найденной фирмы (`firm_id`) динамически определяется имя таблицы задач (`tasks_{firm_id}`) в [[💾 tasks-database - База данных YandexDatabase.md]].
		- Выполняется запрос к каждой такой таблице, который извлекает все задачи и фильтрует их в коде, проверяя наличие `user_id` в полях `assignee_ids_json`, `observer_ids_json`, `creator_ids_json`.
		- Логируется обработка каждой фирмы и количество найденных задач.
	5. **Агрегация и логирование**: Все полученные данные (информация о пользователе, список фирм, список задач с полными данными) собираются в единый JSON-объект. Логируются финальные выходные данные.
	6. **Отключение кеширования**: Все ответы содержат заголовки `Cache-Control`, `Pragma` и `Expires` для предотвращения кеширования.

На выходе:
	-> `200 OK`: `{"user_info": {"user_id": "...", "email": "...", "user_name": "..."}, "firms": [{"firm_id": "...", "firm_name": "...", "owner_user_id": "...", "integrations": {...}, "user_roles": ["OWNER", "ADMIN"]}], "tasks": [{"task_id": "...", "title": "...", "description": "...", "firm_id": "...", /* и другие поля задачи */}]}`
	-> `401 Unauthorized`: Если токен невалиден.
	-> `404 Not Found`: Если пользователь из токена не найден в `jwt-database`.
	-> `500 Internal Server Error`: В случае ошибок при работе с базами данных.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT`, `YDB_DATABASE` ([[💾 jwt-database - База данных YandexDatabase.md]])
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase.md]])
	- `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS` ([[💾 tasks-database - База данных YandexDatabase.md]])
	- `SA_KEY_FILE`
	- `JWT_SECRET`

---

#### Код функции

```python
import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils

logging.getLogger().setLevel(logging.INFO)

class AuthError(Exception): pass
class NotFoundError(Exception): pass

def _log_event_context(event, context):
    """Logs detailed information about the incoming event and context."""
    try:
        print("RAW EVENT: %s" % json.dumps(event, default=str, ensure_ascii=False)[:10000])
    except Exception:
        print("RAW EVENT (non-json serialisable): %s" % event)
    
    if context is not None:
        print(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s" %
            (getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None))
        )

def get_user_info(session, user_id):
    logging.info(f"Fetching user info for user_id: {user_id}")
    query_text = "DECLARE $user_id AS Utf8; SELECT user_id, email, user_name FROM users WHERE user_id = $user_id;"
    result = session.transaction(ydb.SerializableReadWrite()).execute(
        session.prepare(query_text), {"$user_id": user_id}, commit_tx=True
    )
    if not result[0].rows:
        raise NotFoundError(f"User with id {user_id} not found in the main database.")
    user_data = result[0].rows[0]
    return {"user_id": user_data.user_id, "email": user_data.email, "user_name": user_data.user_name}

def get_user_firms(session, user_id):
    logging.info(f"Fetching firms for user_id: {user_id}")
    # Находим все фирмы, в которых состоит пользователь
    query_text = "DECLARE $user_id AS Utf8; SELECT firm_id, roles FROM Users WHERE user_id = $user_id AND is_active = true;"
    result_users = session.transaction(ydb.SerializableReadWrite()).execute(
        session.prepare(query_text), {"$user_id": user_id}, commit_tx=True
    )
    
    if not result_users[0].rows:
        return []

    user_firm_roles = {row.firm_id: json.loads(row.roles or '[]') for row in result_users[0].rows}
    firm_ids = list(user_firm_roles.keys())
    
    # Получаем детали этих фирм
    query_text_firms = f"""
        DECLARE $firm_ids AS List<Utf8>;
        SELECT firm_id, firm_name, owner_user_id, integrations_json FROM Firms WHERE firm_id IN $firm_ids;
    """
    result_firms = session.transaction(ydb.SerializableReadWrite()).execute(
        session.prepare(query_text_firms), {"$firm_ids": firm_ids}, commit_tx=True
    )

    firms_data = []
    for row in result_firms[0].rows:
        firm_id = row.firm_id
        firms_data.append({
            "firm_id": firm_id,
            "firm_name": row.firm_name,
            "owner_user_id": row.owner_user_id,
            "integrations": json.loads(row.integrations_json or '{}'),
            "user_roles": user_firm_roles.get(firm_id, [])
        })
    logging.info(f"Firms fetched: {firms_data}")
    return firms_data

def get_user_tasks_for_firm(session, firm_id, user_id):
    table_name = f"tasks_{firm_id}"
    query_text = f"""
        DECLARE $user_id_str AS Utf8;
        SELECT * FROM `{table_name}` WHERE 
            JsonValue(creator_ids_json, '$[*]') LIKE $user_id_str OR
            JsonValue(assignee_ids_json, '$[*]') LIKE $user_id_str OR
            JsonValue(observer_ids_json, '$[*]') LIKE $user_id_str;
    """
    # YQL LIKE не поддерживает поиск в JSON, поэтому используем обходной путь
    # В реальном приложении лучше использовать полнотекстовый индекс или денормализацию
    
    # Более надежный, но медленный способ - вычитать все и отфильтровать в коде.
    # Для демонстрации оставим так, но с оговоркой.
    
    all_tasks_query = f"SELECT * FROM `{table_name}`"
    result = session.transaction(ydb.SerializableReadWrite()).execute(
        session.prepare(all_tasks_query), commit_tx=True
    )
    
    user_tasks = []
    for row in result[0].rows:
        is_related = False
        related_fields = ['creator_ids_json', 'assignee_ids_json', 'observer_ids_json']
        for field in related_fields:
            ids_list = json.loads(row[field] or '[]')
            if user_id in ids_list:
                is_related = True
                break
        
        if is_related:
            task_data = {c.name: row[c.name] for c in result[0].columns}
            task_data['firm_id'] = firm_id # Добавляем firm_id для контекста
            user_tasks.append(task_data)
            
    return user_tasks

def handler(event, context):
    _log_event_context(event, context)
    try:
        logging.info("Starting authorization process")
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing Bearer token.")

        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired token.")
        
        user_id = user_payload['user_id']
        logging.info(f"Fetching data for user_id: {user_id}")
        
        # Подключения к БД
        auth_driver = ydb_utils.get_ydb_driver()
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        tasks_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_TASKS"], os.environ["YDB_DATABASE_TASKS"])

        auth_pool = ydb.SessionPool(auth_driver)
        firms_pool = ydb.SessionPool(firms_driver)
        tasks_pool = ydb.SessionPool(tasks_driver)
        
        # 1. Получаем инфо о пользователе
        user_info = auth_pool.retry_operation_sync(lambda s: get_user_info(s, user_id))
        logging.info("User info retrieved")
        
        # 2. Получаем список фирм
        user_firms = firms_pool.retry_operation_sync(lambda s: get_user_firms(s, user_id))
        logging.info("User firms retrieved")
        
        # 3. Получаем задачи для каждой фирмы
        all_user_tasks = []
        for firm in user_firms:
            firm_id = firm['firm_id']
            logging.info(f"Processing firm: {firm_id}")
            try:
                tasks_for_firm = tasks_pool.retry_operation_sync(
                    lambda s: get_user_tasks_for_firm(s, firm_id, user_id)
                )
                all_user_tasks.extend(tasks_for_firm)
                logging.info(f"Tasks for firm {firm_id} added")
            except Exception as e:
                logging.warning(f"Could not fetch tasks for firm {firm_id}. Reason: {e}")
                continue
        logging.info(f"Total tasks collected: {len(all_user_tasks)}")
        
        # 4. Собираем финальный ответ
        final_response = {
            "user_info": user_info,
            "firms": user_firms,
            "tasks": all_user_tasks
        }
        logging.info("Final response prepared")
        logging.info(f"Output data: {json.dumps(final_response, default=str)}")
        return {"statusCode": 200, "headers": {"Cache-Control": "no-cache, no-store, must-revalidate", "Pragma": "no-cache", "Expires": "0"}, "body": json.dumps(final_response, default=str)}

    except AuthError as e:
        return {"statusCode": 401, "headers": {"Cache-Control": "no-cache, no-store, must-revalidate", "Pragma": "no-cache", "Expires": "0"}, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "headers": {"Cache-Control": "no-cache, no-store, must-revalidate", "Pragma": "no-cache", "Expires": "0"}, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical error in get-user-data: {e}", exc_info=True)
        return {"statusCode": 500, "headers": {"Cache-Control": "no-cache, no-store, must-revalidate", "Pragma": "no-cache", "Expires": "0"}, "body": json.dumps({"message": "Internal Server Error"})}
```