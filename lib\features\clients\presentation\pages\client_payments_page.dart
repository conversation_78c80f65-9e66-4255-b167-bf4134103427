import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:collection/collection.dart';
import 'package:auto_route/auto_route.dart';

import 'package:balansoved_enterprise/features/clients/domain/entities/client_payment_entity.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_payments_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_payment_dialog.dart';
import 'package:balansoved_enterprise/presentation/widgets/resizable_data_table.dart';
import 'package:balansoved_enterprise/injection_container.dart';

@RoutePage()
class ClientPaymentsPage extends StatefulWidget {
  const ClientPaymentsPage({super.key});

  @override
  _ClientPaymentsPageState createState() => _ClientPaymentsPageState();
}

class _ClientPaymentsPageState extends State<ClientPaymentsPage> {
  late final ScrollController _horizontalScrollController;
  int _currentYear = DateTime.now().year;
  int _sortColumnIndex = 0;
  bool _sortAsc = true;

  @override
  void initState() {
    super.initState();
    _horizontalScrollController = ScrollController();
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm != null) {
      context.read<ClientPaymentsCubit>().loadData(
        firmState.selectedFirm!.id,
        _currentYear,
      );
    }
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    super.dispose();
  }

  void _showEditDialog(
    BuildContext context,
    client,
    String firmId,
    int year,
    int month,
    payment,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return BlocProvider.value(
          value: BlocProvider.of<ClientPaymentsCubit>(context),
          child: EditPaymentDialog(
            client: client,
            year: year,
            month: month,
            payment: payment,
            firmId: firmId,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ClientPaymentsCubit>(
      create: (_) => sl<ClientPaymentsCubit>(),
      child: BlocListener<ActiveFirmCubit, ActiveFirmState>(
        listener: (context, state) {
          if (state.selectedFirm != null) {
            context.read<ClientPaymentsCubit>().loadData(
              state.selectedFirm!.id,
              _currentYear,
            );
          }
        },
        child: Scaffold(
          appBar: AppBar(title: const Text('Оплата клиентов')),
          body: BlocConsumer<ClientPaymentsCubit, ClientPaymentsState>(
            listener: (context, state) {
              if (state.error != null && state.error!.isNotEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.error!),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            builder: (context, state) {
              final firmId =
                  context.watch<ActiveFirmCubit>().state.selectedFirm?.id;

              if (state.isLoading && state.clients.isEmpty) {
                return const Center(child: CircularProgressIndicator());
              }
              if (state.noAccess) {
                return const Center(
                  child: Text('У вас нет прав для просмотра этого раздела.'),
                );
              }
              if (state.clients.isEmpty) {
                return const Center(child: Text('Нет данных для отображения.'));
              }

              final clients = state.clients;
              final payments = state.paymentsByClient;

              // Сортировка клиентов
              final sortedClients = List<dynamic>.from(clients);
              if (_sortColumnIndex == 0) {
                sortedClients.sort((a, b) {
                  final res = a.name.compareTo(b.name);
                  return _sortAsc ? res : -res;
                });
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (state.isLoading) const LinearProgressIndicator(),
                  Expanded(
                    child: ResizableDataTable(
                      prefsKey: 'client_payments_table_column_widths',
                      sortColumnIndex: _sortColumnIndex,
                      sortAscending: _sortAsc,
                      initialColumnWidths: [
                        200.0, // Клиент
                        ...List.filled(12, 80.0), // Месяцы
                      ],
                      columns: [
                        DataColumn(
                          label: const Center(child: Text('Клиент')),
                          onSort: (columnIndex, ascending) {
                            setState(() {
                              _sortColumnIndex = columnIndex;
                              _sortAsc = ascending;
                            });
                          },
                        ),
                        ...List.generate(
                          12,
                          (index) => DataColumn(
                            label: Center(
                              child: Text(
                                DateFormat.MMM(
                                  'ru',
                                ).format(DateTime(0, index + 1)),
                              ),
                            ),
                          ),
                        ),
                      ],
                      rows:
                          sortedClients.map((client) {
                            return DataRow(
                              cells: [
                                DataCell(
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                    ),
                                    child: Text(
                                      client.name,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                                ...List.generate(12, (monthIndex) {
                                  final month = monthIndex + 1;
                                  final clientPayments =
                                      payments[client.id] ?? [];
                                  final paymentForMonth = clientPayments
                                      .firstWhereOrNull(
                                        (p) =>
                                            p.period.year == _currentYear &&
                                            p.period.month == month,
                                      );

                                  final cellPeriod = DateTime(
                                    _currentYear,
                                    month,
                                  );

                                  // If нет записи за месяц, ячейка пустая
                                  if (paymentForMonth == null) {
                                    // Ищем тариф из прошлых периодов для tooltip
                                    double? latestTariffForEmpty;
                                    ClientPaymentEntity? latestTariffPayment;
                                    for (final p in clientPayments) {
                                      if (p.tariffAnnualAmount != null &&
                                          !p.period.isAfter(cellPeriod)) {
                                        if (latestTariffPayment == null ||
                                            p.period.isAfter(
                                              latestTariffPayment.period,
                                            )) {
                                          latestTariffPayment = p;
                                          latestTariffForEmpty = p.tariffAnnualAmount;
                                        }
                                      }
                                    }
                                    
                                    // Формируем tooltip для пустой ячейки
                                    String emptyTooltipText = '';
                                    if (latestTariffForEmpty != null && latestTariffForEmpty > 0) {
                                      emptyTooltipText += 'Тариф: ${latestTariffForEmpty.toStringAsFixed(0)} ₽\n';
                                    } else {
                                      emptyTooltipText += 'Тариф: не установлен\n';
                                    }
                                    emptyTooltipText += 'Оплачено: 0 ₽\n';
                                    emptyTooltipText += 'Дата оплаты: не указана';
                                    
                                    return DataCell(
                                      Tooltip(
                                        message: emptyTooltipText,
                                        waitDuration: const Duration(milliseconds: 500),
                                        child: InkWell(
                                          onTap: () {
                                            if (firmId != null) {
                                              _showEditDialog(
                                                context,
                                                client,
                                                firmId,
                                                _currentYear,
                                                month,
                                                null, // нет платежа, создаем новый
                                              );
                                            }
                                          },
                                          child: SizedBox(
                                            width: 80,
                                            height: 56,
                                            child: Container(
                                              color: Colors.transparent,
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  }

                                  double? effectiveTariff =
                                      paymentForMonth.tariffAnnualAmount;

                                  if (effectiveTariff == null) {
                                    // Берём самый свежий тариф до текущей даты (включительно)
                                    ClientPaymentEntity? latestTariff;
                                    for (final p in clientPayments) {
                                      if (p.tariffAnnualAmount != null &&
                                          !p.period.isAfter(cellPeriod)) {
                                        if (latestTariff == null ||
                                            p.period.isAfter(
                                              latestTariff.period,
                                            )) {
                                          latestTariff = p;
                                        }
                                      }
                                    }
                                    effectiveTariff =
                                        latestTariff?.tariffAnnualAmount;
                                  }

                                  final actualAmount =
                                      paymentForMonth.actualAmountPaid;
                                  final hasNewTariff =
                                      paymentForMonth.tariffAnnualAmount !=
                                      null;

                                  final colorScheme =
                                      Theme.of(context).colorScheme;
                                  Color? cellColor;
                                  String cellText = '';

                                  if (effectiveTariff != null &&
                                      effectiveTariff > 0) {
                                    final paidAmount = actualAmount ?? 0.0;
                                    final percentage =
                                        (paidAmount / effectiveTariff) * 100;
                                    cellText =
                                        '${percentage.toStringAsFixed(0)}%';

                                    if (paidAmount >= effectiveTariff) {
                                      cellColor = colorScheme.secondaryContainer
                                          .withOpacity(0.5);
                                    } else if (paidAmount > 0) {
                                      cellColor = colorScheme.tertiaryContainer
                                          .withOpacity(0.5);
                                    } else {
                                      cellColor = colorScheme.errorContainer
                                          .withOpacity(0.5);
                                    }
                                  } else if (actualAmount != null) {
                                    // Если тарифа нет даже в прошлом, просто показать сумму оплаты
                                    cellText = actualAmount.toStringAsFixed(0);
                                  }

                                  // Формируем текст для tooltip
                                  String tooltipText = '';
                                  if (effectiveTariff != null && effectiveTariff > 0) {
                                    tooltipText += 'Тариф: ${effectiveTariff.toStringAsFixed(0)} ₽\n';
                                  } else {
                                    tooltipText += 'Тариф: не установлен\n';
                                  }
                                  
                                  if (actualAmount != null) {
                                    tooltipText += 'Оплачено: ${actualAmount.toStringAsFixed(0)} ₽\n';
                                  } else {
                                    tooltipText += 'Оплачено: 0 ₽\n';
                                  }
                                  
                                  if (paymentForMonth.paymentDate != null) {
                                    final dateFormat = DateFormat('dd.MM.yyyy', 'ru');
                                    tooltipText += 'Дата оплаты: ${dateFormat.format(paymentForMonth.paymentDate!)}';
                                  } else {
                                    tooltipText += 'Дата оплаты: не указана';
                                  }

                                  return DataCell(
                                    Tooltip(
                                      message: tooltipText,
                                      waitDuration: const Duration(milliseconds: 500),
                                      child: InkWell(
                                        onTap: () {
                                          if (firmId != null) {
                                            _showEditDialog(
                                              context,
                                              client,
                                              firmId,
                                              _currentYear,
                                              month,
                                              paymentForMonth,
                                            );
                                          }
                                        },
                                        child: SizedBox(
                                          width: 80,
                                          height: 56,
                                          child: Stack(
                                            children: [
                                              Positioned.fill(
                                                child: Container(
                                                  color: cellColor ??
                                                      Colors.transparent,
                                                  alignment: Alignment.center,
                                                  child: Text(
                                                    cellText,
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ),
                                              if (hasNewTariff)
                                                Positioned(
                                                  top: 2,
                                                  left: 2,
                                                  child: Icon(
                                                    Icons.currency_ruble,
                                                    size: 14,
                                                    color: Theme.of(
                                                      context,
                                                    ).colorScheme.primary,
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }),
                              ],
                            );
                          }).toList(),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.chevron_left),
                        onPressed: () {
                          setState(() {
                            _currentYear--;
                          });
                          final firmId =
                              context
                                  .read<ActiveFirmCubit>()
                                  .state
                                  .selectedFirm
                                  ?.id;
                          if (firmId != null) {
                            context.read<ClientPaymentsCubit>().changeYear(
                              firmId,
                              _currentYear,
                            );
                          }
                        },
                      ),
                      Text(
                        '$_currentYear год',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      IconButton(
                        icon: const Icon(Icons.chevron_right),
                        onPressed: () {
                          setState(() {
                            _currentYear++;
                          });
                          final firmId =
                              context
                                  .read<ActiveFirmCubit>()
                                  .state
                                  .selectedFirm
                                  ?.id;
                          if (firmId != null) {
                            context.read<ClientPaymentsCubit>().changeYear(
                              firmId,
                              _currentYear,
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
