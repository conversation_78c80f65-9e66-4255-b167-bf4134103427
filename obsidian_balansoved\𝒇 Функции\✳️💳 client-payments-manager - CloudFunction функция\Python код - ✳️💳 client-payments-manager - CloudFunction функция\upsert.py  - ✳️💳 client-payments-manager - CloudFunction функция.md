
```python
import json
import datetime
import pytz
import ydb
from decimal import Decimal, InvalidOperation
from custom_errors import LogicError

def upsert_payment(session, table_name, payload):
    if not isinstance(payload, dict):
        raise LogicError("`payload` must be a JSON object.")
    
    client_id = payload.get('client_id')
    period_str = payload.get('period')
    
    if not all([client_id, period_str]):
        raise LogicError("`client_id` and `period` are required in payload.")

    try:
        period_date = datetime.datetime.strptime(period_str, '%Y-%m').date().replace(day=1)
    except ValueError:
        raise LogicError("Invalid `period` format. Use 'YYYY-MM'.")

    now = datetime.datetime.now(pytz.utc)

    try:
        actual_amount_kopeks = None
        if 'actual_amount_paid' in payload and payload['actual_amount_paid'] is not None:
            actual_amount_kopeks = int(Decimal(str(payload['actual_amount_paid'])) * 100)

        tariff_annual_amount_kopeks = None
        if 'tariff_annual_amount' in payload and payload['tariff_annual_amount'] is not None:
            tariff_annual_amount_kopeks = int(Decimal(str(payload['tariff_annual_amount'])) * 100)

        actual_payment_date = None
        if 'actual_payment_date' in payload and payload['actual_payment_date'] is not None:
            try:
                actual_payment_date = datetime.datetime.strptime(payload['actual_payment_date'], '%Y-%m-%d').date()
            except ValueError:
                raise LogicError("Invalid `actual_payment_date` format. Use 'YYYY-MM-DD'.")
    except (InvalidOperation, TypeError):
        raise LogicError("Invalid number format for amount fields.")

    tx = session.transaction(ydb.SerializableReadWrite())

    # Шаг 1: Проверяем наличие записи, чтобы сохранить исходную дату создания (created_at)
    check_query = session.prepare(f"""
        DECLARE $client_id AS Utf8;
        DECLARE $period AS Date;
        SELECT created_at FROM `{table_name}` WHERE client_id = $client_id AND period_start_date = $period;
    """)
    result = tx.execute(check_query, {
        "$client_id": client_id,
        "$period": period_date,
    })

    is_new_record = not result[0].rows
    created_at_to_use = now if is_new_record else result[0].rows[0].created_at
    
    # Шаг 2: Выполняем простой и надежный UPSERT
    upsert_query = session.prepare(f"""
        DECLARE $client_id AS Utf8;
        DECLARE $period AS Date;
        DECLARE $actual_amount AS Optional<Int64>;
        DECLARE $tariff_amount AS Optional<Int64>;
        DECLARE $actual_payment_date AS Optional<Date>;
        DECLARE $created_at AS Timestamp;
        DECLARE $updated_at AS Timestamp;

        UPSERT INTO `{table_name}` (client_id, period_start_date, actual_amount_kopeks, tariff_annual_amount_kopeks, actual_payment_date, created_at, updated_at)
        VALUES (
            $client_id,
            $period,
            $actual_amount,
            $tariff_amount,
            $actual_payment_date,
            $created_at,
            $updated_at
        );
    """)

    tx.execute(
        upsert_query,
        {
            "$client_id": client_id,
            "$period": period_date,
            "$actual_amount": actual_amount_kopeks,
            "$tariff_amount": tariff_annual_amount_kopeks,
            "$actual_payment_date": actual_payment_date,
            "$created_at": created_at_to_use,
            "$updated_at": now,
        }
    )

    tx.commit()
    
    # Возвращаем разные коды статуса для создания и обновления, как ожидает тест
    status_code = 201 if is_new_record else 200
    message = "Payment record created" if is_new_record else "Payment record updated"
    
    return {"statusCode": status_code, "body": json.dumps({"message": message})}
```