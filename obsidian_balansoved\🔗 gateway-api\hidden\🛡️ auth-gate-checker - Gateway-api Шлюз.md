Идентификатор - d5dcghbnv9f73st5ej8b
Имя - auth-gate-checker
Служебный домен - https://d5dcghbnv9f73st5ej8b.a6hc9vya.apigw.yandexcloud.net
Каталог - b1gfk08jac021i2pogfv

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Auth Gate Checker API
  version: 1.0.0
servers:
  - url: https://d5dcghbnv9f73st5ej8b.a6hc9vya.apigw.yandexcloud.net

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - bearerAuth: []

paths:
  /check:
    get:
      summary: Проверяет JWT токен через функцию-авторизатор (гейт)
      operationId: checkGate
      
      # Это и есть наши "ворота". Перед выполнением основной интеграции
      # API Gateway вызовет эту функцию для проверки прав.
      x-yc-apigateway-authorizer:
        type: function
        # ID функции-авторизатора (auth-gate)
        function_id: d4el1emddp7tcl893f1n 
        # ИЗМЕНЕНО: Указан ID вашего нового сервисного аккаунта
        service_account_id: ajek4l2ql5b2e77uo3vb

      # Основная интеграция. Это то, что выполнится, ЕСЛИ гейт пропустит запрос.
      x-yc-apigateway-integration:
        type: dummy
        http_code: 200
        http_headers:
          Content-Type: application/json
        content:
          application/json: |
            {
              "status": "ok",
              "message": "Authentication successful. The gate was passed."
            }
            
      responses:
        '200':
          description: Успешная авторизация. Токен валиден, гейт пройден.
        '403':
          description: Ошибка авторизации. Возвращается API Gateway, если гейт не пропустил запрос.
```