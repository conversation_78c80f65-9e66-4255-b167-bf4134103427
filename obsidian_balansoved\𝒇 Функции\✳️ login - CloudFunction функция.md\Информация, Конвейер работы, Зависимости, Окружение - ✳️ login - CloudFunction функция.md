
Идентификатор - d4e11h046o2u1eihlgf0
Описание - Войти в систему. Возвращает существующий токен или создает новый.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `email`: Email пользователя.
	-> `password`: Пароль пользователя.
Внутренняя работа:
	-> Находит в YDB пользователя по `email`.
	-> Проверяет, активен ли аккаунт (is_active).
	-> Сверяет хеш пароля.
	-> Обновляет `last_login_at`.
	-> **Проверяет, существует ли для пользователя `jwt_token` в базе данных.**
	-> **Если токен существует:** возвращает **существующий** токен.
	-> **Если токена нет:** генерирует новый JWT, сохраняет его в базу и возвращает **новый** токен.
На выходе:
	-> `200 OK`: {"token": "<jwt_token>"}
	-> `401 Unauthorized`: {"message": "Invalid credentials."}
	-> `423 Locked`: {"message": "Account not confirmed. Please verify your email."}

---
#### Зависимости и окружение
- **Необходимые утилиты**: utils/auth_utils.py, utils/ydb_utils.py
- **Переменные окружения**:
    - YDB_ENDPOINT - Эндпоинт базы данных (Источник: [[🗃️ Структура YDB]])
    - YDB_DATABASE - Путь к базе данных (Источник: [[🗃️ Структура YDB]])
    - SA_KEY_FILE - ydb_sa_key.json (Источник: [[ydb_sa_key.json]])
    - JWT_SECRET - Надежная секретная строка (Генерируется пользователем)