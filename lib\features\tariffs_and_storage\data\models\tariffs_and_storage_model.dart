import '../../domain/entities/tariffs_and_storage_entity.dart';
import 'storage_info_model.dart';
import 'subscription_info_model.dart';

/// Модель для данных о тарифах и хранилище (data layer)
class TariffsAndStorageModel extends TariffsAndStorageEntity {
  const TariffsAndStorageModel({
    required super.firmId,
    required super.subscriptionInfo,
    required super.storageInfo,
    required super.confidentialData,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Создать модель из JSON ответа API
  factory TariffsAndStorageModel.fromJson(Map<String, dynamic> json) {
    // Вспомогательная функция для безопасного парсинга даты. Поддерживает ISO-строку и
    // int (микросекунды с начала эпохи). При ошибке возвращает текущую дату.
    DateTime parseDate(dynamic value) {
      try {
        if (value == null) return DateTime.now();
        if (value is String) {
          final intValue = int.tryParse(value);
          if (intValue != null) {
            return DateTime.fromMicrosecondsSinceEpoch(
              intValue,
              isUtc: true,
            ).toLocal();
          }
          return DateTime.parse(value).toLocal();
        }
        if (value is int) {
          return DateTime.fromMicrosecondsSinceEpoch(
            value,
            isUtc: true,
          ).toLocal();
        }
      } catch (_) {}
      return DateTime.now();
    }

    return TariffsAndStorageModel(
      firmId: json['firm_id'] ?? '',
      subscriptionInfo: SubscriptionInfoModel.fromJson(
        json['subscription_info_json'] is String
            ? {} // Если пустая строка, используем пустой объект
            : json['subscription_info_json'] ?? {},
      ),
      storageInfo: StorageInfoModel.fromJson(
        json['storage_info_json'] is String
            ? {} // Если пустая строка, используем пустой объект
            : json['storage_info_json'] ?? {},
      ),
      confidentialData:
          json['confidential_data_json'] is String
              ? {} // Если пустая строка, используем пустой объект
              : Map<String, dynamic>.from(json['confidential_data_json'] ?? {}),
      createdAt: parseDate(json['created_at']),
      updatedAt: parseDate(json['updated_at']),
    );
  }

  /// Преобразовать в JSON
  Map<String, dynamic> toJson() {
    return {
      'firm_id': firmId,
      'subscription_info_json':
          SubscriptionInfoModel.fromEntity(subscriptionInfo).toJson(),
      'storage_info_json': StorageInfoModel.fromEntity(storageInfo).toJson(),
      'confidential_data_json': confidentialData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Преобразовать в Entity
  TariffsAndStorageEntity toEntity() {
    return TariffsAndStorageEntity(
      firmId: firmId,
      subscriptionInfo: subscriptionInfo,
      storageInfo: storageInfo,
      confidentialData: confidentialData,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Создать модель из Entity
  factory TariffsAndStorageModel.fromEntity(TariffsAndStorageEntity entity) {
    return TariffsAndStorageModel(
      firmId: entity.firmId,
      subscriptionInfo: entity.subscriptionInfo,
      storageInfo: entity.storageInfo,
      confidentialData: entity.confidentialData,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
