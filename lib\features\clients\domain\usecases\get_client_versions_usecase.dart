import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/clients_repository.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

class GetClientVersionsUseCase {
  final IClientsRepository repository;

  GetClientVersionsUseCase(this.repository);

  Future<Either<Failure, List<ClientEntity>>> call(String firmId, String clientName) async {
    NetworkLogger.printInfo(
      'GetClientVersionsUseCase: Getting versions for client: $clientName in firmId: $firmId',
    );

    try {
      final result = await repository.getClientVersions(firmId, clientName);

      return result.fold(
        (failure) {
          NetworkLogger.printError(
            'GetClientVersionsUseCase: Repository call failed:',
            failure.message,
          );
          return Left(failure);
        },
        (clientVersions) {
          NetworkLogger.printSuccess(
            'GetClientVersionsUseCase: Successfully retrieved ${clientVersions.length} versions for client $clientName',
          );
          return Right(clientVersions);
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'GetClientVersionsUseCase: Unexpected error:',
        e,
        stackTrace,
      );
      return Left(
        UnexpectedFailure(
          message: 'Неожиданная ошибка при получении версий клиента: $e',
        ),
      );
    }
  }
}