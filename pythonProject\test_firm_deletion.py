# test_firm_deletion.py
import requests
import json
import sys
import datetime
from colorama import init, Fore, Style

# Инициализируем colorama (autoreset=True сбрасывает цвет после каждого print)
init(autoreset=True)

# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
API_USER_DATA_URL = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net/get-user-data"
API_CREATE_FIRM_URL = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net/firms/create"
# Предполагаем, что функция удаления будет размещена в этом же API
API_DELETE_FIRM_URL = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net/firms/delete"

LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL
INFO = Fore.CYAN + "→" + Style.RESET_ALL


def run_test_step(title: str, url: str, method: str, payload: dict, headers: dict, expected_statuses: list):
    """Выполняет один шаг теста (POST или GET), выводит результат и возвращает ответ."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ")

    try:
        if method.upper() == 'POST':
            response = requests.post(url, json=payload, headers=headers, timeout=20)
        elif method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=20)
        else:
            raise ValueError("Unsupported HTTP method")

        if response.status_code in expected_statuses:
            print(f"{TICK} (Статус: {Fore.GREEN}{response.status_code}{Style.RESET_ALL})")
            return response
        else:
            print(
                f"{CROSS} (Ожидался статус в {expected_statuses}, получен {Fore.RED}{response.status_code}{Style.RESET_ALL})")
            print(Fore.RED + "  Текст ответа:")
            try:
                error_json = response.json()
                print(Fore.RED + json.dumps(error_json, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)
            return response  # Возвращаем ответ даже при ошибке для анализа

    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        return None


if __name__ == "__main__":
    print("\n--- Начало интерактивного тестирования удаления фирмы ---\n")

    # --- Шаг 1: Авторизация ---
    auth_response = run_test_step(
        "Шаг 1: Получение JWT токена",
        API_AUTH_URL, 'POST', LOGIN_PAYLOAD, DEFAULT_HEADERS, expected_statuses=[200]
    )
    if not auth_response or auth_response.status_code != 200:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось войти в систему. Тестирование прервано.")

    jwt_token = auth_response.json().get("token")
    authorized_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {jwt_token}"}

    # --- Шаг 2 и 3: Получение списка фирм и выбор ---
    selected_firm_id = None
    selected_firm_name = None

    while not selected_firm_id:
        user_data_response = run_test_step(
            "Шаг 2: Получение данных пользователя и списка фирм",
            API_USER_DATA_URL, 'GET', {}, authorized_headers, expected_statuses=[200]
        )
        if not user_data_response or user_data_response.status_code != 200:
            sys.exit(f"\n{CROSS} Критическая ошибка: не удалось получить данные пользователя.")

        all_firms = user_data_response.json().get("firms", [])
        owner_firms = [f for f in all_firms if "OWNER" in f.get("user_roles", [])]

        print(f"\n{INFO} Найдены фирмы, где вы являетесь владельцем:")
        print(f"     {Style.BRIGHT}[0] Создать НОВУЮ фирму для теста")
        for i, firm in enumerate(owner_firms):
            print(f"     {Style.BRIGHT}[{i + 1}] {firm['firm_name']} (ID: ...{firm['firm_id'][-12:]})")

        try:
            choice_str = input(f"\n{Style.BRIGHT}Введите номер фирмы для удаления: {Style.RESET_ALL}")
            choice = int(choice_str)

            if choice == 0:
                # Создание новой фирмы
                ts = datetime.datetime.now().strftime("%H%M%S")
                new_firm_name = f"Тестовая фирма {ts}"
                create_resp = run_test_step(
                    "Создание новой тестовой фирмы",
                    API_CREATE_FIRM_URL, 'POST', {"firm_name": new_firm_name}, authorized_headers,
                    expected_statuses=[201]
                )
                if create_resp and create_resp.status_code == 201:
                    selected_firm_id = create_resp.json().get("firm_id")
                    selected_firm_name = new_firm_name
                    print(f"   {TICK} Успешно создана фирма '{selected_firm_name}' с ID: {selected_firm_id}")
                else:
                    print(f"   {CROSS} Не удалось создать новую фирму. Попробуйте еще раз.")
                    continue

            elif 1 <= choice <= len(owner_firms):
                selected_firm = owner_firms[choice - 1]
                selected_firm_id = selected_firm['firm_id']
                selected_firm_name = selected_firm['firm_name']
            else:
                print(f"   {CROSS} Неверный номер. Пожалуйста, введите число от 0 до {len(owner_firms)}.")

        except (ValueError, IndexError):
            print(f"   {CROSS} Некорректный ввод. Пожалуйста, введите число.")

    # --- Шаг 4, 5, 6: Основной цикл удаления ---
    while True:
        # Шаг 4: Подтверждение
        while True:
            prompt = (f"\n{Style.BRIGHT}{Fore.YELLOW}Вы уверены, что хотите попытаться удалить фирму "
                      f"'{selected_firm_name}' ({selected_firm_id})?\n"
                      f"Убедитесь, что все условия выполнены (нет активных интеграций, нет вложений у задач/клиентов).\n"
                      f"Продолжить? (y/n): {Style.RESET_ALL}")
            confirm_choice = input(prompt).lower()
            if confirm_choice == 'y':
                break
            elif confirm_choice == 'n':
                print(f"{INFO} Попытка отменена. Запрашиваю подтверждение снова.")
                continue

        # Шаг 5: Попытка удаления
        # Ожидаем 200 (успех) или 412 (не выполнены предусловия) как "успешный" исход теста
        delete_response = run_test_step(
            f"Шаг 5: Попытка удаления фирмы '{selected_firm_name}'",
            API_DELETE_FIRM_URL, 'POST', {"firm_id": selected_firm_id}, authorized_headers,
            expected_statuses=[200, 403, 409, 412]
        )

        if delete_response and delete_response.status_code == 200:
            print(f"\n{TICK}{Style.BRIGHT} Фирма успешно удалена. Тестирование завершено.")
            break

        # Шаг 6: Повторить?
        while True:
            repeat_choice = input(f"\n{Style.BRIGHT}Повторить попытку удаления? (y/n): {Style.RESET_ALL}").lower()
            if repeat_choice in ['y', 'n']:
                break

        if repeat_choice == 'n':
            print(f"\n{INFO} Тестирование завершено пользователем.")
            break