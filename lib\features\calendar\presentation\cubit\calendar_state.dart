import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';

abstract class CalendarState extends Equatable {
  const CalendarState();

  @override
  List<Object?> get props => [];
}

class CalendarInitial extends CalendarState {}

class CalendarLoading extends CalendarState {}

class CalendarLoaded extends CalendarState {
  final List<TaskEntity> tasks;

  const CalendarLoaded({required this.tasks});

  @override
  List<Object?> get props => [tasks];
}

class CalendarError extends CalendarState {
  final String message;

  const CalendarError({required this.message});

  @override
  List<Object?> get props => [message];
}

class CalendarNoAccess extends CalendarState {
  final String message;

  const CalendarNoAccess({this.message = 'Недостаточно прав для просмотра'});

  @override
  List<Object?> get props => [message];
}
