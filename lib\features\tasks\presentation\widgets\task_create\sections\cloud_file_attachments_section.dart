import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb, kDebugMode, debugPrint;
// ignore: avoid_web_libraries_in_flutter
import 'dart:async'; // Для StreamSubscription
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_state.dart';
import 'package:file_saver/file_saver.dart';
import 'package:http/http.dart' as http;

import '../models.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/domain/entities/tariffs_and_storage_entity.dart';

// ------------------ Облачные файловые вложения ------------------
class CloudFileAttachmentsSection extends StatefulWidget {
  final List<FileAttachmentItem> attachments;
  final Function(FileAttachmentItem) onAddAttachment;
  final Function(FileAttachmentItem) onRemoveAttachment;
  final Function(FileAttachmentItem) onUpdateAttachment;
  final bool isEnabled; // Блокировка секции во время операций

  const CloudFileAttachmentsSection({
    super.key,
    required this.attachments,
    required this.onAddAttachment,
    required this.onRemoveAttachment,
    required this.onUpdateAttachment,
    this.isEnabled = true,
  });

  @override
  State<CloudFileAttachmentsSection> createState() =>
      _CloudFileAttachmentsSectionState();
}

class _CloudFileAttachmentsSectionState
    extends State<CloudFileAttachmentsSection> {
  final Set<String> _downloadingFiles = <String>{};

  @override
  Widget build(BuildContext context) {
    return BlocListener<TariffsAndStorageCubit, TariffsAndStorageState>(
      listener: _handleStorageStateChange,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Файловые вложения',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: widget.isEnabled ? _pickFiles : null,
                icon: const Icon(Icons.attach_file),
                label: const Text('Добавить файлы'),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Простая зона для файлов
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[50],
            ),
            child:
                widget.attachments.isEmpty
                    ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.cloud_upload,
                          size: 48,
                          color:
                              widget.isEnabled
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          widget.isEnabled
                              ? 'Нажмите "Добавить файлы" для выбора'
                              : 'Загрузка заблокирована...',
                          style: TextStyle(
                            color:
                                widget.isEnabled
                                    ? Colors.grey[600]
                                    : Colors.grey[400],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    )
                    : _buildAttachmentsList(),
          ),

          // Информация о хранилище
          BlocBuilder<TariffsAndStorageCubit, TariffsAndStorageState>(
            builder: (context, state) {
              if (state is TariffsAndStorageLoaded) {
                return _buildStorageInfo(state.data);
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentsList() {
    return Column(
      children:
          widget.attachments.asMap().entries.map((entry) {
            final attachment = entry.value;
            return _buildAttachmentTile(attachment);
          }).toList(),
    );
  }

  Widget _buildAttachmentTile(FileAttachmentItem attachment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: _buildStatusIcon(attachment),
        title: Text(
          attachment.name,
          style: TextStyle(
            color:
                attachment.status == FileAttachmentStatus.failed
                    ? Colors.red
                    : null,
          ),
        ),
        subtitle: _buildSubtitle(attachment),
        trailing: _buildTrailingActions(attachment),
      ),
    );
  }

  Widget _buildStatusIcon(FileAttachmentItem attachment) {
    switch (attachment.status) {
      case FileAttachmentStatus.pending:
        return const Icon(Icons.schedule, color: Colors.orange);
      case FileAttachmentStatus.uploading:
        return SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            value: attachment.uploadProgress,
            strokeWidth: 2,
          ),
        );
      case FileAttachmentStatus.uploaded:
        return const Icon(Icons.check_circle, color: Colors.green);
      case FileAttachmentStatus.failed:
        return const Icon(Icons.error, color: Colors.red);
      case FileAttachmentStatus.deleting:
        return const SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
    }
  }

  Widget _buildSubtitle(FileAttachmentItem attachment) {
    switch (attachment.status) {
      case FileAttachmentStatus.pending:
        return Text('Ожидает загрузки ${_formatFileSize(attachment.fileSize)}');
      case FileAttachmentStatus.uploading:
        return Text(
          'Загружается ${(attachment.uploadProgress * 100).toStringAsFixed(1)}%',
        );
      case FileAttachmentStatus.uploaded:
        return Text('Загружен ${_formatFileSize(attachment.fileSize)}');
      case FileAttachmentStatus.failed:
        return Text(
          'Ошибка: ${attachment.errorMessage ?? "Неизвестная ошибка"}',
        );
      case FileAttachmentStatus.deleting:
        return const Text('Удаляется...');
    }
  }

  Widget _buildTrailingActions(FileAttachmentItem attachment) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (attachment.status == FileAttachmentStatus.failed)
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _retryUpload(attachment),
            tooltip: 'Повторить загрузку',
          ),
        if (attachment.status == FileAttachmentStatus.uploaded)
          _downloadingFiles.contains(attachment.fileKey)
              ? Container(
                width: 24,
                height: 24,
                padding: const EdgeInsets.all(6),
                child: CircularProgressIndicator(
                  strokeWidth: 1.5,
                  color: Theme.of(context).colorScheme.primary,
                ),
              )
              : IconButton(
                icon: const Icon(Icons.download),
                onPressed: () => _downloadFile(attachment),
                tooltip: 'Скачать файл',
              ),
        if (attachment.status != FileAttachmentStatus.deleting)
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed:
                widget.isEnabled ? () => _deleteAttachment(attachment) : null,
            tooltip: 'Удалить файл',
          ),
      ],
    );
  }

  Widget _buildStorageInfo(TariffsAndStorageEntity data) {
    final quotaBytes = data.subscriptionInfo.quotaBytes;
    final usedBytes = data.storageInfo.usedBytes;
    final usagePercent = quotaBytes > 0 ? (usedBytes / quotaBytes * 100) : 0;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.storage, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                'Хранилище: ${_formatFileSize(usedBytes)} / ${_formatFileSize(quotaBytes)}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: usagePercent / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              usagePercent > 90
                  ? Colors.red
                  : usagePercent > 75
                  ? Colors.orange
                  : Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  void _handleStorageStateChange(
    BuildContext context,
    TariffsAndStorageState state,
  ) {
    if (state is FileUploadInProgress) {
      _updateAttachmentProgress(state.fileName, state.progress);
    } else if (state is FileUploadCompleted) {
      _completeUpload(state.fileName, state.fileKey);
    } else if (state is FileUploadFailed) {
      _failUpload(state.fileName ?? 'Неизвестный файл', state.message);
    } else if (state is FileDeleted) {
      _completeDelete(state.fileKey);
    } else if (state is TariffsAndStorageError) {
      // 1. Ошибка при загрузке
      final uploadingFile = widget.attachments.firstWhere(
        (f) => f.status == FileAttachmentStatus.uploading,
        orElse:
            () => widget.attachments.firstWhere(
              (f) => f.status == FileAttachmentStatus.pending,
              orElse:
                  () => FileAttachmentItem(
                    name: '',
                    fileSize: 0,
                    status: FileAttachmentStatus.pending,
                  ),
            ),
      );

      if (uploadingFile.name.isNotEmpty) {
        String errorMessage = state.message;
        if (state.message.contains('413') ||
            state.message.contains('Request Entity Too Large')) {
          errorMessage = 'Файл слишком большой для загрузки';
        } else if (state.message.contains('insufficient space')) {
          errorMessage = 'Недостаточно места в хранилище';
        }
        _failUpload(uploadingFile.name, errorMessage);
      }

      // 2. Ошибка при удалении
      final deletingFile = widget.attachments.firstWhere(
        (f) => f.status == FileAttachmentStatus.deleting,
        orElse:
            () => FileAttachmentItem(
              name: '',
              fileSize: 0,
              status: FileAttachmentStatus.deleting,
            ),
      );

      if (deletingFile.name.isNotEmpty) {
        final reverted = deletingFile.copyWith(
          status: FileAttachmentStatus.uploaded,
          errorMessage: state.message,
        );
        widget.onUpdateAttachment(reverted);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Ошибка хранилища: ${state.message}'),
          backgroundColor: Colors.red,
        ),
      );
    } else if (state is SpaceCheckCompleted && !state.spaceAvailable) {
      // Находим файл, для которого проверялось место, и помечаем как failed
      final pendingFile = widget.attachments.firstWhere(
        (f) => f.status == FileAttachmentStatus.pending,
        orElse:
            () => FileAttachmentItem(
              name: '',
              fileSize: 0,
              status: FileAttachmentStatus.pending,
            ),
      );

      if (pendingFile.name.isNotEmpty) {
        _failUpload(pendingFile.name, 'Недостаточно места в хранилище');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Недостаточно места в хранилище. Требуется: ${_formatFileSize(state.requestedFileSize)}',
          ),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _pickFiles() async {
    try {
      if (kDebugMode) {
        debugPrint('📁 [FileUpload] Начинаем выбор файлов...');
        debugPrint(
          '📁 [FileUpload] Platform: ${kIsWeb ? "Web" : "Desktop/Mobile"}',
        );
      }

      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.any,
        withData: true, // Важно для web
      );

      if (kDebugMode) {
        debugPrint(
          '📁 [FileUpload] Результат: ${result?.files.length ?? 0} файлов',
        );
      }

      if (result != null && result.files.isNotEmpty) {
        for (final file in result.files) {
          if (file.bytes != null) {
            final attachment = FileAttachmentItem(
              name: file.name,
              fileSize: file.size,
              status: FileAttachmentStatus.pending,
            );
            widget.onAddAttachment(attachment);
            _uploadFileWithData(attachment, file.bytes!);
          }
        }
      }
    } catch (e) {
      // Если file_picker не работает, показываем ошибку с инструкцией
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Ошибка выбора файлов: ${e.toString()}\n'
              'Попробуйте обновить страницу или использовать другой браузер.',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'ОК',
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    }
  }

  void _uploadFileWithData(FileAttachmentItem file, Uint8List fileData) async {
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) {
      _updateFileStatus(file, FileAttachmentStatus.failed, 'Фирма не выбрана');
      return;
    }

    try {
      context.read<TariffsAndStorageCubit>().uploadFile(
        firmId: firmState.selectedFirm!.id,
        fileName: file.name,
        fileBytes: fileData,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Ошибка загрузки файла: ${e.toString()}\n'
              'Попробуйте обновить страницу или использовать другой браузер.',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'ОК',
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    }
  }

  void _updateAttachmentProgress(String fileName, double progress) {
    final index = widget.attachments.indexWhere((a) => a.name == fileName);
    if (index != -1) {
      final updated = widget.attachments[index].copyWith(
        uploadProgress: progress,
        status: FileAttachmentStatus.uploading,
      );
      widget.onUpdateAttachment(updated);
    }
  }

  void _completeUpload(String fileName, String fileKey) {
    final index = widget.attachments.indexWhere((a) => a.name == fileName);
    if (index != -1) {
      final updated = widget.attachments[index].copyWith(
        fileKey: fileKey,
        status: FileAttachmentStatus.uploaded,
        uploadProgress: 1.0,
      );
      widget.onUpdateAttachment(updated);
    }
  }

  void _failUpload(String fileName, String error) {
    final index = widget.attachments.indexWhere((a) => a.name == fileName);
    if (index != -1) {
      final updated = widget.attachments[index].copyWith(
        status: FileAttachmentStatus.failed,
        errorMessage: error,
      );
      widget.onUpdateAttachment(updated);
    }
  }

  void _completeDelete(String fileKey) {
    final index = widget.attachments.indexWhere((a) => a.fileKey == fileKey);
    if (index != -1) {
      widget.onRemoveAttachment(widget.attachments[index]);
    }
  }

  Future<void> _retryUpload(FileAttachmentItem attachment) async {
    // Для retry нужно сохранить оригинальные байты файла
    // Это упрощённая версия - в реальности нужно хранить байты
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Для повтора загрузки выберите файл заново'),
      ),
    );
    widget.onRemoveAttachment(attachment);
  }

  Future<void> _downloadFile(FileAttachmentItem attachment) async {
    if (attachment.fileKey == null) return;

    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) return;

    // Устанавливаем состояние загрузки
    setState(() {
      _downloadingFiles.add(attachment.fileKey!);
    });

    final storageCubit = context.read<TariffsAndStorageCubit>();

    // Создаем подписку на состояния ПЕРЕД вызовом getDownloadUrl
    late StreamSubscription sub;

    try {
      // Устанавливаем подписку сначала
      sub = storageCubit.stream.listen((state) async {
        // Обрабатываем только события, связанные с нашим файлом
        if (state is FileDownloadUrlReady &&
            state.fileKey == attachment.fileKey) {
          // Успешно получили ссылку - скачиваем файл
          try {
            // Скачиваем данные файла по URL
            final response = await http.get(Uri.parse(state.downloadUrl));
            if (response.statusCode == 200) {
              // Сохраняем файл с помощью file_saver
              await FileSaver.instance.saveFile(
                name: attachment.name,
                bytes: response.bodyBytes,
              );
            } else {
              throw Exception('Ошибка загрузки файла: ${response.statusCode}');
            }
          } catch (e) {
            debugPrint('Ошибка скачивания файла: $e');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Ошибка скачивания файла: $e'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          }

          sub.cancel();
          if (mounted) {
            setState(() {
              _downloadingFiles.remove(attachment.fileKey!);
            });
          }
        } else if (state is TariffsAndStorageError) {
          // Для ошибок мы не можем точно знать, к какому файлу они относятся
          // Поэтому показываем ошибку только если наш файл находится в процессе загрузки
          if (_downloadingFiles.contains(attachment.fileKey)) {
            sub.cancel();
            if (mounted) {
              setState(() {
                _downloadingFiles.remove(attachment.fileKey!);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Ошибка скачивания: ${state.message}'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          }
        }
      });

      // Теперь вызываем запрос ПОСЛЕ установки подписки
      await storageCubit.getDownloadUrl(
        firmId: firmState.selectedFirm!.id,
        fileKey: attachment.fileKey!,
      );

      // Добавляем таймаут безопасности на случай, если состояние не придет
      Future.delayed(const Duration(seconds: 30), () {
        if (_downloadingFiles.contains(attachment.fileKey)) {
          sub.cancel();
          if (mounted) {
            setState(() {
              _downloadingFiles.remove(attachment.fileKey!);
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Превышено время ожидания скачивания'),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        }
      });
    } catch (e) {
      sub.cancel();
      if (mounted) {
        setState(() {
          _downloadingFiles.remove(attachment.fileKey!);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка запроса: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _deleteAttachment(FileAttachmentItem file) async {
    if (file.status == FileAttachmentStatus.deleting) return;

    // Если файл не был успешно загружен (failed, pending, uploading) или fileKey == null
    if (file.fileKey == null ||
        file.status == FileAttachmentStatus.failed ||
        file.status == FileAttachmentStatus.pending ||
        file.status == FileAttachmentStatus.uploading) {
      widget.onRemoveAttachment(file);
      return;
    }

    // Только для успешно загруженных файлов - удаляем с сервера
    if (file.status == FileAttachmentStatus.uploaded) {
      // Обновляем статус на "удаляется"
      final updated = file.copyWith(status: FileAttachmentStatus.deleting);
      widget.onUpdateAttachment(updated);

      final firmState = context.read<ActiveFirmCubit>().state;
      if (firmState.selectedFirm == null) {
        widget.onRemoveAttachment(file);
        return;
      }

      await context.read<TariffsAndStorageCubit>().deleteFile(
        firmId: firmState.selectedFirm!.id,
        fileKey: file.fileKey!,
      );
    }
  }

  String _formatFileSize(int? bytes) {
    if (bytes == null || bytes == 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    int unitIndex = 0;
    double size = bytes.toDouble();

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(unitIndex == 0 ? 0 : 1)} ${units[unitIndex]}';
  }

  void _updateFileStatus(
    FileAttachmentItem file,
    FileAttachmentStatus status,
    String errorMessage,
  ) {
    final updated = file.copyWith(status: status, errorMessage: errorMessage);
    widget.onUpdateAttachment(updated);
  }
}
