
```python
import json, logging, os
import ydb
from custom_errors import LogicError, AuthError, NotFoundError
import reminders_processor
# Импортируем хелперы из upsert.py, они нам все еще нужны
from upsert import _update_main_task_subtasks, _update_periodic_parent_children

def _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
    print(f"DELETE_PERMISSIONS: Checking modify permissions for task {task_id}, user {requesting_user_id}, admin/owner: {is_admin_or_owner}")
    if is_admin_or_owner: 
        print(f"DELETE_PERMISSIONS: User {requesting_user_id} has admin/owner access to task {task_id}")
        return True
    
    res = session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(f"DECLARE $task_id AS Utf8; SELECT creator_ids_json, assignee_ids_json, options_json FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    if not res[0].rows: 
        print(f"DELETE_PERMISSIONS: Task {task_id} not found when checking permissions")
        raise NotFoundError(f"Task with id {task_id} not found.")
    
    task_data = res[0].rows[0]
    creator_ids = json.loads(task_data.get('creator_ids_json', '[]') or '[]')
    assignee_ids = json.loads(task_data.get('assignee_ids_json', '[]') or '[]')
    options = json.loads(task_data.get('options_json', '{}') or '{}')
    
    print(f"DELETE_PERMISSIONS: Task {task_id} - creators: {len(creator_ids)}, assignees: {len(assignee_ids)}")
    
    if requesting_user_id in creator_ids: 
        print(f"DELETE_PERMISSIONS: User {requesting_user_id} is creator of task {task_id}")
        return True
    
    if requesting_user_id in assignee_ids:
        allow_assignee_edit = options.get('allow_assignee_to_edit', False)
        print(f"DELETE_PERMISSIONS: User {requesting_user_id} is assignee of task {task_id}, allow_assignee_to_edit: {allow_assignee_edit}")
        if allow_assignee_edit: 
            return True
    
    print(f"DELETE_PERMISSIONS: User {requesting_user_id} has no modify permissions for task {task_id}")
    return False

def delete_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner, user_jwt):
    print(f"DELETE_TASK: Starting deletion process for task_id: {task_id}, user: {requesting_user_id}")
    
    if not task_id: 
        print(f"DELETE_TASK: Error - task_id is required")
        raise LogicError("task_id is required for DELETE action.")
    
    print(f"DELETE_TASK: Checking deletion permissions for task {task_id}")
    if not _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner): 
        print(f"DELETE_TASK: Permission denied for user {requesting_user_id} to delete task {task_id}")
        raise AuthError("You do not have permission to delete this task.")
    print(f"DELETE_TASK: Permission granted for task {task_id} deletion")
    
    # Сначала всегда удаляем напоминания, эта логика не меняется.
    print(f"DELETE_TASK: Deleting reminders for task {task_id}")
    try:
        reminders_processor.sync_reminders(task_id, {"reminders_json": "[]"}, user_jwt)
        print(f"DELETE_TASK: Successfully deleted reminders for task {task_id}")
    except Exception as e:
        print(f"DELETE_TASK: Error deleting reminders for task {task_id}: {e}")
        logging.error(f"[{task_id}] Error deleting reminders before task removal: {e}", exc_info=True)

    tx = session.transaction(ydb.SerializableReadWrite())
    
    # Получаем информацию о задаче, включая recurrence_json для извлечения event_id
    print(f"DELETE_TASK: Fetching task data from database for task {task_id}")
    res = tx.execute(
        session.prepare(f"DECLARE $task_id AS Utf8; SELECT main_task_id, periodic_parent_id, periodic_children_json, recurrence_json FROM `{table_name}` WHERE task_id = $task_id;"), 
        {"$task_id": task_id}
    )

    if res[0].rows:
        task_data = res[0].rows[0]
        main_task_id = task_data.main_task_id
        periodic_parent_id = task_data.periodic_parent_id
        periodic_children_str = task_data.periodic_children_json
        recurrence_json_str = task_data.recurrence_json
        is_periodic_parent = periodic_children_str and json.loads(periodic_children_str)
        
        print(f"DELETE_TASK: Task data retrieved - main_task_id: {main_task_id}, periodic_parent_id: {periodic_parent_id}, is_periodic_parent: {bool(is_periodic_parent)}, has_recurrence: {bool(recurrence_json_str)}")

        # Если задача имеет recurrence_json, пытаемся удалить соответствующее событие планировщика
        if recurrence_json_str and recurrence_json_str != 'null':
            print(f"DELETE_TASK: Task {task_id} has recurrence_json, attempting to delete scheduler event")
            logging.info(f"[{task_id}] Task has recurrence_json, attempting to delete scheduler event.")
            
            try:
                recurrence_data = json.loads(recurrence_json_str)
                event_id = recurrence_data.get('event_id')
                
                if event_id:
                    print(f"DELETE_TASK: Found event_id {event_id} in recurrence_json for task {task_id}")
                    logging.info(f"[{task_id}] Found event_id {event_id} in recurrence_json, deleting scheduler event.")
                    
                    delete_event_payload = {
                        "action": "DELETE",
                        "event_id": event_id
                    }
                    
                    reminders_processor.invoke_utils.invoke_function(
                        function_id=os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER"),
                        payload=delete_event_payload,
                        user_jwt=user_jwt
                    )
                    print(f"DELETE_TASK: Successfully deleted scheduler event {event_id} for task {task_id}")
                    logging.info(f"[{task_id}] Successfully deleted scheduler event {event_id}.")
                else:
                    print(f"DELETE_TASK: No event_id found in recurrence_json for task {task_id}")
                    logging.warning(f"[{task_id}] No event_id found in recurrence_json, cannot delete scheduler event.")
                    
            except (json.JSONDecodeError, TypeError) as e:
                print(f"DELETE_TASK: Error parsing recurrence_json for task {task_id}: {e}")
                logging.error(f"[{task_id}] Error parsing recurrence_json: {e}")
            except Exception as e:
                print(f"DELETE_TASK: Error deleting scheduler event for task {task_id}: {e}")
                logging.error(f"[{task_id}] Failed to delete scheduler event, but continuing with task deletion. Error: {e}", exc_info=True)

        # Обновляем связи для подзадач и родительских периодических задач (если применимо)
        if main_task_id:
            print(f"DELETE_TASK: Removing task {task_id} from subtasks of main task {main_task_id}")
            _update_main_task_subtasks(tx, table_name, main_task_id, task_id, action='remove')
        
        if periodic_parent_id:
            print(f"DELETE_TASK: Removing task {task_id} from children of periodic parent {periodic_parent_id}")
            _update_periodic_parent_children(tx, table_name, periodic_parent_id, task_id, action='remove')

    # Удаляем саму задачу из базы данных задач
    print(f"DELETE_TASK: Deleting task {task_id} from database table {table_name}")
    tx.execute(session.prepare(f"DECLARE $task_id AS Utf8; DELETE FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    
    tx.commit()
    print(f"DELETE_TASK: Successfully deleted task {task_id} from database")
    print(f"DELETE_TASK: Task deletion operation completed successfully for task {task_id}")
    return {"statusCode": 200, "body": json.dumps({"message": "Task and all associated reminders deleted"})}
```