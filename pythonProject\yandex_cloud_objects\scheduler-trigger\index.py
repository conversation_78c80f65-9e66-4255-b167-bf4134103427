import json
import os
import logging
import datetime
import pytz
import ydb
import boto3

from utils import ydb_utils

logging.getLogger().setLevel(logging.DEBUG)


def _log_event_context(event, context):
    """Выводит в лог сырое событие и контекст вызова для отладки."""
    try:
        logging.debug("RAW EVENT: %s", json.dumps(event, default=str)[:10000])
    except Exception:
        logging.debug("RAW EVENT (non-json serialisable): %s", event)
    if context is not None:
        logging.debug(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s",
            getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None),
        )


def get_functions_client():
    """Инициализирует boto3 клиент для вызова других облачных функций."""
    return boto3.client(
        "lambda",
        region_name=os.environ['YC_REGION'],
        endpoint_url='https://functions.yandexcloud.net',  # <-- ДОБАВЛЕНА ЭТА СТРОКА
        aws_access_key_id=os.environ.get('STATIC_ACCESS_KEY_ID'),
        aws_secret_access_key=os.environ.get('STATIC_SECRET_ACCESS_KEY')
    )


def _to_datetime(value) -> datetime.datetime:
    """
    Безопасно конвертирует значение Timestamp из YDB (int или datetime)
    в timezone-aware datetime объект.
    """
    if isinstance(value, datetime.datetime):
        return value
    if isinstance(value, int):
        return datetime.datetime.fromtimestamp(value / 1_000_000, tz=pytz.utc)
    raise TypeError(f"Unsupported type for timestamp conversion: {type(value)}")


def handler(event, context):
    _log_event_context(event, context)

    now_utc = datetime.datetime.now(pytz.utc)
    logging.info(f"Scheduler trigger started at {now_utc.isoformat()}")

    processed_events_count = 0

    try:
        driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_SCHEDULER"],
            os.environ["YDB_DATABASE_SCHEDULER"]
        )
        pool = ydb.SessionPool(driver)
        functions_client = get_functions_client()

        def process_events_transaction(session):
            nonlocal processed_events_count
            tx = session.transaction(ydb.SerializableReadWrite())

            select_query_text = "SELECT * FROM `ScheduledEvents` WHERE is_active = true;"
            logging.debug("Executing YQL query: %s", select_query_text)

            select_query = session.prepare(select_query_text)
            res = tx.execute(select_query)

            if not res[0].rows:
                logging.info("No active scheduled events found in the database.")
                return

            events_to_process = res[0].rows
            logging.info(f"Found {len(events_to_process)} active events to check.")

            for event in events_to_process:
                logging.debug("--- Processing event_id: %s ---", event.event_id)
                try:
                    execution_dates = json.loads(event.execution_dates_json or '[]')
                    if not execution_dates:
                        continue

                    execution_dates.sort()

                    earliest_due_date = datetime.datetime.fromisoformat(execution_dates[0])
                    if earliest_due_date > now_utc:
                        continue

                    created_at_ts = _to_datetime(event.created_at)
                    updated_at_ts = _to_datetime(event.updated_at)
                    date_to_process = None
                    due_date_obj = None

                    for date_str in execution_dates:
                        due_date = datetime.datetime.fromisoformat(date_str)

                        is_due = due_date <= now_utc
                        is_unprocessed = (created_at_ts == updated_at_ts) or (updated_at_ts < due_date)

                        logging.debug("  - Checking date: %s. Is due: %s. Is unprocessed: %s.", date_str, is_due,
                                      is_unprocessed)

                        if is_due and is_unprocessed:
                            date_to_process = date_str
                            due_date_obj = due_date
                            logging.info("  - Found date to process: %s", date_to_process)
                            break

                    if not date_to_process:
                        continue

                    logging.info("Triggering event %s for due date %s...", event.event_id, date_to_process)

                    payload_to_send = json.dumps(event.request_body_json or {})
                    functions_client.invoke(
                        FunctionName=event.function_id,
                        InvocationType='Event',
                        Payload=payload_to_send.encode('utf-8')  # Теперь кодируется строка
                    )
                    processed_events_count += 1

                    execution_dates.remove(date_to_process)

                    if event.is_annual:
                        next_year_date = due_date_obj.replace(year=due_date_obj.year + 1)
                        execution_dates.append(next_year_date.isoformat())

                    new_is_active = not (not execution_dates and not event.is_annual)

                    update_query = session.prepare("""
                        DECLARE $event_id AS Utf8;
                        DECLARE $dates_json AS Json;
                        DECLARE $is_active AS Bool;
                        DECLARE $updated_at AS Timestamp;
                        UPDATE `ScheduledEvents` SET 
                            execution_dates_json = $dates_json,
                            is_active = $is_active,
                            updated_at = $updated_at
                        WHERE event_id = $event_id;
                    """)
                    tx.execute(update_query, {
                        "$event_id": event.event_id,
                        "$dates_json": json.dumps(execution_dates),
                        "$is_active": new_is_active,
                        "$updated_at": now_utc
                    })

                except Exception as e:
                    logging.error("Failed to process event %s due to data error: %s", event.event_id, e, exc_info=True)
                    continue

            tx.commit()

        pool.retry_operation_sync(process_events_transaction)

        final_message = f"Scheduler trigger finished. Processed {processed_events_count} events."
        logging.info(final_message)
        return {"statusCode": 200, "body": json.dumps({"message": final_message})}

    except Exception as e:
        logging.error("Critical error in scheduler trigger: %s", e, exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}