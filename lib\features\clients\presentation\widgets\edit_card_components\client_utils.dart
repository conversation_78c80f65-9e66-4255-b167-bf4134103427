import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';

/// Утилиты для работы с клиентами
class ClientUtils {
  /// Копирует текст в буфер обмена и показывает уведомление
  static void copyToClipboard(
    BuildContext context,
    String text,
    String fieldName,
  ) {
    if (text.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: text));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$fieldName скопировано в буфер обмена'),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  /// Парсит и форматирует поле даты при потере фокуса
  static void parseAndFormatDateField(
    FocusNode fn,
    TextEditingController ctrl,
    Function(DateTime?) updateModel,
  ) {
    if (!fn.hasFocus) {
      final text = ctrl.text.trim();
      if (text.isEmpty) {
        updateModel(null);
        return;
      }

      DateTime? parsedDate = tryParseDate(text);
      if (parsedDate != null) {
        ctrl.text = DateFormat('dd.MM.yyyy').format(parsedDate);
        updateModel(parsedDate);
      } else {
        // Если не удалось распарсить, оставляем как есть
        // Пользователь увидит ошибку валидации
      }
    }
  }

  /// Пытается распарсить дату из различных форматов
  static DateTime? tryParseDate(String input) {
    input = input.trim();

    // 1) Форматы без разделителей: ddMMyyyy или ddMMyy (6 или 8 цифр)
    if (RegExp(r'^\d{8} ?$').hasMatch(input)) {
      final day = int.parse(input.substring(0, 2));
      final month = int.parse(input.substring(2, 4));
      final year = int.parse(input.substring(4, 8));
      if (_isValidDayMonth(day, month)) {
        return DateTime(year, month, day);
      }
    }
    if (RegExp(r'^\d{6} ?$').hasMatch(input)) {
      final day = int.parse(input.substring(0, 2));
      final month = int.parse(input.substring(2, 4));
      final yr = int.parse(input.substring(4, 6));
      if (_isValidDayMonth(day, month)) {
        final correctedYear = yr < 50 ? 2000 + yr : 1900 + yr;
        return DateTime(correctedYear, month, day);
      }
    }

    // 2) Существующие форматы с разделителями
    final formats = [
      'dd.MM.yyyy',
      'dd/MM/yyyy',
      'dd-MM-yyyy',
      'dd.MM.yy',
      'dd/MM/yy',
      'dd-MM-yy',
      'dd.MM',
      'dd/MM',
      'dd-MM',
    ];

    // Сначала пробуем стандартные форматы
    for (final format in formats) {
      try {
        DateTime date = DateFormat(format).parseStrict(input);
        // Если год не указан, используем текущий
        if (format.contains('.MM') && !format.contains('y')) {
          date = DateTime(DateTime.now().year, date.month, date.day);
        }
        // Если указан двузначный год, корректируем его
        if (format.contains('yy') && !format.contains('yyyy')) {
          if (date.year < 50) {
            date = DateTime(date.year + 2000, date.month, date.day);
          } else {
            date = DateTime(date.year + 1900, date.month, date.day);
          }
        }
        return date;
      } catch (e) {
        // Продолжаем пробовать другие форматы
      }
    }

    // 3) Пробуем извлечь числа и собрать дату
    final numbers =
        RegExp(
          r'\d+',
        ).allMatches(input).map((m) => int.parse(m.group(0)!)).toList();
    if (numbers.length >= 2) {
      final day = numbers[0];
      final month = numbers[1];
      final year = numbers.length >= 3 ? numbers[2] : DateTime.now().year;

      // Корректируем двузначный год
      int correctedYear = year;
      if (year < 100) {
        correctedYear = year < 50 ? year + 2000 : year + 1900;
      } else if (year < 1000) {
        // 3-значный год, берем последние две цифры
        final yy = year % 100;
        correctedYear = yy < 50 ? 2000 + yy : 1900 + yy;
      }

      if (_isValidDayMonth(day, month)) {
        try {
          return DateTime(correctedYear, month, day);
        } catch (e) {
          // Неверная дата (например, 31 февраля)
        }
      }
    }

    return null;
  }

  /// Парсит и форматирует поле дат фиксированных взносов
  static void parseAndFormatFixedContributionsField(
    FocusNode fn,
    TextEditingController ctrl,
    List<String> fixedContributionsIP,
    Function(List<int>) updateModel,
  ) {
    if (!fn.hasFocus && fixedContributionsIP.isNotEmpty) {
      final text = ctrl.text.trim();
      if (text.isEmpty) {
        updateModel([]);
        return;
      }

      try {
        final parsed = ClientConstants.parsePaymentDates(
          text,
          fixedContributionsIP.first,
        );
        final formatted = ClientConstants.formatPaymentDates(parsed);
        ctrl.text = formatted;
        updateModel(parsed);
      } catch (e) {
        // Оставляем как есть, валидатор покажет ошибку
      }
    }
  }

  /// Парсит и форматирует поле дня (1-31) для графиков платежей
  static void parseAndFormatDayField(
    FocusNode fn,
    TextEditingController ctrl,
    Function(int) updateModel,
  ) {
    if (!fn.hasFocus) {
      final text = ctrl.text.trim();

      if (text.isEmpty) {
        return;
      }

      // Ищем первое число в тексте
      final match = RegExp(r'\d+').firstMatch(text);

      if (match != null) {
        final day = int.parse(match.group(0)!);

        // Проверяем диапазон от 1 до 31
        if (day >= 1 && day <= 31) {
          ctrl.text = '$day число';
          updateModel(day);
        } else {
          // Если число вне диапазона, оставляем как есть для валидации
        }
      } else {
        // Если не найдены цифры, оставляем как есть для валидации
      }
    }
  }

  /// Проверяет валидность дня и месяца
  static bool _isValidDayMonth(int day, int month) =>
      day >= 1 && day <= 31 && month >= 1 && month <= 12;

  /// Проверяет валидность дня для графика платежей
  static bool isValidDay(int? day) => day != null && day >= 1 && day <= 31;
}
