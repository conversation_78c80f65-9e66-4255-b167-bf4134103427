
```python
import json
import datetime
import ydb
from custom_errors import <PERSON>Error

def get_payments(session, table_name, year, client_id=None):
    if not isinstance(year, int):
        raise LogicError("`year` parameter must be an integer.")

    start_date_obj = datetime.date(year, 1, 1)
    end_date_obj = datetime.date(year, 12, 31)

    params = {"$start_date": start_date_obj, "$end_date": end_date_obj}
    declare_clauses = ["DECLARE $start_date AS Date;", "DECLARE $end_date AS Date;"]
    where_clauses = ["period_start_date >= $start_date", "period_start_date <= $end_date"]

    if client_id:
        params["$client_id"] = client_id
        declare_clauses.append("DECLARE $client_id AS Utf8;")
        where_clauses.append("client_id = $client_id")

    query_text = f"""
        {" ".join(declare_clauses)}
        SELECT client_id, period_start_date, actual_amount_kopeks, tariff_annual_amount_kopeks, actual_payment_date, created_at, updated_at FROM `{table_name}`
        WHERE {" AND ".join(where_clauses)}
        ORDER BY period_start_date ASC;
    """
    
    tx = session.transaction(ydb.SerializableReadWrite())
    res = tx.execute(session.prepare(query_text), params)
    tx.commit()
    
    data = []
    for row in res[0].rows:
        period_val = row.period_start_date
        period_str = ""
        
        # ИСПРАВЛЕНИЕ: Проверяем тип данных, полученных из YDB
        if isinstance(period_val, int):
            # Если это int, считаем его количеством дней с эпохи Unix
            period_date = datetime.date(1970, 1, 1) + datetime.timedelta(days=period_val)
            period_str = period_date.strftime('%Y-%m')
        elif isinstance(period_val, datetime.date):
            # Если это уже объект date, просто форматируем его
            period_str = period_val.strftime('%Y-%m')
        else:
            # Запасной вариант на случай другого формата
            period_str = str(period_val)

        actual_date_str = None
        if row.actual_payment_date is not None:
            if isinstance(row.actual_payment_date, int):
                actual_date = datetime.date(1970, 1, 1) + datetime.timedelta(days=row.actual_payment_date)
                actual_date_str = actual_date.strftime('%Y-%m-%d')
            elif isinstance(row.actual_payment_date, datetime.date):
                actual_date_str = row.actual_payment_date.strftime('%Y-%m-%d')
            else:
                actual_date_str = str(row.actual_payment_date)

        item = {
            "client_id": row.client_id,
            "period": period_str,
            "actual_amount_paid": (float(row.actual_amount_kopeks) / 100.0) if row.actual_amount_kopeks is not None else None,
            "tariff_annual_amount": (float(row.tariff_annual_amount_kopeks) / 100.0) if row.tariff_annual_amount_kopeks is not None else None,
            "actual_payment_date": actual_date_str,
            "created_at": str(row.created_at),
            "updated_at": str(row.updated_at),
        }
        data.append(item)

    return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}
```