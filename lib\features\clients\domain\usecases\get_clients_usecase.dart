import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/clients_repository.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

class GetClientsUseCase {
  final IClientsRepository repository;

  GetClientsUseCase(this.repository);

  Future<Either<Failure, List<ClientEntity>>> call(String firmId, {bool onlyActual = false}) async {
    NetworkLogger.printInfo(
      'GetClientsUseCase: Starting call for firmId: $firmId',
    );

    try {
      final result = await repository.getClients(firmId, onlyActual: onlyActual);

      return result.fold(
        (failure) {
          NetworkLogger.printError(
            'GetClientsUseCase: Repository call failed:',
            failure.message,
          );
          return Left(failure);
        },
        (clients) {
          NetworkLogger.printSuccess(
            'GetClientsUseCase: Successfully retrieved ${clients.length} clients',
          );
          return Right(clients);
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'GetClientsUseCase: Unexpected error:',
        e,
        stackTrace,
      );
      return Left(
        UnexpectedFailure(
          message: 'Неожиданная ошибка при получении клиентов: $e',
        ),
      );
    }
  }
}
