/// Модель для файловых вложений в облачном хранилище
class FileAttachmentItem {
  final String name;
  final String? fileKey; // Ключ файла в S3 после успешной загрузки
  final int? fileSize; // Размер файла в байтах
  final FileAttachmentStatus status;
  final double uploadProgress; // Прогресс загрузки от 0.0 до 1.0
  final String? errorMessage;

  FileAttachmentItem({
    required this.name,
    this.fileKey,
    this.fileSize,
    this.status = FileAttachmentStatus.pending,
    this.uploadProgress = 0.0,
    this.errorMessage,
  });

  FileAttachmentItem copyWith({
    String? name,
    String? fileKey,
    int? fileSize,
    FileAttachmentStatus? status,
    double? uploadProgress,
    String? errorMessage,
  }) {
    return FileAttachmentItem(
      name: name ?? this.name,
      fileKey: fileKey ?? this.fileKey,
      fileSize: fileSize ?? this.fileSize,
      status: status ?? this.status,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Преобразует в Map для использования в API
  Map<String, dynamic> toMap() {
    return {'name': name, 'fileKey': fileKey, 'fileSize': fileSize};
  }

  /// Создает из Map данных
  factory FileAttachmentItem.fromMap(Map<String, dynamic> map) {
    return FileAttachmentItem(
      name: map['name'] ?? '',
      fileKey: map['fileKey'],
      fileSize: map['fileSize'],
      status:
          FileAttachmentStatus.uploaded, // По умолчанию для загруженных файлов
    );
  }

  @override
  String toString() {
    return 'FileAttachmentItem(name: $name, fileKey: $fileKey, fileSize: $fileSize, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FileAttachmentItem &&
        other.name == name &&
        other.fileKey == fileKey &&
        other.fileSize == fileSize &&
        other.status == status;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        fileKey.hashCode ^
        fileSize.hashCode ^
        status.hashCode;
  }
}

/// Статусы файловых вложений
enum FileAttachmentStatus {
  pending, // Ожидает загрузки
  uploading, // Загружается
  uploaded, // Успешно загружен
  failed, // Ошибка загрузки
  deleting, // Удаляется
}

extension FileAttachmentStatusExtension on FileAttachmentStatus {
  /// Возвращает человекочитаемое название статуса
  String get displayName {
    switch (this) {
      case FileAttachmentStatus.pending:
        return 'Ожидает загрузки';
      case FileAttachmentStatus.uploading:
        return 'Загружается';
      case FileAttachmentStatus.uploaded:
        return 'Загружен';
      case FileAttachmentStatus.failed:
        return 'Ошибка';
      case FileAttachmentStatus.deleting:
        return 'Удаляется';
    }
  }

  /// Проверяет, завершён ли процесс
  bool get isCompleted {
    return this == FileAttachmentStatus.uploaded ||
        this == FileAttachmentStatus.failed;
  }

  /// Проверяет, идёт ли процесс
  bool get isInProgress {
    return this == FileAttachmentStatus.uploading ||
        this == FileAttachmentStatus.deleting;
  }
}
