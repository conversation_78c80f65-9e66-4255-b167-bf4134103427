
Идентификатор - etnrvbh5fcgs3fuiqfdg
Путь к базе - /ru-central1/b1g2bgg0i9r8beucbthc/etnrvbh5fcgs3fuiqfdg
Эндпоинт - grpcs://ydb.serverless.yandexcloud.net:2135/?database=/ru-central1/b1g2bgg0i9r8beucbthc/etnrvbh5fcgs3fuiqfdg

---
# Таблицы
В данной базе данных хранятся записи об оплате услуг клиентами для каждой обслуживающей компании. Для обеспечения изоляции данных и масштабируемости используется подход "одна таблица на одну компанию".

#### Таблица-шаблон: `client_payments_{firm_id}`
Имя таблицы формируется путем добавления `firm_id` к префиксу `client_payments_`. Облачная функция будет автоматически создавать эту таблицу при первой записи платежа для новой фирмы.

| #   | Имя                           | Ключ | Тип                | Описание                                                                                                                                                                                                                                                                                                                                                                                                                          |
| --- | ----------------------------- | ---- | ------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 0   | `client_id`                   | PK   | `Utf8`             | Уникальный идентификатор клиента фирмы (UUID). Связан с полем `client_id` в таблицах `clients_{firm_id}`. Часть составного первичного ключа.                                                                                                                                                                                                                                                                                   |
| 1   | `period_start_date`           | PK   | `Date`             | Дата начала учетного периода (месяца), к которому относятся платеж и тариф. Например, для января 2025 года это будет `2025-01-01`. Часть составного первичного ключа.                                                                                                                                                                                                                                                           |
| 2   | `actual_amount_kopeks`        |      | `Optional<Int64>`  | Фактическая сумма оплаты в копейках, полученная от клиента за данный учетный период. Может быть `NULL`, если оплата еще не поступала.                                                                                                                                                                                                                                                                                            |
| 3   | `tariff_annual_amount_kopeks` |      | `Optional<Int64>`  | МЕСЯЧНАЯ сумма тарифа в копейках, установленная для данного клиента, начиная с этого учетного периода. Например, если с января 2025 года тариф установлен в 70 000 рублей в месяц, то это значение будет храниться для `period_start_date = '2025-01-01'`. Если в октябре 2025 года тариф изменится на 80 000 рублей, то появится новая запись с `period_start_date = '2025-10-01'` и новым значением тарифа. Может быть `NULL`. |
| 4   | `actual_payment_date`         |      | `Optional<Date>`   | Дата фактической оплаты в пределах учетного периода. Может быть `NULL`.                                                                                                                                                                                                                                                                                                                                                           |
| 5   | `created_at`                  |      | `Timestamp`        | Системное время создания записи об оплате/тарифе.                                                                                                                                                                                                                                                                                                                                                                         |
| 6   | `updated_at`                  |      | `Timestamp`        | Системное время последнего обновления записи.                                                                                                                                                                                                                                                                                                                                                                             |

---
#### YQL для управления таблицами

Эти запросы демонстрируют, как создавать и удалять таблицы платежей вручную. В рабочей системе создание таблицы будет автоматически выполняться облачной функцией при первом обращении к таблице для новой фирмы.

**YQL для создания таблицы (шаблон)**
*Замените `{firm_id}` на реальный идентификатор фирмы.*
```yql
CREATE TABLE `client_payments_{firm_id}` (
    client_id Utf8,
    period_start_date Date,
    actual_amount_kopeks Optional<Int64>,
    tariff_annual_amount_kopeks Optional<Int64>,
    actual_payment_date Optional<Date>,
    created_at Timestamp,
    updated_at Timestamp,
    PRIMARY KEY (client_id, period_start_date)
);
```

**YQL для удаления таблицы (шаблон)**
*Замените `{firm_id}` на реальный идентификатор фирмы.*
```yql
DROP TABLE `client_payments_{firm_id}`;
```
