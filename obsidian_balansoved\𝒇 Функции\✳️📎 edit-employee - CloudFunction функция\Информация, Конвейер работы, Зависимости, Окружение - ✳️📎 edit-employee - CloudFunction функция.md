
Идентификатор - d4eim418vpibjvph5ujv
Описание - 🪪 Редактировать сотрудника, управлять ролями и получать информацию
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: <PERSON>о<PERSON><PERSON>н администратора или владельца.
	-> Тело запроса:
		- `firm_id`: **(Обязательно)** ID фирмы, в которой происходит действие.
		- `action`: **(Обязательно)** Одно из следующих значений:
			- `"GET_INFO"`: Получить информацию.
			- `"ADD_ROLE"`: Добавить роль сотруднику.
			- `"REMOVE_ROLE"`: Удалить роль у сотрудника.
		- `user_id_to_edit`: (Необязательно для `GET_INFO`) ID пользователя. Если не указан при `GET_INFO`, вернется список всех сотрудников фирмы.
		- `role`: **(Обязательно для ADD/REMOVE_ROLE)** Строка с названием роли (например, `"ADMIN"`).
Внутренняя работа:
	-> Логирование: Установка уровня логирования на INFO.
	-> Авторизация:
		-> Проверка наличия и формата заголовка Authorization.
		-> Верификация JWT токена и извлечение admin_user_id.
	-> Парсинг запроса:
		-> Безопасное получение и парсинг тела запроса с помощью request_parser.
	-> Валидация:
		-> Проверка наличия firm_id и action.
		-> Для ADD_ROLE/REMOVE_ROLE: Проверка наличия user_id_to_edit и role, валидация role в EDITABLE_ROLES, проверка что admin_user_id != user_id_to_edit.
	-> Подключение к firms-database: Получение драйвера и пула сессий.
	-> Обработка действия:
		-> Если action == "GET_INFO":
			-> Транзакция: Если user_id_to_edit указан, запрос на конкретного пользователя; иначе на всех в фирме.
			-> Возврат данных пользователя(ей).
		-> Если action in ["ADD_ROLE", "REMOVE_ROLE"]:
			-> Транзакция: Получение ролей admin и target.
			-> Проверки: Наличие обоих, target не OWNER, admin_score > target_score.
			-> Обновление ролей: Добавление/удаление роли, обновление в БД.
	-> Обработка исключений: AuthError (403), LogicError (400), NotFoundError (404), другие (500) с логированием.
На выходе:
	-> `200 OK` (для `GET_INFO` с `user_id_to_edit`): `{"data": {"user_id": ..., "roles": [...]}}`
	-> `200 OK` (для `GET_INFO` без `user_id_to_edit`): `{"data": [{"user_id": ...}, {"user_id": ...}]}`
	-> `200 OK` (для `ADD/REMOVE_ROLE`): `{"message": "Role '...' successfully added/removed."}`
	-> `400 Bad Request`: Ошибки валидации, некорректная роль, попытка саморедактирования, роль уже/не существует.
	-> `403 Forbidden`: Недостаточно прав, нарушение иерархии, редактирование OWNER.
	-> `404 Not Found`: Пользователь или администратор не найдены в указанной фирме.
	-> `500 Internal Server Error`: Внутренняя ошибка сервера.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`