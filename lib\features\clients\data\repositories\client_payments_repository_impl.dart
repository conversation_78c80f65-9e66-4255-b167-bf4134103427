import 'package:dartz/dartz.dart';

import '../../../../core/error/exception.dart';
import '../../../../core/error/failure.dart';
import '../../../auth/data/data_source/auth_local_data_source.dart';
import '../../domain/entities/client_payment_entity.dart';
import '../../domain/repositories/client_payments_repository.dart';
import '../data_source/client_payments_remote_data_source.dart';

class ClientPaymentsRepositoryImpl implements IClientPaymentsRepository {
  final IClientPaymentsRemoteDataSource remoteDataSource;
  final IAuthLocalDataSource localAuth;

  ClientPaymentsRepositoryImpl({
    required this.remoteDataSource,
    required this.localAuth,
  });

  @override
  Future<Either<Failure, List<ClientPaymentEntity>>> getPayments(
    String firmId,
    int year, {
    String? clientId,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      final paymentsList = await remoteDataSource.fetchPayments(
        token,
        firmId,
        year,
        clientId: clientId,
      );
      return Right(paymentsList);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, details: e.responseBody));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(
        UnexpectedFailure(message: 'Не удалось получить платежи: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> upsertPayment({
    required String firmId,
    required String clientId,
    required DateTime period,
    required double? actualAmount,
    required double? tariffAmount,
    DateTime? paymentDate,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      await remoteDataSource.upsertPayment(
        token,
        firmId,
        clientId,
        period,
        actualAmount,
        tariffAmount,
        paymentDate,
      );
      return const Right(unit);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, details: e.responseBody));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(
        UnexpectedFailure(message: 'Не удалось сохранить платеж: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> deletePayment({
    required String firmId,
    required String clientId,
    required DateTime period,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      await remoteDataSource.deletePayment(token, firmId, clientId, period);
      return const Right(unit);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, details: e.responseBody));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnexpectedFailure(message: 'Не удалось удалить платеж: $e'));
    }
  }
}
