
Идентификатор - d4e4eh8qs34ce11rmgte
Описание - 𐀪 **Физически удалить** сотрудника из конкретной фирмы с проверкой прав и иерархии
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен администратора или владельца.
	-> Тело запроса:
		- `firm_id`: **(Обязательно)** ID фирмы, в которой происходит действие.
		- `user_id_to_delete`: ID пользователя, которого нужно удалить.
Внутренняя работа:
	-> Логирование: Установка уровня логирования на INFO.
	-> Авторизация:
		-> Проверка наличия и формата заголовка Authorization.
		-> Верификация JWT токена и извлечение admin_user_id.
	-> Парсинг запроса:
		-> Извлечение firm_id и user_id_to_delete из тела запроса с помощью request_parser.
	-> Валидация:
		-> Проверка наличия firm_id и user_id_to_delete.
		-> Проверка, что admin_user_id != user_id_to_delete (нельзя удалять самого себя).
	-> Подключение к firms-database: Получение драйвера и пула сессий.
	-> Транзакция в firms-database:
		-> Запрос для получения ролей admin и target пользователя в указанной firm_id.
		-> Проверка наличия обоих пользователей в фирме.
		-> Проверка, что target не имеет роли "OWNER".
		-> Расчет наивысших баллов ролей для admin и target с использованием ROLE_HIERARCHY.
		-> Проверка, что балл admin > балл target.
		-> Удаление записи из таблицы Users по user_id_to_delete и firm_id.
	-> Обработка исключений: AuthError (403), LogicError (400), NotFoundError (404), другие (500) с логированием.
На выходе:
	-> `200 OK`: {"message": "Employee successfully deleted."}
	-> `400 Bad Request`: Отсутствуют обязательные поля, ошибка парсинга или попытка удалить самого себя.
	-> `403 Forbidden`: Недостаточно прав (роль не выше, или target - OWNER).
	-> `404 Not Found`: Администратор или target не найдены в фирме.
	-> `500 Internal Server Error`: Внутренняя ошибка сервера.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`