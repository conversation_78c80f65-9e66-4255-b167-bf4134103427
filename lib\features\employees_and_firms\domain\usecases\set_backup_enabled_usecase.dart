import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/backup_integration_entity.dart';
import '../repositories/integrations_repository.dart';

class SetBackupEnabledUseCase {
  final IntegrationsRepository repository;
  const SetBackupEnabledUseCase(this.repository);

  Future<Either<Failure, BackupIntegrationEntity>> call({
    required String firmId,
    required bool enabled,
  }) {
    return repository.setBackupEnabled(firmId: firmId, enabled: enabled);
  }
}
