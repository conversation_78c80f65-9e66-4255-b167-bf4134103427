
Идентификатор - d4er76v5l270502p7qu2
Описание - Запросить код для регистрации или создать пользователя в тестовом режиме.
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `email` (**обязательно**): Email пользователя.
	-> `password` (**обязательно**): Пароль пользователя.
	-> `user_name` (string, **обязательно только при первой регистрации**): Имя пользователя. При повторной отправке кода не требуется.

Внутренняя работа:
	-> Парсинг тела запроса для извлечения `email`, `password` и `user_name`.
	-> Проверка наличия `email` и `password`; если отсутствуют, возврат 400.
	-> Определение режима: `AUTO_CONFIRM_MODE` = true или false.
	-> Получение драйверов YDB для `jwt-database` и `firms-database`.
	-> Транзакционная обработка в `jwt-database`:
		-> Проверка существования пользователя по `email`.
		-> Если пользователь существует и активен, возврат 409.
		-> Если существует, но не активен:
			-> Проверка времени с предыдущей генерации кода.
			-> Если прошло >= 3 мин, генерация нового кода, обновление и отправка email (в стандартном режиме).
			-> Если < 3 мин, продление срока кода до 1 года.
		-> Если пользователь не существует:
			-> Проверка наличия приглашений в `firms-database` для повторного использования `user_id`.
			-> Генерация `user_id` (из приглашения или новый), хэширование пароля.
			-> В тестовом режиме: создание активного пользователя, обновление приглашений в `firms-database`, генерация JWT.
			-> В стандартном режиме: создание неактивного пользователя с кодом, отправка email.
	-> Обработка ошибок: возврат 500 при сбоях, например, отправки email.

На выходе:
	-> `201 Created`: {"token": "<jwt_token>"} (Только в тестовом режиме)
	-> `200 OK`: {"message": "Verification code sent."} (В стандартном режиме)
	-> `409 Conflict`: {"message": "User with this email already exists."} (активный пользователь)
	-> `423 Locked`: {"message": "Account not confirmed. Please verify your email."}
	-> `400 Bad Request`: При невалидных данных.
	-> `500 Internal Server Error`: В случае критических ошибок, например, при сбое отправки email.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/email_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
    - `YDB_ENDPOINT`, `YDB_DATABASE` ([[💾 jwt-database - База данных YandexDatabase]])
    - `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
    - SA_KEY_FILE ([[ydb_sa_key.json]])
    - JWT_SECRET
    - `AUTO_CONFIRM_MODE` - `true` или `false`.
    - `UNISENDER_API_KEY` - API ключ от сервиса Unisender.
    - `UNISENDER_SENDER_EMAIL` - Email отправителя, подтвержденный в Unisender.
    - `UNISENDER_SENDER_NAME` - Имя отправителя.
    - `UNISENDER_LIST_ID` - ID списка контактов в Unisender.