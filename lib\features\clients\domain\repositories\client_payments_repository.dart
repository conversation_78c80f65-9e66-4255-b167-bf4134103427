import 'package:balansoved_enterprise/features/clients/domain/entities/client_payment_entity.dart';
import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';

abstract class IClientPaymentsRepository {
  Future<Either<Failure, List<ClientPaymentEntity>>> getPayments(
    String firmId,
    int year, {
    String? clientId,
  });

  Future<Either<Failure, Unit>> upsertPayment({
    required String firmId,
    required String clientId,
    required DateTime period,
    required double? actualAmount,
    required double? tariffAmount,
    DateTime? paymentDate,
  });

  Future<Either<Failure, Unit>> deletePayment({
    required String firmId,
    required String clientId,
    required DateTime period,
  });
}
