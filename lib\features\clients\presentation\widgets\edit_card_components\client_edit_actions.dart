// lib/features/clients/presentation/widgets/edit_card_components/client_edit_actions.dart
import 'package:flutter/material.dart';

class ClientEditActions extends StatelessWidget {
  final bool isEditing;
  final bool hasActiveFileOperations;
  final VoidCallback onCancelEdit;
  final VoidCallback onSave;
  final VoidCallback onClose;
  final VoidCallback onEdit;

  const ClientEditActions({
    super.key,
    required this.isEditing,
    required this.hasActiveFileOperations,
    required this.onCancelEdit,
    required this.onSave,
    required this.onClose,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (isEditing) ...[
          TextButton(
            onPressed: hasActiveFileOperations ? null : onCancelEdit,
            child: const Text('Отмена'),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: hasActiveFileOperations ? null : onSave,
            child: const Text('Сохранить'),
          ),
        ] else ...[
          TextButton(onPressed: onClose, child: const Text('Закрыть')),
          const SizedBox(width: 8),
          ElevatedButton(onPressed: onEdit, child: const Text('Редактировать')),
        ],
      ],
    );
  }
}
