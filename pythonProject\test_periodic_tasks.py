import requests
import json
import sys
import time
from datetime import datetime, timedelta, timezone
from colorama import init, Fore, Style

# Инициализируем colorama
init(autoreset=True)

# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net"
API_FIRMS_URL = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net/get-user-data"
API_TASKS_URL = "https://d5d7ct0sul274igh4vij.aqkd4clz.apigw.yandexcloud.net/manage"
API_SCHEDULER_URL = "https://d5dln4usaaktunc0kvia.8wihnuyr.apigw.yandexcloud.net/trigger"

LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# Символы для статуса
TICK = Fore.GREEN + "✓"
CROSS = Fore.RED + "✗"
INFO = Fore.CYAN + "→"
WARN = Fore.YELLOW + "!"


def confirm_step(prompt):
    """Запрашивает подтверждение у пользователя для продолжения."""
    while True:
        choice = input(f"{WARN} {prompt} [Y/n]: ").lower().strip()
        if choice in ['y', 'yes', '']:
            return True
        if choice in ['n', 'no']:
            print(f"{INFO} Шаг пропущен по команде пользователя. Прерывание теста.")
            sys.exit(1)


# ИЗМЕНЕНИЕ: Улучшенная обработка ошибок и логирование
def api_call(method: str, url: str, payload: dict = None, headers: dict = None, expected_status: int = 200):
    """Универсальная функция для выполнения API-запросов с обработкой ошибок."""
    try:
        response = requests.request(method, url, json=payload, headers=headers or DEFAULT_HEADERS, timeout=20)
        if response.status_code == expected_status:
            return response  # Успех, возвращаем объект ответа
        else:
            # Ошибка несоответствия статуса
            print(f"\n{CROSS} Ошибка API: Ожидался статус {expected_status}, получен {response.status_code}")
            print(f"{INFO} URL: {method} {url}")
            if payload:
                print(f"{INFO} Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            try:
                print(f"{WARN} Ответ сервера: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError:
                print(f"{WARN} Ответ сервера (не JSON): {response.text}")
            return None  # Возвращаем None при ошибке
    except requests.exceptions.RequestException as e:
        # Сетевая или другая ошибка requests
        print(f"\n{CROSS} Сетевая ошибка: {e}")
        print(f"{INFO} URL: {method} {url}")
        if payload:
            print(f"{INFO} Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        return None  # Возвращаем None при ошибке


def run_test_step(title, func, *args, **kwargs):
    """Обертка для запуска шага теста с выводом статуса."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ", flush=True)
    result = func(*args, **kwargs)
    # ИЗМЕНЕНИЕ: Проверяем на истинность. Объект Response - True, None - False.
    if result:
        print(TICK)
        return result
    else:
        print(f"\n{CROSS} Критическая ошибка на шаге '{title}'. Тестирование прервано.")
        sys.exit(1)


def check_assertion(title, success_condition, error_message=""):
    """Обертка для проверки условия с выводом статуса."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ", flush=True)
    if success_condition:
        print(TICK)
    else:
        print(CROSS)
        sys.exit(f"\n{CROSS} Проверка провалена: '{title}'. {error_message}")


def create_new_periodic_task(firm_id, headers):
    """Создает новую периодическую задачу."""
    print(f"\n{INFO} Создание новой периодической задачи...")

    future_time = datetime.now(timezone.utc) + timedelta(seconds=10)
    dates_for_execution = [future_time.strftime('%Y-%m-%dT%H:%M:%SZ')]
    print(f"{INFO} Задача будет запущена примерно в: {future_time.isoformat()}")

    task_title = f"Тестовая периодическая задача от {datetime.now().strftime('%H:%M:%S')}"

    create_payload = {
        "firm_id": firm_id,
        "action": "UPSERT",
        "payload": {
            "title": task_title,
            "description": "Эта задача должна создаваться по расписанию.",
            "status": "new",
            "recurrence_json": json.dumps({
                "execution_dates_json": dates_for_execution,
                "is_annual": False
            })
        }
    }

    resp = api_call("POST", API_TASKS_URL, payload=create_payload, headers=headers, expected_status=201)
    task_id = resp.json().get("task_id") if resp else None
    if task_id:
        print(f"{Fore.GREEN}{Style.BRIGHT}Создана новая родительская задача с ID: {task_id}{Style.RESET_ALL}")
    return task_id


def get_existing_periodic_tasks(firm_id, headers):
    """Получает все задачи и фильтрует те, у которых есть правила повторения."""
    all_tasks_payload = {"firm_id": firm_id, "action": "GET"}
    resp = api_call("POST", API_TASKS_URL, payload=all_tasks_payload, headers=headers)
    if not resp:
        return []

    all_tasks = resp.json().get("data", [])
    periodic_tasks = [task for task in all_tasks if task.get("recurrence_json")]
    return periodic_tasks


# --- ОСНОВНОЙ СКРИПТ ---

if __name__ == "__main__":
    print("\n--- Начало интерактивного теста периодических задач ---\n")


    # --- Шаг 1: Авторизация ---
    def login():
        resp = api_call("POST", f"{API_AUTH_URL}/login", payload=LOGIN_PAYLOAD, expected_status=200)
        return resp.json().get("token") if resp else None


    jwt_token = run_test_step("Шаг 1: Авторизация и получение JWT", login)
    auth_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {jwt_token}"}


    # --- Шаг 2: Выбор фирмы ---
    def select_firm():
        resp = api_call("GET", API_FIRMS_URL, headers=auth_headers, expected_status=200)
        if not resp: return None

        firms = resp.json().get("firms", [])
        if not firms:
            print(f"\n{CROSS} У пользователя нет доступных фирм.")
            return None

        print(f"\n{INFO} Доступные фирмы:")
        for i, firm in enumerate(firms):
            print(f"  [{i}] - {firm['firm_name']} ({firm['firm_id']})")

        while True:
            try:
                choice_str = input(f"{INFO} Введите номер фирмы для теста [0-{len(firms) - 1}]: ")
                choice = int(choice_str) if choice_str else 0
                if 0 <= choice < len(firms):
                    selected_firm_id = firms[choice]['firm_id']
                    print(f"{INFO} Выбрана фирма: {selected_firm_id}")
                    return selected_firm_id
                else:
                    print(f"{CROSS} Неверный номер. Попробуйте еще раз.")
            except ValueError:
                print(f"{CROSS} Пожалуйста, введите число.")


    firm_id = run_test_step("Шаг 2: Выбор фирмы для тестирования", select_firm)


    # --- Шаг 3: Выбор или создание периодической задачи ---
    def select_or_create_task():
        existing_tasks = get_existing_periodic_tasks(firm_id, auth_headers)

        print(f"\n{INFO} Найдены существующие периодические задачи:")
        print(f"  [0] - {Fore.GREEN}Создать новую задачу{Style.RESET_ALL}")
        for i, task in enumerate(existing_tasks, 1):
            print(f"  [{i}] - {task['title']} ({task['task_id']})")

        while True:
            try:
                choice_str = input(f"{INFO} Введите номер задачи для теста [0-{len(existing_tasks)}]: ")
                choice = int(choice_str) if choice_str else 0
                if choice == 0:
                    confirm_step("Вы уверены, что хотите создать новую периодическую задачу?")
                    return create_new_periodic_task(firm_id, auth_headers)
                elif 1 <= choice <= len(existing_tasks):
                    selected_task = existing_tasks[choice - 1]
                    selected_task_id = selected_task['task_id']
                    print(f"{INFO} Выбрана задача {selected_task_id}. Обновление расписания для нового запуска...")

                    recurrence_data = json.loads(selected_task.get('recurrence_json', '{}'))
                    dates_list = recurrence_data.get('execution_dates_json', [])
                    new_date = (datetime.now(timezone.utc) + timedelta(seconds=10)).strftime('%Y-%m-%dT%H:%M:%SZ')
                    dates_list.append(new_date)
                    recurrence_data['execution_dates_json'] = dates_list

                    update_payload = {
                        "firm_id": firm_id,
                        "action": "UPSERT",
                        "task_id": selected_task_id,
                        "payload": {
                            "recurrence_json": json.dumps(recurrence_data)
                        }
                    }

                    update_resp = api_call("POST", API_TASKS_URL, payload=update_payload, headers=auth_headers,
                                           expected_status=200)
                    if not update_resp:
                        return None

                    print(f"{INFO} Расписание задачи {selected_task_id} обновлено. Новый запуск в {new_date}")
                    return selected_task_id
                else:
                    print(f"{CROSS} Неверный номер. Попробуйте еще раз.")
            except ValueError:
                print(f"{CROSS} Пожалуйста, введите число.")


    parent_task_id = run_test_step("Шаг 3: Выбор/создание родительской периодической задачи", select_or_create_task)


    # --- Шаг 4: Ручной вызов триггера ---
    def trigger_scheduler():
        print(f"\n{INFO} Ожидание 10 секунд для гарантии обработки события...")
        time.sleep(10)
        return api_call("POST", API_SCHEDULER_URL, expected_status=200)


    run_test_step("Шаг 4: Ручной запуск планировщика (первый раз)", trigger_scheduler)
    confirm_step("Планировщик запущен. Продолжить проверку создания дочерней задачи?")


    # --- Шаг 5: Проверка создания дочерней задачи ---
    def verify_child_creation():
        get_parent_payload = {"firm_id": firm_id, "action": "GET", "task_id": parent_task_id}
        resp = api_call("POST", API_TASKS_URL, payload=get_parent_payload, headers=auth_headers, expected_status=200)
        if not resp: return None

        parent_task_data = resp.json().get("data", {})
        children_json = parent_task_data.get("periodic_children_json")
        if not children_json or not json.loads(children_json):
            print(f"\n{WARN} У задачи {parent_task_id} еще нет дочерних элементов. Триггер мог не успеть сработать.")
            return []

        children = json.loads(children_json)
        print(f"\n{INFO} Найдено дочерних задач у родителя {parent_task_id}: {len(children)}")
        return children


    child_tasks_before_delete = run_test_step("Шаг 5: Проверка состояния дочерних задач", verify_child_creation)
    initial_child_count = len(child_tasks_before_delete)
    check_assertion("Проверка: создана хотя бы одна дочерняя задача", initial_child_count > 0,
                    "Дочерняя задача не была создана после первого запуска триггера.")


    # --- Шаг 6: Удаление основной задачи ---
    def delete_parent_task():
        prompt = f"Вы уверены, что хотите УДАЛИТЬ родительскую задачу {parent_task_id}?"
        confirm_step(prompt)
        delete_payload = {"firm_id": firm_id, "action": "DELETE", "task_id": parent_task_id}
        return api_call("POST", API_TASKS_URL, payload=delete_payload, headers=auth_headers, expected_status=200)


    run_test_step(f"Шаг 6: Удаление родительской задачи {parent_task_id}", delete_parent_task)
    run_test_step("Шаг 7: Ручной запуск планировщика (второй раз)", trigger_scheduler)


    # --- Шаг 8: Проверка, что НОВАЯ дочерняя задача НЕ создалась ---
    def verify_no_new_child():
        print()
        get_parent_payload = {"firm_id": firm_id, "action": "GET", "task_id": parent_task_id}
        resp_404 = api_call("POST", API_TASKS_URL, payload=get_parent_payload, headers=auth_headers,
                            expected_status=404)
        if not resp_404:
            # Если мы НЕ получили 404, значит что-то пошло не так (либо задача не удалилась, либо сетевая ошибка)
            return None

        print(f"{INFO} Родительская задача {parent_task_id} успешно удалена (получен статус 404).")

        get_all_payload = {"firm_id": firm_id, "action": "GET"}
        resp_all = api_call("POST", API_TASKS_URL, payload=get_all_payload, headers=auth_headers, expected_status=200)
        if not resp_all: return None

        all_tasks = resp_all.json().get("data", [])
        children_of_deleted_parent = [task for task in all_tasks if task.get('periodic_parent_id') == parent_task_id]
        final_child_count = len(children_of_deleted_parent)
        print(
            f"{INFO} Количество дочерних задач после удаления родителя: {final_child_count} (было {initial_child_count}).")

        if final_child_count == initial_child_count:
            return True
        else:
            print(
                f"{CROSS} Ошибка: количество дочерних задач изменилось! Ожидалось {initial_child_count}, найдено {final_child_count}.")
            return None


    run_test_step("Шаг 8: Проверка, что новый дубликат НЕ создан", verify_no_new_child)

    print(Fore.GREEN + "\n--- Тестирование периодических задач успешно завершено ---")