import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/router.dart';

class DateCell extends StatelessWidget {
  final DateTime date;
  final ThemeData theme;

  const DateCell({super.key, required this.date, required this.theme});

  @override
  Widget build(BuildContext context) {
    final isToday = _isToday(date);
    final isWeekend =
        date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;

    Color? backgroundColor;
    Color? textColor;

    if (isToday) {
      backgroundColor = theme.colorScheme.primaryContainer;
      textColor = theme.colorScheme.onPrimaryContainer;
    } else if (isWeekend) {
      backgroundColor = theme.colorScheme.errorContainer.withValues(alpha: 0.3);
      textColor = theme.colorScheme.onErrorContainer;
    } else {
      textColor = theme.colorScheme.onSurface;
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      decoration: BoxDecoration(color: backgroundColor),
      child: Text(
        '${date.day}',
        style: TextStyle(
          fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
          color: textColor,
        ),
      ),
    );
  }

  bool _isToday(DateTime date) {
    final today = DateTime.now();
    return date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;
  }
}

class TaskCell extends StatelessWidget {
  final List<TaskEntity> tasks;
  final ThemeData theme;
  final DateTime date;
  final String? clientId;

  const TaskCell({
    super.key,
    required this.tasks,
    required this.theme,
    required this.date,
    this.clientId,
  });

  @override
  Widget build(BuildContext context) {
    final taskContent =
        tasks.isEmpty
            ? const SizedBox.shrink()
            : tasks.length == 1
            ? _buildSingleTask(context)
            : _buildMultipleTasks(context);

    if (tasks.isEmpty) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(4),
        child: taskContent,
      );
    }

    return GestureDetector(
      onTap: () {
        if (tasks.length == 1) {
          // Если задача одна - открываем TasksViewPage напрямую
          context.router.push(TasksViewRoute(taskId: tasks.first.id));
        } else {
          // Если задач несколько - открываем TasksPage с фильтрами
          final params = TaskRequestParams.dated(
            month: date.month,
            year: date.year,
            clientId: clientId,
            filterByMonth: true, // Включаем фильтр по месяцу при переходе из календаря
          );
          context.router.push(TasksRoute(initialParams: params));
        }
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(4),
        child: taskContent,
      ),
    );
  }

  Widget _buildSingleTask(BuildContext context) {
    final task = tasks.first;
    return Tooltip(
      message: task.title,
      textStyle: TextStyle(
        fontSize: 16,
        color: Theme.of(context).colorScheme.onInverseSurface,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.inverseSurface,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        decoration: BoxDecoration(
          color: _getTaskColor(task, theme),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            task.title,
            style: TextStyle(
              fontSize: 10,
              color: _getTaskTextColor(task, theme),
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildMultipleTasks(BuildContext context) {
    final remaining = tasks.length - 1;

    return Tooltip(
      message: tasks.map((t) => t.title).join('\n'),
      textStyle: TextStyle(
        fontSize: 16,
        color: Theme.of(context).colorScheme.onInverseSurface,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.inverseSurface,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildTaskChip(tasks.first),
          const SizedBox(height: 2),
          _buildSummaryChip(remaining),
        ],
      ),
    );
  }

  Widget _buildTaskChip(TaskEntity task) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: _getTaskColor(task, theme),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Center(
        child: Text(
          task.title,
          style: TextStyle(
            fontSize: 10,
            color: _getTaskTextColor(task, theme),
            fontWeight: FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildSummaryChip(int remaining) {
    // Проверяем, все ли задачи КРОМЕ ПЕРВОЙ завершены (так как первая показана отдельно)
    final remainingTasksCompleted = _areRemainingTasksCompleted();
    final backgroundColor =
        remainingTasksCompleted
            ? Colors.green[600]! // Красивый зеленый для завершенных
            : theme.colorScheme.primary; // Обычный цвет для незавершенных

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Center(
        child: Text(
          'ещё $remaining задач',
          style: TextStyle(
            fontSize: 10,
            color:
                remainingTasksCompleted
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onPrimary,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Color _getPriorityColor(String priority, ThemeData theme) {
    switch (priority.toLowerCase()) {
      case 'high':
        return theme.colorScheme.error;
      case 'medium':
        return theme.colorScheme.tertiary;
      case 'low':
        return theme.colorScheme.secondary;
      default:
        return theme.colorScheme.outline;
    }
  }

  Color _getTextColorForPriority(String priority, ThemeData theme) {
    switch (priority.toLowerCase()) {
      case 'high':
        return theme.colorScheme.onError;
      case 'medium':
        return theme.colorScheme.onTertiary;
      case 'low':
        return theme.colorScheme.onSecondary;
      default:
        return theme.colorScheme.onSurface;
    }
  }

  /// Проверяет, завершена ли задача
  bool _isTaskCompleted(TaskEntity task) {
    final status = task.status.toLowerCase();
    return status == 'completed' || status == 'done';
  }

  /// Проверяет, завершены ли все задачи КРОМЕ ПЕРВОЙ (для блока "ещё XXX задач")
  bool _areRemainingTasksCompleted() {
    if (tasks.length <= 1) {
      return false; // Если задач 1 или меньше, то блока "ещё" нет
    }

    // Проверяем все задачи начиная со второй (skip(1))
    final remainingTasks = tasks.skip(1);
    return remainingTasks.isNotEmpty && remainingTasks.every(_isTaskCompleted);
  }

  /// Получает цвет для задачи с учетом статуса (зеленый для завершенных)
  Color _getTaskColor(TaskEntity task, ThemeData theme) {
    if (_isTaskCompleted(task)) {
      return Colors.green[600]!; // Красивый зеленый для завершенных задач
    }
    return _getPriorityColor(
      task.priority,
      theme,
    ); // Обычная логика по приоритету
  }

  /// Получает цвет текста для задачи с учетом статуса
  Color _getTaskTextColor(TaskEntity task, ThemeData theme) {
    if (_isTaskCompleted(task)) {
      return Colors.white; // Белый текст на зеленом фоне
    }
    return _getTextColorForPriority(
      task.priority,
      theme,
    ); // Обычная логика по приоритету
  }
}
