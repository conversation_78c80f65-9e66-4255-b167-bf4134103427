
```python
import json
import ydb
import uuid
import pytz
from custom_errors import Auth<PERSON>rror, LogicError, NotFoundError
from upsert import update_is_actual_flags

def delete_client(session, table_name, client_id, creation_date, roles, log_accumulator):
    log_accumulator.append(f"[delete_client] Starting DELETE operation with client_id={client_id}, creation_date={creation_date}, table={table_name}")
    log_accumulator.append(f"[delete_client] User roles: {roles}")
    
    if 'OWNER' not in roles and 'ADMIN' not in roles:
        log_accumulator.append(f"[delete_client] Error: Insufficient permissions. User roles: {roles}")
        raise AuthError("Forbidden: Insufficient permissions for DELETE action.")
    if not client_id or not creation_date:
        log_accumulator.append(f"[delete_client] Error: Missing required parameters. client_id={client_id}, creation_date={creation_date}")
        raise LogicError("client_id and creation_date are required for DELETE action.")
    
    log_accumulator.append("[delete_client] Permission check passed")
    tx = session.transaction(ydb.SerializableReadWrite())
    log_accumulator.append("[delete_client] Started database transaction")
    check_query = f"""DECLARE $cid AS Utf8; DECLARE $cdate AS Date;
                      SELECT is_actual FROM `{table_name}` WHERE client_id = $cid AND manual_creation_date = $cdate;"""
    log_accumulator.append(f"[delete_client] Checking if version exists: {check_query}")
    result = tx.execute(session.prepare(check_query), {'$cid': client_id, '$cdate': creation_date})
    log_accumulator.append(f"[delete_client] Check query result: {len(result[0].rows) if result and result[0].rows else 0} rows found")
    if not result[0].rows:
        log_accumulator.append("[delete_client] Error: Version not found")
        raise NotFoundError("Client version not found.")
    is_version_actual = result[0].rows[0].is_actual
    log_accumulator.append(f"[delete_client] Version found. is_actual={is_version_actual}")
    
    delete_query = f"""DECLARE $cid AS Utf8; DECLARE $cdate AS Date;
                       DELETE FROM `{table_name}` WHERE client_id = $cid AND manual_creation_date = $cdate;"""
    log_accumulator.append(f"[delete_client] Executing delete query: {delete_query}")
    tx.execute(session.prepare(delete_query), {'$cid': client_id, '$cdate': creation_date})
    log_accumulator.append("[delete_client] Version deleted successfully")

    # (ИСПРАВЛЕНО) Передаем и session, и tx в функцию
    log_accumulator.append("[delete_client] Updating is_actual flags for remaining versions")
    update_is_actual_flags(session, tx, table_name, client_id, log_accumulator)

    tx.commit()
    log_accumulator.append("[delete_client] Transaction committed successfully")
    result = {"message": "Client version deleted"}
    return {"statusCode": 200, "body": json.dumps(result)}
```