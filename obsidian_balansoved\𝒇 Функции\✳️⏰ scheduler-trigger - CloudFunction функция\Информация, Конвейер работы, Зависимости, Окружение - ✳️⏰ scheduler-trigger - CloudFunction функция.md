
Идентификатор - d4ed209l95con54mj89a
Описание - ⏰ Проверяет и запускает запланированные события. Вызывается по таймеру.
Точка входа - index.handler
Таймаут - 60 сек

---

На входе:
	-> **Триггер**: Функция предназначена для вызова по таймеру (Yandex Cloud Trigger). Входящее событие (`event`) игнорируется.

Внутренняя работа:
	-> **Инициализация**: Получает текущее время в UTC, настраивает логирование, кэширует IAM-токен.
	-> **Сбор событий из YDB**: В транзакции выбирает все активные события (`is_active = true`), парсит даты, проверяет на просрочку относительно `last_invoked_at`.
		-> Для ежегодных событий: Игнорирует год, проверяет шаблоны дат для текущего/прошлого года.
		-> Для разовых: Проверяет точные даты из списка.
		-> Сортирует даты, выбирает первую подходящую для вызова.
	-> **Многопоточный вызов функций**: Использует ThreadPoolExecutor для асинхронного вызова целевых функций с IAM-токеном, заголовками и payload.
	-> **Обновление событий в YDB**: В транзакции обновляет `last_invoked_at`, `updated_at`, деактивирует если последняя дата (для не ежегодных).
	-> **Обработка ошибок**: Логирует таймауты, ошибки, возвращает 500 при критических сбоях.

На выходе:
	-> `200 OK`: `{"message": "Processed X events."}` - Отчет о обработанных событиях.
	-> `500 Internal Server Error`: При ошибках.

---
#### Зависимости и окружение
- **Необходимые утилиты**: [[📄 utils - ydb_utils.md]]
- **Зависимости в `requirements.txt`**: [[💉 requirements.txt (общий).md]]
- **Переменные окружения**:
	- `YDB_ENDPOINT_SCHEDULER` - [[💾 scheduler-database - База данных YandexDatabase.md]]
	- `YDB_DATABASE_SCHEDULER` - [[💾 scheduler-database - База данных YandexDatabase.md]]
	- `SA_KEY_FILE` ([[ydb_sa_key.json]])
	- `YC_REGION` - Регион Yandex Cloud (например, `ru-central1`).
	- `STATIC_ACCESS_KEY_ID` - [[🗝️ auth-service-acc - Статический ключ доступа.md]]
	- `STATIC_SECRET_ACCESS_KEY` - [[🗝️ auth-service-acc - Статический ключ доступа.md]]