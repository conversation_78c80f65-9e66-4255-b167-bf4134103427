import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/employees_repository.dart';

class CreateEmployeeUseCase {
  final IEmployeesRepository repository;
  CreateEmployeeUseCase(this.repository);

  Future<Either<Failure, Unit>> call(String firmId, String email) {
    return repository.addEmployee(firmId, email);
  }
}
