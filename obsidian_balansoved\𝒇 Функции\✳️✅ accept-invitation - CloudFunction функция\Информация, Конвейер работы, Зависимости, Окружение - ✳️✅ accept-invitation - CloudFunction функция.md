
Идентификатор - d4ek5l50tl7lsh5kartj
Описание - ✅ Активировать приглашение сотрудника с помощью ключа.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> Тело запроса:
		- `invitation_key` (string, **обязательно**): Уникальный ключ-приглашение, полученный из email.

Внутренняя работа:
	-> Парсинг запроса: Извлекается `invitation_key` из тела запроса с использованием `request_parser.parse_request_body`.
	-> Проверка наличия `invitation_key`: Если ключ отсутствует, выбрасывается `LogicError` с сообщением "invitation_key is required.".
	-> Логирование: Записывается отладочная информация о полученном ключе.
	-> Инициализация YDB: Создается драйвер и пул сессий с использованием `ydb_utils.get_driver_for_db` и переменных окружения `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`.
	-> Транзакция в `firms-database`:
		-> Подготовка и выполнение запроса на поиск: Ищется `user_id` в таблице `Users` по `invitation_key`, где `is_active` false или NULL.
		-> Логирование количества найденных строк.
		-> Если запись не найдена: Откат транзакции и возврат None.
		-> Если запись найдена: Логирование активации, подготовка и выполнение обновления - установка `is_active` в true и `invitation_key` в NULL.
		-> Коммит транзакции и возврат True.
	-> Выполнение транзакции с ретраем: Используется `pool.retry_operation_sync`.
	-> Обработка результата: Если True, логирование успеха и возврат 200 OK; иначе выбрасывается `NotFoundError`.
	-> Обработка исключений: `LogicError` -> 400, `NotFoundError` -> 404, другие -> 500 с соответствующими логами.

На выходе:
	-> `200 OK`: `{"message": "Employee activated successfully."}`
	-> `400 Bad Request`: `{"message": "invitation_key is required."}`
	-> `404 Not Found`: `{"message": "Invalid or expired invitation key."}`
	-> `500 Internal Server Error`: `{"message": "Internal Server Error"}`

---
#### Зависимости и окружение
- **Необходимые утилиты**: [[📄 utils - ydb_utils.md]], [[📄 utils - request_parser.md]]
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` [[💾 firms-database - База данных YandexDatabase.md]]
	- `SA_KEY_FILE` [[🗝️ auth-service-acc - Статический ключ доступа.md]]