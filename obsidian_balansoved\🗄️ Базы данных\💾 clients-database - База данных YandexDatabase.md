
---
Идентификатор - etnoaj8d2t64s7td7jdt
Путь к базе - /ru-central1/b1g2bgg0i9r8beucbthc/etnoaj8d2t64s7td7jdt
Эндпоинт - grpcs://ydb.serverless.yandexcloud.net:2135/?database=/ru-central1/b1g2bgg0i9r8beucbthc/etnoaj8d2t64s7td7jdt

---
# Таблицы
В данной базе данных таблицы с клиентами (компаниями) создаются динамически для каждой обслуживающей компании по принципу "одна таблица на одну компанию" для изоляции данных.

#### Таблица-шаблон: `clients_{firm_id}`
Имя таблицы формируется путем добавления `firm_id` к префиксу `clients_`.

| #   | Имя                       | Ключ | Тип         | Описание                                                                                                                                                                              |
| --- | ------------------------- | ---- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 0   | `client_id`               | PK   | `Utf8`      | ID клиента. **Часть составного ключа.**                                                                                                                                               |
| 1   | `manual_creation_date`    | PK   | `Date`      | Дата версии. **Часть составного ключа.**                                                                                                                                              |
| 2   | `is_actual`               |      | `Bool`      | **(НОВОЕ)** Флаг `true` только для актуальной версии.                                                                                                                                 |
| 3   | `client_name`             |      | `Utf8`      | Полное наименование клиента (например, ООО "Ромашка").                                                                                                                                |
| 4   | `short_name`              |      | `Utf8`      | Сокращенное наименование клиента.                                                                                                                                                     |
| 5   | `contacts_json`           |      | `Json`      | Список контактов в формате JSON. Пример: `[{"name": "Иванов И.И.", "phone": "+7...", "email": "ceo@..."}]`                                                                            |
| 6   | `tax_and_legal_info_json` |      | `Json`      | JSON-объект с налоговой и юридической информацией (все поля "ТИП"). Обеспечивает гибкость. Также включает список прикрепленных к карточке клиента файлов с доп. информацией о клиенте |
| 7   | `payment_schedule_json`   |      | `Json`      | JSON-объект с датами выплат. Пример: `{"salary": {"day": 15, "transfer_day": 18}, "prepayment": {"day": 30, "transfer_day": 2}}`                                                      |
| 8   | `patents_json`            |      | `Json`      | Список патентов в формате JSON. Каждый объект в массиве содержит полную структуру патента, включая данные об оплате и уменьшении.                                                     |
| 9   | `tags_json`               |      | `Json`      | Список дополнительных тегов в виде JSON-массива строк. Пример: `["Важный клиент", "ВЭД"]`                                                                                             |
| 10  | `comment`                 |      | `Utf8`      | Произвольный текстовый комментарий.                                                                                                                                                   |
| 11  | `is_active`               |      | `Bool`      | Флаг, указывающий, ведется ли работа с клиентом.                                                                                                                                      |
| 12  | `created_at`              |      | `Timestamp` | Системное время создания записи о клиенте.                                                                                                                                            |
| 13  | `updated_at`              |      | `Timestamp` | Системное время последнего обновления данных клиента.                                                                                                                                 |