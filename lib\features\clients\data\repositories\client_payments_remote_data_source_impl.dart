import 'dart:convert';

import 'package:balansoved_enterprise/core/constants/constants.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';
import 'package:balansoved_enterprise/features/clients/data/data_source/client_payments_remote_data_source.dart';
import 'package:balansoved_enterprise/features/clients/data/models/client_payment_model.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_payment_entity.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class ClientPaymentsRemoteDataSourceImpl
    implements IClientPaymentsRemoteDataSource {
  final Dio? dio;
  final http.Client? httpClient;

  ClientPaymentsRemoteDataSourceImpl({this.dio, this.httpClient})
    : assert(
        dio != null || httpClient != null,
        'Either dio or httpClient must be provided',
      );

  @override
  Future<List<ClientPaymentEntity>> fetchPayments(
    String token,
    String firmId,
    int year, {
    String? clientId,
  }) async {
    final url = Uri.parse(ClientsApiUrls.managePayments());
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
    final requestData = {
      'firm_id': firmId,
      'action': 'GET',
      'year': year,
      if (clientId != null) 'client_id': clientId,
    };
    final body = jsonEncode(requestData);

    NetworkLogger.printInfo('PAYMENTS DS: URL: $url');
    NetworkLogger.printJson('PAYMENTS DS: Request body', requestData);

    try {
      if (kIsWeb && httpClient != null) {
        final response = await httpClient!.post(
          url,
          headers: headers,
          body: body,
        );
        NetworkLogger.printInfo(
          'PAYMENTS DS: HTTP status ${response.statusCode}',
        );
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body) as Map<String, dynamic>;
          final listJson = data['data'] as List<dynamic>? ?? [];
          final payments =
              listJson
                  .map((e) => ClientPaymentModel.fromJson(e).toEntity())
                  .toList();
          return payments;
        }
        throw ServerException(
          message: 'HTTP ${response.statusCode}: ${response.body}',
          statusCode: response.statusCode,
          responseBody: response.body,
          requestUrl: url.toString(),
        );
      }

      if (dio != null) {
        final response = await dio!.post(
          url.toString(),
          options: Options(headers: headers),
          data: body,
        );
        NetworkLogger.printInfo(
          'PAYMENTS DS: DIO status ${response.statusCode}',
        );
        if (response.statusCode == 200) {
          final data = response.data as Map<String, dynamic>;
          final listJson = data['data'] as List<dynamic>? ?? [];
          final payments =
              listJson
                  .map((e) => ClientPaymentModel.fromJson(e).toEntity())
                  .toList();
          return payments;
        }
        throw ServerException(
          message: 'DIO ${response.statusCode}: ${response.data}',
          statusCode: response.statusCode,
          responseBody: response.data.toString(),
          requestUrl: url.toString(),
        );
      }
      throw NetworkException(message: 'Нет доступных HTTP клиентов');
    } catch (e, stackTrace) {
      NetworkLogger.printError('PAYMENTS DS: Exception', e, stackTrace);
      if (e is ServerException || e is NetworkException) rethrow;
      throw NetworkException(message: 'Ошибка сети: $e');
    }
  }

  @override
  Future<void> upsertPayment(
    String token,
    String firmId,
    String clientId,
    DateTime period,
    double? actualAmount,
    double? tariffAmount,
    DateTime? paymentDate,
  ) async {
    final url = Uri.parse(ClientsApiUrls.managePayments());
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
    final payload = {
      'client_id': clientId,
      'period':
          '${period.year.toString().padLeft(4, '0')}-${period.month.toString().padLeft(2, '0')}',
      'actual_amount_paid': actualAmount,
      'tariff_annual_amount': tariffAmount,
      if (paymentDate != null) 'actual_payment_date': paymentDate.toIso8601String().split('T')[0],
    };
    final requestData = {
      'firm_id': firmId,
      'action': 'UPSERT',
      'payload': payload,
    };
    final body = jsonEncode(requestData);

    try {
      final response =
          await (kIsWeb && httpClient != null
              ? httpClient!.post(url, headers: headers, body: body)
              : dio!.post(
                url.toString(),
                options: Options(headers: headers),
                data: body,
              ));

      final statusCode =
          response is http.Response
              ? response.statusCode
              : (response as Response).statusCode;

      if (statusCode != 200 && statusCode != 201) {
        final responseBody =
            response is http.Response
                ? response.body
                : (response as Response).data.toString();
        throw ServerException(
          message: 'HTTP $statusCode: $responseBody',
          statusCode: statusCode,
          responseBody: responseBody,
          requestUrl: url.toString(),
        );
      }
    } catch (e, stackTrace) {
      NetworkLogger.printError('PAYMENTS DS: UPSERT Exception', e, stackTrace);
      if (e is ServerException || e is NetworkException) rethrow;
      throw NetworkException(message: 'Ошибка сети: $e');
    }
  }

  @override
  Future<void> deletePayment(
    String token,
    String firmId,
    String clientId,
    DateTime period,
  ) async {
    final url = Uri.parse(ClientsApiUrls.managePayments());
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
    final requestData = {
      'firm_id': firmId,
      'action': 'DELETE',
      'client_id': clientId,
      'period':
          '${period.year.toString().padLeft(4, '0')}-${period.month.toString().padLeft(2, '0')}',
    };
    final body = jsonEncode(requestData);

    try {
      final response =
          await (kIsWeb && httpClient != null
              ? httpClient!.post(url, headers: headers, body: body)
              : dio!.post(
                url.toString(),
                options: Options(headers: headers),
                data: body,
              ));

      final statusCode =
          response is http.Response
              ? response.statusCode
              : (response as Response).statusCode;

      if (statusCode != 200) {
        final responseBody =
            response is http.Response
                ? response.body
                : (response as Response).data.toString();
        throw ServerException(
          message: 'HTTP $statusCode: $responseBody',
          statusCode: statusCode,
          responseBody: responseBody,
          requestUrl: url.toString(),
        );
      }
    } catch (e, stackTrace) {
      NetworkLogger.printError('PAYMENTS DS: DELETE Exception', e, stackTrace);
      if (e is ServerException || e is NetworkException) rethrow;
      throw NetworkException(message: 'Ошибка сети: $e');
    }
  }
}
