import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/file_preview/inline_image_widget.dart';
import '../cubit/task_detail_cubit.dart';
import '../cubit/task_detail_state.dart';
import '../utils/task_detail_utils.dart';

import 'task_detail/attachments_section.dart';
import 'task_detail/checklist_section.dart';

class TaskDetailCard extends StatefulWidget {
  final TaskEntity task;
  final VoidCallback onClose;
  final VoidCallback onEdit;

  const TaskDetailCard({
    super.key,
    required this.task,
    required this.onClose,
    required this.onEdit,
  });

  @override
  State<TaskDetailCard> createState() => _TaskDetailCardState();
}

class _TaskDetailCardState extends State<TaskDetailCard> {
  final _checklistKey = GlobalKey<TaskDetailChecklistSectionState>();
  bool _isChecklistDirty = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _initializeTaskDetail();
  }

  void _initializeTaskDetail() {
    final activeFirmState = context.read<ActiveFirmCubit>().state;
    if (activeFirmState.selectedFirm != null) {
      context.read<TaskDetailCubit>().initialize(
        widget.task,
        activeFirmState.selectedFirm!.id,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TasksCubit, TasksState>(
      listener: (context, tasksState) {
        if (tasksState is TasksLoaded) {
          final taskIndex = tasksState.tasks.indexWhere(
            (t) => t.id == widget.task.id,
          );

          if (taskIndex != -1) {
            final updatedTask = tasksState.tasks[taskIndex];
            final detailCubit = context.read<TaskDetailCubit>();

            if (detailCubit.state is TaskDetailLoaded) {
              final currentTaskInDetail =
                  (detailCubit.state as TaskDetailLoaded).task;
              if (updatedTask != currentTaskInDetail) {
                detailCubit.initialize(
                  updatedTask,
                  context.read<ActiveFirmCubit>().state.selectedFirm!.id,
                );
              }
            }
          }
        }
      },
      child: BlocBuilder<TaskDetailCubit, TaskDetailState>(
        builder: (context, state) {
          if (state is TaskDetailLoading) {
            return const Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: EdgeInsets.all(24.0),
                child: Center(child: CircularProgressIndicator()),
              ),
            );
          }

          if (state is TaskDetailError) {
            return Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('Ошибка: ${state.message}'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _initializeTaskDetail,
                      child: const Text('Повторить'),
                    ),
                  ],
                ),
              ),
            );
          }

          if (state is! TaskDetailLoaded) {
            return const Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: EdgeInsets.all(24.0),
                child: Text('Инициализация...'),
              ),
            );
          }

          return _buildTaskDetailContent(context, state, state.task);
        },
      ),
    );
  }

  Widget _buildTaskDetailContent(
    BuildContext context,
    TaskDetailLoaded state,
    TaskEntity task,
  ) {
    final cubit = context.read<TaskDetailCubit>();

    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Просмотр задачи',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: SelectableText(
                    task.title,
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: widget.onClose,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (task.description != null && task.description!.isNotEmpty)
              _buildDescriptionSection(task),
            const SizedBox(height: 12),
            SelectableText(
              'Статус: ${TaskDetailUtils.translateStatus(task.status)}',
            ),
            const SizedBox(height: 8),
            SelectableText(
              'Приоритет: ${TaskDetailUtils.translatePriority(task.priority)}',
            ),
            if (task.dueDate != null) ...[
              const SizedBox(height: 8),
              SelectableText(
                'Срок: ${TaskDetailUtils.formatDate(task.dueDate)}',
              ),
            ],
            const Divider(height: 32),

            _buildListSection('Клиенты', task.clientIds, cubit.getClientName),
            _buildListSection(
              'Исполнитель',
              task.assigneeIds.isNotEmpty ? [task.assigneeIds.first] : [],
              cubit.getEmployeeName,
            ),
            if (task.assigneeIds.length > 1)
              _buildListSection(
                'Соисполнители',
                task.assigneeIds.sublist(1),
                cubit.getEmployeeName,
              ),
            _buildListSection(
              'Наблюдатели',
              task.observerIds,
              cubit.getEmployeeName,
            ),
            _buildCreatorSection(task.creatorIds, cubit.getEmployeeName),

            if (_hasAttachments(task) ||
                _hasChecklist(task) ||
                _hasReminders(task) ||
                _hasOptions(task) ||
                _hasRecurrence(task) ||
                _hasHolidayRule(task))
              const Divider(height: 32),

            if (_hasAttachments(task)) ...[
              TaskDetailAttachmentsSection(attachments: task.attachments),
              const SizedBox(height: 16),
            ],

            if (_hasChecklist(task)) ...[
              TaskDetailChecklistSection(
                key: _checklistKey,
                task: task,
                isEditMode: _isEditing,
                onDirtyChanged: (isDirty) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      setState(() => _isChecklistDirty = isDirty);
                    }
                  });
                },
                onSaved: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Чек-лист сохранен')),
                  );
                },
              ),
              const SizedBox(height: 16),
            ],

            if (_hasReminders(task)) ...[
              _buildRemindersSection(task),
              const SizedBox(height: 16),
            ],

            if (_hasOptions(task)) ...[
              _buildOptionsSection(task),
              const SizedBox(height: 16),
            ],

            if (_hasRecurrence(task)) ...[
              _buildRecurrenceSection(task),
              const SizedBox(height: 16),
            ],

            if (_hasHolidayRule(task)) ...[
              SelectableText(
                'Правило переноса (праздники): ${TaskDetailUtils.translateHolidayRule(task.holidayTransferRule)}',
              ),
              const SizedBox(height: 16),
            ],

            const SizedBox(height: 24),

            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: widget.onClose,
                  child: const Text('Закрыть'),
                ),
                const SizedBox(width: 16),
                if (_isChecklistDirty)
                  ElevatedButton.icon(
                    onPressed:
                        () => _checklistKey.currentState?.saveChecklist(),
                    icon: const Icon(Icons.save),
                    label: const Text('Сохранить'),
                  )
                else
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _isEditing = true;
                      });
                      widget.onEdit();
                    },
                    icon: const Icon(Icons.edit),
                    label: const Text('Редактировать'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListSection(
    String title,
    List<String> ids,
    String Function(String) nameResolver,
  ) {
    if (ids.isEmpty) return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 4,
          children:
              ids.map((id) => Chip(label: Text(nameResolver(id)))).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildCreatorSection(
    List<String> ids,
    String Function(String) nameResolver,
  ) {
    if (ids.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: const [
          Text('Постановщик', style: TextStyle(fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          SelectableText('не определён'),
          SizedBox(height: 16),
        ],
      );
    }
    return _buildListSection('Постановщик', ids, nameResolver);
  }

  Widget _buildRemindersSection(TaskEntity task) {
    if (task.reminders.isEmpty) return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Напоминания',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        ...task.reminders.map((r) {
          final dtRaw = r['datetime'];
          final roleRaw = r['role'];
          String dateText = '-';
          if (dtRaw is String && dtRaw.isNotEmpty) {
            try {
              final dt = DateTime.parse(dtRaw).toLocal();
              dateText = DateFormat('dd.MM.yyyy HH:mm').format(dt);
            } catch (_) {
              dateText = dtRaw;
            }
          }
          final roleText = TaskDetailUtils.translateRole(roleRaw?.toString());
          return SelectableText('• $dateText ($roleText)');
        }),
      ],
    );
  }

  Widget _buildOptionsSection(TaskEntity task) {
    if (task.options.isEmpty) return const SizedBox.shrink();
    final labels = {
      'allow_assignee_to_change_due_date': 'Исполнитель может менять сроки',
      'auto_close_on_due_date': 'Автоматически закрыть по сроку',
      'allow_partial_completion': 'Разрешить частичное выполнение',
      'holiday_transfer_rule': 'Перенос при празднике/выходном',
    };
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Параметры', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        ...task.options.entries.map((e) {
          final label = labels[e.key] ?? e.key;
          String value;
          if (e.value is bool) {
            value = e.value ? 'да' : 'нет';
          } else if (e.key == 'holiday_transfer_rule') {
            value = TaskDetailUtils.translateHolidayRule(e.value?.toString());
          } else {
            value = e.value?.toString() ?? '';
          }
          return SelectableText('$label: $value');
        }),
      ],
    );
  }

  Widget _buildRecurrenceSection(TaskEntity task) {
    final rec = task.recurrence;
    if (rec == null || rec.isEmpty) return const SizedBox.shrink();

    final type = rec['type']?.toString() ?? '';
    if (type.isEmpty || type == 'none') return const SizedBox.shrink();

    if (type == 'еженедельно' || type == 'weekly') {
      final interval = rec['interval'] ?? 1;
      final days = (rec['days'] as List?)?.cast<String>() ?? [];
      final dayNames = days.map(TaskDetailUtils.weekdayName).join(', ');
      return SelectableText('Повторение: каждые $interval нед. ($dayNames)');
    }
    if (type == 'periodic') {
      final interval = rec['interval'] ?? 1;
      return SelectableText('Повторение: каждые $interval периодически');
    }
    if (type == 'arbitrary') {
      final datesRaw =
          (rec['execution_dates_json'] as List?)?.cast<String>() ?? [];
      final isAnnual = rec['is_annual'] == true;
      final execDates =
          datesRaw
              .map<DateTime?>((d) {
                try {
                  return DateTime.parse(d).toLocal();
                } catch (_) {
                  return null;
                }
              })
              .whereType<DateTime>()
              .toList();

      String determineLabel() {
        if (isAnnual) return 'ежегодное';
        final months = execDates.map((d) => d.month).toSet();
        if (months.length == 12) return 'ежемесячное';
        const quarters = [
          [1, 4, 7, 10],
          [2, 5, 8, 11],
          [3, 6, 9, 12],
        ];
        for (final q in quarters) {
          if (q.every(months.contains)) return 'ежеквартальное';
        }
        return 'произвольное';
      }

      // Построение сетки 4x3 календарей (как при выборе, но без интеракций)
      Widget buildMonthCalendar(String name, int index) {
        return Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(4),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Text(
                    name,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(4),
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 7,
                          childAspectRatio: 1,
                          crossAxisSpacing: 2,
                          mainAxisSpacing: 2,
                        ),
                    itemCount: 31,
                    itemBuilder: (ctx, i) {
                      final day = i + 1;
                      final selectedDate = execDates.firstWhereOrNull(
                        (d) => d.month == index + 1 && d.day == day,
                      );
                      final selected = selectedDate != null;

                      final timeString =
                          selected
                              ? '${selectedDate.hour.toString().padLeft(2, '0')}:${selectedDate.minute.toString().padLeft(2, '0')}'
                              : '';

                      return Tooltip(
                        message: selected ? 'Время: $timeString' : '',
                        child: Container(
                          decoration: BoxDecoration(
                            color:
                                selected
                                    ? Theme.of(
                                      context,
                                    ).colorScheme.primaryContainer
                                    : Colors.transparent,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color:
                                  selected
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(
                                        context,
                                      ).dividerColor.withOpacity(0.4),
                              width: selected ? 2 : 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              day.toString(),
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight:
                                    selected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                color:
                                    selected
                                        ? Theme.of(
                                          context,
                                        ).colorScheme.onPrimaryContainer
                                        : Theme.of(
                                          context,
                                        ).textTheme.bodyMedium?.color,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      }

      Widget buildYearlyCalendars() {
        const months = [
          'Январь',
          'Февраль',
          'Март',
          'Апрель',
          'Май',
          'Июнь',
          'Июль',
          'Август',
          'Сентябрь',
          'Октябрь',
          'Ноябрь',
          'Декабрь',
        ];
        return Column(
          children: [
            for (int row = 0; row < 3; row++) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  for (int col = 0; col < 4; col++)
                    buildMonthCalendar(months[row * 4 + col], row * 4 + col),
                ],
              ),
              if (row != 2) const SizedBox(height: 12),
            ],
          ],
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(child: SelectableText('Повторение: ${determineLabel()}')),
          const SizedBox(height: 8),
          Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 1024),
              child: buildYearlyCalendars(),
            ),
          ),
        ],
      );
    }
    return SelectableText('Повторение: $type');
  }

  bool _hasAttachments(TaskEntity task) => task.attachments.isNotEmpty;
  bool _hasChecklist(TaskEntity task) => task.checklist.isNotEmpty;
  bool _hasReminders(TaskEntity task) => task.reminders.isNotEmpty;
  bool _hasOptions(TaskEntity task) => task.options.isNotEmpty;
  bool _hasRecurrence(TaskEntity task) {
    final rec = task.recurrence;
    if (rec == null || rec.isEmpty) return false;
    final type = rec['type']?.toString() ?? '';
    return type.isNotEmpty && type != 'none';
  }

  bool _hasHolidayRule(TaskEntity task) {
    final rule = task.holidayTransferRule;
    return rule != null && rule.isNotEmpty && rule != 'no_transfer';
  }

  Widget _buildDescriptionSection(TaskEntity task) {
    final description = task.description ?? '';
    final tokens = RegExp(r'\[\[(.+?)\]\]').allMatches(description).toList();
    if (tokens.isEmpty) {
      return SelectableText('Описание: $description');
    }

    final List<Widget> widgets = [];
    int lastIndex = 0;
    for (final m in tokens) {
      final start = m.start;
      if (start > lastIndex) {
        widgets.add(SelectableText(description.substring(lastIndex, start)));
      }
      final keyWithDimensions = m.group(1) ?? '';
      final parts = keyWithDimensions.split('|');
      final fileKey = parts[0];
      final dimensions = parts.length > 1 ? parts[1] : '300x200';

      // Парсим размеры
      final dimensionParts = dimensions.split('x');
      final width = int.tryParse(dimensionParts[0]) ?? 300;
      final height =
          int.tryParse(dimensionParts.length > 1 ? dimensionParts[1] : '200') ??
          200;

      // ищем вложение по fileKey вместо имени файла
      final Map<String, dynamic> attachment = task.attachments.firstWhere(
        (a) => (a['fileKey'] ?? '') == fileKey,
        orElse: () => {},
      );
      if (attachment['fileKey'] != null) {
        widgets.add(
          _buildImageWithPlaceholder(
            fileKey: attachment['fileKey'],
            fileName: attachment['name'] ?? fileKey,
            width: width,
            height: height,
          ),
        );
      } else {
        // если не найдено, выводим как текст
        widgets.add(SelectableText('[[$keyWithDimensions]]'));
      }
      lastIndex = m.end;
    }
    if (lastIndex < description.length) {
      widgets.add(SelectableText(description.substring(lastIndex)));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Описание:', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          constraints: const BoxConstraints(
            maxHeight: 400, // Максимальная высота для описания
          ),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: widgets,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageWithPlaceholder({
    required String fileKey,
    required String fileName,
    required int width,
    required int height,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Placeholder фиксированной высоты 48px, ширина заполняет контейнер

          return Stack(
            alignment: Alignment.center,
            children: [
              const SizedBox(
                width: double.infinity,
                height: 48,
                child: LoadingTile(height: 48),
              ),

              SizedBox(
                width: double.infinity,
                child: InlineImageWidget(
                  fileKey: fileKey,
                  fileName: fileName,
                  originalWidth: width,
                  originalHeight: height,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
