import requests
import json
import sys
import os
from colorama import init, Fore, Style

# Инициализируем colorama (autoreset=True сбрасывает цвет после каждого print)
init(autoreset=True)

# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
API_TARIFFS_URL = "https://d5d5vaj6bd49bcvhnfbh.sk0vql13.apigw.yandexcloud.net/manage"

FIRM_ID = "9a33483b-dfad-44a3-a36d-102b498ec0ef"
LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL


# --- ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ ---

def format_bytes(size_in_bytes):
    """Форматирует байты в читаемый вид (КБ, МБ, ГБ)."""
    if size_in_bytes < 1024:
        return f"{size_in_bytes} B"
    elif size_in_bytes < 1024 ** 2:
        return f"{size_in_bytes / 1024:.2f} KB"
    elif size_in_bytes < 1024 ** 3:
        return f"{size_in_bytes / 1024 ** 2:.2f} MB"
    else:
        return f"{size_in_bytes / 1024 ** 3:.2f} GB"


def print_storage_usage(url: str, firm_id: str, headers: dict):
    """Получает и выводит текущее использование хранилища."""
    payload = {"firm_id": firm_id, "action": "GET_RECORD"}
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=15)
        if response.status_code == 200:
            data = response.json().get('data', {})
            storage_info = data.get('storage_info_json', {})
            subscription_info = data.get('subscription_info_json', {})

            used = storage_info.get('used_bytes', 0)
            quota = subscription_info.get('quota_bytes', 0)

            print(f"   {Fore.MAGENTA}ℹ️  Хранилище: {format_bytes(used)} / {format_bytes(quota)}{Style.RESET_ALL}")
        else:
            print(
                f"   {Fore.RED}Не удалось получить данные о хранилище (статус: {response.status_code}){Style.RESET_ALL}")
    except requests.exceptions.RequestException as e:
        print(f"   {Fore.RED}Ошибка запроса данных о хранилище: {e}{Style.RESET_ALL}")


def run_test_step(title: str, url: str, payload: dict, headers: dict, expected_status: int):
    """Выполняет один шаг теста (POST), выводит результат и возвращает ответ в случае успеха."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ")
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=25)
        if response.status_code == expected_status:
            print(f"{TICK} (Статус: {response.status_code})")
            return response
        else:
            print(f"{CROSS} (Ожидался статус {expected_status}, получен {response.status_code})")
            print(Fore.RED + "  Текст ошибки:")
            try:
                error_json = response.json()
                print(Fore.RED + json.dumps(error_json, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        return None


if __name__ == "__main__":
    print("\n--- Начало комплексного тестирования Tariffs and Storage API ---\n")

    # --- Шаг 1: Аутентификация ---
    login_response = run_test_step("Шаг 1: Получение JWT токена", API_AUTH_URL, LOGIN_PAYLOAD, DEFAULT_HEADERS, 200)
    if not login_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось войти в систему. Тестирование прервано.")

    jwt_token = login_response.json().get("token")
    auth_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {jwt_token}"}

    # --- Показываем начальное состояние хранилища ---
    print_storage_usage(API_TARIFFS_URL, FIRM_ID, auth_headers)

    # --- Шаг 3: Получение ссылки на загрузку ---
    file_to_upload = 'dummy.pdf'
    try:
        file_size = os.path.getsize(file_to_upload)
    except FileNotFoundError:
        print(f"{CROSS} (Файл не найден)")
        sys.exit(f"\n{Fore.RED}Критическая ошибка: не найден файл '{file_to_upload}' рядом со скриптом.")

    get_url_payload = {
        "firm_id": FIRM_ID,
        "action": "GET_UPLOAD_URL",
        "filename": "my-test-document.pdf",
        "filesize": file_size
    }
    upload_response = run_test_step("Шаг 3: Получение pre-signed URL для загрузки", API_TARIFFS_URL, get_url_payload,
                                    auth_headers, 200)
    if not upload_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось получить ссылку на загрузку. Тестирование прервано.")

    upload_data = upload_response.json()
    file_key = upload_data.get("file_key")
    upload_url = upload_data.get("upload_url")
    if not all([file_key, upload_url]):
        sys.exit(f"\n{CROSS} Критическая ошибка: в ответе отсутствуют 'file_key' или 'upload_url'.")

    print(f"   {Fore.CYAN}i{Style.RESET_ALL} Получен ключ файла: {file_key}")

    # --- Шаг 3.5: Загрузка файла по полученной ссылке ---
    print(f"{Style.BRIGHT}► Шаг 3.5: Загрузка тестового файла в S3 ({format_bytes(file_size)})", end=" ... ")
    try:
        with open(file_to_upload, 'rb') as f:
            dummy_file_content = f.read()

        upload_headers = {'Content-Type': 'application/pdf'}
        put_response = requests.put(upload_url, data=dummy_file_content, headers=upload_headers, timeout=25)
        if put_response.status_code == 200:
            print(f"{TICK} (Статус: {put_response.status_code})")
        else:
            print(f"{CROSS} (Ожидался статус 200, получен {put_response.status_code})")
            print(Fore.RED + f"  Текст ошибки: {put_response.text}")
            sys.exit("\nКритическая ошибка: не удалось загрузить файл. Тестирование прервано.")
    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        sys.exit("\nКритическая ошибка: не удалось загрузить файл. Тестирование прервано.")

    # --- НОВЫЙ ШАГ 3.5.5: Подтверждение загрузки ---
    confirm_payload = {"firm_id": FIRM_ID, "action": "CONFIRM_UPLOAD", "file_key": file_key}
    run_test_step("Шаг 3.5.5: Подтверждение загрузки и обновление квоты", API_TARIFFS_URL, confirm_payload,
                  auth_headers, 200)

    # --- Показываем состояние хранилища ПОСЛЕ загрузки ---
    print_storage_usage(API_TARIFFS_URL, FIRM_ID, auth_headers)

    # --- Шаг 3.6: Получение ссылки на СКАЧИВАНИЕ ---
    download_payload = {"firm_id": FIRM_ID, "action": "GET_DOWNLOAD_URL", "file_key": file_key}
    download_response = run_test_step("Шаг 3.6: Получение URL для скачивания", API_TARIFFS_URL, download_payload,
                                      auth_headers, 200)
    if download_response:
        download_url = download_response.json().get('download_url')
        print(f"   {Fore.BLUE}⬇️  Ссылка для скачивания (действует 1 день):{Style.RESET_ALL}")
        print(f"     {download_url}")

    # --- Пауза перед удалением ---
    print(f"\n{Fore.YELLOW}Тест приостановлен перед удалением файла.")
    input(f"{Fore.YELLOW}--> Нажмите Enter, чтобы продолжить и выполнить Шаг 5...{Style.RESET_ALL}")

    # --- Показываем состояние хранилища ДО удаления ---
    print_storage_usage(API_TARIFFS_URL, FIRM_ID, auth_headers)

    # --- Шаг 5: Удаление файла ---
    delete_payload = {"firm_id": FIRM_ID, "action": "DELETE_FILE", "file_key": file_key}
    run_test_step(f"Шаг 5: Удаление файла по ключу ({format_bytes(file_size)})", API_TARIFFS_URL, delete_payload,
                  auth_headers, 200)

    # --- Показываем состояние хранилища ПОСЛЕ удаления ---
    print_storage_usage(API_TARIFFS_URL, FIRM_ID, auth_headers)

    # --- Шаг 6: Проверка удаления ---
    run_test_step("Шаг 6: Проверка удаления (ожидается 404)", API_TARIFFS_URL, delete_payload, auth_headers, 404)

    print("\n--- Тестирование успешно завершено ---")