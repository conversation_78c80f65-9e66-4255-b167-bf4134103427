# test_auth_api.py
import requests
import json
import sys
from colorama import init, Fore, Style

# Инициализируем colorama (autoreset=True сбрасывает цвет после каждого print)
init(autoreset=True)

# --- Конфигурация и константы ---
# URL для auth-api. Убедитесь, что эндпоинт /refresh-token добавлен в спецификацию.
BASE_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net"
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# --- Данные для теста ---
register_payload = {
    "email": "<EMAIL>",
    "password": "458854Mm",
    "user_name": "<PERSON><PERSON><PERSON>"
}
login_payload = {
    "email": "ctac<PERSON><PERSON><PERSON><EMAIL>",
    "password": "458854Mm"
}
incorrect_login_payload = {
    "email": "<EMAIL>",
    "password": "WrongPassword123"
}

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL


def run_test_step(title: str, url: str, payload: dict, headers: dict, expected_statuses: list):
    """Выполняет один шаг теста, выводит результат и возвращает ответ в случае успеха."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ")

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=15)

        if response.status_code in expected_statuses:
            print(f"{TICK} (Статус: {response.status_code})")
            return response
        else:
            print(f"{CROSS} (Ожидался статус в {expected_statuses}, получен {response.status_code})")
            print(Fore.RED + "  Текст ошибки:")
            try:
                error_json = response.json()
                print(Fore.RED + json.dumps(error_json, indent=4, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        return None


if __name__ == "__main__":
    print("\n--- Начало комплексного тестирования Auth API ---\n")

    # Шаг 1: Регистрация. Считаем успешным, если код отправлен (200) или пользователь уже существует (409)
    run_test_step(
        "Шаг 1: Запрос на регистрацию",
        f"{BASE_URL}/register-request",
        register_payload,
        DEFAULT_HEADERS,
        expected_statuses=[200, 409]
    )

    # Шаг 2: Первоначальная авторизация
    login_response_1 = run_test_step(
        "Шаг 2: Первоначальная авторизация",
        f"{BASE_URL}/login",
        login_payload,
        DEFAULT_HEADERS,
        expected_statuses=[200]
    )
    if not login_response_1:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось войти в систему. Тестирование прервано.")

    # Шаг 3: Некорректная авторизация
    run_test_step(
        "Шаг 3: Проверка входа с неверным паролем",
        f"{BASE_URL}/login",
        incorrect_login_payload,
        DEFAULT_HEADERS,
        expected_statuses=[401]
    )

    # Шаг 4: Принудительное обновление токена
    refresh_response = run_test_step(
        "Шаг 4: Принудительное обновление токена",
        f"{BASE_URL}/refresh-token",
        login_payload,
        DEFAULT_HEADERS,
        expected_statuses=[200]
    )
    if not refresh_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось обновить токен. Тестирование прервано.")
    refreshed_token = refresh_response.json().get("token")

    # Шаг 5: Повторная авторизация для проверки, что вернулся обновленный токен
    login_response_2 = run_test_step(
        "Шаг 5: Повторная авторизация после обновления",
        f"{BASE_URL}/login",
        login_payload,
        DEFAULT_HEADERS,
        expected_statuses=[200]
    )
    if not login_response_2:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось выполнить повторный вход. Тестирование прервано.")
    new_login_token = login_response_2.json().get("token")

    # Шаг 6: Финальная проверка
    print(f"\n{Style.BRIGHT}► Шаг 6: Финальная проверка токенов", end=" ... ")
    if refreshed_token and new_login_token and refreshed_token == new_login_token:
        print(f"{TICK} (Токены полностью совпадают)")
    else:
        print(f"{CROSS} (Токены НЕ совпадают)")
        print(Fore.RED + f"  Токен из refresh: ...{refreshed_token[-15:] if refreshed_token else 'N/A'}")
        print(Fore.RED + f"  Токен из login:   ...{new_login_token[-15:] if new_login_token else 'N/A'}")

    print("\n--- Тестирование Auth API успешно завершено ---")