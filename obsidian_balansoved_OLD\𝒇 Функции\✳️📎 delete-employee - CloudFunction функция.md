
Идентификатор - d4e4eh8qs34ce11rmgte
Описание - 𐀪 **Физически удалить** сотрудника из конкретной фирмы с проверкой прав и иерархии
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен администратора или владельца.
	-> Тело запроса:
		- `firm_id`: **(Обязательно)** ID фирмы, в которой происходит действие.
		- `user_id_to_delete`: ID пользователя, которого нужно удалить.
Внутренняя работа:
	-> Логирование: Установка уровня логирования на INFO.
	-> Авторизация:
		-> Проверка наличия и формата заголовка Authorization.
		-> Верификация JWT токена и извлечение admin_user_id.
	-> Парсинг запроса:
		-> Извлечение firm_id и user_id_to_delete из тела запроса с помощью request_parser.
	-> Валидация:
		-> Проверка наличия firm_id и user_id_to_delete.
		-> Проверка, что admin_user_id != user_id_to_delete (нельзя удалять самого себя).
	-> Подключение к firms-database: Получение драйвера и пула сессий.
	-> Транзакция в firms-database:
		-> Запрос для получения ролей admin и target пользователя в указанной firm_id.
		-> Проверка наличия обоих пользователей в фирме.
		-> Проверка, что target не имеет роли "OWNER".
		-> Расчет наивысших баллов ролей для admin и target с использованием ROLE_HIERARCHY.
		-> Проверка, что балл admin > балл target.
		-> Удаление записи из таблицы Users по user_id_to_delete и firm_id.
	-> Обработка исключений: AuthError (403), LogicError (400), NotFoundError (404), другие (500) с логированием.
На выходе:
	-> `200 OK`: {"message": "Employee successfully deleted."}
	-> `400 Bad Request`: Отсутствуют обязательные поля, ошибка парсинга или попытка удалить самого себя.
	-> `403 Forbidden`: Недостаточно прав (роль не выше, или target - OWNER).
	-> `404 Not Found`: Администратор или target не найдены в фирме.
	-> `500 Internal Server Error`: Внутренняя ошибка сервера.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`

---

```python
import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

# Словарь для определения веса ролей
ROLE_HIERARCHY = {
    "OWNER": 3,
    "ADMIN": 2,
    "EMPLOYEE": 1
}

def get_highest_role_score(roles_json_str: str) -> int:
    """Возвращает наивысший балл из списка ролей пользователя."""
    roles = json.loads(roles_json_str)
    if not roles:
        return 0
    return max(ROLE_HIERARCHY.get(role, 0) for role in roles)

def handler(event, context):
    try:
        # 1. Авторизация администратора
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized")
        
        token = auth_header.split(' ')[1]
        admin_payload = auth_utils.verify_jwt(token)
        if not admin_payload or 'user_id' not in admin_payload:
            raise AuthError("Invalid token")
        
        admin_user_id = admin_payload['user_id']

        # 2. Валидация входных данных
        try:
            data = request_parser.parse_request_body(event)
        except ValueError as e:
            raise LogicError(str(e))

        user_id_to_delete = data.get('user_id_to_delete')
        firm_id = data.get('firm_id')

        if not all([user_id_to_delete, firm_id]):
            raise LogicError("firm_id and user_id_to_delete are required.")

        # 3. Проверка на удаление самого себя
        if admin_user_id == user_id_to_delete:
            raise LogicError("You cannot delete yourself.")

        driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        pool = ydb.SessionPool(driver)

        def delete_employee_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())

            # 4. Один запрос для получения данных админа и цели в рамках ОДНОЙ фирмы
            query = session.prepare("""
                DECLARE $admin_id AS Utf8;
                DECLARE $target_id AS Utf8;
                DECLARE $firm_id AS Utf8;
                SELECT user_id, roles FROM Users 
                WHERE firm_id = $firm_id AND user_id IN ($admin_id, $target_id);
            """)
            res = tx.execute(query, {
                '$admin_id': admin_user_id, 
                '$target_id': user_id_to_delete,
                '$firm_id': firm_id
            })
            
            admin_data, target_data = None, None
            for row in res[0].rows:
                if row.user_id == admin_user_id:
                    admin_data = row
                elif row.user_id == user_id_to_delete:
                    target_data = row
            
            if not admin_data:
                raise NotFoundError("Requesting user (admin) not found in the specified firm.")
            if not target_data:
                raise NotFoundError("Target user to delete not found in the specified firm.")

            # 5. Проверка безопасности и иерархии
            target_roles = json.loads(target_data.roles)
            if "OWNER" in target_roles:
                raise AuthError("Cannot delete the firm owner.")

            admin_score = get_highest_role_score(admin_data.roles)
            target_score = get_highest_role_score(target_data.roles)

            if admin_score <= target_score:
                raise AuthError("Insufficient permissions: your role must be higher than the target user's role.")

            # 6. Физическое удаление пользователя из КОНКРЕТНОЙ фирмы
            delete_query = session.prepare("""
                DECLARE $user_id AS Utf8;
                DECLARE $firm_id AS Utf8;
                DELETE FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;
            """)
            tx.execute(
                delete_query,
                {'$user_id': user_id_to_delete, '$firm_id': firm_id}
            )
            tx.commit()
            return True

        pool.retry_operation_sync(delete_employee_transaction)
        return {"statusCode": 200, "body": json.dumps({"message": "Employee successfully deleted."})}

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error deleting employee: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```
