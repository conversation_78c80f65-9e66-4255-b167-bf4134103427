
Идентификатор - d5d5vaj6bd49bcvhnfbh
Имя - tariffs-and-storage-api
Служебный домен - https://d5d5vaj6bd49bcvhnfbh.sk0vql13.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Tariffs and Storage API
  version: 1.0.0
servers:
  - url: https://d5d5vaj6bd49bcvhnfbh.sk0vql13.apigw.yandexcloud.net

x-yc-apigateway:
  cors:
    origin: "*"
    methods:
      - "POST"
      - "OPTIONS"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    allowCredentials: true
    maxAge: 3600

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - bearerAuth: []

paths:
  /manage:
    post:
      summary: Универсальный метод для управления тарифами и хранилищем
      description: |
        Позволяет выполнять следующие операции:
        - GET_RECORD: Получить запись о тарифе и хранилище для фирмы.
        - UPDATE_JSON: Атомарно обновить данные в JSON-полях.
        - CLEAR_JSON: Очистить JSON-поля.
        - GET_UPLOAD_URL: Получить pre-signed URL для загрузки файла.
        - GET_DOWNLOAD_URL: Получить pre-signed URL для скачивания файла.
        - CONFIRM_UPLOAD: Подтвердить загрузку и обновить квоту.
        - DELETE_FILE: Удалить файл из хранилища.
      operationId: manageTariffsAndStorage
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ek9ojgjnoibdsi1qut
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action:
                  type: string
                  enum: [GET_RECORD, UPDATE_JSON, CLEAR_JSON, GET_UPLOAD_URL, GET_DOWNLOAD_URL, CONFIRM_UPLOAD, DELETE_FILE]
                firm_id:
                  type: string
                
                # Поля для UPDATE_JSON
                target_json_field:
                  type: string
                  description: "Имя поля для обновления. Обязательно для action: UPDATE_JSON."
                updates:
                  type: object
                  description: "Объект с обновлениями. Обязательно для action: UPDATE_JSON."

                # Поля для CLEAR_JSON
                fields_to_clear:
                  type: array
                  items:
                    type: string
                  description: "Список полей для очистки. Обязательно для action: CLEAR_JSON."

                # Поля для GET_UPLOAD_URL
                filename:
                  type: string
                  description: "Имя файла. Обязательно для action: GET_UPLOAD_URL."
                filesize:
                  type: integer
                  description: "Размер файла в байтах. Обязательно для action: GET_UPLOAD_URL."
                  
                # Поля для GET_DOWNLOAD_URL, DELETE_FILE, CONFIRM_UPLOAD
                file_key:
                  type: string
                  description: "Ключ файла в S3. Обязательно для action: DELETE_FILE, GET_DOWNLOAD_URL, CONFIRM_UPLOAD."
              required:
                - action
                - firm_id
      responses:
        '200':
          description: Успешное выполнение.
        '400':
          description: Неверные параметры в запросе.
        '403':
          description: Ошибка авторизации или недостаточно прав.
        '404':
          description: Ресурс не найден (например, запись о фирме или файл).
        '413':
          description: Квота на хранилище превышена.
        '500':
          description: Внутренняя ошибка сервера.
  /integrations:
    post:
      summary: Управление интеграциями фирмы
      description: |
        Позволяет выполнять следующие операции:
        - GET: Получить текущий JSON интеграций фирмы.
        - UPSERT: Добавить новые / обновить существующие ключи интеграций.
        - DELETE: Удалить указанные интеграции по ключам.
      operationId: manageFirmIntegrations
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4eqfkchno7rtj8rlhak
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action:
                  type: string
                  enum: [GET, UPSERT, DELETE]
                firm_id:
                  type: string
                payload:
                  type: object
                  description: "JSON-объект интеграций для action: UPSERT."
                integration_keys:
                  type: array
                  items:
                    type: string
                  description: "Список ключей для удаления. Обязательно для action: DELETE."
              required:
                - action
                - firm_id
      responses:
        '200':
          description: Успешное выполнение.
        '400':
          description: Неверные параметры запроса.
        '403':
          description: Ошибка авторизации или недостаточно прав.
        '404':
          description: Фирма не найдена.
        '500':
          description: Внутренняя ошибка сервера.
```