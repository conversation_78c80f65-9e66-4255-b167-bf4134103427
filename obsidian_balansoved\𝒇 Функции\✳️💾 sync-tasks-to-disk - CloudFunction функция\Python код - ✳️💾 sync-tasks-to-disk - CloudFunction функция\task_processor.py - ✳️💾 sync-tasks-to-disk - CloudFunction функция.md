
```python
import json
import os
import requests
from io import Bytes<PERSON>
from concurrent.futures import ThreadPoolExecutor, as_completed

import runtime_utils
import data_fetchers
import yandex_disk_uploader
import pdf_generator
import datetime as dt

# Повышаем максимальную общую задержку backoff в retry до 30 секунд
requests.packages.urllib3.util.retry.Retry.BACKOFF_MAX = 30
def download_file_to_memory(url: str):
    session = requests.Session()
    retries = requests.packages.urllib3.util.retry.Retry(total=5, backoff_factor=1, status_forcelist=[500, 502, 503, 504])
    session.mount('https://', requests.adapters.HTTPAdapter(max_retries=retries))
    try:
        with session.get(url, stream=True, timeout=60) as r:
            r.raise_for_status()
            buffer = BytesIO()
            for chunk in r.iter_content(chunk_size=8192):
                buffer.write(chunk)
            return buffer.getvalue()
    except requests.exceptions.RequestException as e:
        runtime_utils.log_message(f"Ошибка скачивания файла с {url}: {e}", "ERROR")
        return None

def process_single_task(task_info: dict, user_jwt: str, firm_id: str, yadisk_token: str, temp_dir: str):
    task_summary = task_info['data']
    task_id = task_summary['task_id']
    task_title_raw = task_summary.get('title', 'Без_названия')
    task_title_sanitized = yandex_disk_uploader.sanitize_filename(task_title_raw)
    runtime_utils.log_progress(f"--- Обработка задачи: {task_title_raw} ---")

    get_task_payload = {"firm_id": firm_id, "action": "GET", "task_id": task_id}
    task_response = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_TASK, get_task_payload, user_jwt)
    if not task_response: return
    task_data = task_response.get('data', {})

    base_path_on_disk = f"{runtime_utils.YADISK_BASE_FOLDER}/бессрочные задачи" if task_info['type'] == 'timeless' else f"{runtime_utils.YADISK_BASE_FOLDER}/задачи/{task_info['year']}/{task_info['month']:02d}"
    
    client_folder_name = data_fetchers.get_client_folder_name(task_data.get('client_ids_json'), user_jwt, firm_id)
    task_folder_name = f"{task_title_sanitized} ({task_id})"
    task_folder_on_disk = f"{base_path_on_disk}/{client_folder_name}/{task_folder_name}"

    runtime_utils.log_progress(f"  {runtime_utils.INFO} Путь на Диске: {task_folder_on_disk}")
    
    # ► Умный пропуск с еженедельным обновлением в течение 30 дней
    marker_file_path = f"{task_folder_on_disk}/task_info_raw.json"
    disk_info = yandex_disk_uploader.get_resource_info(yadisk_token, marker_file_path)

    now_utc = dt.datetime.now(dt.timezone.utc)
    skip_attachments = False

    if disk_info:
        try:
            created_dt = dt.datetime.fromisoformat(disk_info.get('created').replace("Z", "+00:00"))
            modified_dt = dt.datetime.fromisoformat(disk_info.get('modified').replace("Z", "+00:00"))
        except Exception:
            # Если не удаётся распарсить даты, выполняем полную синхронизацию
            created_dt = modified_dt = None

        if created_dt and modified_dt:
            age_days = (now_utc - created_dt).days
            days_since_mod = (now_utc - modified_dt).days

            if age_days > 30:
                # Папка старше 30 дней – дальнейшие обновления не требуются
                runtime_utils.log_progress(f"  {runtime_utils.INFO} Задача старше 30 дней, пропуск обновления.")
                return

            if days_since_mod < 7:
                # Последнее обновление было менее недели назад – рано обновлять
                runtime_utils.log_progress(f"  {runtime_utils.INFO} Задача обновлялась менее недели назад, пропуск.")
                return

            # Требуется обновление без перезагрузки вложений
            runtime_utils.log_progress(f"  {runtime_utils.WARN} Выполняется плановое недельное обновление задачи (без вложений)...")
            skip_attachments = True
        else:
            runtime_utils.log_progress(f"  {runtime_utils.WARN} Не удалось определить даты ресурса, выполняется полная синхронизация...")

    if not yandex_disk_uploader.ensure_path_recursively(yadisk_token, task_folder_on_disk): return

    new_attachments_with_urls = []
    if not skip_attachments:
        attachments_from_task = json.loads(task_data.get('attachments_json', '[]'))
        if attachments_from_task:
            files_folder_on_disk = f"{task_folder_on_disk}/файлы"
            if not yandex_disk_uploader.ensure_folder_exists(yadisk_token, files_folder_on_disk): return
            runtime_utils.log_progress(f"  {runtime_utils.INFO} Найдено вложений: {len(attachments_from_task)}. Загрузка...")

            def handle_attachment(attachment: dict):
                file_key = attachment.get('file_key') or attachment.get('fileKey')
                filename = attachment.get('name', os.path.basename(file_key or 'unknown_file'))
                if not file_key: return None
                
                payload = {"firm_id": firm_id, "action": "GET_DOWNLOAD_URL", "file_key": file_key}
                resp = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_TARIFFS_AND_STORAGE_MANAGER, payload, user_jwt)
                if not resp or not resp.get('download_url'): return None
                
                file_bytes = download_file_to_memory(resp['download_url'])
                if not file_bytes: return None

                target_path = f"{files_folder_on_disk}/{filename}"
                yadisk_link = yandex_disk_uploader.upload_and_get_link_from_bytes(yadisk_token, file_bytes, target_path)
                if not yadisk_link: return None

                return {"name": filename, "public_url": yadisk_link, "file_key": file_key}

            with ThreadPoolExecutor(max_workers=4) as executor:
                future_map = {executor.submit(handle_attachment, att): att for att in attachments_from_task}
                for future in as_completed(future_map):
                    result = future.result()
                    if result: new_attachments_with_urls.append(result)
        else:
            runtime_utils.log_progress(f"  {runtime_utils.INFO} Вложения отсутствуют.")
    else:
        runtime_utils.log_progress(f"  {runtime_utils.INFO} Пропуск загрузки вложений для еженедельного обновления.")

    runtime_utils.log_progress(f"  {runtime_utils.INFO} Подготовка данных для PDF...")
    participant_roles = ['assignee_ids_json', 'creator_ids_json', 'observer_ids_json']
    participants_map = {role.split('_')[0]: [f"{data_fetchers.get_employee_name(eid, user_jwt, firm_id)} ({eid})" for eid in json.loads(task_data.get(role, '[]'))] for role in participant_roles}
    clients_map = [f"{data_fetchers.get_client_name(cid, user_jwt, firm_id)} ({cid})" for cid in json.loads(task_data.get('client_ids_json', '[]'))]

    pdf_path = os.path.join(temp_dir, 'task_report.pdf')
    if pdf_generator.generate_task_pdf(pdf_path, task_data, participants_map, clients_map, new_attachments_with_urls):
        yandex_disk_uploader.upload_and_get_link(yadisk_token, pdf_path, f"{task_folder_on_disk}/Отчет по задаче.pdf")
        runtime_utils.log_progress(f"  {runtime_utils.TICK} Отчет PDF сохранен.")
    
    if not skip_attachments:
        task_data['attachments_json'] = json.dumps(new_attachments_with_urls, ensure_ascii=False)

    task_json_path = os.path.join(temp_dir, 'task_info_raw.json')
    with open(task_json_path, 'w', encoding='utf-8') as f:
        json.dump(task_data, f, ensure_ascii=False, indent=4)
    yandex_disk_uploader.upload_and_get_link(yadisk_token, task_json_path, f"{task_folder_on_disk}/task_info_raw.json")
    runtime_utils.log_progress(f"  {runtime_utils.TICK} Файл JSON сохранен.")
```