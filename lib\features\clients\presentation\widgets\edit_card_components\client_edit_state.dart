import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';

/// Класс для управления состоянием ClientEditCard
class ClientEditState {
  // Основные поля
  DateTime? creationDate;
  String? ownershipForm;
  String? directorType;
  String? directorName;
  DateTime? directorStartDate;

  // Налоговые системы и отчётность
  List<String> taxSystems = [];
  List<String> profitTaxTypes = [];
  List<String> vatTypes = [];
  List<String> propertyTypes = [];
  String? reportingType;
  String? reportingOperator;
  List<String> exciseGoods = [];
  List<String> excisePaymentTerms = [];
  List<String> edoOperators = [];
  String? enpType;
  List<String> activityTypes = [];
  String? ndflType;

  // Дополнительные поля
  List<String> additionalTags = [];
  List<String> fixedContributionsIP = [];
  List<int> fixedContributionsPaymentDate = [];
  List<String> contributionsIP1Percent = [];
  List<int> contributionsIP1PercentPaymentDate = [];

  // Графики платежей
  PaymentSchedule? salaryPayment;
  bool salaryPaymentEnabled = false;
  PaymentSchedule? advancePayment;
  PaymentSchedule? ndflPayment;

  // Связанные сущности
  List<ContactEntity> contacts = [];
  List<PatentEntity> patents = [];
  List<Map<String, dynamic>> attachments = [];
  String? attachmentComments;

  // Новые поля для расширенной информации
  List<FnsInfo> fnsInfo = [];
  List<KppInfo> kppInfo = [];
  SfrInfo? sfrInfo;
  String? okpo;
  bool? cashOrBank;
  bool cashPayment = false;
  bool bankPayment = false;
  bool hasEmployees = false;
  bool isSoleFounderDirector = false;
  String? ogrn;
  String? legalAddress;
  String? actualAddress;
  bool actualAddressSameAsLegal = false;

  // ЭЦП
  DateTime? digitalSignatureExpiryDate;

  // Режим редактирования
  bool isEditing = false;

  /// Инициализирует состояние данными из клиента
  void initializeFromClient(ClientEntity client) {
    creationDate = client.creationDate;
    fixedContributionsPaymentDate = client.fixedContributionsPaymentDate;

    // Фильтруем данные по доступным константам
    ownershipForm =
        ClientConstants.ownershipForms.contains(client.ownershipForm)
            ? client.ownershipForm
            : null;

    directorType =
        ClientConstants.directorTypes.contains(client.directorType)
            ? client.directorType
            : null;

    directorName = client.directorName;
    directorStartDate = client.directorStartDate;

    taxSystems =
        client.taxSystems
            .where((item) => ClientConstants.taxSystems.contains(item))
            .toList();

    profitTaxTypes.clear();
    profitTaxTypes.addAll(
      client.profitTaxTypes
          .where((item) => ClientConstants.profitTaxTypes.contains(item))
          .toList(),
    );

    vatTypes =
        client.vatTypes
            .where((item) => ClientConstants.vatTypes.contains(item))
            .toList();

    propertyTypes =
        client.propertyTypes
            .where((item) => ClientConstants.propertyTypes.contains(item))
            .toList();

    reportingType =
        ClientConstants.reportingTypes.contains(client.reportingType)
            ? client.reportingType
            : null;

    reportingOperator =
        ClientConstants.reportingOperators.contains(client.reportingOperator)
            ? client.reportingOperator
            : null;

    exciseGoods =
        client.exciseGoods
            .where((item) => ClientConstants.exciseGoods.contains(item))
            .toList();

    excisePaymentTerms =
        client.excisePaymentTerms
            .where((item) => ClientConstants.excisePaymentTerms.contains(item))
            .toList();

    edoOperators =
        client.edoOperators
            .where((item) => ClientConstants.edoOperators.contains(item))
            .toList();

    enpType =
        ClientConstants.enpTypes.contains(client.enpType)
            ? client.enpType
            : null;

    activityTypes =
        client.activityTypes
            .where((item) => ClientConstants.activityTypes.contains(item))
            .toList();

    ndflType =
        ClientConstants.ndflTypes.contains(client.ndflType)
            ? client.ndflType
            : null;

    additionalTags = List.from(client.additionalTags);

    fixedContributionsIP =
        client.fixedContributionsIP
            .where(
              (item) => ClientConstants.fixedContributionsIP.contains(item),
            )
            .toList();

    contributionsIP1Percent =
        client.contributionsIP1Percent
            .where(
              (item) => ClientConstants.contributionsIP1Percent.contains(item),
            )
            .toList();

    contributionsIP1PercentPaymentDate =
        client.contributionsIP1PercentPaymentDate;

    // Графики платежей
    salaryPayment = client.salaryPayment;
    salaryPaymentEnabled = client.salaryPaymentEnabled;
    advancePayment = client.advancePayment;
    ndflPayment = client.ndflPayment;

    // Связанные сущности
    contacts = List.from(client.contacts);
    patents = List.from(client.patents);
    attachments = List.from(client.attachments);
    attachmentComments = client.attachmentComments;

    // Новые поля для расширенной информации
    fnsInfo = List.from(client.fnsInfo);
    kppInfo = List.from(client.kppInfo);
    sfrInfo = client.sfrInfo;
    okpo = client.okpo;
    cashOrBank = client.cashOrBank;
    cashPayment = client.cashPayment ?? false;
    bankPayment = client.bankPayment ?? false;
    hasEmployees = client.hasEmployees ?? false;
    isSoleFounderDirector = client.isSoleFounderDirector ?? false;
    ogrn = client.ogrn;
    legalAddress = client.legalAddress;
    actualAddress = client.actualAddress;
    actualAddressSameAsLegal = client.actualAddressSameAsLegal;
    digitalSignatureExpiryDate = client.digitalSignatureExpiryDate;
  }

  /// Обновляет состояние данными из сущности клиента
  void updateFromEntity(ClientEntity client) {
    initializeFromClient(client);
  }

  /// Создаёт ClientEntity из текущего состояния
  ClientEntity createClientEntity(
    String clientId,
    String name,
    String? inn,
    String? shortName,
    String? comment,
  ) {
    return ClientEntity(
      id: clientId,
      name: name,
      inn: inn,
      shortName: shortName,
      comment: comment,
      creationDate: creationDate,
      ownershipForm: ownershipForm,
      directorType: directorType,
      directorName: directorName,
      directorStartDate: directorStartDate,
      taxSystems: taxSystems,
      profitTaxTypes: profitTaxTypes,
      vatTypes: vatTypes,
      propertyTypes: propertyTypes,
      reportingType: reportingType,
      reportingOperator: reportingOperator,
      exciseGoods: exciseGoods,
      excisePaymentTerms: excisePaymentTerms,
      edoOperators: edoOperators,
      enpType: enpType,
      activityTypes: activityTypes,
      ndflType: ndflType,
      additionalTags: additionalTags,
      fixedContributionsIP: fixedContributionsIP,
      fixedContributionsPaymentDate: fixedContributionsPaymentDate,
      contributionsIP1Percent: contributionsIP1Percent,
      contributionsIP1PercentPaymentDate: contributionsIP1PercentPaymentDate,
      salaryPayment: salaryPayment,
      salaryPaymentEnabled: salaryPaymentEnabled,
      advancePayment: advancePayment,
      ndflPayment: ndflPayment,
      contacts: contacts,
      patents: patents,
      attachments: attachments,
      attachmentComments: attachmentComments,
      // Новые поля для расширенной информации
      fnsInfo: fnsInfo,
      kppInfo: kppInfo,
      sfrInfo: sfrInfo,
      okpo: okpo,
      cashOrBank: cashOrBank,
      cashPayment: cashPayment,
      bankPayment: bankPayment,
      hasEmployees: hasEmployees,
      isSoleFounderDirector: isSoleFounderDirector,
      ogrn: ogrn,
      legalAddress: legalAddress,
      actualAddress: actualAddress,
      actualAddressSameAsLegal: actualAddressSameAsLegal,
      digitalSignatureExpiryDate: digitalSignatureExpiryDate,
    );
  }
}
