Идентификатор - d4er76v5l270502p7qu2
Описание - Запросить код для регистрации или создать пользователя в тестовом режиме.
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `email` (**обязательно**): Email пользователя.
	-> `password` (**обязательно**): Пароль пользователя.
	-> `user_name` (string, **обязательно только при первой регистрации**): Имя пользователя. При повторной отправке кода не требуется.

Внутренняя работа:
	-> Парсинг тела запроса для извлечения `email`, `password` и `user_name`.
	-> Проверка наличия `email` и `password`; если отсутствуют, возврат 400.
	-> Определение режима: `AUTO_CONFIRM_MODE` = true или false.
	-> Получение драйверов YDB для `jwt-database` и `firms-database`.
	-> Транзакционная обработка в `jwt-database`:
		-> Проверка существования пользователя по `email`.
		-> Если пользователь существует и активен, возврат 409.
		-> Если существует, но не активен:
			-> Проверка времени с предыдущей генерации кода.
			-> Если прошло >= 3 мин, генерация нового кода, обновление и отправка email (в стандартном режиме).
			-> Если < 3 мин, продление срока кода до 1 года.
		-> Если пользователь не существует:
			-> Проверка наличия приглашений в `firms-database` для повторного использования `user_id`.
			-> Генерация `user_id` (из приглашения или новый), хэширование пароля.
			-> В тестовом режиме: создание активного пользователя, обновление приглашений в `firms-database`, генерация JWT.
			-> В стандартном режиме: создание неактивного пользователя с кодом, отправка email.
	-> Обработка ошибок: возврат 500 при сбоях, например, отправки email.

На выходе:
	-> `201 Created`: {"token": "<jwt_token>"} (Только в тестовом режиме)
	-> `200 OK`: {"message": "Verification code sent."} (В стандартном режиме)
	-> `409 Conflict`: {"message": "User with this email already exists."} (активный пользователь)
	-> `423 Locked`: {"message": "Account not confirmed. Please verify your email."}
	-> `400 Bad Request`: При невалидных данных.
	-> `500 Internal Server Error`: В случае критических ошибок, например, при сбое отправки email.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/email_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
    - `YDB_ENDPOINT`, `YDB_DATABASE` ([[💾 jwt-database - База данных YandexDatabase]])
    - `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
    - SA_KEY_FILE ([[ydb_sa_key.json]])
    - JWT_SECRET
    - `AUTO_CONFIRM_MODE` - `true` или `false`.
    - `UNISENDER_API_KEY` - API ключ от сервиса Unisender.
    - `UNISENDER_SENDER_EMAIL` - Email отправителя, подтвержденный в Unisender.
    - `UNISENDER_SENDER_NAME` - Имя отправителя.
    - `UNISENDER_LIST_ID` - ID списка контактов в Unisender.

---

#### Финальная версия кода
```python
import json
import os
import uuid
import random
import datetime
import pytz
import logging
import ydb
from utils import ydb_utils, auth_utils, email_utils, request_parser

logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    logging.info("--- NEW INVOCATION ---")
    
    auto_confirm_mode = os.environ.get('AUTO_CONFIRM_MODE', 'false').lower() == 'true'

    try:
        data = request_parser.parse_request_body(event)
    except ValueError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}

    email = data.get('email', '').lower()
    password = data.get('password')
    user_name = data.get('user_name')

    # email и password обязательны всегда
    if not all([email, password]):
        return {"statusCode": 400, "body": json.dumps({"message": "Email and password are required."})}

    # Основной драйвер для jwt-database
    auth_driver = ydb_utils.get_ydb_driver()
    auth_pool = ydb.SessionPool(auth_driver)

    # Драйвер для firms-database (понадобится в режиме auto_confirm)
    firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
    firms_pool = ydb.SessionPool(firms_driver)

    def transaction(session):
        # Эта транзакция работает только с jwt-database
        tx = session.transaction(ydb.SerializableReadWrite())

        # 1. Проверяем, не занят ли email в основной базе
        check_query = f"PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8; SELECT user_id, is_active, code_expires_at FROM users WHERE email = $email;"
        prepared_check = session.prepare(check_query)
        result_sets = tx.execute(prepared_check, {'$email': email})
        
        if result_sets[0].rows:
            existing_row = result_sets[0].rows[0]
            if not existing_row.is_active:
                # Пользователь уже запрашивал код. Проверяем, прошло ли >= 3 минут
                prev_expires = existing_row.code_expires_at
                now_utc = datetime.datetime.now(pytz.utc)

                # Если prev_expires == None, считаем, что коду больше 3 минут
                three_minutes_ago = now_utc - datetime.timedelta(minutes=3)

                # Код был создан 10 минут назад: вычисляем предполагаемое время создания
                if prev_expires:
                    if isinstance(prev_expires, datetime.datetime):
                        created_at_est = prev_expires - datetime.timedelta(minutes=10)
                    else:
                        # prev_expires в мкс → datetime
                        created_at_est = datetime.datetime.fromtimestamp(prev_expires / 1_000_000, tz=pytz.utc) - datetime.timedelta(minutes=10)
                else:
                    created_at_est = now_utc - datetime.timedelta(minutes=10)

                if created_at_est <= three_minutes_ago:
                    # Генерируем новый код и отправляем
                    new_code = str(random.randint(100000, 999999))
                    new_expires = now_utc + datetime.timedelta(minutes=10)

                    update_query = f"""
                        PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8; DECLARE $code AS Utf8; DECLARE $expires AS Timestamp;
                        UPDATE users SET verification_code = $code, code_expires_at = $expires WHERE email = $email AND is_active = false;
                    """
                    tx.execute(session.prepare(update_query), {
                        '$email': email, '$code': new_code, '$expires': new_expires
                    })

                    # Отправляем письмо ДО коммита, как и ранее
                    email_sent = email_utils.send_verification_code(email, new_code)
                    if not email_sent:
                        tx.rollback()
                        logging.error(f"Failed to send verification code to {email}. Rolling back update.")
                        return {"status": 500, "message": "Failed to send verification code."}

                    tx.commit()
                    return {"status": 200, "message": "Verification code sent."}
                else:
                    # Меньше 3 минут — продлеваем срок существующего кода до 1 года
                    annual_expires = now_utc + datetime.timedelta(days=365)
                    update_query = f"""
                        PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8; DECLARE $expires AS Timestamp;
                        UPDATE users SET code_expires_at = $expires WHERE email = $email AND is_active = false;
                    """
                    tx.execute(session.prepare(update_query), {
                        '$email': email, '$expires': annual_expires
                    })
                    tx.commit()
                    return {"status": 200, "message": "Verification code already sent. Please check your email."}
            else:
                tx.rollback()
                return {"status": 409, "message": "User with this email already exists."}

        # Требуем user_name только при создании новой учётной записи
        if not user_name:
            tx.rollback()
            return {"status": 400, "message": "user_name is required for new account."}

        # НОВАЯ ЛОГИКА: Проверяем, есть ли приглашение в firms-database
        def check_pending_invitation(firm_session):
            check_tx = firm_session.transaction(ydb.SerializableReadWrite())
            check_query = """
                DECLARE $email AS Utf8;
                SELECT user_id FROM Users WHERE email = $email AND is_active = false LIMIT 1;
            """
            result = check_tx.execute(
                firm_session.prepare(check_query),
                {'$email': email}
            )
            check_tx.commit()
            return result[0].rows[0].user_id if result[0].rows else None

        # Проверяем наличие приглашения в firms-database
        try:
            existing_invitation_user_id = firms_pool.retry_operation_sync(check_pending_invitation)
        except Exception as e:
            logging.warning(f"Failed to check pending invitations for {email}: {e}")
            existing_invitation_user_id = None

        # Используем существующий user_id из приглашения или генерируем новый
        new_user_id = existing_invitation_user_id or str(uuid.uuid4())
        hashed_password = auth_utils.hash_password(password)
        now = datetime.datetime.now(pytz.utc)
        
        logging.info(f"Creating user with user_id: {new_user_id} (from invitation: {existing_invitation_user_id is not None})")

        if auto_confirm_mode:
            # РЕЖИМ БЫСТРОЙ РЕГИСТРАЦИИ
            logging.info(f"AUTO_CONFIRM_MODE: Creating active user {email} with user_id {new_user_id}")
            
            # 3. Создаем активного пользователя в jwt-database
            upsert_query = f"""
                PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}');
                DECLARE $user_id AS Utf8; DECLARE $email AS Utf8; DECLARE $password_hash AS Utf8; DECLARE $user_name AS Utf8; DECLARE $created_at AS Timestamp;
                UPSERT INTO users (user_id, email, password_hash, user_name, created_at, is_active)
                VALUES ($user_id, $email, $password_hash, $user_name, $created_at, true);
            """
            tx.execute(session.prepare(upsert_query), {
                '$user_id': new_user_id, '$email': email, '$password_hash': hashed_password,
                '$user_name': user_name, '$created_at': now
            })
            
            # 4. Обновляем user_id в ожидающих приглашениях в firms-database
            def update_invitations_in_firms(firm_session):
                logging.info(f"Searching for pending invitations for {email} in firms-database.")
                update_tx = firm_session.transaction(ydb.SerializableReadWrite())
                update_query_text = """
                    DECLARE $new_user_id AS Utf8; DECLARE $email AS Utf8;
                    UPDATE Users SET user_id = $new_user_id
                    WHERE email = $email AND is_active = false;
                """
                update_tx.execute(
                    firm_session.prepare(update_query_text),
                    {'$new_user_id': new_user_id, '$email': email}
                )
                update_tx.commit()
                logging.info(f"Updated pending invitations for {email} with new user_id {new_user_id}.")

            firms_pool.retry_operation_sync(update_invitations_in_firms)
            
            # 5. Генерируем токен
            token = auth_utils.generate_jwt(new_user_id, email)
            tx.commit()
            return {"status": 201, "token": token}
        else:
            # СТАНДАРТНЫЙ РЕЖИМ С КОДОМ
            if not result_sets[0].rows:
                # Если email не найден в базе, создаём новую неактивную запись
                code = str(random.randint(100000, 999999))
                expires = now + datetime.timedelta(minutes=10)
                
                # 4. Создаем неактивного пользователя в jwt-database
                upsert_query = f"""
                    PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}');
                    DECLARE $user_id AS Utf8; DECLARE $email AS Utf8; DECLARE $password_hash AS Utf8; DECLARE $user_name AS Utf8;
                    DECLARE $created_at AS Timestamp; DECLARE $code AS Utf8; DECLARE $expires AS Timestamp;
                    UPSERT INTO users (user_id, email, password_hash, user_name, created_at, verification_code, code_expires_at, is_active)
                    VALUES ($user_id, $email, $password_hash, $user_name, $created_at, $code, $expires, false);
                """
                tx.execute(session.prepare(upsert_query), {
                    '$user_id': new_user_id, '$email': email, '$password_hash': hashed_password,
                    '$user_name': user_name, '$created_at': now, '$code': code, '$expires': expires
                })
                
                # 5. Отправляем email (после коммита)
                email_sent = email_utils.send_verification_code(email, code)
                if not email_sent:
                    tx.rollback() # Откатываем создание пользователя, если письмо не ушло
                    logging.error(f"Failed to send verification code to {email}. Rolling back user creation.")
                    return {"status": 500, "message": "Failed to send verification code."}

                tx.commit()
                return {"status": 200, "message": "Verification code sent."}
            else:
                # Если email найден, но аккаунт не подтверждён, проверяем время
                existing_row = result_sets[0].rows[0]
                if not existing_row.is_active:
                    # Вычисляем время, прошедшее с предыдущей генерации кода
                    prev_expires = existing_row.code_expires_at
                    now_utc = datetime.datetime.now(pytz.utc)
                    if prev_expires:
                        prev_expires_utc = datetime.datetime.fromtimestamp(prev_expires / 1_000_000, tz=pytz.utc)
                        time_since_last_code = now_utc - prev_expires_utc
                        if time_since_last_code.total_seconds() >= 180:
                            # Если прошло ≥ 3 мин, генерируем новый код
                            new_code = str(random.randint(100000, 999999))
                            new_expires = now_utc + datetime.timedelta(minutes=10)

                            update_query = f"""
                                PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8; DECLARE $code AS Utf8; DECLARE $expires AS Timestamp;
                                UPDATE users SET verification_code = $code, code_expires_at = $expires WHERE email = $email AND is_active = false;
                            """
                            tx.execute(session.prepare(update_query), {
                                '$email': email, '$code': new_code, '$expires': new_expires
                            })

                            # Отправляем письмо ДО коммита, как и ранее
                            email_sent = email_utils.send_verification_code(email, new_code)
                            if not email_sent:
                                tx.rollback()
                                logging.error(f"Failed to send verification code to {email}. Rolling back update.")
                                return {"status": 500, "message": "Failed to send verification code."}

                            tx.commit()
                            return {"status": 200, "message": "Verification code sent."}
                        else:
                            # Если прошло < 3 мин, продлеваем срок существующего кода до 1 года
                            annual_expires = now_utc + datetime.timedelta(days=365)
                            update_query = f"""
                                PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8; DECLARE $expires AS Timestamp;
                                UPDATE users SET code_expires_at = $expires WHERE email = $email AND is_active = false;
                            """
                            tx.execute(session.prepare(update_query), {
                                '$email': email, '$expires': annual_expires
                            })
                            tx.commit()
                            return {"status": 200, "message": "Verification code already sent. Please check your email."}
                else:
                    tx.rollback()
                    return {"status": 409, "message": "User with this email already exists."}

    try:
        result = auth_pool.retry_operation_sync(transaction)
        
        if result.get("status") == 201:
            return {"statusCode": 201, "body": json.dumps({"token": result["token"]})}
        elif result.get("status") == 200:
             return {"statusCode": 200, "body": json.dumps({"message": result["message"]})}
        elif result.get("status") == 423:
            return {"statusCode": 423, "body": json.dumps({"message": result["message"]})}
        else:
            return {"statusCode": result.get("status", 500), "body": json.dumps({"message": result.get("message", "Internal Server Error")})}
            
    except Exception as e:
        logging.error(f"Critical error during registration request for user {email}: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}