import 'package:flutter/material.dart';
import 'file_card_widget.dart';

class FilesGridSection extends StatelessWidget {
  final List<Map<String, dynamic>> files;
  final String? title;
  final Function(Map<String, dynamic>)? onRemoveFile;
  final bool showRemoveButton;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final double childAspectRatio;

  const FilesGridSection({
    super.key,
    required this.files,
    this.title,
    this.onRemoveFile,
    this.showRemoveButton = false,
    this.crossAxisCount = 6,
    this.crossAxisSpacing = 8,
    this.mainAxisSpacing = 8,
    this.childAspectRatio = 0.85,
  });

  @override
  Widget build(BuildContext context) {
    // Фильтруем только облачные файлы
    final cloudFiles = files.where((file) => file['fileKey'] != null).toList();

    if (cloudFiles.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(title!, style: const TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
        ],
        Wrap(
          spacing: crossAxisSpacing,
          runSpacing: mainAxisSpacing,
          children:
              cloudFiles.map((file) {
                final card = FileCardWidget(
                  fileData: file,
                  showRemoveButton: showRemoveButton,
                  onRemove:
                      onRemoveFile != null ? () => onRemoveFile!(file) : null,
                );
                return SizedBox(width: 145, height: 170, child: card);
              }).toList(),
        ),
      ],
    );
  }
}
