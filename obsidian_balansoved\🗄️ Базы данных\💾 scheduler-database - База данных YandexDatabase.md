
Идентификатор - etngfus3jcus7m6bhkta
Путь к базе - /ru-central1/b1g2bgg0i9r8beucbthc/etngfus3jcus7m6bhkta
Эндпоинт - grpcs://ydb.serverless.yandexcloud.net:2135/?database=/ru-central1/b1g2bgg0i9r8beucbthc/etngfus3jcus7m6bhkta

---
# Таблицы
В данной базе данных хранятся записи о запланированных периодических событиях, которые должны вызывать определенные облачные функции.

#### Таблица: `ScheduledEvents`

| #   | Имя                    | Ключ | Тип         | Описание                                                                                     |
| :-- | :--------------------- | :--- | :---------- | :------------------------------------------------------------------------------------------- |
| 0   | `event_id`             | PK   | `Utf8`      | Уникальный идентификатор запланированного события (UUID).                                    |
| 1   | `function_id`          |      | `Utf8`      | ID облачной функции, которая будет вызвана (например, функция создания задачи).              |
| 2   | `custom_identifier`    |      | `Utf8`      | Идентификатор для связи события с конкретной сущностью (например, ID повторяющейся задачи).  |
| 3   | `is_annual`            |      | `Bool`      | Флаг, указывающий, является ли событие ежегодным.                                            |
| 4   | `execution_dates_json` |      | `Json`      | JSON-массив с датами и временем следующих выполнений в формате ISO.                          |
| 5   | `request_body_json`    |      | `Json`      | JSON-объект, который будет передан в качестве тела запроса (`body`) в вызываемую функцию.    |
| 6   | `is_active`            |      | `Bool`      | Флаг, указывающий, активно ли событие.                                                       |
| 7   | `created_at`           |      | `Timestamp` | Время создания записи о событии.                                                             |
| 8   | `updated_at`           |      | `Timestamp` | Время последнего обновления записи.                                                          |
| 9   | `request_headers_json` |      | `Json`      | JSON-объект с дополнительными HTTP-заголовками для вызова функции (например, Authorization). |
| 10  | `last_invoked_at`      |      | `Timestamp` | Время последнего успешного вызова события.                                                   |


---
#### YQL для управления таблицей

**YQL для создания таблицы:**
```yql
CREATE TABLE `ScheduledEvents` (
    event_id Utf8,
    function_id Utf8,
    custom_identifier Utf8,
    is_annual Bool,
    execution_dates_json Json,
    request_body_json Json,
    is_active Bool,
    created_at Timestamp,
    updated_at Timestamp,
    request_headers_json Json,
    last_invoked_at Timestamp,
    PRIMARY KEY (event_id)
);
```