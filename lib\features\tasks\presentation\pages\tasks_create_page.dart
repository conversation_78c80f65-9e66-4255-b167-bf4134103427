import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_create_card.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/router.dart';

@RoutePage()
class TasksCreatePage extends StatelessWidget {
  const TasksCreatePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.router.navigate(TasksRoute()),
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              Text(
                'Создание задачи',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: [
                TaskCreateCard(
                  onCancel: () => context.router.navigate(TasksRoute()),
                  onCreated: (task) {
                    final firm =
                        context.read<ActiveFirmCubit>().state.selectedFirm;
                    if (firm != null) {
                      context.read<TasksCubit>().saveTask(firm.id, task);
                    }
                    context.router.navigate(TasksRoute());
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
