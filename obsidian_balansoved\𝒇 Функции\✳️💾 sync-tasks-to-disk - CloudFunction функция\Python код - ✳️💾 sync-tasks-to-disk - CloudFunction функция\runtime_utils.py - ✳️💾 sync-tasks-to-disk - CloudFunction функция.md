
```python
import json
import os
import re
import time
import threading
import datetime as dt
import requests
import logging

# --- Инициализация и Константы ---
LOG_MESSAGES = []
LOG_LOCK = threading.Lock()
TICK, CROSS, INFO, WARN = "✓", "✗", "→", "!"

MAX_RETRIES = 3
RETRY_BASE_DELAY_SEC = 3
SYNC_FREQUENCY_DAYS = int(os.environ.get("SYNC_FREQUENCY_DAYS", 7))
YADISK_BASE_FOLDER = os.environ.get("YADISK_BASE_FOLDER", "balansoved enterprise")

# --- Получение IAM-токена для вызова приватных функций ---
IAM_TOKEN_CACHE = {"token": None, "expires_at": 0}
METADATA_URL = "http://169.254.169.254/computeMetadata/v1/instance/service-accounts/default/token"

def get_iam_token() -> str:
    """Возвращает действующий IAM-токен, кешируя его до истечения."""
    now_ts = time.time()
    if IAM_TOKEN_CACHE["token"] and now_ts < IAM_TOKEN_CACHE["expires_at"]:
        return IAM_TOKEN_CACHE["token"]

    try:
        resp = requests.get(METADATA_URL, headers={"Metadata-Flavor": "Google"}, timeout=2)
        resp.raise_for_status()
        data = resp.json()
        IAM_TOKEN_CACHE["token"] = data["access_token"]
        IAM_TOKEN_CACHE["expires_at"] = now_ts + data.get("expires_in", 300) - 60
        return IAM_TOKEN_CACHE["token"]
    except Exception as e:
        log_message(f"Не удалось получить IAM-токен: {e}", "WARN")
        return ""

# --- ID Целевых функций ---
FUNCTION_ID_EDIT_INTEGRATIONS = os.environ.get("FUNCTION_ID_EDIT_INTEGRATIONS")
FUNCTION_ID_EDIT_EMPLOYEE = os.environ.get("FUNCTION_ID_EDIT_EMPLOYEE")
FUNCTION_ID_EDIT_TASK = os.environ.get("FUNCTION_ID_EDIT_TASK")
FUNCTION_ID_EDIT_CLIENT = os.environ.get("FUNCTION_ID_EDIT_CLIENT")
FUNCTION_ID_TARIFFS_AND_STORAGE_MANAGER = os.environ.get("FUNCTION_ID_TARIFFS_AND_STORAGE_MANAGER")
FUNCTION_ID_GET_USER_DATA = os.environ.get("FUNCTION_ID_GET_USER_DATA")

# --- Логирование ---
def log_message(message: str, level: str = "INFO"):
    timestamp = dt.datetime.now().strftime("%H:%M:%S")
    with LOG_LOCK:
        LOG_MESSAGES.append(f"[{timestamp}] [{level.upper()}] {message}")

def print_final_log():
    log_message("="*80)
    log_message("ПОЛНЫЙ ЛОГ ВЫПОЛНЕНИЯ:")
    for msg in LOG_MESSAGES:
        logging.info(msg) # Выводим в стандартный лог функции
    log_message("="*80)

def log_progress(message: str):
    clean_message = re.sub(r'\x1b\[[0-9;]*m', '', message).rstrip()
    if clean_message:
        log_message(clean_message, level="PROGRESS")

# --- Вызов функций ---
def invoke_function(function_id: str, payload: dict, user_jwt: str) -> dict | None:
    """Вызывает облачную функцию по HTTPS. Требует, чтобы функция была публичной
    либо авторизована через переданный JWT (Authorization header)."""

    if not function_id:
        log_message(f"ID функции не указан для вызова с payload: {str(payload)[:100]}", "ERROR")
        return None

    # Составляем URL облачной функции
    function_url = f"https://functions.yandexcloud.net/{function_id}"

    iam_token = get_iam_token()

    # HTTP-заголовки для вызова облачной функции
    http_headers = {"Content-Type": "application/json"}
    if iam_token:
        http_headers["Authorization"] = f"Bearer {iam_token}"

    # Добавляем пользовательский JWT прямо в HTTP-заголовок, как это делает scheduler-trigger
    http_headers["X-Forwarded-Authorization"] = f"Bearer {user_jwt}"

    invoke_event = json.dumps(payload)

    for attempt in range(MAX_RETRIES):
        try:
            resp = requests.post(function_url, data=invoke_event, headers=http_headers, timeout=30)

            if 200 <= resp.status_code < 300:
                try:
                    return resp.json()
                except ValueError:
                    log_message(f"Вызов {function_id} завершился без JSON-тела.", "WARN")
                    return {}
            else:
                # Логируем тело ответа, если возможно
                body_preview = resp.text[:200] if resp.text else "<empty>"
                log_message(f"Ошибка вызова {function_id}. Статус: {resp.status_code}. Тело: {body_preview}", "WARN")

        except requests.exceptions.RequestException as e:
            log_message(f"Исключение при вызове {function_id}: {e}", "WARN")

        # back-off перед повтором
        if attempt + 1 < MAX_RETRIES:
            time.sleep(RETRY_BASE_DELAY_SEC * (2 ** attempt))

    log_message(f"Все попытки вызова {function_id} провалены.", "ERROR")
    return None
```