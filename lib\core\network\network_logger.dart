import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class NetworkLogger {
  /// Функция для декодирования Unicode escape-последовательностей в нормальные русские символы
  static String _decodeUnicodeString(String input) {
    try {
      // Заменяем \uXXXX на соответствующие символы
      return input.replaceAllMapped(RegExp(r'\\u([0-9a-fA-F]{4})'), (
        Match match,
      ) {
        final hexCode = match.group(1)!;
        final codeUnit = int.parse(hexCode, radix: 16);
        return String.fromCharCode(codeUnit);
      });
    } catch (e) {
      // Если декодирование не удалось, возвращаем исходную строку
      return input;
    }
  }

  /// Функция для красивого форматирования JSON с русскими символами
  static String _formatJsonWithCyrillic(dynamic json) {
    try {
      String jsonString;
      if (json is String) {
        // Если уже строка, пытаемся распарсить как JSON
        try {
          final parsed = jsonDecode(json);
          jsonString = const JsonEncoder.withIndent('  ').convert(parsed);
        } catch (_) {
          // Если не JSON, возвращаем как есть
          jsonString = json;
        }
      } else {
        // Конвертируем объект в красивый JSON
        jsonString = const JsonEncoder.withIndent('  ').convert(json);
      }

      // Декодируем Unicode escape-последовательности
      return _decodeUnicodeString(jsonString);
    } catch (e) {
      return json.toString();
    }
  }

  /// Безопасный debugPrint с декодированием русских символов
  static void _safePrint(String message) {
    final decodedMessage = _decodeUnicodeString(message);
    debugPrint(decodedMessage);
  }

  // === Универсальные методы логгирования для использования в других частях приложения ===

  /// Универсальный метод для логгирования с декодированием русских символов
  static void safePrint(String message) {
    if (!kDebugMode) return;
    _safePrint(message);
  }

  /// Логгирование JSON с красивым форматированием и русскими символами
  static void printJson(String prefix, dynamic json) {
    if (!kDebugMode) return;
    final formatted = _formatJsonWithCyrillic(json);
    _safePrint('$prefix $formatted');
  }

  /// Логгирование ошибки с декодированием
  static void printError(
    String prefix,
    dynamic error, [
    StackTrace? stackTrace,
  ]) {
    if (!kDebugMode) return;
    final errorMessage = error.toString();
    _safePrint('🔴 $prefix ${_decodeUnicodeString(errorMessage)}');
    // For ClientException, print more details if available
    if (error is http.ClientException) {
      _safePrint('🔴 ClientException URI: ${error.uri}');
    }
    if (stackTrace != null) {
      _safePrint('🔴 $prefix Stack trace:\n$stackTrace');
    }
  }

  /// Логгирование успеха
  static void printSuccess(String message) {
    if (!kDebugMode) return;
    _safePrint('🟢 $message');
  }

  /// Логгирование информации
  static void printInfo(String message) {
    if (!kDebugMode) return;
    _safePrint('🔵 $message');
  }

  /// Логгирование предупреждения
  static void printWarning(String message) {
    if (!kDebugMode) return;
    _safePrint('🟡 $message');
  }

  /// Логгирование отладочной информации (DEBUG уровень)
  static void printDebug(String message) {
    if (!kDebugMode) return;
    _safePrint('🔍 [DEBUG] $message');
  }

  static void logHttpRequest({
    required String method,
    required String url,
    Map<String, String>? headers,
    String? body,
  }) {
    if (!kDebugMode) return;

    _safePrint('📤 [HTTP] $method Request');
    _safePrint('URL: $url');

    if (headers != null && headers.isNotEmpty) {
      _safePrint('Headers:');
      headers.forEach((key, value) {
        _safePrint('  $key: ${_decodeUnicodeString(value)}');
      });
    }

    if (body != null && body.isNotEmpty) {
      final formattedBody = _formatJsonWithCyrillic(body);
      _safePrint('Body: $formattedBody');
    }

    _safePrint('--- End Request ---');
  }

  static void logHttpResponse({
    required int statusCode,
    Map<String, String>? headers,
    required String body,
    String? error,
  }) {
    if (!kDebugMode) return;

    if (error != null) {
      _safePrint('📥 [HTTP] Error Response');
      _safePrint('Error: ${_decodeUnicodeString(error)}');
    } else {
      _safePrint('📥 [HTTP] Response');
      _safePrint('Status Code: $statusCode');
    }

    if (headers != null && headers.isNotEmpty) {
      _safePrint('Headers:');
      headers.forEach((key, value) {
        _safePrint('  $key: ${_decodeUnicodeString(value)}');
      });
    }

    final formattedBody = _formatJsonWithCyrillic(body);
    _safePrint('Body: $formattedBody');
    _safePrint('--- End Response ---');
  }

  static void logDioRequest(RequestOptions options) {
    if (!kDebugMode) return;

    _safePrint('📤 [DIO] ${options.method} Request');
    _safePrint('URL: ${options.uri}');

    if (options.headers.isNotEmpty) {
      _safePrint('Headers:');
      options.headers.forEach((key, value) {
        _safePrint('  $key: ${_decodeUnicodeString(value.toString())}');
      });
    }

    if (options.data != null) {
      final formattedData = _formatJsonWithCyrillic(options.data);
      _safePrint('Data: $formattedData');
    }

    if (options.queryParameters.isNotEmpty) {
      _safePrint('Query Parameters:');
      options.queryParameters.forEach((key, value) {
        _safePrint('  $key: ${_decodeUnicodeString(value.toString())}');
      });
    }

    _safePrint('--- End Request ---');
  }

  static void logDioResponse(Response response) {
    if (!kDebugMode) return;

    _safePrint('📥 [DIO] Response');
    _safePrint('Status Code: ${response.statusCode}');
    _safePrint(
      'Status Message: ${_decodeUnicodeString(response.statusMessage ?? '')}',
    );

    if (response.headers.map.isNotEmpty) {
      _safePrint('Headers:');
      response.headers.map.forEach((key, value) {
        final decodedValues = value
            .map((v) => _decodeUnicodeString(v))
            .join(', ');
        _safePrint('  $key: $decodedValues');
      });
    }

    final formattedData = _formatJsonWithCyrillic(response.data);
    _safePrint('Data: $formattedData');
    _safePrint('--- End Response ---');
  }

  static void logDioError(DioException error) {
    if (!kDebugMode) return;

    _safePrint('❌ [DIO] Error');
    _safePrint('Type: ${error.type}');
    _safePrint('Message: ${_decodeUnicodeString(error.message ?? '')}');

    _safePrint('Request URL: ${error.requestOptions.uri}');
    _safePrint('Request Method: ${error.requestOptions.method}');

    if (error.response != null) {
      _safePrint('Response Status: ${error.response?.statusCode}');
      final formattedResponseData = _formatJsonWithCyrillic(
        error.response?.data,
      );
      _safePrint('Response Data: $formattedResponseData');
    }

    _safePrint('Stack Trace: ${error.stackTrace}');
    _safePrint('--- End Error ---');
  }

  static void logClientException(
    http.ClientException error, {
    String? url,
    String? method,
  }) {
    if (!kDebugMode) return;

    _safePrint('❌ [HTTP] ClientException');
    if (method != null) _safePrint('Method: $method');
    if (url != null) _safePrint('URL: $url');
    _safePrint('Message: ${_decodeUnicodeString(error.message)}');
    _safePrint('URI: ${error.uri}');
    _safePrint('--- End Error ---');
  }

  static void logNetworkTimeout({
    String? url,
    String? method,
    Duration? timeout,
  }) {
    if (!kDebugMode) return;

    _safePrint('⏰ [NETWORK] Timeout');
    if (method != null) _safePrint('Method: $method');
    if (url != null) _safePrint('URL: $url');
    if (timeout != null) _safePrint('Timeout Duration: ${timeout.inSeconds}s');
    _safePrint('--- End Timeout ---');
  }

  static void logCorsError({required String url, String? method}) {
    if (!kDebugMode) return;

    _safePrint('🚫 [CORS] Cross-Origin Error');
    if (method != null) _safePrint('Method: $method');
    _safePrint('URL: $url');
    _safePrint('Возможные причины:');
    _safePrint('1. Сервер не настроен для CORS');
    _safePrint('2. Неправильные заголовки Access-Control-Allow-*');
    _safePrint('3. Префlight запрос отклонен');
    _safePrint('4. Неподдерживаемый HTTP метод');
    _safePrint('--- End CORS Error ---');
  }
}
