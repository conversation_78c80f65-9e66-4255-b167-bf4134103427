import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_versions_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';

class ClientVersionSelector extends StatefulWidget {
  final String firmId;
  final String clientName;
  final Function(ClientEntity) onVersionSelected;

  const ClientVersionSelector({
    super.key,
    required this.firmId,
    required this.clientName,
    required this.onVersionSelected,
  });

  @override
  State<ClientVersionSelector> createState() => _ClientVersionSelectorState();
}

class _ClientVersionSelectorState extends State<ClientVersionSelector> {
  @override
  void initState() {
    super.initState();
    // Загружаем версии клиента при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.firmId.isNotEmpty && widget.clientName.isNotEmpty) {
        context.read<ClientVersionsCubit>().fetchVersions(
          widget.firmId,
          widget.clientName,
        );
      }
    });
  }

  List<DropdownMenuItem<String>> _buildDropdownItems(
    BuildContext context,
    ClientVersionsState state,
  ) {
    final theme = Theme.of(context);
    final items = <DropdownMenuItem<String>>[];

    // Добавляем существующие версии
    for (final version in state.versions) {
      final date = version.manualCreationDate ?? version.creationDate;
      final dateStr =
          date != null
              ? DateFormat('dd.MM.yyyy HH:mm').format(date)
              : 'Без даты';

      final label = version.isActual ? 'Актуальная ($dateStr)' : dateStr;

      // Создаем уникальный идентификатор версии: client_id + manual_creation_date
      final versionKey = '${version.id}_${(version.manualCreationDate ?? version.creationDate)?.millisecondsSinceEpoch ?? 0}';
      
      items.add(
        DropdownMenuItem<String>(
          value: versionKey,
          child: Row(
            children: [
              // Кнопка редактирования даты
              IconButton(
                icon: Icon(
                  Icons.edit_calendar_outlined,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                onPressed: () {
                  _showEditDateDialog(context, version);
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 24,
                  minHeight: 24,
                ),
              ),
              const SizedBox(width: 4),
              // Кнопка удаления
              if (state.versions.length >
                  1) // Не показываем кнопку удаления, если версия одна
                IconButton(
                  icon: Icon(
                    Icons.delete_outline,
                    size: 16,
                    color: theme.colorScheme.error,
                  ),
                  onPressed: () {
                    _showDeleteConfirmation(context, version);
                  },
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                )
              else
                const SizedBox(width: 24),
              const SizedBox(width: 8),
              // Текст версии
              Expanded(
                child: Text(
                  label,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontWeight:
                        version.isActual ? FontWeight.bold : FontWeight.normal,
                    color:
                        version.isActual
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Добавляем опцию "Новая версия"
    items.add(
      DropdownMenuItem<String>(
        value: 'new_version',
        child: Row(
          children: [
            Icon(
              Icons.add_circle_outline,
              size: 16,
              color: theme.colorScheme.secondary,
            ),
            const SizedBox(width: 8),
            Text(
              'Новая версия',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.secondary,
              ),
            ),
          ],
        ),
      ),
    );

    return items;
  }

  void _showDeleteConfirmation(BuildContext context, ClientEntity version) {
    final date = version.manualCreationDate ?? version.creationDate;
    final dateStr =
        date != null ? DateFormat('dd.MM.yyyy HH:mm').format(date) : 'Без даты';

    showDialog<bool>(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text('Удаление версии'),
            content: Text('Вы уверены, что хотите удалить версию от $dateStr?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx, false),
                child: const Text('Отмена'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(ctx, true),
                child: const Text('Удалить'),
              ),
            ],
          ),
    ).then((confirmed) {
      if (confirmed == true && date != null) {
        context.read<ClientVersionsCubit>().deleteVersion(
          widget.firmId,
          version.id,
          date,
        );
      }
    });
  }

  void _showEditDateDialog(BuildContext context, ClientEntity version) {
    final currentDate = version.manualCreationDate ?? version.creationDate ?? DateTime.now();
    final dateController = TextEditingController(
      text: DateFormat('dd.MM.yyyy').format(currentDate),
    );
    final timeController = TextEditingController(
      text: DateFormat('HH:mm').format(currentDate),
    );

    showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Изменить дату версии'),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: dateController,
                decoration: const InputDecoration(
                  labelText: 'Дата (дд.мм.гггг)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.datetime,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: timeController,
                decoration: const InputDecoration(
                  labelText: 'Время (чч:мм)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.datetime,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx, false),
            child: const Text('Отмена'),
          ),
          TextButton(
            onPressed: () {
              try {
                final dateParts = dateController.text.split('.');
                final timeParts = timeController.text.split(':');
                
                if (dateParts.length == 3 && timeParts.length == 2) {
                  final newDate = DateTime(
                    int.parse(dateParts[2]), // год
                    int.parse(dateParts[1]), // месяц
                    int.parse(dateParts[0]), // день
                    int.parse(timeParts[0]), // час
                    int.parse(timeParts[1]), // минута
                  );
                  Navigator.pop(ctx, true);
                  context.read<ClientVersionsCubit>().updateVersionDate(
                    widget.firmId,
                    version,
                    newDate,
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Неверный формат даты или времени'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Ошибка при парсинге даты'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Сохранить'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ClientVersionsCubit, ClientVersionsState>(
      listener: (context, state) {
        if (state.selectedVersion != null) {
          widget.onVersionSelected(state.selectedVersion!);
        }
      },
      builder: (context, state) {
        if (state.isLoading) {
          return const SizedBox(
            width: 400,
            height: 40,
            child: LoadingTile(height: 40, width: 400),
          );
        }

        if (state.error != null) {
          return Container(
            width: 400,
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              'Ошибка загрузки версий',
              style: TextStyle(color: Colors.red, fontSize: 12),
            ),
          );
        }

        if (state.versions.isEmpty) {
          return Container(
            width: 400,
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              'Нет версий',
              style: TextStyle(color: Colors.grey, fontSize: 12),
            ),
          );
        }

        return SizedBox(
          width: 400,
          child: DropdownButtonFormField<String>(
            value: state.selectedVersion != null 
                ? '${state.selectedVersion!.id}_${(state.selectedVersion!.manualCreationDate ?? state.selectedVersion!.creationDate)?.millisecondsSinceEpoch ?? 0}'
                : null,
            decoration: const InputDecoration(
              labelText: 'Версия клиента',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: _buildDropdownItems(context, state),
            onChanged: (String? selectedId) {
              if (selectedId == 'new_version') {
                // Создаем новую версию на основе текущей выбранной
                if (state.selectedVersion != null) {
                  context.read<ClientVersionsCubit>().createNewVersion(
                    widget.firmId,
                    state.selectedVersion!,
                  );
                }
              } else if (selectedId != null) {
                // Выбираем существующую версию по составному ключу
                final version = state.versions.firstWhere(
                  (v) => '${v.id}_${(v.manualCreationDate ?? v.creationDate)?.millisecondsSinceEpoch ?? 0}' == selectedId,
                );
                context.read<ClientVersionsCubit>().selectVersion(version);
              }
            },
            isExpanded: true,
          ),
        );
      },
    );
  }
}
