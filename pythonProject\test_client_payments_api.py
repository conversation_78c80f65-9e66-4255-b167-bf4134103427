import requests
import json
import sys
from colorama import init, Fore, Style

# Инициализируем colorama (autoreset=True сбрасывает цвет после каждого print)
init(autoreset=True)

# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
# URL для управления профилями клиентов (используется для setup/teardown)
API_CLIENTS_URL = "https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net/manage"
# URL для нового функционала управления оплатами
API_PAYMENTS_URL = "https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net/payments/manage"

FIRM_ID = "9a33483b-dfad-44a3-a36d-102b498ec0ef"
LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# Данные для теста
TEST_YEAR = 2025
TEST_PERIOD = f"{TEST_YEAR}-07"

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL
PAUSE_ICON = Fore.CYAN + "⏸️" + Style.RESET_ALL


def run_test_step(title: str, url: str, payload: dict, headers: dict, expected_status: int):
    """Выполняет один шаг теста, выводит результат и возвращает ответ в случае успеха."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ")

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=20)

        if response.status_code == expected_status:
            print(f"{TICK} (Статус: {response.status_code})")
            return response
        else:
            print(f"{CROSS} (Ожидался статус {expected_status}, получен {response.status_code})")
            print(Fore.RED + "  Текст ошибки:")
            try:
                error_json = response.json()
                print(Fore.RED + json.dumps(error_json, indent=4, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        return None


if __name__ == "__main__":
    print("\n--- Начало комплексного тестирования Client Payments API ---\n")

    client_id = None  # Инициализируем переменную для ID временного клиента

    try:
        # --- Шаг 1: Аутентификация ---
        login_response = run_test_step("Шаг 1: Получение JWT токена", API_AUTH_URL, LOGIN_PAYLOAD, DEFAULT_HEADERS, 200)
        if not login_response:
            sys.exit(f"\n{CROSS} Критическая ошибка: не удалось войти в систему. Тестирование прервано.")

        jwt_token = login_response.json().get("token")
        auth_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {jwt_token}"}

        # --- Шаг 2: Подготовка - создание временного клиента ---
        create_client_payload = {
            "firm_id": FIRM_ID, "action": "UPSERT", "payload": {"client_name": "Payment Test Client"}
        }
        create_client_response = run_test_step("Шаг 2: Создание временного клиента для теста", API_CLIENTS_URL,
                                               create_client_payload, auth_headers, 201)
        if not create_client_response:
            sys.exit(f"\n{CROSS} Критическая ошибка: не удалось создать временного клиента. Тестирование прервано.")

        client_id = create_client_response.json().get("client_id")

        # --- Шаг 3: Создание записи об оплате (UPSERT) ---
        create_payment_payload = {
            "firm_id": FIRM_ID,
            "action": "UPSERT",
            "payload": {
                "client_id": client_id,
                "period": TEST_PERIOD,
                "actual_amount_paid": 5000.00,
                "tariff_annual_amount": 60000.00
            }
        }
        run_test_step("Шаг 3: Создание записи об оплате (UPSERT)", API_PAYMENTS_URL, create_payment_payload,
                      auth_headers, 201)

        # --- Шаг 4: Обновление записи об оплате (UPSERT) ---
        update_payment_payload = {
            "firm_id": FIRM_ID,
            "action": "UPSERT",
            "payload": {
                "client_id": client_id,
                "period": TEST_PERIOD,
                "actual_amount_paid": 5555.55
            }
        }
        run_test_step("Шаг 4: Обновление записи об оплате (UPSERT)", API_PAYMENTS_URL, update_payment_payload,
                      auth_headers, 200)

        # --- Шаг 5: Получение данных по конкретному клиенту за год ---
        get_one_payload = {"firm_id": FIRM_ID, "action": "GET", "year": TEST_YEAR, "client_id": client_id}
        get_one_response = run_test_step(f"Шаг 5: Получение оплат для клиента за {TEST_YEAR} год", API_PAYMENTS_URL,
                                         get_one_payload, auth_headers, 200)
        if get_one_response:
            data = get_one_response.json().get("data", [])
            if data and data[0].get("actual_amount_paid") == 5555.55:
                print(f"      {TICK} Проверка данных: Сумма оплаты корректно обновлена.")
            else:
                print(f"      {CROSS} Проверка данных: Сумма оплаты не была обновлена или данные не найдены.")

        # --- Шаг 6: Получение данных по всем клиентам за год ---
        get_all_payload = {"firm_id": FIRM_ID, "action": "GET", "year": TEST_YEAR}
        run_test_step(f"Шаг 6: Получение всех оплат фирмы за {TEST_YEAR} год", API_PAYMENTS_URL, get_all_payload,
                      auth_headers, 200)

        # --- ПАУЗА ПЕРЕД УДАЛЕНИЕМ ЗАПИСИ ОБ ОПЛАТЕ ---
        print(
            f"\n{PAUSE_ICON} Данные созданы и обновлены. Нажмите Enter для перехода к фазе удаления записи об оплате...")
        input()

        # --- Шаг 7: Удаление записи об оплате ---
        delete_payload = {"firm_id": FIRM_ID, "action": "DELETE", "client_id": client_id, "period": TEST_PERIOD}
        run_test_step("Шаг 7: Удаление записи об оплате", API_PAYMENTS_URL, delete_payload, auth_headers, 200)

        # --- Шаг 8: Проверка удаления ---
        verify_delete_response = run_test_step(f"Шаг 8: Проверка удаления (ожидается пустой список)", API_PAYMENTS_URL,
                                               get_one_payload, auth_headers, 200)
        if verify_delete_response:
            data = verify_delete_response.json().get("data", [])
            if not data:
                print(f"      {TICK} Проверка данных: Список оплат пуст, запись успешно удалена.")
            else:
                print(f"      {CROSS} Проверка данных: Запись об оплате не была удалена.")

    finally:
        # --- ПАУЗА ПЕРЕД ФИНАЛЬНОЙ ОЧИСТКОЙ ---
        print(f"\n{PAUSE_ICON} Тесты завершены. Нажмите Enter для финальной очистки (удаления временного клиента)...")
        input()

        # --- Шаг 9: Очистка - удаление временного клиента ---
        if client_id:
            delete_client_payload = {"firm_id": FIRM_ID, "action": "DELETE", "client_id": client_id}
            run_test_step("Шаг 9: Очистка (удаление временного клиента)", API_CLIENTS_URL, delete_client_payload,
                          auth_headers, 200)

        print("\n--- Тестирование Client Payments API успешно завершено ---")