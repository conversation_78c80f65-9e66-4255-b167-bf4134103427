part of 'clients_cubit.dart';

class ClientsState extends Equatable {
  final List<ClientEntity> clients;
  final bool isLoading;
  final String? error;
  final bool noAccess;

  const ClientsState({
    required this.clients,
    required this.isLoading,
    this.error,
    this.noAccess = false,
  });

  const ClientsState.initial()
    : this(clients: const [], isLoading: false, noAccess: false);

  ClientsState copyWith({
    List<ClientEntity>? clients,
    bool? isLoading,
    String? error,
    bool? noAccess,
  }) {
    return ClientsState(
      clients: clients ?? this.clients,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      noAccess: noAccess ?? this.noAccess,
    );
  }

  @override
  List<Object?> get props => [clients, isLoading, error, noAccess];
}
