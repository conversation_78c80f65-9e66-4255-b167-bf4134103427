
Идентификатор - d4euafv5ijaums363e84
Описание - 🔔 Управляет подписками и отправляет Push-уведомления. **Использует гибридный подход**: Web Push через Yandex CNS, RuStore Push - напрямую.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`
	-> `action` (string, **обязательно**): `ADD`, `DELETE`, `GET_LIST`, `SEND`.
	-> **Для `ADD`**:
		- `push_token` (string, **обязательно**): JSON-строка для Web или токен устройства для RuStore.
		- `device_info` (object, необязательно): Дополнительная информация об устройстве.
	-> **Для `DELETE`**:
		- `push_token` (string, **обязательно**): Токен подписки, которую нужно удалить. **Передается в теле запроса.**
	-> **Для `SEND`**:
		- `user_id_to_notify` (string, **обязательно**): ID пользователя для отправки.
		- `payload` (object, **обязательно**): `{"title": "...", "body": "..."}`.

Внутренняя работа:
    -> Логирование события и контекста.
    -> Получение 'action' из контекста API Gateway или тела запроса.
    -> Парсинг тела запроса.
    -> Авторизация:
        -> Извлечение заголовка авторизации (учитывая внутренние вызовы через X-Forwarded-Authorization).
        -> Проверка JWT и извлечение user_id для пользовательских запросов; для внутренних вызовов user_id не требуется.
    -> Инициализация драйвера и пула сессий YDB для базы endpoints.
    -> Маршрутизация по 'action':
        -> **ADD** (требует авторизации):
            -> Получение push_token и device_info.
            -> Определение платформы (WEB или RUSTORE) по формату токена.
            -> Вычисление хэша токена.
            -> Если WEB: Создание или активация endpoint в Yandex CNS, получение endpoint_arn.
            -> UPSERT записи в UserEndpoints с данными (хэш, user_id, платформа, токен, ARN если WEB, enabled=true, device_info, timestamps).
        -> **DELETE** (требует авторизации):
            -> Получение push_token.
            -> Вычисление хэша и поиск записи в UserEndpoints.
            -> Проверка, что user_id совпадает с запрашивающим.
            -> Если WEB и есть ARN: Удаление endpoint из CNS (игнорируя NotFound).
            -> Удаление записи из UserEndpoints.
        -> **GET_LIST** (требует авторизации):
            -> Запрос всех записей для user_id из UserEndpoints (с индексом).
            -> Парсинг device_info_json и возврат списка в JSON.
        -> **SEND** (не требует пользовательской авторизации, для внутренних вызовов):
            -> Инициализация драйвера и пула для базы notices.
            -> Получение user_id_to_notify и payload (title, body).
            -> Создание таблицы notices_{user_id} если не существует (с полной схемой).
            -> UPSERT уведомления в notices_{user_id} (notice_id, title, provider='приложение', additional_info_json с body, created_at, is_delivered=false).
            -> Получение активных подписок (is_enabled=true) для user_id.
            -> Для каждой подписки:
                -> Если WEB: Формирование payload и публикация в CNS через endpoint_arn.
                -> Если RUSTORE: Отправка через rustore_push_utils.
                -> Сбор невалидных токенов (EndpointDisabled или UNREGISTERED).
            -> Если есть невалидные: Пакетное обновление UserEndpoints, установка is_enabled=false для их хэшей.
    -> Обработка исключений: AuthError (403), LogicError (400), NotFoundError (404), другие (500).

На выходе:
    -> 201 Created для ADD с сообщением успеха.
    -> 200 OK для DELETE, GET_LIST (с данными), SEND (с сообщением о количестве отправленных).
    -> 400 Bad Request для логических ошибок (например, отсутствие параметров).
    -> 403 Forbidden для ошибок авторизации.
    -> 404 Not Found если подписка не найдена.
    -> 500 Internal Server Error для непредвиденных ошибок.

---
#### Зависимости и окружение
-   **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `utils/rustore_push_utils.py`
-   **Переменные окружения**:
    -   `YDB_ENDPOINT_ENDPOINTS`, `YDB_DATABASE_ENDPOINTS`
    -   `YDB_ENDPOINT_NOTICES`, `YDB_DATABASE_NOTICES`
    -   `JWT_SECRET`, `SA_KEY_FILE`
    -   `CNS_PLATFORM_APP_ARN_WEB`: ARN **только** для Web Push-приложения в Yandex Notification Service.
    -   `RUSTORE_PROJECT_ID`: ID проекта из консоли RuStore.
    -   `RUSTORE_SERVICE_TOKEN`: Сервисный токен для авторизации в RuStore.
    -   `CNS_REGION`: Регион, в котором создан сервис (`ru-central1`).
    -   `CNS_ENDPOINT_URL`: URL эндпоинта Yandex Notification Service (`https://notifications.yandexcloud.net`).
    -   `STATIC_ACCESS_KEY_ID`
    -   `STATIC_SECRET_ACCESS_KEY`

---
#### index.py
```python
# index.py

import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils, request_parser
import pprint

import add_endpoint
import delete_endpoint
import get_endpoint
import send_notification
from custom_errors import AuthError, LogicError, NotFoundError

logging.getLogger().setLevel(logging.DEBUG)

def _log_event_context(event, context):
    try:
        logging.debug("RAW EVENT: %s", json.dumps(event, default=str)[:10000])
    except Exception as e:
        logging.debug("RAW EVENT (non-json serialisable): %s", event)
    if context is not None:
        logging.debug(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s",
            getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None),
        )

def handler(event, context):
    _log_event_context(event, context)
    try:
        # 1) Пытаемся получить action из контекста API Gateway (работает для внешних вызовов)
        action = (
            event.get('requestContext', {})
                 .get('apiGateway', {})
                 .get('operationContext', {})
                 .get('action')
        )

        # 2) Разбираем тело запроса (данные понадобятся в любом случае)
        data = request_parser.parse_request_body(event)

        # 3) Если из контекста ничего не получили (внутренний вызов триггера), берём action из тела
        if not action:
            action = data.get('action')

        logging.debug("Action: %s, Parsed body: %s", action, data)
        if not action:
            raise LogicError("Action is a required parameter.")

        # --------- АВТОРИЗАЦИЯ ---------
        user_id = None
        headers = event.get('headers', {}) or {}
        
        # Для внутренних вызовов (от scheduler) JWT пользователя будет в X-Forwarded-Authorization.
        # Для прямых вызовов (от API-GW) - в стандартном Authorization.
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization', None))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))

        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            # Проверяем, является ли токен пользовательским JWT
            user_payload = auth_utils.verify_jwt(token)
            if user_payload and 'user_id' in user_payload:
                user_id = user_payload['user_id']
                logging.debug("Authenticated user_id=%s from user JWT", user_id)
            else:
                logging.debug("Auth header found, but it's not a valid user JWT. Treating as system call.")
        else:
            # Вызов без заголовка Authorization тоже считаем внутренним
            logging.debug("No user Authorization header found. Treating as internal system call.")

        # --- Маршрутизация ---
        endpoints_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_ENDPOINTS"], os.environ["YDB_DATABASE_ENDPOINTS"])
        endpoints_pool = ydb.SessionPool(endpoints_driver)
        logging.debug("Endpoints DB driver initialised")

        if action == 'ADD':
            if not user_id: raise AuthError("Authorization is required for ADD action.")
            push_token = data.get('push_token')
            device_info = data.get('device_info', {})
            return add_endpoint.handle_add_endpoint(endpoints_pool, user_id, push_token, device_info)
        
        elif action == 'DELETE':
            if not user_id: raise AuthError("Authorization is required for DELETE action.")
            push_token = data.get('push_token')
            return delete_endpoint.handle_delete_endpoint(endpoints_pool, push_token, user_id)

        elif action == 'GET_LIST':
            if not user_id: raise AuthError("Authorization is required for GET_LIST action.")
            return get_endpoint.handle_get_endpoints(endpoints_pool, user_id)

        elif action == 'SEND':
            # Для SEND авторизация по JWT не нужна, т.к. это внутренний вызов,
            # и user_id_to_notify передается в теле.
            notices_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_NOTICES"], os.environ["YDB_DATABASE_NOTICES"])
            notices_pool = ydb.SessionPool(notices_driver)
            user_id_to_notify = data.get('user_id_to_notify')
            payload = data.get('payload')
            return send_notification.handle_send_notification(endpoints_pool, notices_pool, notices_driver, user_id_to_notify, payload)

        else:
            raise LogicError(f"Invalid action specified: '{action}'.")

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing endpoint request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```
#### add_endpoint.py
```python
# add_endpoint.py

import os
import json
import logging
import datetime
import pytz
import hashlib
import boto3
import ydb
from botocore.exceptions import ClientError
from custom_errors import LogicError

def _detect_platform(token):
    """Определяет платформу по формату токена."""
    try:
        data = json.loads(token)
        if isinstance(data, dict) and 'endpoint' in data and 'keys' in data:
            return "WEB"
    except (json.JSONDecodeError, TypeError):
        pass
    return "RUSTORE"

def handle_add_endpoint(pool, user_id, push_token, device_info):
    if not push_token:
        raise LogicError("push_token is required.")

    platform = _detect_platform(push_token)
    token_hash = hashlib.sha256(push_token.encode('utf-8')).hexdigest()
    endpoint_arn = None

    if platform == "WEB":
        cns_client = boto3.client(
            'sns',
            region_name=os.environ['CNS_REGION'],
            endpoint_url=os.environ.get('CNS_ENDPOINT_URL', 'https://notifications.yandexcloud.net'),
            aws_access_key_id=os.environ.get('STATIC_ACCESS_KEY_ID'),
            aws_secret_access_key=os.environ.get('STATIC_SECRET_ACCESS_KEY')
        )
        try:
            response = cns_client.create_platform_endpoint(
                PlatformApplicationArn=os.environ['CNS_PLATFORM_APP_ARN_WEB'],
                Token=push_token
            )
            endpoint_arn = response['EndpointArn']
        except ClientError as e:
            if 'Endpoint already exists' in str(e):
                endpoint_arn = str(e).split(' ')[-1]
                logging.warning(f"WEB endpoint already exists with ARN: {endpoint_arn}. Enabling it.")
                cns_client.set_endpoint_attributes(EndpointArn=endpoint_arn, Attributes={'Enabled': 'true'})
            else:
                raise LogicError(f"Could not create WEB push endpoint: {e}")

    def upsert_db_record(session):
        query = session.prepare("""
            DECLARE $hash AS Utf8; DECLARE $uid AS Utf8; DECLARE $platform AS Utf8;
            DECLARE $token AS Utf8; DECLARE $arn AS Utf8?; DECLARE $enabled AS Bool; 
            DECLARE $device AS Json; DECLARE $now AS Timestamp;
            UPSERT INTO UserEndpoints (push_token_hash, user_id, platform, push_token, endpoint_arn, is_enabled, device_info_json, created_at, updated_at)
            VALUES ($hash, $uid, $platform, $token, $arn, $enabled, $device, $now, $now);
        """)
        session.transaction(ydb.SerializableReadWrite()).execute(query, {
            "$hash": token_hash, "$uid": user_id, "$platform": platform, "$token": push_token,
            "$arn": endpoint_arn, "$enabled": True, "$device": json.dumps(device_info),
            "$now": datetime.datetime.now(pytz.utc)
        }, commit_tx=True)

    pool.retry_operation_sync(upsert_db_record)
    logging.info(f"Successfully upserted endpoint for platform {platform} for user {user_id}")

    return {"statusCode": 201, "body": json.dumps({"message": "Endpoint processed successfully"})}
```
#### delete_endpoint.py
```python
# delete_endpoint.py

import os
import json
import logging
import hashlib
import boto3
import ydb
from botocore.exceptions import ClientError
from custom_errors import LogicError, AuthError, NotFoundError

def handle_delete_endpoint(pool, push_token, requesting_user_id):
    if not push_token:
        raise LogicError("push_token is required to delete an endpoint.")

    token_hash = hashlib.sha256(push_token.encode('utf-8')).hexdigest()

    def get_subscription_details(session):
        query = session.prepare("DECLARE $hash AS Utf8; SELECT user_id, platform, endpoint_arn FROM UserEndpoints WHERE push_token_hash = $hash;")
        res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$hash": token_hash}, commit_tx=True)
        if not res[0].rows:
            return None
        return res[0].rows[0]

    sub_details = pool.retry_operation_sync(get_subscription_details)

    if not sub_details:
        raise NotFoundError("Subscription with the provided token not found.")

    if sub_details.user_id != requesting_user_id:
        raise AuthError("You are not authorized to delete this subscription.")

    if sub_details.platform == "WEB" and sub_details.endpoint_arn:
        try:
            cns_client = boto3.client(
                'sns',
                region_name=os.environ['CNS_REGION'],
                endpoint_url=os.environ.get('CNS_ENDPOINT_URL', 'https://notifications.yandexcloud.net'),
                aws_access_key_id=os.environ.get('STATIC_ACCESS_KEY_ID'),
                aws_secret_access_key=os.environ.get('STATIC_SECRET_ACCESS_KEY')
            )
            cns_client.delete_endpoint(EndpointArn=sub_details.endpoint_arn)
            logging.info(f"Successfully deleted WEB endpoint {sub_details.endpoint_arn} from CNS.")
        except ClientError as e:
            if e.response.get("Error", {}).get("Code") == 'NotFound':
                logging.warning(f"WEB endpoint {sub_details.endpoint_arn} was already deleted from CNS.")
            else:
                logging.error(f"CNS error while deleting endpoint {sub_details.endpoint_arn}: {e}")

    def delete_from_db(session):
        query = session.prepare("DECLARE $hash AS Utf8; DELETE FROM UserEndpoints WHERE push_token_hash = $hash;")
        session.transaction(ydb.SerializableReadWrite()).execute(query, {"$hash": token_hash}, commit_tx=True)

    pool.retry_operation_sync(delete_from_db)
    
    logging.info(f"Subscription with hash {token_hash} deleted successfully from DB.")
    return {"statusCode": 200, "body": json.dumps({"message": "Subscription deleted successfully."})}
```
#### get_endpoint.py
```python
# get_endpoint.py

import json
import logging
import ydb

def handle_get_endpoints(pool, user_id):
    logging.debug("[GET_ENDPOINT_LIST] for user_id=%s", user_id)
    
    def get_list(session):
        query = session.prepare("""
            DECLARE $uid AS Utf8;
            SELECT 
                platform,
                push_token,
                endpoint_arn,
                is_enabled,
                device_info_json,
                created_at,
                updated_at
            FROM UserEndpoints VIEW user_id_index WHERE user_id = $uid;
        """)
        res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id}, commit_tx=True)
        
        data = []
        for row in res[0].rows:
            item = {c.name: row[c.name] for c in res[0].columns}
            if 'device_info_json' in item and item['device_info_json']:
                item['device_info_json'] = json.loads(item['device_info_json'])
            data.append(item)
        return data

    result_data = pool.retry_operation_sync(get_list)
    return {"statusCode": 200, "body": json.dumps({"data": result_data}, default=str)}
```
#### send_notification.py
```python
# send_notification.py

import os
import json
import logging
import hashlib
import datetime
import pytz
import uuid
import boto3
import ydb
from botocore.exceptions import ClientError
from custom_errors import LogicError
from utils import rustore_push_utils

def _create_notices_table_if_not_exists(driver, user_id):
    table_path = os.path.join(os.environ["YDB_DATABASE_NOTICES"], f"notices_{user_id}")
    try:
        session = driver.table_client.session().create()
        session.describe_table(table_path)
    except ydb.SchemeError:
        logging.warning(f"Table {table_path} does not exist. Creating with full schema...")
        session.create_table(
            table_path,
            ydb.TableDescription().with_primary_key("notice_id")
            .with_columns(
                ydb.Column("notice_id", ydb.PrimitiveType.Utf8),
                ydb.Column("title", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                # --- НАЧАЛО ИЗМЕНЕНИЙ: Полная схема из документации ---
                ydb.Column("provider", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("tags_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("additional_info_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("action_url", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("created_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
                ydb.Column("is_delivered", ydb.OptionalType(ydb.PrimitiveType.Bool)),
                ydb.Column("delivered_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
                ydb.Column("is_archived", ydb.OptionalType(ydb.PrimitiveType.Bool))
                # --- КОНЕЦ ИЗМЕНЕНИЙ ---
            )
        )
        logging.info(f"Table {table_path} created successfully.")

def handle_send_notification(endpoints_pool, notices_pool, notices_driver, user_id, payload):
    if not all([user_id, payload, payload.get('title'), payload.get('body')]):
        raise LogicError("user_id_to_notify, payload.title and payload.body are required.")

    _create_notices_table_if_not_exists(notices_driver, user_id)
    
    title = payload.get("title")
    body = payload.get("body")

    def create_notice_record(session):
        tx = session.transaction(ydb.SerializableReadWrite())
        table_name = f"notices_{user_id}"
        # --- ИСПРАВЛЕННЫЙ YQL ЗАПРОС ---
        query = session.prepare(f"""
            DECLARE $id AS Utf8;
            DECLARE $title AS Utf8;
            DECLARE $provider AS Utf8;
            DECLARE $additional_info AS Json;
            DECLARE $created AS Timestamp;
            DECLARE $is_delivered AS Bool;
            
            UPSERT INTO `{table_name}` (notice_id, title, provider, additional_info_json, created_at, is_delivered)
            VALUES ($id, $title, $provider, $additional_info, $created, $is_delivered);
        """)
        
        # Собираем JSON с телом уведомления
        additional_info_payload = {
            "body": body,
            # Можно добавить любую другую мета-информацию
            "source": "internal_trigger" 
        }

        tx.execute(query, {
            "$id": str(uuid.uuid4()),
            "$title": title,
            "$provider": "приложение", # Указываем, что это внутреннее уведомление
            "$additional_info": json.dumps(additional_info_payload), # Сохраняем body внутри JSON
            "$created": datetime.datetime.now(pytz.utc),
            "$is_delivered": False
        })
        # --- КОНЕЦ ИСПРАВЛЕНИЙ ---
        tx.commit()
    notices_pool.retry_operation_sync(create_notice_record)

    def get_active_subscriptions(session):
        query = session.prepare("""
            DECLARE $uid AS Utf8;
            SELECT push_token, platform, endpoint_arn FROM UserEndpoints VIEW user_id_index
            WHERE user_id = $uid AND is_enabled = true;
        """)
        res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id}, commit_tx=True)
        return res[0].rows
    
    subscriptions = endpoints_pool.retry_operation_sync(get_active_subscriptions)
    if not subscriptions:
        return {"statusCode": 200, "body": json.dumps({"message": "Notification saved, but no active devices found."})}

    cns_client = boto3.client(
        'sns',
        region_name=os.environ['CNS_REGION'],
        endpoint_url=os.environ.get('CNS_ENDPOINT_URL', 'https://notifications.yandexcloud.net'),
        aws_access_key_id=os.environ.get('STATIC_ACCESS_KEY_ID'),
        aws_secret_access_key=os.environ.get('STATIC_SECRET_ACCESS_KEY')
    )
    rustore_project_id = os.environ['RUSTORE_PROJECT_ID']
    rustore_service_token = os.environ['RUSTORE_SERVICE_TOKEN']
    
    success_count = 0
    disabled_tokens = []

    for sub in subscriptions:
        is_sent = False
        error_reason = ""
        
        if sub.platform == "WEB" and sub.endpoint_arn:
            web_payload = {"notification": {"title": title, "body": body}}
            message_to_publish = {"default": body, "WEB": json.dumps(web_payload)}
            try:
                cns_client.publish(
                    TargetArn=sub.endpoint_arn,
                    Message=json.dumps(message_to_publish),
                    MessageStructure="json"
                )
                is_sent = True
            except ClientError as e:
                if e.response.get("Error", {}).get("Code") == 'EndpointDisabled':
                    error_reason = "EndpointDisabled"
                logging.error(f"CNS Error for {sub.endpoint_arn}: {e}")

        elif sub.platform == "RUSTORE":
            is_sent, error_reason = rustore_push_utils.send_rustore_notification(
                project_id=rustore_project_id, service_token=rustore_service_token,
                device_token=sub.push_token, title=title, body=body
            )

        if is_sent:
            success_count += 1
        elif error_reason in ["EndpointDisabled", "UNREGISTERED"]:
            disabled_tokens.append(sub.push_token)
            logging.warning(f"Marking token for deactivation: ...{sub.push_token[-10:]} Reason: {error_reason}")

    if disabled_tokens:
        logging.info(f"Deactivating {len(disabled_tokens)} disabled subscriptions in the database.")
        disabled_hashes = [hashlib.sha256(t.encode('utf-8')).hexdigest() for t in disabled_tokens]
        
        def disable_subscriptions_batch(session):
            query = session.prepare("""
                DECLARE $hashes AS List<Utf8>;
                UPDATE UserEndpoints SET is_enabled = false, updated_at = CurrentUtcTimestamp()
                WHERE push_token_hash IN $hashes;
            """)
            session.transaction(ydb.SerializableReadWrite()).execute(
                query, {"$hashes": disabled_hashes}, commit_tx=True
            )
        try:
            endpoints_pool.retry_operation_sync(disable_subscriptions_batch)
            logging.info("Successfully deactivated subscriptions in batch.")
        except Exception as db_error:
            logging.error(f"Failed to deactivate subscriptions in batch: {db_error}", exc_info=True)

    message = f"Notification sent to {success_count} of {len(subscriptions)} devices."
    logging.info(message)
    return {"statusCode": 200, "body": json.dumps({"message": message})}
```
#### custom_errors.py
```python
# custom_errors.py

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass
```