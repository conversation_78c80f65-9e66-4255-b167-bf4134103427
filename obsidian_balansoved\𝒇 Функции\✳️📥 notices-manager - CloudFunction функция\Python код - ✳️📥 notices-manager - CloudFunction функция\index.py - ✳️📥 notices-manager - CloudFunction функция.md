
```python
import json
import os
import ydb
from utils import auth_utils, ydb_utils, request_parser

from get import get_notices
from archive import archive_notice
from mark_as_delivered import mark_notices_as_delivered
from custom_errors import AuthError, LogicError, NotFoundError

def _log_event_context(event, context):
    """Выводит в лог подробную информацию о входящем событии и контексте вызова."""
    try:
        print("RAW EVENT: %s" % json.dumps(event, default=str, ensure_ascii=False)[:10000])
    except Exception:
        print("RAW EVENT (non-json serialisable): %s" % event)
    
    if context is not None:
        print(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s" %
            (getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None))
        )

def handler(event, context):
    _log_event_context(event, context)
    
    user_id = "unknown"
    action = "unknown"
    try:
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing Bearer token")

        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired token")
        
        user_id = user_payload['user_id']
        print(f"Authorized request for user_id: {user_id}")

        action = event.get('requestContext', {}).get('apiGateway', {}).get('operationContext', {}).get('action')
        
        if not action:
            raise LogicError("Action is a required parameter (must be set in API Gateway operation context).")

        print(f"Processing action: {action} for user_id: {user_id}")

        table_name = f"notices_{user_id}"
        notices_driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_NOTICES"],
            os.environ["YDB_DATABASE_NOTICES"]
        )
        notices_pool = ydb.SessionPool(notices_driver)
        
        print(f"Routing action '{action}' to table '{table_name}'")

        def notices_transaction_router(session):
            if action == "GET":
                notice_id = event.get('pathParams', {}).get('noticeId')
                page_str = event.get('queryStringParameters', {}).get('page', '0')
                get_archived_str = event.get('queryStringParameters', {}).get('get_archived', 'false')
                
                print(f"GET params: notice_id={notice_id}, page={page_str}, get_archived={get_archived_str}")
                
                return get_notices(
                    session, table_name, notice_id,
                    int(page_str), get_archived_str.lower() == 'true'
                )
            
            data = request_parser.parse_request_body(event)
            print(f"Parsed request body for {action}: {data}")

            if action == "ARCHIVE":
                notice_id = data.get('notice_id')
                return archive_notice(session, table_name, notice_id)
            
            elif action == "MARK_AS_DELIVERED":
                notice_ids = data.get('notice_ids')
                return mark_notices_as_delivered(session, table_name, notice_ids)
            
            else:
                raise LogicError(f"Invalid action specified: '{action}'.")

        response = notices_pool.retry_operation_sync(notices_transaction_router)
        
        if response.get('statusCode') == 200:
            print(f"Action '{action}' for user {user_id} completed successfully.")
        
        print(f"Response to be returned: {response}")
        return response

    except AuthError as e:
        print(f"[AUTH ERROR] {e}")
        return {"statusCode": 403, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": str(e)}, ensure_ascii=False)}
    except LogicError as e:
        print(f"[LOGIC ERROR] {e}")
        return {"statusCode": 400, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": str(e)}, ensure_ascii=False)}
    except NotFoundError as e:
        print(f"[NOT FOUND ERROR] {e}")
        return {"statusCode": 404, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": str(e)}, ensure_ascii=False)}
    except Exception as e:
        print(f"[CRITICAL ERROR] Error processing notice request for user {user_id} on action {action}: {e}")
        return {"statusCode": 500, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": "Internal Server Error"}, ensure_ascii=False)}
```