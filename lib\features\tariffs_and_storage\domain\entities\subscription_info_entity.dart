import 'package:equatable/equatable.dart';

/// Entity для информации о подписке фирмы
class SubscriptionInfoEntity extends Equatable {
  /// ID тарифного плана
  final String planId;

  /// Дата начала подписки
  final DateTime startedAt;

  /// Дата окончания подписки (null для бессрочных)
  final DateTime? expiresAt;

  /// Автоматическое продление
  final bool autoRenew;

  /// Статус подписки
  final String status;

  /// Квота хранилища в байтах
  final int quotaBytes;

  const SubscriptionInfoEntity({
    required this.planId,
    required this.startedAt,
    this.expiresAt,
    required this.autoRenew,
    required this.status,
    required this.quotaBytes,
  });

  /// Активна ли подписка
  bool get isActive => status == 'active';

  /// Истекла ли подписка
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Квота в мегабайтах
  double get quotaMB => quotaBytes / (1024 * 1024);

  @override
  List<Object?> get props => [
    planId,
    startedAt,
    expiresAt,
    autoRenew,
    status,
    quotaBytes,
  ];
}
