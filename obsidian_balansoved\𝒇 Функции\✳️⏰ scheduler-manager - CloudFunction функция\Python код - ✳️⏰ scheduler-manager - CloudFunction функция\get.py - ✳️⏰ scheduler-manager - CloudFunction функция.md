
```python
import json
import logging
import ydb
from custom_errors import NotFoundError

def _format_row(row, columns):
    """Преобразует строку YDB в словарь, парсит JSON-поля."""
    data = {}
    for c in columns:
        value = row[c.name]
        # Парсим JSON-поля, если они не пустые
        if 'json' in c.name and value:
            data[c.name] = json.loads(value)
        else:
            data[c.name] = value
    return data

def get_events(session, table_name, event_id=None, custom_identifier=None):
    """Получает запланированные события по ID, кастомному идентификатору или все."""
    tx = session.transaction(ydb.SerializableReadWrite())

    if event_id:
        logging.debug(f"Fetching event by event_id: {event_id}")
        query_text = f"DECLARE $event_id AS Utf8; SELECT * FROM `{table_name}` WHERE event_id = $event_id;"
        res = tx.execute(session.prepare(query_text), {"$event_id": event_id})
        if not res[0].rows:
            raise NotFoundError(f"Scheduled event with id {event_id} not found.")
        data = _format_row(res[0].rows[0], res[0].columns)

    elif custom_identifier:
        logging.debug(f"Fetching events by custom_identifier: {custom_identifier}")
        query_text = f"DECLARE $custom_identifier AS Utf8; SELECT * FROM `{table_name}` WHERE custom_identifier = $custom_identifier;"
        res = tx.execute(session.prepare(query_text), {"$custom_identifier": custom_identifier})
        data = [_format_row(row, res[0].columns) for row in res[0].rows]
        logging.debug(f"Found {len(data)} events for custom_identifier: {custom_identifier}")

    else:
        logging.debug("Fetching all active scheduled events.")
        query_text = f"SELECT * FROM `{table_name}` WHERE is_active = true;"
        res = tx.execute(session.prepare(query_text))
        data = [_format_row(row, res[0].columns) for row in res[0].rows]

    tx.commit()
    return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}
```