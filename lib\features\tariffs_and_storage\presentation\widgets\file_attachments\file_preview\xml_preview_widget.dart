import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:xml/xml.dart' as xml;

import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class XmlPreviewWidget extends StatefulWidget {
  final String fileKey;
  final String fileName;

  const XmlPreviewWidget({
    super.key,
    required this.fileKey,
    required this.fileName,
  });

  @override
  State<XmlPreviewWidget> createState() => _XmlPreviewWidgetState();
}

class _XmlPreviewWidgetState extends State<XmlPreviewWidget> {
  bool _isLoading = true;
  String? _errorMessage;
  String? _prettyXml;

  @override
  void initState() {
    super.initState();
    _loadXml();
  }

  Future<void> _loadXml() async {
    try {
      final downloadUrl = await _getDownloadUrl();
      if (downloadUrl == null) {
        if (mounted) {
          setState(() {
            _errorMessage = 'Не удалось получить ссылку для предпросмотра';
            _isLoading = false;
          });
        }
        return;
      }

      final response = await http.get(Uri.parse(downloadUrl));
      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}');
      }

      final rawXml = response.body;
      final document = xml.XmlDocument.parse(rawXml);
      final pretty = document.toXmlString(pretty: true, indent: '  ');
      if (mounted) {
        setState(() {
          _prettyXml = pretty;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Ошибка загрузки XML: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<String?> _getDownloadUrl() async {
    final storageCubit = context.read<TariffsAndStorageCubit>();
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) return null;

    final firmId = firmState.selectedFirm!.id;
    final completer = Completer<String?>();
    late StreamSubscription sub;

    sub = storageCubit.stream.listen((state) {
      if (state is FileDownloadUrlReady && state.fileKey == widget.fileKey) {
        if (!completer.isCompleted) completer.complete(state.downloadUrl);
        sub.cancel();
      } else if (state is TariffsAndStorageError) {
        if (!completer.isCompleted) completer.complete(null);
        sub.cancel();
      }
    });

    storageCubit.getDownloadUrl(firmId: firmId, fileKey: widget.fileKey);

    Future.delayed(const Duration(seconds: 15), () {
      if (!completer.isCompleted) {
        completer.complete(null);
        sub.cancel();
      }
    });

    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_errorMessage != null) {
      return Center(
        child: Text(
          _errorMessage!,
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      );
    }
    if (_prettyXml == null) {
      return const Center(child: Text('Не удалось загрузить XML.'));
    }

    return InteractiveViewer(
      maxScale: 5,
      minScale: 0.5,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: SelectableText(
            _prettyXml!,
            style: const TextStyle(
              fontFamily: 'Courier New',
              fontSize: 14,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
