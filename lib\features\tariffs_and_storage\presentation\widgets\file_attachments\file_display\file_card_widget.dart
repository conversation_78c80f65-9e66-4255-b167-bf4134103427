import 'package:flutter/material.dart';
import '../utils/file_utils.dart';
import '../file_preview/file_preview_dialog.dart';

class FileCardWidget extends StatefulWidget {
  final Map<String, dynamic> fileData;
  final VoidCallback? onRemove;
  final bool showRemoveButton;

  const FileCardWidget({
    super.key,
    required this.fileData,
    this.onRemove,
    this.showRemoveButton = false,
  });

  @override
  State<FileCardWidget> createState() => _FileCardWidgetState();
}

class _FileCardWidgetState extends State<FileCardWidget> {
  bool _isDownloading = false;

  @override
  Widget build(BuildContext context) {
    final name = widget.fileData['name'] ?? 'Безымянный файл';
    final fileKey = widget.fileData['fileKey'] as String?;
    final fileSize = widget.fileData['fileSize'] as int?;
    final theme = Theme.of(context);

    if (fileKey == null) {
      return const SizedBox.shrink();
    }

    return InkWell(
      onTap: () {
        if (FileUtils.isPreviewSupported(name)) {
          FilePreviewDialog.show(context, fileKey, name);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Предпросмотр для этого типа файла недоступен.'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: theme.cardColor,
          border: Border.all(color: theme.dividerColor),
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withOpacity(0.05),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(6),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 3,
                    child: Center(child: FileUtils.getFileIcon(name)),
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          name,
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 11,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                        if (fileSize != null) ...[
                          const SizedBox(height: 1),
                          Text(
                            FileUtils.formatFileSize(fileSize),
                            style: theme.textTheme.labelSmall?.copyWith(
                              fontSize: 9,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Кнопка удаления (если нужна)
            if (widget.showRemoveButton && widget.onRemove != null)
              Positioned(
                top: 2,
                left: 2,
                child: GestureDetector(
                  onTap: widget.onRemove,
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(9),
                    ),
                    child: Icon(
                      Icons.close,
                      size: 10,
                      color: theme.colorScheme.onError,
                    ),
                  ),
                ),
              ),
            // Кнопка скачивания
            Positioned(
              top: 2,
              right: 2,
              child:
                  _isDownloading
                      ? Container(
                        width: 18,
                        height: 18,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(9),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(3),
                          child: CircularProgressIndicator(
                            strokeWidth: 1.5,
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),
                      )
                      : GestureDetector(
                        onTap: () => _downloadFile(fileKey, name),
                        child: Container(
                          width: 18,
                          height: 18,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withOpacity(0.8),
                            borderRadius: BorderRadius.circular(9),
                          ),
                          child: Icon(
                            Icons.download,
                            size: 10,
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  void _downloadFile(String fileKey, String fileName) {
    FileUtils.downloadFile(
      context,
      fileKey,
      fileName,
      onStart: () {
        if (mounted) {
          setState(() {
            _isDownloading = true;
          });
        }
      },
      onSuccess: () {
        if (mounted) {
          setState(() {
            _isDownloading = false;
          });
        }
      },
      onError: () {
        if (mounted) {
          setState(() {
            _isDownloading = false;
          });
        }
      },
    );
  }
}
