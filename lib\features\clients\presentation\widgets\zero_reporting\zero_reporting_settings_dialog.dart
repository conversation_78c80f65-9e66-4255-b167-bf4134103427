import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../cubit/zero_reporting_cubit.dart';
import '../../cubit/price_calculator_state.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';

class ZeroReportingSettingsDialog extends StatefulWidget {
  const ZeroReportingSettingsDialog({super.key});

  @override
  State<ZeroReportingSettingsDialog> createState() =>
      _ZeroReportingSettingsDialogState();
}

class _ZeroReportingSettingsDialogState
    extends State<ZeroReportingSettingsDialog> {
  late final ZeroReportingCubit _cubit;
  final Map<ZeroReportingType, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _cubit = context.read<ZeroReportingCubit>();

    // Инициализируем контроллеры для каждого типа отчетности
    for (final type in ZeroReportingType.values) {
      final currentPrice = _cubit.getCustomPrice(type);
      _controllers[type] = TextEditingController(
        text: NumberFormat('#,##0', 'ru_RU').format(currentPrice),
      );
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _save() {
    final Map<ZeroReportingType, int> newPrices = {};

    for (var entry in _controllers.entries) {
      final type = entry.key;
      final controller = entry.value;

      // Парсим цену, убирая пробелы и запятые
      final cleanText = controller.text.replaceAll(RegExp(r'[^\d]'), '');
      final price = int.tryParse(cleanText) ?? type.price;
      newPrices[type] = price;
    }

    _cubit.updateCustomPrices(newPrices);
    Navigator.of(context).pop();
  }

  void _resetToDefaults() {
    for (var entry in _controllers.entries) {
      final type = entry.key;
      entry.value.text = NumberFormat('#,##0', 'ru_RU').format(type.price);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Настройка расценок нулевой отчетности'),
      content: SizedBox(
        width: 500,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Укажите стоимость за квартал для каждого типа отчетности:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            ...ZeroReportingType.values.map((type) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: UniversalNumberField(
                  controller: _controllers[type],
                  labelText: type.label,
                  fieldType: NumberFieldType.integer,
                  suffixText: '₽/квартал',
                  helperText:
                      'Базовая цена: ${NumberFormat('#,##0', 'ru_RU').format(type.price)} ₽',
                  isEditing: true,
                  onChanged: (value) {
                    // Автоматическое форматирование числа
                    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
                    if (cleanValue.isNotEmpty) {
                      final number = int.tryParse(cleanValue);
                      if (number != null) {
                        final formatted = NumberFormat(
                          '#,##0',
                          'ru_RU',
                        ).format(number);
                        if (formatted != value) {
                          _controllers[type]!.value = TextEditingValue(
                            text: formatted,
                            selection: TextSelection.collapsed(
                              offset: formatted.length,
                            ),
                          );
                        }
                      }
                    }
                  },
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        TextButton(onPressed: _resetToDefaults, child: const Text('Сбросить')),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        FilledButton(onPressed: _save, child: const Text('Сохранить')),
      ],
    );
  }
}
