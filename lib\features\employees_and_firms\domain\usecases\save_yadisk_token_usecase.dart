import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/backup_integration_entity.dart';
import '../repositories/integrations_repository.dart';

class SaveYadiskTokenUseCase {
  final IntegrationsRepository repository;
  const SaveYadiskTokenUseCase(this.repository);

  Future<Either<Failure, BackupIntegrationEntity>> call({
    required String firmId,
    required String token,
  }) {
    return repository.saveToken(firmId: firmId, token: token);
  }
}
