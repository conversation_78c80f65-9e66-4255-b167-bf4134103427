
Идентификатор - etn7jrt4a67t8qu1js13
Путь к базе - /ru-central1/b1g2bgg0i9r8beucbthc/etn7jrt4a67t8qu1js13
Эндпоинт - grpcs://ydb.serverless.yandexcloud.net:2135/?database=/ru-central1/b1g2bgg0i9r8beucbthc/etn7jrt4a67t8qu1js13

---
# Таблицы
Эта база данных использует подход "таблица на пользователя" для хранения уведомлений. Такой подход изолирует данные одного пользователя от другого и обеспечивает масштабируемость.

Имя таблицы формируется динамически на основе `user_id` пользователя. Облачная функция `notices-manager` автоматически создает таблицу для пользователя при получении им первого уведомления.

#### Таблица-шаблон: `notices_{user_id}`

| #   | Имя                    | Ключ | Тип                 | Описание                                                                                                                              |
| --- | ---------------------- | ---- | ------------------- | ------------------------------------------------------------------------------------------------------------------------------------- |
| 0   | `notice_id`            | PK   | `Utf8`              | Уникальный идентификатор уведомления (UUID).                                                                                          |
| 1   | `title`                |      | `Utf8`              | Наименование/заголовок уведомления. Пример: "Требование №2934 ФНС России".                                                            |
| 2   | `provider`             |      | `Utf8`              | Источник/провайдер уведомления. Пример: "приложение", "1С Фреш", "ФНС России".                                                        |
| 3   | `tags_json`            |      | `Json`              | JSON-массив с тегами для фильтрации и категоризации. Пример: `["Важно", "ФНС", "Требование"]`.                                         |
| 4   | `additional_info_json` |      | `Json`              | JSON-объект с дополнительными структурированными данными от провайдера. Пример: `{"document_id": "...", "amount": 1234.56}`.         |
| 5   | `action_url`           |      | `Optional<Utf8>`    | URL-адрес для перехода при клике на уведомление (например, ссылка на документ или задачу).                                            |
| 6   | `created_at`           |      | `Timestamp`         | Системное время создания уведомления.                                                                                                 |
| 7   | `is_delivered`         |      | `Bool`              | Флаг, указывающий, было ли уведомление доставлено/прочитано пользователем. По умолчанию `false`.                                       |
| 8   | `delivered_at`         |      | `Optional<Timestamp>` | Время, когда уведомление было помечено как доставленное/прочитанное.                                                                  |
| 9   | `is_archived`          |      | `Optional<Bool>`    | Флаг, указывающий, находится ли уведомление в архиве.                                                                                 |

---
#### YQL для управления таблицами

Эти запросы демонстрируют, как создавать и удалять таблицы уведомлений вручную. В рабочей системе это делает функция `notices-manager`.

**YQL для создания таблицы (шаблон)**
*Замените `{user_id}` на реальный идентификатор пользователя.*
```yql
CREATE TABLE `notices_{user_id}` (
    notice_id Utf8,
    title Utf8,
    provider Utf8,
    tags_json Json,
    additional_info_json Json,
    action_url Utf8,
    created_at Timestamp,
    is_delivered Bool,
    delivered_at Timestamp,
    is_archived Bool,
    PRIMARY KEY (notice_id)
);
```

**YQL для удаления таблицы (шаблон)**
*Замените `{user_id}` на реальный идентификатор пользователя.*
```yql
DROP TABLE `notices_{user_id}`;
```

---
### Примеры записей

Ниже представлены три примера, иллюстрирующие разные типы уведомлений, которые могут быть сохранены в этой базе данных.

#### Пример 1: Уведомление от ФНС
*Простое уведомление с дополнительной информацией и тегами.*
```json
{
    "notice_id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
    "title": "Требование №2934 об уплате налога",
    "provider": "ФНС России",
    "tags_json": "[\"Важно\", \"ФНС\", \"Требование\"]",
    "additional_info_json": "{\"document_id\": \"doc-fns-2934-2025\", \"amount\": 15200.50, \"due_date\": \"2025-09-15\"}",
    "action_url": "https://service.nalog.ru/portal/doc/12345",
    "created_at": "2025-08-20T10:00:00Z",
    "is_delivered": false,
    "delivered_at": null,
    "is_archived": false
}
```

#### Пример 2: Напоминание по задаче
*Уведомление, созданное внутри приложения, со ссылкой на саму задачу.*
```json
{
    "notice_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "title": "Не забудьте про задачу: «Подготовить квартальный отчет»",
    "provider": "приложение",
    "tags_json": "[\"Напоминание\", \"Отчеты\"]",
    "additional_info_json": "{\"task_id\": \"task-abc-123\", \"priority\": \"high\"}",
    "action_url": "/tasks/task-abc-123",
    "created_at": "2025-09-10T09:00:00Z",
    "is_delivered": true,
    "delivered_at": "2025-09-10T09:01:30Z",
    "is_archived": false
}
```

#### Пример 3: Информационное сообщение от 1С
*Уведомление без конкретного действия, помеченное как архивное.*
```json
{
    "notice_id": "z9y8x7w6-v5u4-3210-fedc-ba0987654321",
    "title": "Обновление конфигурации 'Бухгалтерия предприятия' успешно завершено",
    "provider": "1С Фреш",
    "tags_json": "[\"Информация\", \"1С\"]",
    "additional_info_json": "{\"version\": \"3.0.145.20\"}",
    "action_url": null,
    "created_at": "2025-09-11T04:30:00Z",
    "is_delivered": true,
    "delivered_at": "2025-09-11T08:00:00Z",
    "is_archived": true
}
```