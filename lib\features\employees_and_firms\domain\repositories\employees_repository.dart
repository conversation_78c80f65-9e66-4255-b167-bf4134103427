import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/employee_entity.dart';

abstract class IEmployeesRepository {
  Future<Either<Failure, List<EmployeeEntity>>> getEmployees(String firmId);
  Future<Either<Failure, Unit>> addEmployee(String firmId, String email);
  Future<Either<Failure, Unit>> inviteEmployee(String firmId, String email);
  Future<Either<Failure, Unit>> addRole(
    String firmId,
    String userId,
    String role,
  );
  Future<Either<Failure, Unit>> removeRole(
    String firmId,
    String userId,
    String role,
  );
  Future<Either<Failure, Unit>> deleteEmployee(String firmId, String userId);

  /// Создать новую фирму.
  Future<Either<Failure, Unit>> createFirm(String firmName);
}
