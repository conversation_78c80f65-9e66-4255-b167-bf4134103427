import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';

import '../../domain/entities/file_upload_entity.dart';
import '../../domain/usecases/check_space_available_usecase.dart';
import '../../domain/usecases/delete_file_usecase.dart';
import '../../domain/usecases/download_file_usecase.dart';
import '../../domain/usecases/get_storage_info_usecase.dart';
import '../../domain/usecases/upload_file_usecase.dart';
import 'tariffs_and_storage_state.dart';

class TariffsAndStorageCubit extends Cubit<TariffsAndStorageState> {
  final GetStorageInfoUseCase _getStorageInfoUseCase;
  final CheckSpaceAvailableUseCase _checkSpaceAvailableUseCase;
  final UploadFileUseCase _uploadFileUseCase;
  final DownloadFileUseCase _downloadFileUseCase;
  final DeleteFileUseCase _deleteFileUseCase;

  StreamSubscription<FileUploadProgressEntity>? _uploadSubscription;

  TariffsAndStorageCubit({
    required GetStorageInfoUseCase getStorageInfoUseCase,
    required CheckSpaceAvailableUseCase checkSpaceAvailableUseCase,
    required UploadFileUseCase uploadFileUseCase,
    required DownloadFileUseCase downloadFileUseCase,
    required DeleteFileUseCase deleteFileUseCase,
  }) : _getStorageInfoUseCase = getStorageInfoUseCase,
       _checkSpaceAvailableUseCase = checkSpaceAvailableUseCase,
       _uploadFileUseCase = uploadFileUseCase,
       _downloadFileUseCase = downloadFileUseCase,
       _deleteFileUseCase = deleteFileUseCase,
       super(TariffsAndStorageInitial());

  @override
  Future<void> close() {
    _uploadSubscription?.cancel();
    return super.close();
  }

  /// Загрузить информацию о тарифах и хранилище
  Future<void> loadStorageInfo(String firmId) async {
    emit(TariffsAndStorageLoading());

    final result = await _getStorageInfoUseCase(firmId);

    result.fold(
      (failure) => emit(TariffsAndStorageError(message: failure.message)),
      (data) => emit(TariffsAndStorageLoaded(data: data)),
    );
  }

  /// Проверить доступность места для файла
  Future<void> checkSpaceAvailable({
    required String firmId,
    required int fileSize,
  }) async {
    final result = await _checkSpaceAvailableUseCase(
      firmId: firmId,
      fileSize: fileSize,
    );

    result.fold(
      (failure) => emit(TariffsAndStorageError(message: failure.message)),
      (spaceAvailable) => emit(
        SpaceCheckCompleted(
          spaceAvailable: spaceAvailable,
          requestedFileSize: fileSize,
        ),
      ),
    );
  }

  /// Загрузить файл
  Future<void> uploadFile({
    required String firmId,
    required String fileName,
    required List<int> fileBytes,
  }) async {
    emit(
      FileUploadInProgress(
        fileKey: '', // Временный ключ, будет обновлен
        fileName: fileName,
        progress: 0.0,
        status: FileUploadStatus.preparing,
      ),
    );

    // Сначала получаем URL для загрузки
    final uploadUrlResult = await _uploadFileUseCase.getUploadUrl(
      firmId: firmId,
      fileName: fileName,
      fileSize: fileBytes.length,
    );

    uploadUrlResult.fold(
      (failure) =>
          emit(FileUploadFailed(message: failure.message, fileName: fileName)),
      (uploadEntity) {
        // Затем начинаем загрузку
        _uploadSubscription = _uploadFileUseCase
            .uploadFile(
              uploadUrl: uploadEntity.uploadUrl,
              fileKey: uploadEntity.fileKey,
              firmId: firmId,
              fileBytes: fileBytes,
              fileName: fileName,
            )
            .listen(
              (progress) {
                emit(
                  FileUploadInProgress(
                    fileKey: progress.fileKey,
                    fileName: fileName,
                    progress: progress.progress,
                    status: progress.status,
                    errorMessage: progress.errorMessage,
                  ),
                );
              },
              onError: (error) {
                emit(
                  FileUploadFailed(
                    message: 'Ошибка стрима загрузки: $error',
                    fileName: fileName,
                  ),
                );
              },
              onDone: () async {
                final lastState = state;
                if (lastState is FileUploadInProgress) {
                  if (lastState.status == FileUploadStatus.completed) {
                    // 1. Подтверждаем загрузку, чтобы used_bytes обновился
                    final confirmResult = await _uploadFileUseCase
                        .confirmUpload(
                          firmId: firmId,
                          fileKey: lastState.fileKey,
                        );

                    if (confirmResult.isLeft()) {
                      final failureMsg = confirmResult.fold(
                        (f) => f.message,
                        (_) => '',
                      );
                      emit(
                        FileUploadFailed(
                          message:
                              'Не удалось подтвердить загрузку: $failureMsg',
                          fileName: fileName,
                        ),
                      );
                      return;
                    }

                    // 2. Загружаем обновленные данные о хранилище
                    final updatedDataResult = await _getStorageInfoUseCase(
                      firmId,
                    );
                    updatedDataResult.fold(
                      (failure) => emit(
                        FileUploadFailed(
                          message:
                              "Не удалось обновить данные о хранилище: ${failure.message}",
                          fileName: fileName,
                        ),
                      ),
                      (updatedData) => emit(
                        FileUploadCompleted(
                          fileKey: lastState.fileKey,
                          fileName: fileName,
                          updatedData: updatedData,
                        ),
                      ),
                    );
                  } else {
                    emit(
                      FileUploadFailed(
                        message: 'Загрузка завершилась без успешного статуса.',
                        fileName: fileName,
                      ),
                    );
                  }
                }
              },
            );
      },
    );
  }

  /// Получить ссылку для скачивания файла
  Future<void> getDownloadUrl({
    required String firmId,
    required String fileKey,
  }) async {
    debugPrint('🔽 [DOWNLOAD] Запрос URL для скачивания файла: $fileKey');

    final result = await _downloadFileUseCase(firmId: firmId, fileKey: fileKey);

    result.fold(
      (failure) {
        debugPrint('❌ [DOWNLOAD] Ошибка получения URL: ${failure.message}');
        emit(TariffsAndStorageError(message: failure.message));
      },
      (downloadEntity) {
        debugPrint('✅ [DOWNLOAD] URL получен успешно для файла: $fileKey');
        debugPrint('🔗 [DOWNLOAD] URL: ${downloadEntity.downloadUrl}');
        emit(
          FileDownloadUrlReady(
            downloadUrl: downloadEntity.downloadUrl,
            fileKey: fileKey,
          ),
        );
      },
    );
  }

  /// Удалить файл
  Future<void> deleteFile({
    required String firmId,
    required String fileKey,
  }) async {
    final result = await _deleteFileUseCase(firmId: firmId, fileKey: fileKey);

    result.fold(
      (failure) => emit(TariffsAndStorageError(message: failure.message)),
      (_) async {
        // После удаления файла обновляем данные о хранилище
        final updatedDataResult = await _getStorageInfoUseCase(firmId);
        updatedDataResult.fold(
          (failure) => emit(TariffsAndStorageError(message: failure.message)),
          (updatedData) =>
              emit(FileDeleted(fileKey: fileKey, updatedData: updatedData)),
        );
      },
    );
  }

  /// Очистить состояние
  void clearState() {
    _uploadSubscription?.cancel();
    emit(TariffsAndStorageInitial());
  }

  /// Отменить текущую загрузку
  void cancelUpload() {
    _uploadSubscription?.cancel();
  }
}
