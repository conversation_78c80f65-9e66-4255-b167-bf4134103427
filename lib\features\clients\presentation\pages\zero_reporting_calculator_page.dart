import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../cubit/zero_reporting_cubit.dart';
import '../cubit/price_calculator_state.dart';

/// Страница «Калькулятор нулевой отчетности»
@RoutePage()
class ZeroReportingCalculatorPage extends StatelessWidget {
  const ZeroReportingCalculatorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => ZeroReportingCubit(),
      child: const _ZeroReportingBody(),
    );
  }
}

class _ZeroReportingBody extends StatelessWidget {
  const _ZeroReportingBody();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainerLowest,
      appBar: AppBar(
        title: const Text('Калькулятор нулевой отчетности'),
        backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      ),
      body: BlocBuilder<ZeroReportingCubit, ZeroReportingState>(
        builder: (context, state) {
          final cubit = context.read<ZeroReportingCubit>();
          final numberFormat = NumberFormat.currency(
            locale: 'ru_RU',
            symbol: '₽',
          );

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 600),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Выберите тип организации и систему налогообложения',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 24),
                            ...ZeroReportingType.values.map((type) {
                              return RadioListTile<ZeroReportingType>(
                                title: Text(type.label),
                                subtitle: Text(
                                  '${numberFormat.format(type.price)} за квартал',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyMedium?.copyWith(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                value: type,
                                groupValue: state.selectedType,
                                onChanged: (value) {
                                  if (value != null) {
                                    cubit.selectType(value);
                                  }
                                },
                              );
                            }),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Card(
                      elevation: 4,
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          children: [
                            Text(
                              'Итоговая стоимость',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              numberFormat.format(cubit.quarterlyPrice),
                              style: Theme.of(
                                context,
                              ).textTheme.headlineLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'за квартал',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color:
                                    Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
