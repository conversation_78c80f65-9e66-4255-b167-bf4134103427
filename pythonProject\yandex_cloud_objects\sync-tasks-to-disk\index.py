# ✳️⏰ scheduler-trigger/index.py

import json
import os
import logging
import datetime
import pytz
import ydb
import yandexcloud
from yandex.cloud.serverless.functions.v1 import function_service_pb2_grpc
from yandex.cloud.serverless.functions.v1 import function_pb2
from utils import ydb_utils

# Глобальная переменная для кеширования SDK
SDK_CLIENT_CACHE = None

# Настраиваем подробное логирование
logging.getLogger().setLevel(logging.INFO)


def get_sdk_client():
    """Получает или создает клиент SDK для работы с Functions"""
    global SDK_CLIENT_CACHE
    if SDK_CLIENT_CACHE is None:
        # SDK автоматически получит токен из метаданных инстанса
        sdk = yandexcloud.SDK()
        SDK_CLIENT_CACHE = sdk.client(function_service_pb2_grpc.FunctionServiceStub)
    return SDK_CLIENT_CACHE


def _to_datetime(value) -> datetime.datetime:
    """Безопасно конвертирует значение Timestamp из YDB в datetime объект (UTC)."""
    if isinstance(value, datetime.datetime):
        return value.replace(tzinfo=pytz.utc) if value.tzinfo is None else value
    if isinstance(value, int):
        return datetime.datetime.fromtimestamp(value / 1_000_000, tz=pytz.utc)
    raise TypeError(f"Unsupported type for timestamp conversion: {type(value)}")


def invoke_function_async(function_id: str, payload: str, headers: dict = None):
    """
    Асинхронно вызывает функцию через SDK
    """
    try:
        client = get_sdk_client()

        # В Yandex Cloud SDK используется специальный метод для вызова
        # Попробуем найти правильный класс
        from yandex.cloud.serverless.functions.v1 import function_service_pb2

        # Создаем запрос - правильное имя класса
        request = function_service_pb2.InvokeRequest(
            function_id=function_id,
            payload=payload.encode('utf-8') if isinstance(payload, str) else payload,
        )

        try:
            # Используем обычный Invoke с очень малым таймаутом для асинхронности
            import grpc
            # Создаем метаданные для очень короткого таймаута
            metadata = [('grpc-timeout', '100m')]  # 100 миллисекунд

            try:
                response = client.Invoke(request, metadata=metadata, timeout=0.1)
                logging.info(f"Function {function_id} invoked successfully")
            except grpc.RpcError as e:
                # Игнорируем таймаут ошибки - запрос уже отправлен
                if e.code() == grpc.StatusCode.DEADLINE_EXCEEDED:
                    logging.info(f"Function {function_id} invoked (timeout ignored - async call)")
                else:
                    raise

        except Exception as e:
            logging.error(f"Error during function invocation: {e}")
            raise

        return True
    except Exception as e:
        logging.error(f"Failed to invoke function {function_id}: {e}", exc_info=True)
        return False


def handler(event, context):
    now_utc = datetime.datetime.now(pytz.utc)
    logging.info(f"Scheduler trigger started at {now_utc.isoformat()}")

    processed_events_count = 0

    try:
        driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_SCHEDULER"],
            os.environ["YDB_DATABASE_SCHEDULER"]
        )
        pool = ydb.SessionPool(driver)

        def process_events_transaction(session):
            nonlocal processed_events_count
            tx = session.transaction(ydb.SerializableReadWrite())

            result_set = tx.execute(session.prepare("SELECT * FROM `ScheduledEvents` WHERE is_active = true;"))

            if not result_set[0].rows:
                logging.info("No active scheduled events found.")
                return

            events_to_process = result_set[0].rows
            logging.info(f"Found {len(events_to_process)} active events to check.")

            for event_row in events_to_process:
                try:
                    execution_dates_str = event_row.execution_dates_json
                    execution_dates = json.loads(execution_dates_str) if execution_dates_str else []

                    if not execution_dates: continue

                    execution_dates.sort()

                    # Проверка, что событие пора выполнять и оно еще не обработано
                    earliest_due_date = datetime.datetime.fromisoformat(execution_dates[0])
                    if earliest_due_date > now_utc: continue

                    # Безопасно приводим last_invoked_at к datetime (UTC). Если он NULL, берем минимум.
                    if event_row.last_invoked_at is None:
                        last_invoked_ts = datetime.datetime.min.replace(tzinfo=pytz.utc)
                    else:
                        last_invoked_ts = _to_datetime(event_row.last_invoked_at)

                    # Собираем все даты, которые попали в интервал (last_invoked_at, now]
                    due_dates = []
                    for date_str in execution_dates:
                        dd = datetime.datetime.fromisoformat(date_str)
                        if last_invoked_ts < dd <= now_utc:
                            due_dates.append(dd)
                    if not due_dates:  # Ничего нет к выполнению
                        continue
                    due_dates.sort()

                    # Подготавливаем заголовки
                    headers = {}
                    if getattr(event_row, 'request_headers_json', None):
                        try:
                            extra_headers = json.loads(event_row.request_headers_json)
                            headers.update(extra_headers)
                        except Exception as e:
                            logging.warning(f"Could not parse or process request_headers_json: {e}")

                    payload_str = event_row.request_body_json or "{}"

                    # Выбираем только одну (самую раннюю) дату для вызова
                    last_processed_due_date = None
                    selected_due_date = due_dates[0]
                    logging.info(
                        f"Triggering event {event_row.event_id} for scheduled time {selected_due_date.isoformat()} ...")
                    last_processed_due_date = selected_due_date

                    # Асинхронный вызов функции через SDK
                    success = invoke_function_async(
                        function_id=event_row.function_id,
                        payload=payload_str,
                        headers=headers
                    )

                    if success:
                        processed_events_count += 1

                    # Если ежегодное событие, добавляем дату +1 год
                    if event_row.is_annual:
                        next_year_date = selected_due_date.replace(year=selected_due_date.year + 1)
                        execution_dates.append(next_year_date.isoformat())

                    # Обновление записи в БД
                    if last_processed_due_date is not None:
                        update_query = session.prepare("""
                            DECLARE $event_id AS Utf8; DECLARE $dates_json AS Json;
                            DECLARE $updated_at AS Timestamp; DECLARE $invoked_at AS Timestamp;
                            UPDATE `ScheduledEvents` SET execution_dates_json = $dates_json,
                                updated_at = $updated_at, last_invoked_at = $invoked_at
                            WHERE event_id = $event_id;
                        """)
                        tx.execute(update_query, {
                            "$event_id": event_row.event_id,
                            "$dates_json": json.dumps(execution_dates),
                            "$updated_at": int(now_utc.timestamp() * 1_000_000),
                            "$invoked_at": int(now_utc.timestamp() * 1_000_000)
                        })
                        logging.info(f"Event record {event_row.event_id} updated in DB.")

                except Exception as e:
                    logging.error(f"Failed to process event {event_row.event_id}: {e}", exc_info=True)
                    continue
            tx.commit()

        pool.retry_operation_sync(process_events_transaction)

        logging.info(f"Scheduler trigger finished. Processed {processed_events_count} events.")
        return {"statusCode": 200, "body": json.dumps({"message": f"Processed {processed_events_count} events."})}

    except Exception as e:
        logging.error(f"Critical error in scheduler trigger: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}