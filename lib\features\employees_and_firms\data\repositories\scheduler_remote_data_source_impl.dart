import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:balansoved_enterprise/core/constants/constants.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

import '../data_source/scheduler_remote_data_source.dart';

class SchedulerRemoteDataSourceImpl implements ISchedulerRemoteDataSource {
  final Dio? dio;
  final http.Client? httpClient;

  SchedulerRemoteDataSourceImpl({this.dio, this.httpClient})
    : assert(
        dio != null || httpClient != null,
        'Either dio or httpClient must be provided',
      );

  String _logPrefix(String m) => '[SchedulerRemoteDataSource][$m]';

  @override
  Future<String> upsertEvent(String token, Map<String, dynamic> payload) async {
    final body = {'action': 'UPSERT', 'payload': payload};
    final data = await _post(token, body);
    if (data is Map && data['event_id'] != null) {
      return data['event_id'].toString();
    }
    throw ServerException(message: 'event_id not returned from scheduler');
  }

  @override
  Future<void> deleteEvent(String token, String eventId) async {
    final body = {'action': 'DELETE', 'event_id': eventId};
    await _post(token, body);
  }

  Future<dynamic> _post(String token, Map<String, dynamic> body) async {
    final url = SchedulerApiUrls.manage();
    if (dio != null) {
      try {
        NetworkLogger.printInfo('${_logPrefix('DIO')} --> POST $url');
        final response = await dio!.post(
          url,
          data: body,
          options: Options(
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
            },
          ),
        );
        NetworkLogger.printInfo(
          '${_logPrefix('DIO')} <-- ${response.statusCode}',
        );
        dynamic data = response.data;
        if (data is String) {
          data = jsonDecode(data);
        }
        return data;
      } on DioException catch (e) {
        throw NetworkException(
          message: e.message ?? 'dio error',
          originalError: e.toString(),
          requestUrl: url,
        );
      }
    } else {
      final uri = Uri.parse(url);
      try {
        final response = await httpClient!.post(
          uri,
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
          body: jsonEncode(body),
        );
        if (response.statusCode == 200 || response.statusCode == 201) {
          return jsonDecode(response.body);
        }
        throw ServerException(
          message: 'Scheduler API error',
          statusCode: response.statusCode,
          requestUrl: url,
          responseBody: response.body,
        );
      } catch (e) {
        throw NetworkException(message: e.toString(), requestUrl: url);
      }
    }
  }
}
