**1. `send_rustore_notification(project_id: str, service_token: str, device_token: str, title: str, body: str, image_url: str | None = None) -> tuple[bool, str]`**
- **Назначение**: Формирует и отправляет Push-уведомление через API сервиса RuStore Push. Является самодостаточной функцией, не зависящей от глобальных клиентов или конфигураций из окружения.
- **На входе**:
	-> `project_id`: ID проекта из консоли RuStore.
	-> `service_token`: Сервисный токен для авторизации запроса.
	-> `device_token`: Уникальный токен устройства, полученный от RuStore SDK.
	-> `title`: Заголовок уведомления.
	-> `body`: Текст уведомления.
	-> `image_url` (опционально): Ссылка на изображение для уведомления.
- **На выходе**:
	-> Кортеж `(True, Message ID)` от RuStore в случае успеха.
	-> Кортеж `(False, Текст ошибки)` в случае неудачи.

---

```python
# utils/rustore_push_utils.py

import requests
import logging
import json

def send_rustore_notification(project_id: str, service_token: str, device_token: str, title: str, body: str, image_url: str | None = None) -> tuple[bool, str]:
    """
    Отправляет Push-уведомление через сервис RuStore Push.
    """
    api_url = f"https://vkpns.rustore.ru/v1/projects/{project_id}/messages:send"
    
    headers = {
        'Authorization': f'Bearer {service_token}',
        'Content-Type': 'application/json'
    }

    notification_payload = {"title": title, "body": body}
    if image_url:
        notification_payload["image"] = image_url

    request_body = {
        "message": {
            "token": device_token,
            "notification": notification_payload
        }
    }

    try:
        logging.info(f"Отправка RuStore уведомления на токен: ...{device_token[-5:]}")
        response = requests.post(api_url, headers=headers, json=request_body, timeout=10)
        
        # Специальная обработка ошибок RuStore для невалидных токенов
        if response.status_code == 400 and 'is not registered' in response.text:
             error_msg = f"Token unregistered: {response.text}"
             logging.warning(error_msg)
             return False, "UNREGISTERED"

        # Проверяем на все остальные HTTP ошибки
        response.raise_for_status()
        
        # --- ИСПРАВЛЕННАЯ ЛОГИКА ---
        # Если мы дошли сюда, значит, статус ответа 2xx - это УСПЕХ.
        # Пытаемся извлечь message_id, но не падаем, если его нет.
        try:
            response_data = response.json()
            message_id = response_data.get("name", "N/A - Empty Response")
        except json.JSONDecodeError:
            message_id = "N/A - Invalid JSON"

        logging.info(f"Уведомление успешно отправлено. RuStore Message ID: {message_id}")
        return True, message_id # Возвращаем True, т.к. HTTP статус был успешным

    except requests.exceptions.HTTPError as e:
        error_msg = f"Ошибка HTTP {e.response.status_code}: {e.response.text}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg
    except Exception as e:
        error_msg = f"Непредвиденная ошибка: {e}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg
```