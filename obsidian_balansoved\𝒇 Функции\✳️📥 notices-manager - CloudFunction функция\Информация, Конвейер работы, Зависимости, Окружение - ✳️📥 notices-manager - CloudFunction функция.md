
Идентификатор - d4ea448kvn3t4tceoj7i
Описание - 🔔 Управляет уведомлениями пользователя: получение, архивация.
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: То<PERSON><PERSON>н аутентифицированного пользователя.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).
	-> Тело запроса:
		- `action` (string, **обязательно**): Тип операции. Допустимые значения:
			- `GET`: Получить уведомления.
			- `ARCHIVE`: Архивировать уведомление.
			- `MARK_AS_DELIVERED`: Пометить уведомления как доставленные/прочитанные.
		- `notice_id` (string, необязательно): ID уведомления. Обязателен для `ARCHIVE` и для получения одного конкретного уведомления через `GET`.
		- `notice_ids` (list of strings, необязательно): Список ID уведомлений. Обязателен для `MARK_AS_DELIVERED`.
		- **Параметры для `action: GET`**:
			- `page` (integer, необязательно): Номер страницы для пагинации (начиная с 0). По умолчанию `0`.
			- `get_archived` (boolean, необязательно): Если `true`, возвращает список архивированных уведомлений. По умолчанию `false`.

Внутренняя работа:
	-> Авторизация и извлечение user_id:
		-> Проверка наличия и валидности JWT токена из заголовков (Authorization или X-Forwarded-Authorization).
		-> Верификация токена с использованием JWT_SECRET, извлечение user_id.
	-> Подробное логирование:
		-> Вывод в лог полного содержимого event и context для отладки.
	-> Получение action из requestContext.apiGateway.operationContext.
	-> Создание подключения к YDB:
		-> Получение драйвера и пула сессий для notices-database с использованием переменных окружения.
	-> Маршрутизация по action в транзакции:
		-> Для GET:
			-> Извлечение параметров: notice_id из pathParams, page и get_archived из queryStringParameters.
			-> Если notice_id указан: выборка одной записи по ID, форматирование json-полей, возврат данных.
			-> Иначе: подсчет общего количества неархивированных уведомлений, расчет пагинации, выборка с ORDER BY created_at DESC, LIMIT и OFFSET, форматирование и возврат с метаданными.
			-> Обработка случая отсутствия таблицы (новый пользователь): возврат пустого списка.
		-> Для ARCHIVE и MARK_AS_DELIVERED:
			-> Парсинг тела запроса для получения notice_id или notice_ids.
			-> Для ARCHIVE: проверка существования, обновление is_archived = true.
			-> Для MARK_AS_DELIVERED: проверка списка, обновление is_delivered = true и delivered_at = текущему времени для всех IDs.
	-> Обработка ошибок:
		-> AuthError: 403 Forbidden.
		-> LogicError: 400 Bad Request.
		-> NotFoundError: 404 Not Found.
		-> Общие исключения: 500 Internal Server Error.

На выходе:
	-> `200 OK` (GET, список): `{"metadata": {"total": int, "page": int, "pages": int}, "data": [array of notices]}` с форматированными json-полями.
	-> `200 OK` (GET, одно уведомление): `{"data": {notice_object}}` с форматированными json-полями.
	-> `200 OK` (ARCHIVE): `{"message": "Notice archived successfully."}`
	-> `200 OK` (MARK_AS_DELIVERED): `{"message": "{count} notices marked as delivered."}`
	-> `400 Bad Request` для логических ошибок (например, invalid action, missing params).
	-> `403 Forbidden` для ошибок авторизации.
	-> `404 Not Found` для несуществующих уведомлений или страниц.
	-> `500 Internal Server Error` для внутренних ошибок.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_NOTICES` - Эндпоинт для `notices-database`.
	- `YDB_DATABASE_NOTICES` - Путь к `notices-database`.
	- `SA_KEY_FILE`
	- `JWT_SECRET`
	- `STATIC_ACCESS_KEY_ID` [[🗝️ auth-service-acc - Статический ключ доступа.md]]
	- `STATIC_SECRET_ACCESS_KEY` [[🗝️ auth-service-acc - Статический ключ доступа.md]]