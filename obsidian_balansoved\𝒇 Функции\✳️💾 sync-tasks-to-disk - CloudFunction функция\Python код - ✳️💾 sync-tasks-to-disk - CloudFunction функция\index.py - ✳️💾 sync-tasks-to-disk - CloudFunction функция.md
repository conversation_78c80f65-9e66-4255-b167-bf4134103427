
```python
import json
import os
import time
import tempfile
from datetime import timezone, timedelta
import datetime as dt
import logging
import traceback, sys
# --- Защищённый импорт локальных модулей ---
try:
    import runtime_utils
    import data_fetchers
    import task_processor
    from custom_errors import AuthError, LogicError
    from utils import auth_utils, request_parser, ydb_utils
    import ydb
except Exception:  # Логируем любые фатальные ошибки ещё до инициализации runtime_utils
    logging.basicConfig(level=logging.ERROR)
    logging.error("🔥 FATAL import/initialization error 🔥\n%s", traceback.format_exc())
    sys.exit(1)

# --- Включаем немедленный вывод каждой лог-записи ---
orig_log_message = runtime_utils.log_message

def log_message_live(message: str, level: str = "INFO"):
    """Proxy для runtime_utils.log_message с одновременным выводом в stdout."""
    orig_log_message(message, level)
    # Отображаем сразу в Cloud Logs
    level_up = level.upper()
    log_level = {
        "ERROR": logging.ERROR,
        "WARN": logging.WARNING,
        "WARNING": logging.WARNING,
        "INFO": logging.INFO,
        "PROGRESS": logging.INFO,
        "DEBUG": logging.DEBUG,
    }.get(level_up, logging.INFO)
    logging.log(log_level, message)

# Подменяем функцию во всём модуле
runtime_utils.log_message = log_message_live

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def _verify_owner_permissions(user_id: str, firm_id: str):
    """Проверяет, является ли пользователь владельцем указанной фирмы."""
    runtime_utils.log_progress(f"► Проверка прав владельца для пользователя {user_id} в фирме {firm_id}...")
    
    firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
    firms_pool = ydb.SessionPool(firms_driver)
    
    def check_role_in_db(session):
        query = session.prepare("DECLARE $uid AS Utf8; DECLARE $fid AS Utf8; SELECT roles FROM Users WHERE user_id = $uid AND firm_id = $fid;")
        res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id, "$fid": firm_id}, commit_tx=True)
        
        if not res[0].rows:
            raise AuthError("User is not a member of the specified firm.")
        
        roles = json.loads(res[0].rows[0].roles or '[]')
        if "OWNER" not in roles:
            raise AuthError("Access denied. Only the firm owner can initiate the sync.")
        return True
    
    firms_pool.retry_operation_sync(check_role_in_db)
    runtime_utils.log_progress(f"  {runtime_utils.TICK} Права владельца подтверждены.")

def handler(event, context):
    script_start = time.time()
    runtime_utils.log_message("--- Запуск функции синхронизации с Яндекс Диском ---")

    try:
        # --- ДЕТАЛЬНЫЙ ЛОГ ВХОДНОГО СОБЫТИЯ ---
        raw_headers = event.get('headers', {}) or {}
        # Маскируем длинные токены для безопасности
        masked_headers = {
            k: (v[:10] + '...' + v[-4:] if isinstance(v, str) and 'authorization' in k.lower() and len(v) > 20 else v)
            for k, v in raw_headers.items()
        }
        body_preview = (event.get('body') or '')
        if isinstance(body_preview, (bytes, bytearray)):
            body_preview = body_preview.decode('utf-8', errors='ignore')
        body_preview = body_preview[:1000] + ('...' if len(body_preview) > 1000 else '')
        runtime_utils.log_message(f"RAW HEADERS: {json.dumps(masked_headers, ensure_ascii=False)}")
        runtime_utils.log_message(f"RAW BODY PREVIEW (first 1k chars): {body_preview}")

        # 1. Авторизация и проверка прав
        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        if not firm_id:
            raise LogicError("`firm_id` is required in the request body.")

        headers = event.get('headers', {}) or {}
        auth_header = headers.get('Authorization') or headers.get('authorization')
        xfwd_header = headers.get('X-Forwarded-Authorization') or headers.get('x-forwarded-authorization')

        token_header = None
        if xfwd_header and xfwd_header.lower().startswith('bearer '):
            token_header = xfwd_header  # Приоритет user JWT, проброшенного планировщиком
        elif auth_header and auth_header.lower().startswith('bearer '):
            token_header = auth_header  # Фоллбэк — прямой вызов из клиента

        if not token_header:
            raise AuthError("Bearer token required in Authorization или X-Forwarded-Authorization header.")

        user_jwt = token_header.split(' ', 1)[1]
        
        user_payload = auth_utils.verify_jwt(user_jwt)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired JWT.")
        
        user_id = user_payload['user_id']
        _verify_owner_permissions(user_id, firm_id)

        # 2. Получение токена Я.Диска и проверка квоты
        def _to_bool(val):
            """Нормализует различные формы true/false к bool."""
            if isinstance(val, bool):
                return val
            if isinstance(val, (int, float)):
                return val != 0
            if isinstance(val, str):
                return val.strip().lower() in ("true", "1", "yes")
            return False

        env_test_mode = _to_bool(os.environ.get("SYNC_TASKS_TEST_MODE", "false"))
        is_test_mode = env_test_mode
        if is_test_mode:
            runtime_utils.log_message("РЕЖИМ ТЕСТИРОВАНИЯ АКТИВЕН", "WARN")

        runtime_utils.log_progress("► Получение интеграций Я.Диска...")
        integrations_response = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_INTEGRATIONS, {"firm_id": firm_id, "action": "GET"}, user_jwt)
        if not integrations_response:
            raise RuntimeError("Failed to get integrations.")

        yadisk_data = integrations_response.get('integrations', {}).get('yandex_disk', {})
        yadisk_token = yadisk_data.get('token')
        if not yadisk_token:
            raise RuntimeError("Yandex Disk token not found in integrations.")
        runtime_utils.log_progress(f"  {runtime_utils.TICK} Токен Яндекс Диска получен.")

        if not is_test_mode:
            last_sync_str = yadisk_data.get('last_sync_utc')
            if last_sync_str:
                try:
                    last_sync_dt = dt.datetime.fromisoformat(last_sync_str.replace("Z", "+00:00"))
                    if dt.datetime.now(timezone.utc) - last_sync_dt < timedelta(days=runtime_utils.SYNC_FREQUENCY_DAYS):
                        raise LogicError(f"Sync was performed less than {runtime_utils.SYNC_FREQUENCY_DAYS} days ago. Skipping.")
                except ValueError:
                    runtime_utils.log_message(f"Неверный формат даты '{last_sync_str}'. Игнорирование.", "WARN")

        # 3. Обновление времени синхронизации
        now_utc_iso = dt.datetime.now(timezone.utc).isoformat()
        yadisk_data['last_sync_utc'] = now_utc_iso
        update_payload = {"firm_id": firm_id, "action": "UPSERT", "payload": {"yandex_disk": yadisk_data}}
        if not runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_INTEGRATIONS, update_payload, user_jwt):
            raise RuntimeError("Failed to update sync time.")
        runtime_utils.log_progress(f"  {runtime_utils.TICK} Время последней синхронизации обновлено.")

        # 4. Получение задач
        data_fetchers.populate_employee_cache(user_jwt, firm_id)
        tasks_to_process = data_fetchers.get_all_tasks(user_jwt, firm_id)
        
        if not tasks_to_process:
            runtime_utils.log_message("Актуальных задач для обработки не найдено.")
            return {"statusCode": 200, "body": json.dumps({"message": "No tasks to process."})}

        # 5. Обработка задач
        with tempfile.TemporaryDirectory() as temp_dir:
            runtime_utils.log_progress(f"Всего задач для обработки: {len(tasks_to_process)}.")
            for i, task_info in enumerate(tasks_to_process):
                runtime_utils.log_progress(f"\n[ЗАДАЧА {i+1}/{len(tasks_to_process)}]")
                task_processor.process_single_task(task_info, user_jwt, firm_id, yadisk_token, temp_dir)
                time.sleep(0.1)

        runtime_utils.log_message(f"Синхронизация успешно завершена! Время выполнения: {time.time() - script_start:.2f} сек.")
        return {"statusCode": 200, "body": json.dumps({"message": f"Successfully processed {len(tasks_to_process)} tasks."})}

    except AuthError as e:
        runtime_utils.log_message(f"Ошибка прав доступа: {e}", "ERROR")
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        runtime_utils.log_message(f"Ошибка логики: {e}", "ERROR")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        runtime_utils.log_message(f"Критическая ошибка: {e}", "ERROR")
        import traceback
        runtime_utils.log_message(f"Трассировка: {traceback.format_exc()}", "ERROR")
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
    finally:
        runtime_utils.print_final_log()
```