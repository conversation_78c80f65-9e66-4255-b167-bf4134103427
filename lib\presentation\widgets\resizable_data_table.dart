import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Виджет таблицы с изменяемой шириной столбцов
class ResizableDataTable extends StatefulWidget {
  final List<DataColumn> columns;
  final List<DataRow> rows;
  final bool showCheckboxColumn;
  final int? sortColumnIndex;
  final bool sortAscending;
  final String prefsKey;
  final List<double>? initialColumnWidths;
  final double minColumnWidth;
  final double defaultColumnWidth;
  final double borderWidth;

  const ResizableDataTable({
    super.key,
    required this.columns,
    required this.rows,
    required this.prefsKey,
    this.showCheckboxColumn = false,
    this.sortColumnIndex,
    this.sortAscending = true,
    this.initialColumnWidths,
    this.minColumnWidth = 80.0,
    this.defaultColumnWidth = 150.0,
    this.borderWidth = 1.0,
  });

  @override
  State<ResizableDataTable> createState() => _ResizableDataTableState();
}

class _ResizableDataTableState extends State<ResizableDataTable> {
  late List<double> _columnWidths;
  bool _isInitialized = false;
  final ScrollController _scrollController = ScrollController();
  int? _resizingColumnIndex;

  @override
  void initState() {
    super.initState();
    _initializeColumnWidths();
  }

  Future<void> _initializeColumnWidths() async {
    await _loadColumnWidths();
    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  Future<void> _loadColumnWidths() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(widget.prefsKey);

      if (jsonString != null && jsonString.isNotEmpty) {
        final List<dynamic> widths = jsonDecode(jsonString);
        _columnWidths = widths.cast<double>();

        if (_columnWidths.length != widget.columns.length) {
          _resetToDefaults();
        }
      } else {
        _resetToDefaults();
      }
    } catch (e) {
      _resetToDefaults();
    }
  }

  void _resetToDefaults() {
    if (widget.initialColumnWidths != null &&
        widget.initialColumnWidths!.length == widget.columns.length) {
      _columnWidths = List.from(widget.initialColumnWidths!);
    } else {
      _columnWidths = List.filled(
        widget.columns.length,
        widget.defaultColumnWidth,
      );
    }
  }

  Future<void> _saveColumnWidths() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(widget.prefsKey, jsonEncode(_columnWidths));
    } catch (e) {
      // Игнорируем ошибки сохранения
    }
  }

  void _updateColumnWidth(int index, double delta) {
    setState(() {
      _columnWidths[index] = (_columnWidths[index] + delta).clamp(
        widget.minColumnWidth,
        double.infinity,
      );
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.surfaceContainerHighest.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: widget.borderWidth,
          ),
        ),
      ),
      child: Row(
        children: List.generate(widget.columns.length * 2 - 1, (index) {
          if (index.isOdd) {
            // Это разделитель
            final columnIndex = index ~/ 2;
            return GestureDetector(
              onPanStart: (_) {
                _resizingColumnIndex = columnIndex;
              },
              onPanUpdate: (details) {
                _updateColumnWidth(columnIndex, details.delta.dx);
              },
              onPanEnd: (_) {
                _resizingColumnIndex = null;
                _saveColumnWidths();
              },
              child: MouseRegion(
                cursor: SystemMouseCursors.resizeColumn,
                child: Container(
                  width: 8,
                  height: 56,
                  color: Colors.transparent,
                  child: Center(
                    child: Container(
                      width: widget.borderWidth,
                      color:
                          _resizingColumnIndex == columnIndex
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).dividerColor,
                    ),
                  ),
                ),
              ),
            );
          } else {
            // Это заголовок столбца
            final columnIndex = index ~/ 2;
            final column = widget.columns[columnIndex];
            return Container(
              width: _columnWidths[columnIndex],
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              alignment: Alignment.centerLeft,
              child: InkWell(
                onTap:
                    column.onSort != null
                        ? () {
                          final newAscending =
                              widget.sortColumnIndex == columnIndex
                                  ? !widget.sortAscending
                                  : true;
                          column.onSort!(columnIndex, newAscending);
                        }
                        : null,
                child: Row(
                  children: [
                    Expanded(child: column.label),
                    if (widget.sortColumnIndex == columnIndex)
                      Icon(
                        widget.sortAscending
                            ? Icons.arrow_upward
                            : Icons.arrow_downward,
                        size: 16,
                      ),
                  ],
                ),
              ),
            );
          }
        }),
      ),
    );
  }

  Widget _buildRow(DataRow row, int rowIndex) {
    final isEven = rowIndex.isEven;
    return Container(
      decoration: BoxDecoration(
        color:
            isEven
                ? Theme.of(context).colorScheme.surface
                : Theme.of(
                  context,
                ).colorScheme.surfaceContainerHighest.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor.withOpacity(0.3),
            width: widget.borderWidth,
          ),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap:
              row.onSelectChanged != null
                  ? () => row.onSelectChanged!(true)
                  : null,
          child: Row(
            children: List.generate(widget.columns.length * 2 - 1, (index) {
              if (index.isOdd) {
                // Это разделитель
                return Container(
                  width: 8,
                  height: 48,
                  color: Colors.transparent,
                );
              } else {
                // Это ячейка
                final columnIndex = index ~/ 2;
                return Container(
                  width: _columnWidths[columnIndex],
                  height: 48,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.centerLeft,
                  child: row.cells[columnIndex].child,
                );
              }
            }),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    final totalWidth =
        _columnWidths.reduce((a, b) => a + b) +
        (widget.columns.length - 1) * 8; // Учитываем ширину разделителей

    return Scrollbar(
      controller: _scrollController,
      thumbVisibility: true,
      trackVisibility: true,
      scrollbarOrientation: ScrollbarOrientation.bottom,
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: SizedBox(
          width: totalWidth,
          child: Column(
            mainAxisSize: MainAxisSize.min, // Важно! Подстраиваемся под контент
            children: [
              _buildHeader(),
              ...widget.rows.asMap().entries.map((entry) {
                return _buildRow(entry.value, entry.key);
              }),
            ],
          ),
        ),
      ),
    );
  }
}
