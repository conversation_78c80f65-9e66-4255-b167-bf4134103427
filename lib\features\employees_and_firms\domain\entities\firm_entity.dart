import 'package:equatable/equatable.dart';

class FirmEntity extends Equatable {
  final String id;
  final String name;
  final String ownerUserId;
  final List<String> roles; // роли текущего пользователя в этой фирме

  const FirmEntity({
    required this.id,
    required this.name,
    required this.ownerUserId,
    required this.roles,
  });

  @override
  List<Object?> get props => [id, name, ownerUserId, roles];

  bool get isOwner => roles.contains('OWNER');
  bool get isAdmin => roles.contains('ADMIN');
}
