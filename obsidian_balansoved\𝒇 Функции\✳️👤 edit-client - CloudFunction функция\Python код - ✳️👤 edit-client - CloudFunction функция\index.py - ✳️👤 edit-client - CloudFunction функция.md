+
```python
import json, os, datetime, pytz, logging, uuid
import ydb
from utils import auth_utils, ydb_utils, request_parser
from get import get_client
from upsert import upsert_client
from delete import delete_client
from custom_errors import AuthError, LogicError, NotFoundError # <<< ИЗМЕНЕНО

# Глобальный список для накопления логов
log_accumulator = []

def check_membership_and_get_roles(session, user_id, firm_id):
    log_accumulator.append(f"[check_membership_and_get_roles] Checking membership for user_id={user_id}, firm_id={firm_id}")
    query_text = """
        DECLARE $user_id AS Utf8;
        DECLARE $firm_id AS Utf8;
        SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;
    """
    log_accumulator.append(f"[check_membership_and_get_roles] Executing query: {query_text}")
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(
        query,
        {"$user_id": user_id, "$firm_id": firm_id},
        commit_tx=True
    )
    log_accumulator.append(f"[check_membership_and_get_roles] Query result rows count: {len(result[0].rows) if result and result[0].rows else 0}")
    if not result[0].rows:
        log_accumulator.append(f"[check_membership_and_get_roles] User {user_id} is not a member of firm {firm_id}")
        raise AuthError("User is not a member of the specified firm.")
    roles = json.loads(result[0].rows[0].roles or '[]')
    log_accumulator.append(f"[check_membership_and_get_roles] User {user_id} is a member of firm {firm_id} with roles: {roles}")
    return roles

def handler(event, context):
    global log_accumulator
    log_accumulator = []  # Сброс накопителя логов для каждого запроса
    
    try:
        # Логирование входных данных в сыром виде
        log_accumulator.append(f"[handler] RAW INPUT EVENT: {json.dumps(event, default=str)}")
        log_accumulator.append(f"[handler] RAW INPUT CONTEXT: {json.dumps(context.__dict__ if hasattr(context, '__dict__') else str(context), default=str)}")
        
        log_accumulator.append("[handler] Starting request processing")
        headers = event.get('headers', {}) or {}
        log_accumulator.append(f"[handler] Headers: {headers}")
        
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        log_accumulator.append(f"[handler] Auth header found: {'Yes' if auth_header else 'No'}")
        
        if not auth_header.startswith('Bearer '):
            log_accumulator.append("[handler] Auth header does not start with 'Bearer '")
            raise AuthError("Unauthorized")
        parts = auth_header.split(' ')
        if len(parts) != 2:
            log_accumulator.append(f"[handler] Invalid Bearer token format, parts count: {len(parts)}")
            raise AuthError("Unauthorized: Invalid Bearer token format.")
        token = parts[1]
        log_accumulator.append(f"[handler] Token extracted, length: {len(token)}")
        
        user_payload = auth_utils.verify_jwt(token)
        log_accumulator.append(f"[handler] JWT verification result: {user_payload is not None}")
        if not user_payload or 'user_id' not in user_payload: 
            log_accumulator.append("[handler] Invalid token or missing user_id")
            raise AuthError("Invalid token")
        requesting_user_id = user_payload['user_id']
        log_accumulator.append(f"[handler] Requesting user ID: {requesting_user_id}")
        try:
            data = request_parser.parse_request_body(event)
            log_accumulator.append(f"[handler] Parsed request body: {data}")
        except ValueError as e:
            log_accumulator.append(f"[handler] Request body parsing failed: {str(e)}")
            raise LogicError(str(e))
        
        firm_id = data.get('firm_id')
        action = data.get('action')
        client_id = data.get('client_id')
        creation_date_str = data.get('creation_date')
        payload = data.get('payload', {})
        
        log_accumulator.append(f"[handler] Request parameters - firm_id: {firm_id}, action: {action}, client_id: {client_id}, creation_date: {creation_date_str}")
        log_accumulator.append(f"[handler] Payload size: {len(str(payload))} characters")
        
        if not all([firm_id, action]):
            log_accumulator.append("[handler] Missing required parameters: firm_id and/or action")
            raise LogicError("firm_id and action are required.")
        
        creation_date = None
        if creation_date_str:
            log_accumulator.append(f"[handler] Parsing creation_date: {creation_date_str} (type: {type(creation_date_str)})")
            try:
                creation_date = datetime.datetime.strptime(str(creation_date_str), '%Y-%m-%d').date()
                log_accumulator.append(f"[handler] Successfully parsed creation_date: {creation_date}")
            except (ValueError, TypeError) as e:
                log_accumulator.append(f"[handler] Failed to parse creation_date '{creation_date_str}' (type: {type(creation_date_str).__name__}): {str(e)}")
                raise LogicError(f"Invalid date format for 'creation_date' '{creation_date_str}' (type: {type(creation_date_str).__name__}). Use YYYY-MM-DD.")
        log_accumulator.append("[handler] Initializing YDB connections")
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        log_accumulator.append("[handler] Firms YDB connection established")
        
        log_accumulator.append("[handler] Checking user membership and roles")
        user_roles = firms_pool.retry_operation_sync(
            lambda s: check_membership_and_get_roles(s, requesting_user_id, firm_id)
        )
        log_accumulator.append(f"[handler] User roles retrieved: {user_roles}")
        
        clients_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_CLIENTS"], os.environ["YDB_DATABASE_CLIENTS"])
        clients_pool = ydb.SessionPool(clients_driver)
        log_accumulator.append("[handler] Clients YDB connection established")
        
        table_name = f"clients_{firm_id}"
        log_accumulator.append(f"[handler] Target table: {table_name}")
        def client_transaction_router(session):
            log_accumulator.append(f"[client_transaction_router] Routing action: {action}")
            if action == "GET":
                log_accumulator.append("[client_transaction_router] Executing GET operation")
                return get_client(session, table_name, client_id, creation_date, log_accumulator)
            elif action == "UPSERT":
                log_accumulator.append("[client_transaction_router] Executing UPSERT operation")
                return upsert_client(session, table_name, payload, client_id, creation_date, user_roles, log_accumulator)
            elif action == "DELETE":
                log_accumulator.append("[client_transaction_router] Executing DELETE operation")
                return delete_client(session, table_name, client_id, creation_date, user_roles, log_accumulator)
            else:
                log_accumulator.append(f"[client_transaction_router] Invalid action: {action}")
                raise LogicError(f"Invalid action: {action}")
        
        log_accumulator.append("[handler] Starting database transaction")
        result = clients_pool.retry_operation_sync(client_transaction_router)
        log_accumulator.append(f"[handler] Transaction completed successfully with status: {result.get('statusCode')}")
        return result
    except AuthError as e:
        log_accumulator.append(f"[handler] AuthError occurred: {str(e)}")
        result = {"statusCode": 403, "body": json.dumps({"error": str(e)})}
    except LogicError as e:
        log_accumulator.append(f"[handler] LogicError occurred: {str(e)}")
        result = {"statusCode": 400, "body": json.dumps({"error": str(e)})}
    except NotFoundError as e:
        log_accumulator.append(f"[handler] NotFoundError occurred: {str(e)}")
        result = {"statusCode": 404, "body": json.dumps({"error": str(e)})}
    except Exception as e:
        log_accumulator.append(f"[handler] Unhandled exception occurred: {str(e)}")
        result = {"statusCode": 500, "body": json.dumps({"error": "Internal Server Error"})}
    finally:
        # Вывод всех накопленных логов единым блоком
        accumulated_logs = "\n".join(log_accumulator)
        print(f"=== ACCUMULATED LOGS START ===\n{accumulated_logs}\n=== ACCUMULATED LOGS END ===")
        
    return result
```