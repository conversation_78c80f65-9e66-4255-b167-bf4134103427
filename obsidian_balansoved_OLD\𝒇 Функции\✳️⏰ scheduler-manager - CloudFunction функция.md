Идентификатор - d4eh9o1sau3l2mv3g09n
Описание - ⏰ Управляет запланированными событиями: создание, обновление, удаление и получение.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> **Аутентификация**: Предполагается, что доступ к этой функции ограничен на уровне API Gateway и она не вызывается напрямую клиентом. Аутентификация через JWT токен пользователя не требуется.
	-> Тело запроса:
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
		- `event_id` (string, необязательно): ID события.
			- Обязателен для `DELETE`.
			- Обязателен для обновления при `UPSERT`.
			- Приоритетен для `GET`.
		- `custom_identifier` (string, необязательно): Кастомный идентификатор для поиска событий при `GET`. Используется, если `event_id` не указан.
		- `payload` (object, необязательно): Объект с данными события. Обязателен для `UPSERT`.

Внутренняя работа:
    -> **Парсинг запроса**: Используется `utils.request_parser` для извлечения данных из тела запроса, включая `action`, `event_id`, `custom_identifier` и `payload`.
    -> **Подключение к БД**: Инициализируется драйвер YDB для `scheduler-database` с использованием переменных окружения `YDB_ENDPOINT_SCHEDULER` и `YDB_DATABASE_SCHEDULER`.
    -> **Маршрутизация по `action`** в транзакции:
        -> **`GET`**:
            -> Если указан `event_id`, выполняется запрос на выборку события по ID; если не найдено, ошибка 404.
            -> Иначе, если указан `custom_identifier`, выборка событий по нему.
            -> Иначе, выборка всех активных событий (`is_active = true`).
            -> Результаты форматируются, парсятся JSON-поля и возвращаются в `data`.
        -> **`UPSERT`**:
            -> Если цель - `endpoints-manager`, модифицируется `request_body_json` для упрощения payload.
            -> Если `event_id` указан (обновление): обновляются поля в таблице, включая `updated_at`.
            -> Иначе (создание): генерируется новый `event_id`, устанавливается `created_at`, `is_active=true` (если не указано), обновляется в таблице с помощью UPSERT.
            -> Возвращается 201 для создания или 200 для обновления.
        -> **`DELETE`**: Удаление записи по `event_id` из таблицы.
    -> **Обработка ошибок**: Логирование, возврат 400 для логики, 404 для не найденного, 500 для внутренних ошибок.

На выходе:
	-> `200 OK` (GET): `{"data": [{...}]}` или `{"data": {...}}`
	-> `201 Created` (UPSERT/create): `{"message": "Scheduled event created", "event_id": "..."}`
	-> `200 OK` (UPSERT/update): `{"message": "Scheduled event updated"}`
	-> `200 OK` (DELETE): `{"message": "Scheduled event deleted"}`
	-> `400 Bad Request`: Неверные или отсутствующие параметры.
	-> `404 Not Found`: Указанный `event_id` не найден.
	-> `500 Internal Server Error`: Ошибка на стороне сервера или БД.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/ydb_utils.py` ([[📄 utils - ydb_utils.md]]), `utils/request_parser.py` ([[📄 utils - request_parser.md]])
- **Переменные окружения**:
	- `YDB_ENDPOINT_SCHEDULER` - Эндпоинт для [[💾 scheduler-database - База данных YandexDatabase.md]].
	- `YDB_DATABASE_SCHEDULER` - Путь к [[💾 scheduler-database - База данных YandexDatabase.md]].
	- `SA_KEY_FILE` ([[ydb_sa_key.json]])

### index.py
```python
import json
import os
import logging
import ydb
from utils import ydb_utils, request_parser

from get import get_events
from upsert import upsert_event
from delete import delete_event
from custom_errors import LogicError, NotFoundError

# Настраиваем подробное логирование
logging.getLogger().setLevel(logging.DEBUG)

def _log_event_context(event, context):
    """Выводит в лог подробную информацию о входящем событии и контексте вызова."""
    try:
        logging.debug("RAW EVENT: %s", json.dumps(event, default=str)[:10000])
    except Exception as e:
        logging.debug("RAW EVENT (non-json serialisable): %s", event)
    if context is not None:
        logging.debug(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s",
            getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None),
        )

def handler(event, context):
    _log_event_context(event, context)
    try:
        # Аутентификация пользователя здесь не требуется, т.к. функция внутренняя
        data = request_parser.parse_request_body(event)
        action = data.get('action')
        
        if not action:
            raise LogicError("Action is a required parameter.")

        driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_SCHEDULER"],
            os.environ["YDB_DATABASE_SCHEDULER"]
        )
        pool = ydb.SessionPool(driver)
        table_name = "ScheduledEvents"

        def transaction_router(session):
            event_id = data.get('event_id')
            custom_identifier = data.get('custom_identifier')
            payload = data.get('payload', {})

            if action == "GET":
                return get_events(session, table_name, event_id, custom_identifier)
            
            elif action == "UPSERT":
                # Если клиент не передал request_headers_json, копируем Authorization из вызова
                if 'request_headers_json' not in payload:
                    logging.debug("'request_headers_json' not in payload. Checking event headers.")
                    hdrs = event.get('headers', {}) or {}
                    auth_val = hdrs.get('Authorization') or hdrs.get('authorization') or hdrs.get('X-Forwarded-Authorization') or hdrs.get('x-forwarded-authorization')
                    if auth_val:
                        logging.debug("Found 'Authorization' header. Copying to payload.")
                        payload['request_headers_json'] = {"Authorization": auth_val}
                    else:
                        logging.warning("'Authorization' header not found in event. 'request_headers_json' will be empty.")
                else:
                    logging.debug("'request_headers_json' already present in payload.")
                return upsert_event(session, table_name, payload, event_id)
            
            elif action == "DELETE":
                return delete_event(session, table_name, event_id)
            
            else:
                raise LogicError(f"Invalid action '{action}'.")

        return pool.retry_operation_sync(transaction_router)

    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing scheduled event request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```

### get.py
```python
import json
import logging
import ydb
from custom_errors import NotFoundError

def _format_row(row, columns):
    """Преобразует строку YDB в словарь, парсит JSON-поля."""
    data = {}
    for c in columns:
        value = row[c.name]
        # Парсим JSON-поля, если они не пустые
        if 'json' in c.name and value:
            data[c.name] = json.loads(value)
        else:
            data[c.name] = value
    return data

def get_events(session, table_name, event_id=None, custom_identifier=None):
    """Получает запланированные события по ID, кастомному идентификатору или все."""
    tx = session.transaction(ydb.SerializableReadWrite())

    if event_id:
        logging.debug(f"Fetching event by event_id: {event_id}")
        query_text = f"DECLARE $event_id AS Utf8; SELECT * FROM `{table_name}` WHERE event_id = $event_id;"
        res = tx.execute(session.prepare(query_text), {"$event_id": event_id})
        if not res[0].rows:
            raise NotFoundError(f"Scheduled event with id {event_id} not found.")
        data = _format_row(res[0].rows[0], res[0].columns)

    elif custom_identifier:
        logging.debug(f"Fetching events by custom_identifier: {custom_identifier}")
        query_text = f"DECLARE $custom_identifier AS Utf8; SELECT * FROM `{table_name}` WHERE custom_identifier = $custom_identifier;"
        res = tx.execute(session.prepare(query_text), {"$custom_identifier": custom_identifier})
        data = [_format_row(row, res[0].columns) for row in res[0].rows]
        logging.debug(f"Found {len(data)} events for custom_identifier: {custom_identifier}")

    else:
        logging.debug("Fetching all active scheduled events.")
        query_text = f"SELECT * FROM `{table_name}` WHERE is_active = true;"
        res = tx.execute(session.prepare(query_text))
        data = [_format_row(row, res[0].columns) for row in res[0].rows]

    tx.commit()
    return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}
```

### upsert.py
```python
# scheduler-manager/upsert.py

import json
import uuid
import datetime
import pytz
import ydb
from custom_errors import LogicError

def _get_declare_for_event(payload):
    """Генерирует DECLARE и параметры для YQL-запроса из словаря."""
    declare_clauses, params = "", {}
    type_map = {
        'event_id': ydb.PrimitiveType.Utf8,
        'function_id': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'custom_identifier': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'is_annual': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'execution_dates_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'request_body_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'request_headers_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'last_invoked_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
        'is_active': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'created_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
        'updated_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
    }

    for key, value in payload.items():
        if key in type_map:
            if 'json' in key:
                # Если значение - словарь или список, преобразуем в JSON-строку.
                if isinstance(value, (dict, list)):
                    params[f"${key}"] = json.dumps(value)
                # Если значение - уже строка, используем как есть (предполагая, что это готовый JSON).
                elif isinstance(value, str):
                    params[f"${key}"] = value
            else:
                 params[f"${key}"] = value
                 
            type_name_str = str(type_map[key])
            type_name = type_name_str.replace("Optional[", "").replace("]", "") if "Optional" in type_name_str else type_name_str
            declare_clauses += f"DECLARE ${key} AS {type_name}; "
            
    return declare_clauses, params

def upsert_event(session, table_name, payload, event_id=None):
    if not payload:
        raise LogicError("Payload is required for UPSERT action.")

    # --- ИСПРАВЛЕННЫЙ БЛОК ---
    ENDPOINTS_MANAGER_FUNC_ID = "d4euafv5ijaums363e84" 
    target_function_id = payload.get('function_id')

    if target_function_id == ENDPOINTS_MANAGER_FUNC_ID:
        # original_request_body - это словарь вида: {"user_id_to_notify": "...", "payload": {...}}
        original_request_body = payload.get('request_body_json') or {}

        # Если тело - строка (клиент передал готовый JSON), парсим его
        body_as_dict = {}
        if isinstance(original_request_body, str):
            try:
                body_as_dict = json.loads(original_request_body)
            except json.JSONDecodeError:
                pass # Оставляем пустым, если парсинг не удался
        elif isinstance(original_request_body, dict):
            body_as_dict = original_request_body
            
        # Создаем простой payload для `endpoints-manager`
        endpoints_payload = {
            "action": "SEND",
            **body_as_dict
        }
            
        # Кладём в payload JSON-строку этого нового, простого payload'а
        payload['request_body_json'] = json.dumps(endpoints_payload)
    # --- КОНЕЦ ИСПРАВЛЕННОГО БЛОКА ---

    tx = session.transaction(ydb.SerializableReadWrite())
    now = datetime.datetime.now(pytz.utc)
    payload['updated_at'] = now

    if event_id:  # Update
        payload['event_id'] = event_id
        declare_clauses, params = _get_declare_for_event(payload)
        set_clauses = ", ".join([f"`{k}` = ${k}" for k in payload if k != 'event_id'])
        
        query_text = f"{declare_clauses} UPDATE `{table_name}` SET {set_clauses} WHERE event_id = $event_id;"
        tx.execute(session.prepare(query_text), params)
        tx.commit()
        return {"statusCode": 200, "body": json.dumps({"message": "Scheduled event updated"})}
    
    else:  # Create
        new_event_id = str(uuid.uuid4())
        payload['event_id'] = new_event_id
        payload['created_at'] = now
        if 'is_active' not in payload:
            payload['is_active'] = True
        # Для нового события оставляем updated_at пустым (NULL), чтобы триггер выполнил его при первом запуске
        payload.pop('updated_at', None)

        declare_clauses, params = _get_declare_for_event(payload)
        columns = ", ".join([f"`{k}`" for k in payload.keys()])
        placeholders = ", ".join([f"${k}" for k in payload.keys()])
        
        query_text = f"{declare_clauses} UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"
        tx.execute(session.prepare(query_text), params)
        tx.commit()
        return {"statusCode": 201, "body": json.dumps({"message": "Scheduled event created", "event_id": new_event_id})}
```

### delete.py
```python
import json
import ydb
from custom_errors import LogicError

def delete_event(session, table_name, event_id):
    if not event_id:
        raise LogicError("event_id is required for DELETE action.")

    tx = session.transaction(ydb.SerializableReadWrite())
    query = session.prepare(f"DECLARE $event_id AS Utf8; DELETE FROM `{table_name}` WHERE event_id = $event_id;")
    tx.execute(query, {"$event_id": event_id})
    tx.commit()

    return {"statusCode": 200, "body": json.dumps({"message": "Scheduled event deleted"})}
```

### custom_errors.py
```python
class LogicError(Exception):
    """Ошибка бизнес-логики (например, неверные параметры)."""
    pass

class NotFoundError(Exception):
    """Запрошенный ресурс не найден."""
    pass
```