
---
Идентификатор - etnhgtg29jpjakvf0v6d
Путь к базе - /ru-central1/b1g2bgg0i9r8beucbthc/etnhgtg29jpjakvf0v6d
Эндпоинт - grpcs://ydb.serverless.yandexcloud.net:2135/?database=/ru-central1/b1g2bgg0i9r8beucbthc/etnhgtg29jpjakvf0v6d

---
# Таблицы
В данной базе хранятся данные о подписках и использовании ресурсов компаниями.

#### Таблица: `tariffs_and_storage`

| # | Имя | Ключ | Тип | Описание |
| --- | ------------------------- | ---- | ----------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 0 | `firm_id` | PK | `Utf8` | Уникальный идентификатор компании (UUID), связанный с `Firms.firm_id`. |
| 1 | `subscription_info_json` | | `Json` | JSON-объект с деталями подписки. Пример: `{"plan_id": "pro", "started_at": "...", "expires_at": "...", "auto_renew": true, "status": "active"}` |
| 2 | `storage_info_json` | | `Json` | JSON-объект с данными о хранилище. Пример: `{"quota_bytes": 10737418240, "used_bytes": 512000, "last_recalculated_at": "..."}` |
| 3 | `confidential_data_json` | | `Json` | JSON-объект с конфиденциальными данными, например, зашифрованными API-ключами для внешних интеграций. Доступ к нему должен быть строго ограничен. |
| 4 | `created_at` | | `Timestamp` | Системное время создания записи. |
| 5 | `updated_at` | | `Timestamp` | Системное время последнего обновления записи. |