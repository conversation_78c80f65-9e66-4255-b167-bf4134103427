import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/task_entity.dart';
import '../../../employees_and_firms/domain/entities/employee_entity.dart';
import '../../../clients/domain/entities/client_entity.dart';
import '../../../employees_and_firms/domain/usecases/get_employees_usecase.dart';
import '../../../clients/domain/usecases/get_clients_usecase.dart';
import 'task_detail_state.dart';

class TaskDetailCubit extends Cubit<TaskDetailState> {
  final GetEmployeesUseCase _getEmployeesUseCase;
  final GetClientsUseCase _getClientsUseCase;

  TaskDetailCubit({
    required GetEmployeesUseCase getEmployeesUseCase,
    required GetClientsUseCase getClientsUseCase,
  }) : _getEmployeesUseCase = getEmployeesUseCase,
       _getClientsUseCase = getClientsUseCase,
       super(TaskDetailInitial());

  Future<void> initialize(TaskEntity task, String firmId) async {
    emit(TaskDetailLoading());
    
    try {
      // Загружаем сотрудников и клиентов параллельно
      final results = await Future.wait([
        _getEmployeesUseCase(firmId),
        _getClientsUseCase(firmId),
      ]);
      
      final employeesResult = results[0];
      final clientsResult = results[1];
      
      // Обрабатываем результаты Either
      final employees = employeesResult.fold(
        (failure) => throw Exception(failure.toString()),
        (employees) => employees,
      );
      
      final clients = clientsResult.fold(
        (failure) => throw Exception(failure.toString()),
        (clients) => clients,
      );
      
      emit(TaskDetailLoaded(
        task: task,
        employees: employees.cast<EmployeeEntity>(),
        clients: clients.cast<ClientEntity>(),
      ));
    } catch (e) {
      emit(TaskDetailError(message: e.toString()));
    }
  }

  void toggleExpanded() {
    final currentState = state;
    if (currentState is TaskDetailLoaded) {
      emit(currentState.copyWith(isExpanded: !currentState.isExpanded));
    }
  }

  void toggleShowFullDescription() {
    final currentState = state;
    if (currentState is TaskDetailLoaded) {
      emit(currentState.copyWith(showFullDescription: !currentState.showFullDescription));
    }
  }

  void updateTask(TaskEntity updatedTask) {
    final currentState = state;
    if (currentState is TaskDetailLoaded) {
      emit(currentState.copyWith(task: updatedTask));
    }
  }

  String getEmployeeName(String id) {
    final currentState = state;
    if (currentState is TaskDetailLoaded) {
      final match = currentState.employees.where((e) => e.id == id);
      if (match.isNotEmpty) {
        final emp = match.first;
        return emp.userName ?? emp.email ?? id;
      }
    }
    return id;
  }

  String getClientName(String id) {
    final currentState = state;
    if (currentState is TaskDetailLoaded) {
      final match = currentState.clients.where((cl) => cl.id == id);
      if (match.isNotEmpty) {
        return match.first.name;
      }
    }
    return id;
  }
}