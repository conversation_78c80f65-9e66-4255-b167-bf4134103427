
Идентификатор - d4e4mpjj10hka0o8ct9d
Описание - 📝 Управляет задачами. Позволяет получать, создавать, обновлять и удалять задачи, а также автоматически синхронизирует напоминания.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы.
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
 		- `task_id` (string, необязательно): ID задачи для `GET` (одна), `UPSERT` (обновление), `DELETE`.
		- `payload` (object, необязательно): Данные для `UPSERT`. **Может содержать `main_task_id`, `subtasks_json`, `periodic_parent_id`, `periodic_children_json`, `reminders_json`**.
		- **Параметры для `action: GET`**:
			- **Для бессрочных задач:**
				- `page` (integer, необязательно): Номер страницы (с 0). По умолчанию `0`.
			- **Для задач с крайним сроком:**
				- `get_dated_tasks` (boolean, **обязательно**): Установить в `true`.
				- `month` (integer, **обязательно**): Месяц (1-12).
				- `year` (integer, **обязательно**): Год.
			- **Дополнительный фильтр (для любого списка):**
				- `client_id` (string, необязательно): ID клиента для фильтрации списка задач.

Внутренняя работа:
    -> Логирование начала запроса и авторизация:
        -> Проверка JWT токена, извлечение user_id.
        -> Проверка членства в фирме и минимальной роли EDITOR для UPSERT/DELETE.
    -> Парсинг запроса:
        -> Извлечение firm_id, action (GET, UPSERT, DELETE), task_id, payload и других параметров.
    -> Подключение к YDB базам фирм и задач.
    -> Маршрутизация по action:
        -> GET:
            -> Если task_id: Получение одной задачи по ID, включая подзадачи и напоминания.
            -> Если month/year: Получение срочных задач за период, с фильтром по client_id если указан.
            -> Иначе: Пагинированное получение бессрочных задач, с фильтром по client_id.
        -> UPSERT:
            -> Проверка прав на изменение (создатель или исполнитель).
            -> Создание новой задачи если нет task_id, иначе обновление.
            -> Обработка подзадач: Обновление subtasks_json в главной, main_task_id в подзадачах.
            -> Обработка периодических: Обновление periodic_children_json в родителе, periodic_parent_id в детях.
            -> Синхронизация напоминаний: Сравнение существующих и желаемых, создание/удаление событий в scheduler_manager.
            -> Транзакционная запись в YDB.
        -> DELETE:
            -> Проверка прав на удаление.
            -> Удаление напоминаний и событий планировщика.
            -> Если подзадача: Удаление из subtasks_json родителя, установка main_task_id в null.
            -> Если периодический экземпляр: Удаление из periodic_children_json родителя.
            -> Если периодический родитель: Рекурсивное удаление всех детей.
            -> Удаление задачи из YDB.
    -> Обработка ошибок и возврат ответа.

На выходе:
    -> Успешные ответы:
        -> GET: 200 OK с JSON данными (одна задача, пагинированный список бессрочных с next_page_token, или список срочных).
        -> UPSERT: 201 Created для создания (с task_id), 200 OK для обновления (с сообщением).
        -> DELETE: 200 OK с сообщением об удалении.
    -> Ошибки:
        -> 400 Bad Request: Неверные параметры, парсинг ошибок.
        -> 401 Unauthorized: Недействительный токен.
        -> 403 Forbidden: Недостаточно прав, не член фирмы.
        -> 404 Not Found: Задача/фирма не найдена.
        -> 500 Internal Server Error: Внутренние ошибки, проблемы с DB или вызовами функций.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `reminders_processor.py`, `invoke_utils.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`
	- `FUNCTION_ID_SCHEDULER_MANAGER`
	- `FUNCTION_ID_ENDPOINTS_MANAGER`
	- `FUNCTION_ID_EDIT_TASK` ([[✳️📝 edit-task - CloudFunction функция]])