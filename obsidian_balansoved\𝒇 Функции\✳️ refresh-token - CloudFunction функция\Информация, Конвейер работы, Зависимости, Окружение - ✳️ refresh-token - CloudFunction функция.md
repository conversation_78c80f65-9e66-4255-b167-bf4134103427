
Идентификатор - d4elom5l31834a68a8r7
Описание - Принудительно обновить JWT токен по реквизитам доступа.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `email`: Email пользователя.
	-> `password`: Пароль пользователя.
Внутренняя работа:
    -> **Использует утилиту `utils.request_parser` для безопасного извлечения и парсинга тела запроса.**
	-> Находит в YDB активного пользователя по `email`.
	-> Сверяет хеш пароля.
	-> Если все верно, принудительно генерирует **новый** JWT токен.
	-> **Проверяет и обновляет `user_id` в `firms-database`** для обеспечения консистентности данных. Для исправления некорректного `user_id` используется безопасный механизм "создать копию -> удалить старую запись" в рамках одной транзакции, так как первичный ключ нельзя напрямую обновить.
	-> Обновляет `last_login_at` и **перезаписывает** `jwt_token` в базе данных.
На выходе:
	-> `200 OK`: {"token": "<jwt_token>"}
    -> `400 Bad Request`: В случае проблем с телом запроса.
	-> `401 Unauthorized`: {"message": "Invalid credentials."}

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
    - `YDB_ENDPOINT` - Эндпоинт для `jwt-database`
    - `YDB_DATABASE` - Путь к `jwt-database`
    - `YDB_ENDPOINT_FIRMS` - Эндпоинт для `firms-database` (опционально, по умолчанию = `YDB_ENDPOINT`)
    - `YDB_DATABASE_FIRMS` - Путь к `firms-database` (опционально, по умолчанию = `YDB_DATABASE/firms`)
    - `SA_KEY_FILE` - Путь к файлу ключа сервисного аккаунта
    - `JWT_SECRET` - Секретный ключ для JWT