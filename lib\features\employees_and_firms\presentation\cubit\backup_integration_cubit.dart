import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/backup_integration_entity.dart';
import '../../domain/usecases/get_backup_status_usecase.dart';
import '../../domain/usecases/set_backup_enabled_usecase.dart';
import '../../domain/usecases/save_yadisk_token_usecase.dart';

part 'backup_integration_state.dart';

class BackupIntegrationCubit extends Cubit<BackupIntegrationState> {
  final GetBackupStatusUseCase _getBackupStatus;
  final SetBackupEnabledUseCase _setBackupEnabled;
  final SaveYadiskTokenUseCase _saveTokenUseCase;

  BackupIntegrationCubit({
    required GetBackupStatusUseCase getBackupStatusUseCase,
    required SetBackupEnabledUseCase setBackupEnabledUseCase,
    required SaveYadiskTokenUseCase saveYadiskTokenUseCase,
  }) : _getBackupStatus = getBackupStatusUseCase,
       _setBackupEnabled = setBackupEnabledUseCase,
       _saveTokenUseCase = saveYadiskTokenUseCase,
       super(const BackupIntegrationInitial());

  Future<void> loadStatus(String firmId) async {
    emit(const BackupIntegrationLoading());
    final result = await _getBackupStatus(firmId);
    result.fold(
      (failure) => emit(BackupIntegrationError(message: failure.message)),
      (entity) => emit(BackupIntegrationLoaded(entity: entity)),
    );
  }

  Future<void> toggleBackup(String firmId, bool enable) async {
    emit(const BackupIntegrationLoading());
    final result = await _setBackupEnabled(firmId: firmId, enabled: enable);
    result.fold(
      (failure) => emit(BackupIntegrationError(message: failure.message)),
      (entity) => emit(BackupIntegrationLoaded(entity: entity)),
    );
  }

  Future<void> saveToken(String firmId, String token) async {
    emit(const BackupIntegrationLoading());
    final result = await _saveTokenUseCase(firmId: firmId, token: token);
    result.fold(
      (failure) => emit(BackupIntegrationError(message: failure.message)),
      (entity) => emit(BackupIntegrationLoaded(entity: entity)),
    );
  }

  Future<void> enableBackupAndSetToken(String firmId, String token) async {
    emit(const BackupIntegrationLoading());
    // Временно используем существующие use-cases, чтобы не менять сигнатуры
    // Сначала сохраняем токен
    final tokenResult = await _saveTokenUseCase(firmId: firmId, token: token);
    await tokenResult.fold(
      (failure) async {
        emit(BackupIntegrationError(message: failure.message));
      },
      (entity) async {
        // Если токен сохранился, включаем бэкап
        final enableResult = await _setBackupEnabled(
          firmId: firmId,
          enabled: true,
        );
        enableResult.fold(
          (failure) => emit(BackupIntegrationError(message: failure.message)),
          (finalEntity) => emit(BackupIntegrationLoaded(entity: finalEntity)),
        );
      },
    );
  }
}
