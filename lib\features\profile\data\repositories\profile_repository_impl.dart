import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_local_data_source.dart';
import 'package:balansoved_enterprise/features/profile/data/data_source/user_data_remote_data_source.dart';
import 'package:balansoved_enterprise/features/profile/domain/entities/profile_entity.dart';
import 'package:balansoved_enterprise/features/profile/domain/repositories/profile_repository.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

class ProfileRepositoryImpl implements IProfileRepository {
  final IUserDataRemoteDataSource remote;
  final IAuthLocalDataSource localAuth;

  ProfileRepositoryImpl({required this.remote, required this.localAuth});

  @override
  Future<Either<Failure, ProfileEntity>> getProfile() async {
    try {
      NetworkLogger.printInfo(
        'PROFILE REPO: Получаем JWT из локального хранилища',
      );
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      final entity = await remote.fetchProfile(token);
      NetworkLogger.printSuccess('PROFILE REPO: Данные получены (сырые):');
      NetworkLogger.printJson('PROFILE REPO: Entity data:', entity.toString());
      return Right(entity);
    } catch (e) {
      NetworkLogger.printError('PROFILE REPO: Error getting user profile:', e);
      return Left(NetworkFailure(message: 'Ошибка при получении профиля: $e'));
    }
  }
}
