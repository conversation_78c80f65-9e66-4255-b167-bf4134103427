
```python
# get_endpoint.py

import json
import logging
import ydb

def handle_get_endpoints(pool, user_id):
    logging.debug("[GET_ENDPOINT_LIST] for user_id=%s", user_id)
    
    def get_list(session):
        query = session.prepare("""
            DECLARE $uid AS Utf8;
            SELECT 
                platform,
                push_token,
                endpoint_arn,
                is_enabled,
                device_info_json,
                created_at,
                updated_at
            FROM UserEndpoints VIEW user_id_index WHERE user_id = $uid;
        """)
        res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id}, commit_tx=True)
        
        data = []
        for row in res[0].rows:
            item = {c.name: row[c.name] for c in res[0].columns}
            if 'device_info_json' in item and item['device_info_json']:
                item['device_info_json'] = json.loads(item['device_info_json'])
            data.append(item)
        return data

    result_data = pool.retry_operation_sync(get_list)
    return {"statusCode": 200, "body": json.dumps({"data": result_data}, default=str)}
```