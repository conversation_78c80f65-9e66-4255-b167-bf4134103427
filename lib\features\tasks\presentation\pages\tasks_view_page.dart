import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/router.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_detail_card.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';

@RoutePage()
class TasksViewPage extends StatefulWidget {
  final String taskId;

  const TasksViewPage({super.key, @PathParam('taskId') required this.taskId});

  @override
  State<TasksViewPage> createState() => _TasksViewPageState();
}

class _TasksViewPageState extends State<TasksViewPage> {
  TaskEntity? _task;
  final bool _isLoading = true;
  final bool _hasTriedToLoad = false;



  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.router.navigate(TasksRoute()),
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              Text(
                'Просмотр задачи',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: BlocBuilder<TasksCubit, TasksState>(
              builder: (context, state) {
                if (state is TaskLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is TaskLoaded) {
                  return _buildBody(state.task);
                } else if (state is TasksLoaded) {
                  try {
                    final task = state.tasks.firstWhere((task) => task.id == widget.taskId);
                    return _buildBody(task);
                  } catch (e) {
                    return _buildBody(null);
                  }
                } else if (state is TaskError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Задача не найдена',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(state.message),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed:
                              () => context.router.navigate(TasksRoute()),
                          child: const Text('Вернуться к списку задач'),
                        ),
                      ],
                    ),
                  );
                } else {
                  return _buildBody(null);
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(TaskEntity? task) {
    if (task == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'Задача не найдена',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text(
              'Возможно, задача была удалена или у вас нет доступа к ней.',
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.router.navigate(TasksRoute()),
              child: const Text('Вернуться к списку задач'),
            ),
          ],
        ),
      );
    }

    return ListView(
      children: [
        TaskDetailCard(
          task: task,
          onClose: () => context.router.navigate(TasksRoute()),
          onEdit: () {
            context.router.navigate(TasksEditRoute(taskId: task.id));
          },
        ),
      ],
    );
  }
}
