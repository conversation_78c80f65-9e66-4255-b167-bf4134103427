import 'package:equatable/equatable.dart';
import 'storage_info_entity.dart';
import 'subscription_info_entity.dart';

/// Entity для данных о тарифах и хранилище фирмы
class TariffsAndStorageEntity extends Equatable {
  /// ID фирмы
  final String firmId;

  /// Информация о подписке
  final SubscriptionInfoEntity subscriptionInfo;

  /// Информация о хранилище
  final StorageInfoEntity storageInfo;

  /// Конфиденциальные данные (например, API ключи)
  final Map<String, dynamic> confidentialData;

  /// Дата создания записи
  final DateTime createdAt;

  /// Дата последнего обновления
  final DateTime updatedAt;

  const TariffsAndStorageEntity({
    required this.firmId,
    required this.subscriptionInfo,
    required this.storageInfo,
    required this.confidentialData,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Проверка, хватит ли места для загрузки файла
  bool hasSpaceForFile(int fileSize) {
    return storageInfo.hasSpaceForFile(fileSize, subscriptionInfo.quotaBytes);
  }

  /// Прогресс заполнения хранилища (0.0 - 1.0)
  double get usageProgress {
    return storageInfo.getUsageProgress(subscriptionInfo.quotaBytes);
  }

  /// Форматированный текст использования хранилища
  String get formattedUsage {
    return storageInfo.getFormattedUsage(subscriptionInfo.quotaBytes);
  }

  @override
  List<Object?> get props => [
    firmId,
    subscriptionInfo,
    storageInfo,
    confidentialData,
    createdAt,
    updatedAt,
  ];
}
