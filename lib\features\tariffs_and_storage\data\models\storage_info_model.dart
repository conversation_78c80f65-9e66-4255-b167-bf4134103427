import '../../domain/entities/storage_info_entity.dart';

/// Модель для информации о хранилище (data layer)
class StorageInfoModel extends StorageInfoEntity {
  const StorageInfoModel({
    required super.usedBytes,
    required super.lastRecalculatedAt,
  });

  /// Создать модель из JSON
  factory StorageInfoModel.fromJson(Map<String, dynamic> json) {
    DateTime parseDate(dynamic value) {
      try {
        if (value == null) return DateTime.now();
        if (value is int) {
          return DateTime.fromMicrosecondsSinceEpoch(
            value,
            isUtc: true,
          ).toLocal();
        }
        if (value is String && value.isNotEmpty) {
          return DateTime.parse(value).toLocal();
        }
      } catch (_) {}
      return DateTime.now();
    }

    return StorageInfoModel(
      usedBytes: json['used_bytes'] ?? 0,
      lastRecalculatedAt: parseDate(json['last_recalculated_at']),
    );
  }

  /// Преобразовать в JSON
  Map<String, dynamic> toJson() {
    return {
      'used_bytes': usedBytes,
      'last_recalculated_at': lastRecalculatedAt.toIso8601String(),
    };
  }

  /// Преобразовать в Entity
  StorageInfoEntity toEntity() {
    return StorageInfoEntity(
      usedBytes: usedBytes,
      lastRecalculatedAt: lastRecalculatedAt,
    );
  }

  /// Создать модель из Entity
  factory StorageInfoModel.fromEntity(StorageInfoEntity entity) {
    return StorageInfoModel(
      usedBytes: entity.usedBytes,
      lastRecalculatedAt: entity.lastRecalculatedAt,
    );
  }
}
