import 'package:equatable/equatable.dart';
import '../../domain/entities/tariffs_and_storage_entity.dart';
import '../../domain/entities/file_upload_entity.dart';

/// Состояния для управления тарифами и хранилищем
abstract class TariffsAndStorageState extends Equatable {
  const TariffsAndStorageState();

  @override
  List<Object?> get props => [];
}

/// Начальное состояние
class TariffsAndStorageInitial extends TariffsAndStorageState {}

/// Состояние загрузки
class TariffsAndStorageLoading extends TariffsAndStorageState {}

/// Состояние успешного получения данных
class TariffsAndStorageLoaded extends TariffsAndStorageState {
  final TariffsAndStorageEntity data;

  const TariffsAndStorageLoaded({required this.data});

  @override
  List<Object?> get props => [data];
}

/// Состояние ошибки
class TariffsAndStorageError extends TariffsAndStorageState {
  final String message;

  const TariffsAndStorageError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Состояние загрузки файла
class FileUploadInProgress extends TariffsAndStorageState {
  final String fileKey;
  final String fileName;
  final double progress;
  final FileUploadStatus status;
  final String? errorMessage;

  const FileUploadInProgress({
    required this.fileKey,
    required this.fileName,
    required this.progress,
    required this.status,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
    fileKey,
    fileName,
    progress,
    status,
    errorMessage,
  ];
}

/// Состояние успешной загрузки файла
class FileUploadCompleted extends TariffsAndStorageState {
  final String fileKey;
  final String fileName;
  final TariffsAndStorageEntity updatedData;

  const FileUploadCompleted({
    required this.fileKey,
    required this.fileName,
    required this.updatedData,
  });

  @override
  List<Object?> get props => [fileKey, fileName, updatedData];
}

/// Состояние ошибки загрузки файла
class FileUploadFailed extends TariffsAndStorageState {
  final String message;
  final String? fileName;

  const FileUploadFailed({required this.message, this.fileName});

  @override
  List<Object?> get props => [message, fileName];
}

/// Состояние получения ссылки для скачивания
class FileDownloadUrlReady extends TariffsAndStorageState {
  final String downloadUrl;
  final String fileKey;

  const FileDownloadUrlReady({
    required this.downloadUrl,
    required this.fileKey,
  });

  @override
  List<Object?> get props => [downloadUrl, fileKey];
}

/// Состояние успешного удаления файла
class FileDeleted extends TariffsAndStorageState {
  final String fileKey;
  final TariffsAndStorageEntity updatedData;

  const FileDeleted({required this.fileKey, required this.updatedData});

  @override
  List<Object?> get props => [fileKey, updatedData];
}

/// Состояние проверки доступности места
class SpaceCheckCompleted extends TariffsAndStorageState {
  final bool spaceAvailable;
  final int requestedFileSize;

  const SpaceCheckCompleted({
    required this.spaceAvailable,
    required this.requestedFileSize,
  });

  @override
  List<Object?> get props => [spaceAvailable, requestedFileSize];
}
