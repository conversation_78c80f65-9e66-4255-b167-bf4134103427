import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'image_preview_widget.dart';

/// Inline виджет для отображения изображений в тексте с возможностью клика для полного просмотра
class InlineImageWidget extends StatefulWidget {
  final String fileKey;
  final String fileName;
  final int originalWidth;
  final int originalHeight;

  const InlineImageWidget({
    super.key,
    required this.fileKey,
    required this.fileName,
    required this.originalWidth,
    required this.originalHeight,
  });

  @override
  State<InlineImageWidget> createState() => _InlineImageWidgetState();
}

class _InlineImageWidgetState extends State<InlineImageWidget> {
  bool _isLoading = true;
  String? _errorMessage;
  String? _imageUrl;

  StreamSubscription? _storageSub;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void dispose() {
    _storageSub?.cancel();
    super.dispose();
  }

  Future<void> _loadImage() async {
    try {
      final downloadUrl = await _getDownloadUrl();
      if (downloadUrl == null) {
        if (!mounted) return;
        setState(() {
          _errorMessage = 'Не удалось получить ссылку';
          _isLoading = false;
        });
        return;
      }
      if (!mounted) return;
      setState(() {
        _imageUrl = downloadUrl;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _errorMessage = 'Ошибка загрузки: $e';
        _isLoading = false;
      });
    }
  }

  Future<String?> _getDownloadUrl() async {
    final storageCubit = context.read<TariffsAndStorageCubit>();
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) return null;

    final completer = Completer<String?>();
    _storageSub = storageCubit.stream.listen((state) {
      if (state is FileDownloadUrlReady && state.fileKey == widget.fileKey) {
        if (!completer.isCompleted) completer.complete(state.downloadUrl);
        _storageSub?.cancel();
      } else if (state is TariffsAndStorageError) {
        if (!completer.isCompleted) completer.complete(null);
        _storageSub?.cancel();
      }
    });

    storageCubit.getDownloadUrl(
      firmId: firmState.selectedFirm!.id,
      fileKey: widget.fileKey,
    );

    Future.delayed(const Duration(seconds: 10), () {
      if (!completer.isCompleted) {
        completer.complete(null);
        _storageSub?.cancel();
      }
    });

    return completer.future;
  }

  void _showFullPreview() {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder:
          (context) => Dialog.fullscreen(
            child: Stack(
              children: [
                ImagePreviewWidget(
                  fileKey: widget.fileKey,
                  fileName: widget.fileName,
                ),
                Positioned(
                  top: 16,
                  right: 16,
                  child: IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.black54,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth =
            constraints.maxWidth == double.infinity
                ? MediaQuery.of(context).size.width
                : constraints.maxWidth;

        final aspectRatio =
            (widget.originalWidth == 0 || widget.originalHeight == 0)
                ? 1.5
                : widget.originalWidth / widget.originalHeight;

        final displayHeight = availableWidth / aspectRatio;

        // Placeholder while loading or error
        if (_isLoading) {
          return _buildPlaceholder(
            context,
            colorScheme,
            null,
            fixedHeight: displayHeight,
          );
        }

        if (_errorMessage != null || _imageUrl == null) {
          return _buildPlaceholder(
            context,
            colorScheme,
            _errorMessage ?? 'Изображение недоступно',
            fixedHeight: displayHeight,
          );
        }

        return GestureDetector(
          onTap: _showFullPreview,
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                _imageUrl!,
                fit: BoxFit.contain,
                width: double.infinity,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return _buildPlaceholder(
                    context,
                    colorScheme,
                    null,
                    fixedHeight: displayHeight,
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholder(
                    context,
                    colorScheme,
                    'Ошибка загрузки',
                    fixedHeight: displayHeight,
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlaceholder(
    BuildContext context,
    ColorScheme colorScheme,
    String? text, {
    double? fixedHeight,
  }) {
    // Если передан текст (ошибка), показываем сообщение в привычном оформлении.
    if (text != null) {
      return Container(
        height: fixedHeight ?? 100,
        width: double.infinity,
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: colorScheme.outline.withOpacity(0.3)),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(color: colorScheme.onSurfaceVariant),
          ),
        ),
      );
    }

    // Для состояния загрузки возвращаем пустой виджет без оформления
    return SizedBox(
      // height: fixedHeight ?? 100,
      height: 0,
      width: double.infinity,
    );
  }
}
