import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'custom_payments_editor.dart';

class PatentReductionSection extends StatelessWidget {
  final PatentReduction reduction;
  final bool isEditing;
  final Function(PatentReduction) onReductionChanged;

  const PatentReductionSection({
    super.key,
    required this.reduction,
    required this.isEditing,
    required this.onReductionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaymentsEditor(
      payments: reduction.customReductions ?? [],
      isEditing: isEditing,
      title: 'Уменьшение',
      onUpdate: (updatedList) {
        onReductionChanged(reduction.copyWith(customReductions: updatedList));
      },
    );
  }
}
