import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/client_entity.dart';
import '../../domain/entities/client_payment_entity.dart';
import '../cubit/client_payments_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';
import 'edit_card_components/form_widgets.dart';

class EditPaymentDialog extends StatefulWidget {
  final ClientEntity client;
  final int year;
  final int month;
  final ClientPaymentEntity? payment;
  final String firmId;

  const EditPaymentDialog({
    super.key,
    required this.client,
    required this.year,
    required this.month,
    required this.firmId,
    this.payment,
  });

  @override
  _EditPaymentDialogState createState() => _EditPaymentDialogState();
}

class _EditPaymentDialogState extends State<EditPaymentDialog> {
  late final TextEditingController _actualAmountController;
  late final TextEditingController _tariffAmountController;
  late final TextEditingController _paymentDateController;
  DateTime? _selectedPaymentDate;

  @override
  void initState() {
    super.initState();
    _actualAmountController = TextEditingController(
      text: widget.payment?.actualAmountPaid?.toStringAsFixed(2) ?? '',
    );
    _tariffAmountController = TextEditingController(
      text: widget.payment?.tariffAnnualAmount?.toStringAsFixed(2) ?? '',
    );
    _selectedPaymentDate = widget.payment?.paymentDate;
    _paymentDateController = TextEditingController(
      text: _selectedPaymentDate != null
          ? DateFormat('dd.MM.yyyy').format(_selectedPaymentDate!)
          : '',
    );
  }

  @override
  void dispose() {
    _actualAmountController.dispose();
    _tariffAmountController.dispose();
    _paymentDateController.dispose();
    super.dispose();
  }

  final _formKey = GlobalKey<FormState>();

  void _onSave() async {
    // Валидируем форму перед сохранением
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    final actualAmount = double.tryParse(_actualAmountController.text);
    final tariffAmount = double.tryParse(_tariffAmountController.text);

    // Сохраняем значения перед закрытием диалога
    final cubit = context.read<ClientPaymentsCubit>();
    
    // Запускаем операцию сохранения и ждем ее завершения
    await cubit.upsertPayment(
      firmId: widget.firmId,
      clientId: widget.client.id,
      period: DateTime(widget.year, widget.month),
      actualAmount: actualAmount,
      tariffAmount: tariffAmount,
      paymentDate: _selectedPaymentDate,
    );
    
    // Закрываем диалог только после завершения операции и если виджет еще смонтирован
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  void _onDelete() async {
    final cubit = context.read<ClientPaymentsCubit>();
    
    // Ждем завершения операции удаления
    await cubit.deletePayment(
      firmId: widget.firmId,
      clientId: widget.client.id,
      period: DateTime(widget.year, widget.month),
    );
    
    // Закрываем диалог только после завершения операции и если виджет еще смонтирован
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Изменить платеж - ${widget.client.name}'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: ListBody(
            children: <Widget>[
            Text(
              'Период: ${DateFormat.yMMMM('ru').format(DateTime(widget.year, widget.month))}',
            ),
            const SizedBox(height: 16),
            UniversalNumberField(
              controller: _actualAmountController,
              labelText: 'Фактическая оплата',
              fieldType: NumberFieldType.decimal,
              suffixText: '₽',
              isEditing: true,
            ),
            const SizedBox(height: 16),
            UniversalNumberField(
              controller: _tariffAmountController,
              labelText: 'Ежемесячная плата',
              fieldType: NumberFieldType.decimal,
              suffixText: '₽',
              helperText: 'Устанавливается для текущего и будущих месяцев',
              isEditing: true,
            ),
            const SizedBox(height: 16),
            DateInputFormField(
               controller: _paymentDateController,
               labelText: 'Дата оплаты',
               prefixIcon: Icons.calendar_today,
               isEditing: true,
               validator: (value) {
                 if (value == null || value.isEmpty) {
                   return null; // Поле необязательное
                 }
                 
                 try {
                   final inputDate = DateFormat('dd.MM.yyyy').parse(value);
                   final periodStart = DateTime(widget.year, widget.month, 1);
                   final periodEnd = DateTime(widget.year, widget.month + 1, 0);
                   
                   if (inputDate.isBefore(periodStart) || inputDate.isAfter(periodEnd)) {
                     return 'Дата должна быть в пределах ${DateFormat('MMMM yyyy', 'ru').format(periodStart)}';
                   }
                 } catch (e) {
                   return 'Некорректный формат даты';
                 }
                 
                 return null;
               },
              onIconTap: () async {
                 // Ограничиваем выбор даты рамками месяца платежа
                 final periodStart = DateTime(widget.year, widget.month, 1);
                 final periodEnd = DateTime(widget.year, widget.month + 1, 0); // последний день месяца
                 
                 final selectedDate = await showDatePicker(
                   context: context,
                   initialDate: _selectedPaymentDate ?? periodStart,
                   firstDate: periodStart,
                   lastDate: periodEnd,
                 );
                if (selectedDate != null) {
                  setState(() {
                    _selectedPaymentDate = selectedDate;
                    _paymentDateController.text = DateFormat('dd.MM.yyyy').format(selectedDate);
                  });
                }
              },
              onDateChanged: (date) {
                 // Проверяем, что дата находится в рамках месяца платежа
                 if (date != null) {
                   final periodStart = DateTime(widget.year, widget.month, 1);
                   final periodEnd = DateTime(widget.year, widget.month + 1, 0);
                   
                   if (date.isAfter(periodEnd) || date.isBefore(periodStart)) {
                     // Если дата вне диапазона, сбрасываем её
                     return;
                   }
                 }
                 setState(() {
                   _selectedPaymentDate = date;
                 });
               },
            ),
           ],
         ),
       ),
     ),
      actions: <Widget>[
        if (widget.payment != null)
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            onPressed: _onDelete,
            child: const Text('Удалить'),
          ),
        const SizedBox(width: 8),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        FilledButton(onPressed: _onSave, child: const Text('Сохранить')),
      ],
    );
  }
}
