import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/create_firm_usecase.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/injection_container.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';

part 'create_firm_state.dart';

class CreateFirmCubit extends Cubit<CreateFirmState> {
  final CreateFirmUseCase _useCase;
  CreateFirmCubit({required CreateFirmUseCase useCase})
    : _useCase = useCase,
      super(const CreateFirmState.initial());

  Future<void> createFirm(String name) async {
    if (state.isLoading) return;
    emit(state.copyWith(isLoading: true, errorMessage: null));

    final result = await _useCase(name);
    await result.fold(
      (failure) async {
        emit(
          state.copyWith(isLoading: false, errorMessage: _mapFailure(failure)),
        );
      },
      (_) async {
        // Перезагружаем профиль для подтягивания новой фирмы
        await sl<ProfileCubit>().reloadProfile();
        emit(state.copyWith(isLoading: false, success: true));
      },
    );
  }

  String _mapFailure(Failure f) => f.details ?? f.message;
}
