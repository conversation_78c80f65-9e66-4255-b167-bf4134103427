import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/tariffs_and_storage_entity.dart';
import '../entities/file_upload_entity.dart';

/// Интерфейс репозитория для работы с тарифами и хранилищем
abstract class ITariffsAndStorageRepository {
  /// Получить данные о тарифах и хранилище фирмы
  Future<Either<Failure, TariffsAndStorageEntity>> getTariffsAndStorage(
    String firmId,
  );

  /// Обновить JSON поля
  Future<Either<Failure, void>> updateJsonFields({
    required String firmId,
    required String targetJsonField,
    required Map<String, dynamic> updates,
  });

  /// Очистить JSON поля
  Future<Either<Failure, void>> clearJsonFields({
    required String firmId,
    required List<String> fieldsToClear,
  });

  /// Получить URL для загрузки файла
  Future<Either<Failure, FileUploadEntity>> getUploadUrl({
    required String firmId,
    required String fileName,
    required int fileSize,
  });

  /// Получить URL для скачивания файла
  Future<Either<Failure, FileDownloadEntity>> getDownloadUrl({
    required String firmId,
    required String fileKey,
  });

  /// Загрузить файл
  Stream<FileUploadProgressEntity> uploadFile({
    required String uploadUrl,
    required String fileKey,
    required String firmId,
    required List<int> fileBytes,
    required String fileName,
  });

  /// Подтвердить загрузку
  Future<Either<Failure, void>> confirmUpload({
    required String firmId,
    required String fileKey,
  });

  /// Удалить файл
  Future<Either<Failure, void>> deleteFile({
    required String firmId,
    required String fileKey,
  });

  /// Проверить, хватит ли места для загрузки файла
  Future<Either<Failure, bool>> checkSpaceAvailable({
    required String firmId,
    required int fileSize,
  });
}
