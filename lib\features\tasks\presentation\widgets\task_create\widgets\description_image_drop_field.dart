import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_dropzone/flutter_dropzone.dart';
import 'dart:typed_data';
import 'dart:html' as html;

/// Виджет текстового поля описания задачи с поддержкой перетаскивания изображений (Web).
/// При успешном добавлении изображения вставляет токен `[[fileName.ext]]` в текст и
/// вызывает [onImageDropped] с байтами файла для дальнейшей загрузки.
class DescriptionImageDropField extends StatefulWidget {
  final TextEditingController controller;
  final void Function(String fileName, Uint8List bytes) onImageDropped;

  const DescriptionImageDropField({
    super.key,
    required this.controller,
    required this.onImageDropped,
  });

  @override
  State<DescriptionImageDropField> createState() =>
      _DescriptionImageDropFieldState();
}

class _DescriptionImageDropFieldState extends State<DescriptionImageDropField> {
  DropzoneViewController? _dropzoneController;
  bool _isHovering = false;

  // Поддерживаемые расширения изображений
  static const _allowedExt = ['jpg', 'jpeg', 'png', 'webp', 'jpe'];

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        TextFormField(
          controller: widget.controller,
          maxLines: 6,
          decoration: const InputDecoration(
            labelText: 'Описание',
            border: OutlineInputBorder(),
          ),
        ),
        if (kIsWeb)
          Positioned.fill(
            child: DropzoneView(
              onCreated: (ctrl) => _dropzoneController = ctrl,
              operation: DragOperation.copy,
              cursor: CursorType.grab,
              onHover: () => setState(() => _isHovering = true),
              onLeave: () => setState(() => _isHovering = false),
              onDrop: _onFileDropped,
              onDropMultiple: _onFilesDropped,
            ),
          ),
        if (kIsWeb && _isHovering)
          Positioned.fill(
            child: Container(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.05),
            ),
          ),
      ],
    );
  }

  Future<void> _onFileDropped(dynamic event) async {
    if (!kIsWeb) return;
    if (event is html.File) {
      await _processFile(event);
    }
    setState(() => _isHovering = false);
  }

  Future<void> _onFilesDropped(List<dynamic>? events) async {
    if (!kIsWeb || events == null) return;
    for (final ev in events) {
      if (ev is html.File) {
        await _processFile(ev);
      }
    }
    setState(() => _isHovering = false);
  }

  Future<void> _processFile(html.File htmlFile) async {
    final ext = htmlFile.name.split('.').last.toLowerCase();
    if (!_allowedExt.contains(ext)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Формат .$ext не поддерживается для вставки'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      return;
    }

    final reader = html.FileReader();
    reader.readAsArrayBuffer(htmlFile);
    await reader.onLoadEnd.first;
    final bytes = reader.result as Uint8List;

    // Вставляем токен
    final token = "[[${htmlFile.name}]]";
    final selection = widget.controller.selection;
    final start =
        selection.start >= 0 ? selection.start : widget.controller.text.length;
    final end =
        selection.end >= 0 ? selection.end : widget.controller.text.length;
    final newText = widget.controller.text.replaceRange(start, end, token);
    widget.controller.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: start + token.length),
    );

    // Передаём данные назад для загрузки
    widget.onImageDropped(htmlFile.name, bytes);
  }
}
