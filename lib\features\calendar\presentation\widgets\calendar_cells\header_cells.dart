import 'package:flutter/material.dart';

class TableHeaderCell extends StatelessWidget {
  final String text;
  final ThemeData theme;

  const TableHeaderCell({super.key, required this.text, required this.theme});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurfaceVariant,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class RotatedHeaderCell extends StatelessWidget {
  final String fullName;
  final String? shortName;
  final ThemeData theme;

  const RotatedHeaderCell({
    super.key,
    required this.fullName,
    this.shortName,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    final displayText = shortName?.isNotEmpty == true ? shortName! : fullName;

    return Tooltip(
      message: fullName,
      textStyle: TextStyle(
        fontSize: 16,
        color: Theme.of(context).colorScheme.onInverseSurface,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.inverseSurface,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Container(
        height: 80,
        width: 120,
        padding: const EdgeInsets.all(8),
        alignment: Alignment.center,
        child: Text(
          displayText,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurfaceVariant,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
          maxLines: 4,
          overflow: TextOverflow.ellipsis,
          softWrap: true,
        ),
      ),
    );
  }
}
