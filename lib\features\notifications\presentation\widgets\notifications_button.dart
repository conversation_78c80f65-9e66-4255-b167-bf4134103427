import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/notifications_cubit.dart';
import 'notifications_popup.dart';

class NotificationsButton extends StatefulWidget {
  const NotificationsButton({super.key});

  @override
  State<NotificationsButton> createState() => _NotificationsButtonState();
}

class _NotificationsButtonState extends State<NotificationsButton> {
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _toggleOverlay() {
    if (_overlayEntry == null) {
      _showOverlay();
    } else {
      _removeOverlay();
    }
  }

  void _showOverlay() {
    final notificationsCubit = context.read<NotificationsCubit>();
    
    // Помечаем все неотправленные уведомления как доставленные при открытии окна
    notificationsCubit.markAllUndeliveredAsDelivered();
    
    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // GestureDetector to close the overlay when tapping outside
          Positioned.fill(
            child: GestureDetector(
              onTap: _removeOverlay,
              behavior: HitTestBehavior.opaque,
              child: Container(
                color: Colors.transparent, // No background dimming
              ),
            ),
          ),
          // The actual popup content
          CompositedTransformFollower(
            link: _layerLink,
            showWhenUnlinked: false,
            offset: const Offset(-350, 55), // Adjust to position correctly
            child: BlocProvider.value(
              value: notificationsCubit,
              child: NotificationsPopup(onClose: _removeOverlay),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return BlocBuilder<NotificationsCubit, NotificationsState>(
      builder: (context, state) {
        int undeliveredCount = 0;

        if (state is NotificationsLoaded) {
          undeliveredCount = state.undeliveredCount;
        } else if (state is NotificationsLoadingMore) {
          undeliveredCount = state.undeliveredCount;
        }

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: CompositedTransformTarget(
            link: _layerLink,
            child: Stack(
              children: [
                IconButton(
                  onPressed: _toggleOverlay,
                  icon: Icon(
                    Icons.notifications_outlined,
                    color: colorScheme.onSurface,
                    size: 24,
                  ),
                  tooltip: 'Уведомления',
                ),
                if (undeliveredCount > 0)
                  Positioned(
                    right: 6,
                    top: 6,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: colorScheme.error,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 18,
                        minHeight: 18,
                      ),
                      child: Text(
                        undeliveredCount >= 100 ? '99+' : undeliveredCount.toString(),
                        style: TextStyle(
                          color: colorScheme.onError,
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}