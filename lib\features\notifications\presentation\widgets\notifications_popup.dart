import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/notifications_cubit.dart';
import 'notification_item.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';

class NotificationsPopup extends StatelessWidget {
  final VoidCallback onClose;

  const NotificationsPopup({super.key, required this.onClose});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Material(
      elevation: 4,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        width: 400,
        height: 600,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Заголовок
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Уведомления',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: onClose,
                  icon: Icon(
                    Icons.close,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Список уведомлений
            Expanded(
              child: BlocBuilder<NotificationsCubit, NotificationsState>(
                builder: (context, state) {
                  if (state is NotificationsLoading) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          LoadingTile(height: 60, maxWidth: 350),
                          SizedBox(height: 8),
                          LoadingTile(height: 60, maxWidth: 350),
                          SizedBox(height: 8),
                          LoadingTile(height: 60, maxWidth: 350),
                        ],
                      ),
                    );
                  }
                  
                  if (state is NotificationsError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: colorScheme.error,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Ошибка загрузки уведомлений',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: colorScheme.error,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              context.read<NotificationsCubit>().loadNotifications(refresh: true);
                            },
                            child: const Text('Повторить'),
                          ),
                        ],
                      ),
                    );
                  }
                  
                  if (state is NotificationsLoaded || state is NotificationsLoadingMore) {
                    final notifications = state is NotificationsLoaded 
                        ? state.notifications 
                        : (state as NotificationsLoadingMore).notifications;
                    
                    if (notifications.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.notifications_none,
                              size: 48,
                              color: colorScheme.onSurface.withValues(alpha: 0.5),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Нет уведомлений',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    
                    return Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            itemCount: notifications.length,
                            itemBuilder: (context, index) {
                              return NotificationItem(
                                notification: notifications[index],
                              );
                            },
                          ),
                        ),
                        
                        // Кнопка "Получить ещё"
                        if (context.read<NotificationsCubit>().hasMoreNotifications)
                          Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child: SizedBox(
                              width: double.infinity,
                              child: state is NotificationsLoadingMore
                                  ? const LoadingTile(height: 40, maxWidth: double.infinity)
                                  : ElevatedButton(
                                      onPressed: () {
                                        context.read<NotificationsCubit>().loadMoreNotifications();
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: colorScheme.primaryContainer,
                                        foregroundColor: colorScheme.onPrimaryContainer,
                                      ),
                                      child: const Text('Получить ещё'),
                                    ),
                            ),
                          ),
                      ],
                    );
                  }
                  
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}