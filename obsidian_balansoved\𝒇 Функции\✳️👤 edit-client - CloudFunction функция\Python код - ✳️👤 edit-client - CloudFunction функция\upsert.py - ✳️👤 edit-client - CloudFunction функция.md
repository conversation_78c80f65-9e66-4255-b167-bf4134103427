
```python
import json, uuid, datetime, pytz
import ydb
from custom_errors import AuthError, LogicError

def update_is_actual_flags(session, tx, table_name, client_id, log_accumulator):
    """
    (ИСПРАВЛЕНО) Автоматически устанавливает is_actual=true для самой последней версии
    и is_actual=false для всех остальных. Работает внутри существующей транзакции.
    """
    log_accumulator.append(f"[update_is_actual_flags] Updating is_actual flags for client_id={client_id}")
    
    reset_query = session.prepare(f"""
        DECLARE $client_id AS Utf8;
        UPDATE `{table_name}` SET is_actual = false WHERE client_id = $client_id;
    """)
    log_accumulator.append(f"[update_is_actual_flags] Resetting all is_actual flags to false")
    tx.execute(reset_query, {'$client_id': client_id})
    
    find_latest_query = session.prepare(f"""
        DECLARE $client_id AS Utf8;
        SELECT manual_creation_date FROM `{table_name}` 
        WHERE client_id = $client_id 
        ORDER BY manual_creation_date DESC LIMIT 1;
    """)
    log_accumulator.append(f"[update_is_actual_flags] Finding latest manual_creation_date")
    latest_result = tx.execute(find_latest_query, {'$client_id': client_id})
    
    if latest_result and latest_result[0].rows:
        latest_date = latest_result[0].rows[0].manual_creation_date
        log_accumulator.append(f"[update_is_actual_flags] Found latest date: {latest_date}")
        
        update_latest_query = session.prepare(f"""
            DECLARE $client_id AS Utf8; DECLARE $latest_date AS Date;
            UPDATE `{table_name}` SET is_actual = true 
            WHERE client_id = $client_id AND manual_creation_date = $latest_date;
        """)
        log_accumulator.append(f"[update_is_actual_flags] Setting is_actual=true for latest version")
        tx.execute(update_latest_query, {'$client_id': client_id, '$latest_date': latest_date})
        log_accumulator.append(f"[update_is_actual_flags] Successfully updated is_actual flags")
    else:
        log_accumulator.append(f"[update_is_actual_flags] No records found for client_id={client_id}")

def get_declare_clauses_and_params(payload, log_accumulator):
    log_accumulator.append(f"[get_declare_clauses_and_params] Building query parameters for payload with {len(payload)} fields")
    log_accumulator.append(f"[get_declare_clauses_and_params] Payload fields: {list(payload.keys())}")
    
    declare_clauses = ""
    params = {}
    type_map = {
        'client_id': ydb.PrimitiveType.Utf8,
        'client_name': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'short_name': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'contacts_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'tax_and_legal_info_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'payment_schedule_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'patents_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'tags_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'comment': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'manual_creation_date': ydb.PrimitiveType.Date,
        'is_actual': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'is_active': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'created_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
        'updated_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
    }
    
    processed_fields = []
    for key, value in payload.items():
        if key in type_map:
            is_date_type = False
            if isinstance(type_map[key], ydb.OptionalType):
                is_date_type = type_map[key].item == ydb.PrimitiveType.Date
            else:
                is_date_type = type_map[key] == ydb.PrimitiveType.Date
            if is_date_type and isinstance(value, str):
                try:
                    params[f"${key}"] = datetime.datetime.strptime(value, '%Y-%m-%d').date()
                    log_accumulator.append(f"[get_declare_clauses_and_params] Parsed date field '{key}': {value} -> {params[f'${key}']}")
                except ValueError:
                    log_accumulator.append(f"[get_declare_clauses_and_params] Error parsing date field '{key}': {value}")
                    raise LogicError(f"Invalid date format for '{key}'. Use YYYY-MM-DD.")
            else:
                params[f"${key}"] = value
                log_accumulator.append(f"[get_declare_clauses_and_params] Added field '{key}': {type(value).__name__}")
            type_name_str = str(type_map[key])
            if "Optional" in type_name_str:
                type_name = type_name_str.replace("Optional[", "").replace("]", "")
            else:
                type_name = type_name_str
            declare_clauses += f"DECLARE ${key} AS {type_name}; "
            processed_fields.append(key)
    
    log_accumulator.append(f"[get_declare_clauses_and_params] Processed fields: {processed_fields}")
    log_accumulator.append(f"[get_declare_clauses_and_params] Total parameters: {len(params)}")
    return declare_clauses, params

def upsert_client(session, table_name, payload, client_id, creation_date, roles, log_accumulator):
    log_accumulator.append(f"[upsert_client] Starting UPSERT operation with client_id={client_id}, creation_date={creation_date}, table={table_name}")
    log_accumulator.append(f"[upsert_client] User roles: {roles}")
    log_accumulator.append(f"[upsert_client] Payload: {payload}")
    
    if 'OWNER' not in roles and 'ADMIN' not in roles:
        log_accumulator.append(f"[upsert_client] Error: Insufficient permissions. User roles: {roles}")
        raise AuthError("Forbidden: Insufficient permissions for UPSERT action.")
    
    if not payload:
        log_accumulator.append("[upsert_client] Error: Empty payload")
        raise LogicError("Payload is required for UPSERT action.")
    
    log_accumulator.append("[upsert_client] Permission check passed")
    
    now = datetime.datetime.now(pytz.utc)
    payload['updated_at'] = now
    log_accumulator.append(f"[upsert_client] Added updated_at timestamp: {now}")
    
    tx = session.transaction(ydb.SerializableReadWrite())
    log_accumulator.append("[upsert_client] Started database transaction")
    
    if client_id and creation_date:
        log_accumulator.append("[upsert_client] Mode: Update existing client version")
        payload.pop('client_id', None)
        payload.pop('manual_creation_date', None)
        payload.pop('is_actual', None)
        if not payload:
            log_accumulator.append("[upsert_client] Error: No fields provided for update after cleanup")
            raise LogicError("No fields provided for update.")
        
        log_accumulator.append(f"[upsert_client] Fields to update: {list(payload.keys())}")
        set_clauses = ", ".join([f"{key} = ${key}" for key in payload.keys()])
        declare_clauses, params = get_declare_clauses_and_params(payload, log_accumulator)
        params['$client_id'] = client_id
        params['$creation_date'] = creation_date
        query_text = f""" {declare_clauses}
                        DECLARE $client_id AS Utf8; DECLARE $creation_date AS Date;
                        UPDATE `{table_name}` SET {set_clauses} WHERE client_id = $client_id AND manual_creation_date = $creation_date;"""
        log_accumulator.append(f"[upsert_client] Update query: {query_text}")
        tx.execute(session.prepare(query_text), params)
        
        update_is_actual_flags(session, tx, table_name, client_id, log_accumulator)
        
        tx.commit()
        log_accumulator.append("[upsert_client] Successfully updated existing client version")
        result = {"message": "Client version updated", "client_id": client_id, "creation_date": creation_date.isoformat() if creation_date else None}
        status_code = 200
    elif client_id and 'manual_creation_date' in payload:
        log_accumulator.append("[upsert_client] Mode: Create new version of existing client")
        new_creation_date_str = payload['manual_creation_date']
        log_accumulator.append(f"[upsert_client] New creation date: {new_creation_date_str}")
        try:
            new_creation_date = datetime.datetime.strptime(str(new_creation_date_str), '%Y-%m-%d').date()
            log_accumulator.append(f"[upsert_client] Parsed new creation date: {new_creation_date}")
        except (ValueError, TypeError) as e:
            log_accumulator.append(f"[upsert_client] Error parsing new creation date: {str(e)}")
            raise LogicError("Invalid date format for 'manual_creation_date' in payload.")
        
        payload['client_id'] = client_id
        payload['created_at'] = now
        payload.pop('is_actual', None)
        log_accumulator.append("[upsert_client] Added created_at, is_actual will be set automatically")
        
        declare_clauses, params = get_declare_clauses_and_params(payload, log_accumulator)
        columns = ", ".join(payload.keys())
        placeholders = ", ".join([f"${key}" for key in payload.keys()])
        query_text = f"""{declare_clauses}
                        UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"""
        log_accumulator.append(f"[upsert_client] New version upsert query: {query_text}")
        tx.execute(session.prepare(query_text), params)
        
        update_is_actual_flags(session, tx, table_name, client_id, log_accumulator)
        
        tx.commit()
        log_accumulator.append("[upsert_client] Successfully created new client version")
        result = {"message": "New client version created", "client_id": client_id, "creation_date": new_creation_date_str}
        status_code = 200
    elif not client_id and 'manual_creation_date' in payload:
        log_accumulator.append("[upsert_client] Mode: Create new client")
        new_client_id = str(uuid.uuid4())
        log_accumulator.append(f"[upsert_client] Generated new client_id: {new_client_id}")
        payload['client_id'] = new_client_id
        payload['is_active'] = True
        payload['created_at'] = now
        payload.pop('is_actual', None)
        log_accumulator.append("[upsert_client] Set new client as active, is_actual will be set automatically")
        
        declare_clauses, params = get_declare_clauses_and_params(payload, log_accumulator)
        columns = ", ".join(payload.keys())
        placeholders = ", ".join([f"${key}" for key in payload.keys()])
        query_text = f"""{declare_clauses}
                        UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"""
        log_accumulator.append(f"[upsert_client] New client upsert query: {query_text}")
        tx.execute(session.prepare(query_text), params)
        
        update_is_actual_flags(session, tx, table_name, new_client_id, log_accumulator)
        
        tx.commit()
        log_accumulator.append(f"[upsert_client] Successfully created new client with id: {new_client_id}")
        result = {"message": "Client created", "client_id": new_client_id, "creation_date": payload['manual_creation_date']}
        status_code = 201
    else:
        log_accumulator.append("[upsert_client] Error: Invalid UPSERT combination")
        raise LogicError("Invalid UPSERT combination. Provide client_id and new manual_creation_date for a new version, or just manual_creation_date for a new client.")
    
    log_accumulator.append(f"[upsert_client] Operation completed with status: {status_code}")
    return {"statusCode": status_code, "body": json.dumps(result)}
```