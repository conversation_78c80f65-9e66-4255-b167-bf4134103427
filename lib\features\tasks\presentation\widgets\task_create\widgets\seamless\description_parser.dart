import 'package:collection/collection.dart';

import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';
import './description_models.dart';

List<Block> parseDescription(
  String? text,
  List<FileAttachmentItem> attachments,
) {
  final blocks = <Block>[];
  final initialText = text ?? '';

  if (initialText.isEmpty) {
    blocks.add(Block.text(''));
    return blocks;
  }

  // Make regex multiline and dotall to handle newlines
  final regex = RegExp(r'\[\[([^\]]+)\]\]', multiLine: true, dotAll: true);
  final matches = regex.allMatches(initialText).toList();

  if (matches.isEmpty) {
    // Try manual parsing as fallback
    final manualMatches = <ManualMatch>[];
    int searchStart = 0;
    while (true) {
      final startIndex = initialText.indexOf('[[', searchStart);
      if (startIndex == -1) break;
      final endIndex = initialText.indexOf(']]', startIndex);
      if (endIndex == -1) break;

      final content = initialText.substring(startIndex + 2, endIndex);
      manualMatches.add(ManualMatch(startIndex, endIndex + 2, content));
      searchStart = endIndex + 2;
    }

    if (manualMatches.isNotEmpty) {
      _parseWithMatches(
        initialText,
        manualMatches.cast<dynamic>(),
        blocks,
        attachments,
      );
      // Ensure we end with a text block
      if (blocks.isEmpty || blocks.last.type != BlockType.text) {
        blocks.add(Block.text(''));
      }
      return blocks;
    }

    blocks.add(Block.text(initialText));
    return blocks;
  }

  _parseWithMatches(initialText, matches.cast<dynamic>(), blocks, attachments);
  // Ensure we end with a text block
  if (blocks.isEmpty || blocks.last.type != BlockType.text) {
    blocks.add(Block.text(''));
  }
  return blocks;
}

void _parseWithMatches(
  String text,
  List<dynamic> matches,
  List<Block> blocks,
  List<FileAttachmentItem> attachments,
) {
  int lastIndex = 0;
  for (final match in matches) {
    final start = match.start;
    final end = match.end;

    // Add text before this match
    if (start > lastIndex) {
      final textPart = text.substring(lastIndex, start);
      blocks.add(Block.text(textPart));
    }

    // Parse image token
    final content =
        match.group(1) ??
        match.group(0)!.replaceAll('[[', '').replaceAll(']]', '');
    final parts = content.split('|');
    final fileKey = parts[0].trim();
    final dimensions = parts.length > 1 ? parts[1] : '300x200';
    final dimParts = dimensions.split('x');
    final w = int.tryParse(dimParts[0]) ?? 300;
    final h = dimParts.length > 1 ? int.tryParse(dimParts[1]) ?? 200 : 200;

    blocks.add(
      Block.image(
        fileKey: fileKey,
        fileName: _fileNameByKey(fileKey, attachments),
        width: w,
        height: h,
      ),
    );

    lastIndex = end;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    final remainingText = text.substring(lastIndex);
    blocks.add(Block.text(remainingText));
  }
}

String _fileNameByKey(String fileKey, List<FileAttachmentItem> attachments) {
  final attachment = attachments.firstWhereOrNull((a) => a.fileKey == fileKey);
  if (attachment != null) return attachment.name;
  if (fileKey.startsWith('local/')) return fileKey.substring(6);
  return fileKey;
}
