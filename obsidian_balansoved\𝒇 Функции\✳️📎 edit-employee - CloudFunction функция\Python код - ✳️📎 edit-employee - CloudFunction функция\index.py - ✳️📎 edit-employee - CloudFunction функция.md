
```python
import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

# Словарь для определения веса ролей
ROLE_HIERARCHY = {"OWNER": 3, "ADMIN": 2, "EMPLOYEE": 1}
EDITABLE_ROLES = {"ADMIN", "EMPLOYEE"}

def get_highest_role_score(roles_json_str: str) -> int:
    """Возвращает наивысший балл из списка ролей пользователя."""
    roles = json.loads(roles_json_str or '[]')
    if not roles: return 0
    return max(ROLE_HIERARCHY.get(role, 0) for role in roles)

def handler(event, context):
    try:
        # 1. Авторизация и базовая валидация
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized")

        token = auth_header.split(' ', 1)[1]
        admin_payload = auth_utils.verify_jwt(token)
        if not admin_payload or 'user_id' not in admin_payload: raise AuthError("Invalid token")
        
        admin_user_id = admin_payload['user_id']

        # Безопасное получение и парсинг тела запроса
        try:
            data = request_parser.parse_request_body(event)
        except ValueError as e:
            raise LogicError(str(e))

        firm_id = data.get('firm_id')
        user_id_to_edit = data.get('user_id_to_edit')
        action = data.get('action')

        # Проверяем только обязательные для всех полей
        if not all([firm_id, action]):
            raise LogicError("firm_id and action are required.")

        driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        pool = ydb.SessionPool(driver)

        if action == "GET_INFO":
            def get_info_transaction(session):
                tx = session.transaction(ydb.SerializableReadWrite())
                
                if user_id_to_edit:
                    query = session.prepare("""
                        DECLARE $firm_id AS Utf8; DECLARE $target_id AS Utf8;
                        SELECT * FROM Users WHERE firm_id = $firm_id AND user_id = $target_id;
                    """)
                    res = tx.execute(query, {'$firm_id': firm_id, '$target_id': user_id_to_edit})
                    if not res[0].rows: raise NotFoundError("Target user not found in this firm.")
                    target_data = res[0].rows[0]
                    tx.commit()
                    return {"data": {
                        "user_id": target_data.user_id, "firm_id": target_data.firm_id, "email": target_data.email,
                        "full_name": target_data.full_name, "roles": json.loads(target_data.roles or '[]'),
                        "is_active": target_data.is_active, "created_at": str(target_data.created_at)
                    }}
                else:
                    query = session.prepare("DECLARE $firm_id AS Utf8; SELECT * FROM Users WHERE firm_id = $firm_id;")
                    res = tx.execute(query, {'$firm_id': firm_id})
                    tx.commit()
                    users_list = []
                    for row in res[0].rows:
                        users_list.append({
                            "user_id": row.user_id, "email": row.email, "full_name": row.full_name,
                            "roles": json.loads(row.roles or '[]'), "is_active": row.is_active
                        })
                    return {"data": users_list}

            result_data = pool.retry_operation_sync(get_info_transaction)
            return {"statusCode": 200, "body": json.dumps(result_data, default=str)}

        elif action in ["ADD_ROLE", "REMOVE_ROLE"]:
            if not user_id_to_edit:
                raise LogicError("user_id_to_edit is required for ADD_ROLE/REMOVE_ROLE actions.")
            
            role_to_change = data.get('role')
            if not role_to_change: raise LogicError("Field 'role' is required for this action.")
            if role_to_change not in EDITABLE_ROLES: raise LogicError(f"Role '{role_to_change}' cannot be managed.")
            if admin_user_id == user_id_to_edit: raise LogicError("You cannot edit your own roles.")

            def edit_roles_transaction(session):
                tx = session.transaction(ydb.SerializableReadWrite())
                query = session.prepare("""
                    DECLARE $firm_id AS Utf8; DECLARE $admin_id AS Utf8; DECLARE $target_id AS Utf8;
                    SELECT user_id, roles FROM Users WHERE firm_id = $firm_id AND user_id IN ($admin_id, $target_id);
                """)
                res = tx.execute(query, {'$firm_id': firm_id, '$admin_id': admin_user_id, '$target_id': user_id_to_edit})
                
                admin_data, target_data = None, None
                for row in res[0].rows:
                    if row.user_id == admin_user_id: admin_data = row
                    if row.user_id == user_id_to_edit: target_data = row
                
                if not admin_data: raise NotFoundError("Admin not found in this firm.")
                if not target_data: raise NotFoundError("Target user not found in this firm.")
                if "OWNER" in json.loads(target_data.roles or '[]'): raise AuthError("The firm owner's roles cannot be edited.")
                
                admin_score = get_highest_role_score(admin_data.roles)
                target_score = get_highest_role_score(target_data.roles)
                if admin_score <= target_score: raise AuthError("Insufficient permissions.")

                roles_set = set(json.loads(target_data.roles or '[]'))
                
                if action == "ADD_ROLE":
                    if role_to_change in roles_set: raise LogicError(f"User already has the role '{role_to_change}'.")
                    roles_set.add(role_to_change)
                elif action == "REMOVE_ROLE":
                    if role_to_change not in roles_set: raise LogicError(f"User does not have the role '{role_to_change}'.")
                    roles_set.remove(role_to_change)

                new_roles_json = json.dumps(sorted(list(roles_set)))
                update_query = session.prepare("""
                    DECLARE $roles AS Json; DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8;
                    UPDATE Users SET roles = $roles WHERE user_id = $user_id AND firm_id = $firm_id;
                """)
                tx.execute(update_query, {'$roles': new_roles_json, '$user_id': user_id_to_edit, '$firm_id': firm_id})
                tx.commit()

            pool.retry_operation_sync(edit_roles_transaction)
            return {"statusCode": 200, "body": json.dumps({"message": f"Role '{role_to_change}' successfully {action.split('_')[0].lower()}ed."})}
        
        else:
            raise LogicError(f"Invalid action '{action}'. Valid actions are GET_INFO, ADD_ROLE, REMOVE_ROLE.")

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error editing employee: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```