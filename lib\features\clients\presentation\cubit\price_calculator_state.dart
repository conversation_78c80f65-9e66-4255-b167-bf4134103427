import 'package:equatable/equatable.dart';

// Экспорт состояния разовых услуг для удобства
export 'one_time_services_state.dart';

/// Тип организации/клиента
enum EntityType {
  ooo('ООО'),
  ao('АО'),
  ip('ИП'),
  kfh('КФХ'),
  kh('КХ');

  const EntityType(this.label);
  final String label;

  static EntityType fromString(String value) {
    return EntityType.values.firstWhere(
      (e) => e.name == value,
      orElse: () => EntityType.ooo,
    );
  }
}

/// Вид деятельности для базового тарифа
enum BusinessActivityType {
  services('Услуги'),
  trade('Торговля'),
  production('Производство/Строительство/Общепит');

  const BusinessActivityType(this.label);
  final String label;

  static BusinessActivityType fromString(String value) {
    return BusinessActivityType.values.firstWhere(
      (e) => e.name == value,
      orElse: () => BusinessActivityType.services,
    );
  }
}

/// Система налогообложения для базового тарифа
enum TaxSystem {
  osno('ОСНО'),
  usn6('УСН доход'),
  usn15('УСН 15%'),
  patent('Патент'),
  eskhn('ЕСХН'),
  patentUsn6('Патент+УСН доход'),
  patentUsn15('Патент+УСН доход-расход');

  const TaxSystem(this.label);
  final String label;

  static TaxSystem fromString(String value) {
    return TaxSystem.values.firstWhere(
      (e) => e.name == value,
      orElse: () => TaxSystem.usn6,
    );
  }
}

class PriceCalculatorState extends Equatable {
  const PriceCalculatorState({
    required this.entityType,
    required this.businessActivityType,
    required this.taxSystem,
    required this.baseRate,
    required this.operationsQty,
    required this.generatePrimaryDocs,
    required this.employeesQty,
    required this.paymentsQty,
    required this.vedOption,
    required this.kkmBso,
    required this.extraAccountQty,
    required this.currencyAccountQty,
    required this.nomenclatureQty,
    required this.taxSystemMix,
    required this.ndsRates,
    required this.pbu18,
    required this.osNma,
    // Новые опции ВЭД и акцизов
    required this.ftsEaes,
    required this.exciseGoods,
    required this.alcoholDeclaration,
    // Обособленные подразделения
    required this.subdivisionsQty,
    // Сложные операции
    required this.complexOperations,
    // Фиксированные услуги
    required this.our1C,
    required this.separate1CAccess,
    required this.vpn,
    required this.documentResigning,
    required this.courierPayment,
    required this.accountUnblocking,
    required this.rates,
    required this.total,
  });

  final EntityType entityType;
  final BusinessActivityType businessActivityType;
  final TaxSystem taxSystem;
  final int baseRate;
  final int operationsQty;
  final bool generatePrimaryDocs;
  final int employeesQty;
  final int paymentsQty;
  final VedOption vedOption;
  final bool kkmBso;
  final int extraAccountQty;
  final int currencyAccountQty;
  final int nomenclatureQty;
  final bool taxSystemMix;
  final bool ndsRates;
  final bool pbu18;
  final bool osNma;
  // Новые опции ВЭД и акцизов
  final bool ftsEaes;
  final bool exciseGoods;
  final bool alcoholDeclaration;
  // Обособленные подразделения
  final int subdivisionsQty;
  // Сложные операции
  final bool complexOperations;
  // Фиксированные услуги
  final bool our1C;
  final bool separate1CAccess;
  final bool vpn;
  final bool documentResigning;
  final bool courierPayment;
  final bool accountUnblocking;
  final Map<String, double> rates;
  final int total;

  PriceCalculatorState copyWith({
    EntityType? entityType,
    BusinessActivityType? businessActivityType,
    TaxSystem? taxSystem,
    int? baseRate,
    int? operationsQty,
    bool? generatePrimaryDocs,
    int? employeesQty,
    int? paymentsQty,
    VedOption? vedOption,
    bool? kkmBso,
    int? extraAccountQty,
    int? currencyAccountQty,
    int? nomenclatureQty,
    bool? taxSystemMix,
    bool? ndsRates,
    bool? pbu18,
    bool? osNma,
    bool? ftsEaes,
    bool? exciseGoods,
    bool? alcoholDeclaration,
    int? subdivisionsQty,
    bool? complexOperations,
    bool? our1C,
    bool? separate1CAccess,
    bool? vpn,
    bool? documentResigning,
    bool? courierPayment,
    bool? accountUnblocking,
    Map<String, double>? rates,
    int? total,
  }) {
    return PriceCalculatorState(
      entityType: entityType ?? this.entityType,
      businessActivityType: businessActivityType ?? this.businessActivityType,
      taxSystem: taxSystem ?? this.taxSystem,
      baseRate: baseRate ?? this.baseRate,
      operationsQty: operationsQty ?? this.operationsQty,
      generatePrimaryDocs: generatePrimaryDocs ?? this.generatePrimaryDocs,
      employeesQty: employeesQty ?? this.employeesQty,
      paymentsQty: paymentsQty ?? this.paymentsQty,
      vedOption: vedOption ?? this.vedOption,
      kkmBso: kkmBso ?? this.kkmBso,
      extraAccountQty: extraAccountQty ?? this.extraAccountQty,
      currencyAccountQty: currencyAccountQty ?? this.currencyAccountQty,
      nomenclatureQty: nomenclatureQty ?? this.nomenclatureQty,
      taxSystemMix: taxSystemMix ?? this.taxSystemMix,
      ndsRates: ndsRates ?? this.ndsRates,
      pbu18: pbu18 ?? this.pbu18,
      osNma: osNma ?? this.osNma,
      ftsEaes: ftsEaes ?? this.ftsEaes,
      exciseGoods: exciseGoods ?? this.exciseGoods,
      alcoholDeclaration: alcoholDeclaration ?? this.alcoholDeclaration,
      subdivisionsQty: subdivisionsQty ?? this.subdivisionsQty,
      complexOperations: complexOperations ?? this.complexOperations,
      our1C: our1C ?? this.our1C,
      separate1CAccess: separate1CAccess ?? this.separate1CAccess,
      vpn: vpn ?? this.vpn,
      documentResigning: documentResigning ?? this.documentResigning,
      courierPayment: courierPayment ?? this.courierPayment,
      accountUnblocking: accountUnblocking ?? this.accountUnblocking,
      rates: rates ?? this.rates,
      total: total ?? this.total,
    );
  }

  @override
  List<Object?> get props => [
    entityType,
    businessActivityType,
    taxSystem,
    baseRate,
    operationsQty,
    generatePrimaryDocs,
    employeesQty,
    paymentsQty,
    vedOption,
    kkmBso,
    extraAccountQty,
    currencyAccountQty,
    nomenclatureQty,
    taxSystemMix,
    ndsRates,
    pbu18,
    osNma,
    ftsEaes,
    exciseGoods,
    alcoholDeclaration,
    subdivisionsQty,
    complexOperations,
    our1C,
    separate1CAccess,
    vpn,
    documentResigning,
    courierPayment,
    accountUnblocking,
    rates,
    total,
  ];
}

enum VedOption { none, importExport, both }

/// Типы для калькулятора нулевой отчетности
enum ZeroReportingType {
  oooUsn('ООО на УСН', 7000),
  oooOsno('ООО на ОСНО', 10000),
  ipUsn('ИП на УСН', 2000),
  ipOsno('ИП на ОСНО', 3000);

  const ZeroReportingType(this.label, this.price);
  final String label;
  final int price; // цена за квартал

  static ZeroReportingType fromString(String value) {
    return ZeroReportingType.values.firstWhere(
      (e) => e.name == value,
      orElse: () => ZeroReportingType.oooUsn,
    );
  }
}

/// Состояние калькулятора нулевой отчетности
class ZeroReportingState extends Equatable {
  const ZeroReportingState({required this.selectedType});

  final ZeroReportingType selectedType;

  ZeroReportingState copyWith({ZeroReportingType? selectedType}) {
    return ZeroReportingState(selectedType: selectedType ?? this.selectedType);
  }

  @override
  List<Object?> get props => [selectedType];
}
