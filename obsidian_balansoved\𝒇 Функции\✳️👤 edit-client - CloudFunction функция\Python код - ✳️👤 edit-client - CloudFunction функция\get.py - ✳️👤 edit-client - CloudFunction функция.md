
```python
import json
import ydb
import decimal
import datetime
import uuid
import pytz

class NotFoundError(Exception): pass

def _json_serializer(obj):
    # Эта функция теперь будет вызываться реже, но остается для подстраховки
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()
    elif isinstance(obj, datetime.date):
        return obj.isoformat()
    elif isinstance(obj, decimal.Decimal):
        return float(obj)
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def rows_to_json(rows):
    """
    Преобразует строки YDB в список словарей, корректно форматируя даты.
    """
    if rows is None:
        return None
    
    # --- НАЧАЛО ИЗМЕНЕНИЙ ---
    # Гарантируем, что работаем со списком для единообразной обработки
    if not isinstance(rows, list):
        rows = [rows]

    result_list = []
    for row in rows:
        row_dict = {}
        for column_name in row:
            value = row[column_name]
            # ЯВНОЕ ПРЕОБРАЗОВАНИЕ ДАТЫ ИЗ ЧИСЛА В СТРОКУ
            if column_name == 'manual_creation_date' and isinstance(value, int):
                # YDB возвращает тип Date как количество дней с 1970-01-01
                converted_date = datetime.date(1970, 1, 1) + datetime.timedelta(days=value)
                row_dict[column_name] = converted_date.isoformat()
            else:
                row_dict[column_name] = value
        result_list.append(row_dict)
        
    # Возвращаем один объект, если на входе был не список, для обратной совместимости
    if len(result_list) == 1 and not isinstance(rows, list):
        return result_list[0]
    
    return result_list
    # --- КОНЕЦ ИЗМЕНЕНИЙ ---

def get_client(session, table_name, client_id, creation_date, log_accumulator):
    log_accumulator.append(f"[get_client] Starting GET operation with client_id={client_id}, creation_date={creation_date}, table={table_name}")
    
    tx = session.transaction(ydb.SerializableReadWrite())
    if client_id and creation_date:
        log_accumulator.append("[get_client] Mode: Get specific client by ID and creation date")
        query_text = f"""DECLARE $client_id AS Utf8; DECLARE $creation_date AS Date;
                         SELECT * FROM `{table_name}` WHERE client_id = $client_id AND manual_creation_date = $creation_date;"""
        log_accumulator.append(f"[get_client] Query: {query_text}")
        result = tx.execute(session.prepare(query_text), {'$client_id': client_id, '$creation_date': creation_date})
        log_accumulator.append(f"[get_client] Query executed, rows found: {len(result[0].rows) if result and result[0].rows else 0}")
        data = result[0].rows[0] if result and result[0].rows else None
        if data is None:
            log_accumulator.append(f"[get_client] Client not found: client_id={client_id}, creation_date={creation_date}")
        else:
            log_accumulator.append("[get_client] Successfully found specific client")
    elif client_id:
        log_accumulator.append("[get_client] Mode: Get all versions of client by ID")
        query_text = f"""DECLARE $client_id AS Utf8;
                         SELECT * FROM `{table_name}` WHERE client_id = $client_id ORDER BY manual_creation_date DESC;"""
        log_accumulator.append(f"[get_client] Query: {query_text}")
        result = tx.execute(session.prepare(query_text), {'$client_id': client_id})
        log_accumulator.append(f"[get_client] Query executed, rows found: {len(result[0].rows) if result and result[0].rows else 0}")
        data = result[0].rows if result else []
        log_accumulator.append(f"[get_client] Successfully found {len(data)} versions of client")
    else:
        log_accumulator.append("[get_client] Mode: Get all actual clients")
        query_text = f"""SELECT * FROM `{table_name}` WHERE is_actual = true;"""
        log_accumulator.append(f"[get_client] Query: {query_text}")
        result = tx.execute(session.prepare(query_text))
        log_accumulator.append(f"[get_client] Query executed, total rows found: {len(result[0].rows) if result and result[0].rows else 0}")
        data = result[0].rows if result else []
        log_accumulator.append("[get_client] Successfully retrieved all actual clients")
    
    tx.commit()
    log_accumulator.append("[get_client] Transaction committed successfully")
    # Используем наш новый rows_to_json и стандартный json.dumps
    return {"statusCode": 200, "body": json.dumps({"data": rows_to_json(data)}, default=_json_serializer)}
```