import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_clients_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_client_versions_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/upsert_client_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/delete_client_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/delete_all_client_versions_usecase.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';

part 'clients_state.dart';

class ClientsCubit extends Cubit<ClientsState> {
  final GetClientsUseCase getClientsUseCase;
  final GetClientVersionsUseCase getClientVersionsUseCase;
  final UpsertClientUseCase upsertClientUseCase;
  final DeleteClientUseCase deleteClientUseCase;
  final DeleteAllClientVersionsUseCase deleteAllClientVersionsUseCase;

  ClientsCubit({
    required this.getClientsUseCase,
    required this.getClientVersionsUseCase,
    required this.upsertClientUseCase,
    required this.deleteClientUseCase,
    required this.deleteAllClientVersionsUseCase,
  }) : super(const ClientsState.initial());

  Future<void> fetchClients(String firmId, {bool onlyActual = true}) async {
    emit(state.copyWith(isLoading: true, noAccess: false));
    NetworkLogger.printInfo(
      'ClientsCubit: Starting fetchClients for firmId: $firmId, onlyActual: $onlyActual',
    );

    try {
      final result = await getClientsUseCase.call(
        firmId,
        onlyActual: onlyActual,
      );

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientsCubit: fetchClients failed with error:',
            failure.message,
          );
          NetworkLogger.printError(
            'ClientsCubit: Full failure object:',
            failure,
          );

          if (failure is AccessDeniedFailure) {
            emit(
              state.copyWith(
                isLoading: false,
                error: failure.message,
                noAccess: true,
              ),
            );
          } else {
            emit(state.copyWith(isLoading: false, error: failure.message));
          }
        },
        (clients) {
          NetworkLogger.printSuccess(
            'ClientsCubit: fetchClients success, loaded ${clients.length} clients',
          );
          emit(
            state.copyWith(
              isLoading: false,
              clients: clients,
              error: null,
              noAccess: false,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientsCubit: Unexpected error in fetchClients:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<void> fetchClientVersions(String firmId, String clientName) async {
    emit(state.copyWith(isLoading: true, noAccess: false));
    NetworkLogger.printInfo(
      'ClientsCubit: Starting fetchClientVersions for firmId: $firmId, clientName: $clientName',
    );

    try {
      final result = await getClientVersionsUseCase.call(firmId, clientName);

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientsCubit: fetchClientVersions failed with error:',
            failure.message,
          );
          NetworkLogger.printError(
            'ClientsCubit: Full failure object:',
            failure,
          );

          if (failure is AccessDeniedFailure) {
            emit(
              state.copyWith(
                isLoading: false,
                error: failure.message,
                noAccess: true,
              ),
            );
          } else {
            emit(state.copyWith(isLoading: false, error: failure.message));
          }
        },
        (clientVersions) {
          NetworkLogger.printSuccess(
            'ClientsCubit: fetchClientVersions success, loaded ${clientVersions.length} versions',
          );
          emit(
            state.copyWith(
              isLoading: false,
              clients: clientVersions,
              error: null,
              noAccess: false,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientsCubit: Unexpected error in fetchClientVersions:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<ClientEntity?> saveClient(
    String firmId,
    ClientEntity client, {
    bool isCreation = false,
  }) async {
    emit(state.copyWith(isLoading: true));
    NetworkLogger.printInfo(
      'ClientsCubit: Saving client ${client.name} for firmId: $firmId',
    );
    NetworkLogger.printInfo(
      'ClientsCubit: Client profitTaxTypes: ${client.profitTaxTypes}',
    );

    try {
      // КРИТИЧЕСКИ ВАЖНО: НЕ очищаем id при создании версий!
      // Очищаем id только для создания совершенно нового клиента (когда id пустой)
      final clientToSave =
          isCreation && client.id.isEmpty ? client.copyWith(id: '') : client;
      final result = await upsertClientUseCase(firmId, clientToSave);

      return result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientsCubit: saveClient failed with error:',
            failure.message,
          );
          NetworkLogger.printError(
            'ClientsCubit: Full failure object:',
            failure,
          );
          emit(state.copyWith(isLoading: false, error: failure.message));
          return null;
        },
        (savedClient) {
          NetworkLogger.printSuccess('ClientsCubit: saveClient success');

          // Обновляем локальное состояние вместо полной перезагрузки
          final updatedClients = List<ClientEntity>.from(state.clients);
          final existingIndex = updatedClients.indexWhere(
            (c) =>
                c.id == savedClient.id &&
                c.creationDate?.millisecondsSinceEpoch ==
                    savedClient.creationDate?.millisecondsSinceEpoch,
          );

          if (existingIndex != -1) {
            // Обновляем существующего клиента
            updatedClients[existingIndex] = savedClient;
          } else {
            // Добавляем нового клиента
            updatedClients.add(savedClient);
          }

          emit(
            state.copyWith(
              isLoading: false,
              clients: updatedClients,
              error: null,
            ),
          );

          return savedClient;
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientsCubit: Unexpected error in saveClient:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
    return null;
  }

  Future<ClientEntity?> createClient(String firmId, ClientEntity client) async {
    return saveClient(firmId, client, isCreation: true);
  }

  Future<void> deleteClient(
    String firmId,
    String clientId,
    DateTime creationDate,
  ) async {
    emit(state.copyWith(isLoading: true));
    NetworkLogger.printInfo(
      'ClientsCubit: Deleting client $clientId for firmId: $firmId with creationDate: $creationDate',
    );

    try {
      final result = await deleteClientUseCase.call(
        firmId,
        clientId,
        creationDate,
      );

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientsCubit: deleteClient failed with error:',
            failure.message,
          );
          NetworkLogger.printError(
            'ClientsCubit: Full failure object:',
            failure,
          );
          emit(state.copyWith(isLoading: false, error: failure.message));
        },
        (_) {
          NetworkLogger.printSuccess('ClientsCubit: deleteClient success');
          fetchClients(firmId); // Обновляем список
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientsCubit: Unexpected error in deleteClient:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<void> deleteAllClientVersions(String firmId, String clientName) async {
    emit(state.copyWith(isLoading: true));
    NetworkLogger.printInfo(
      'ClientsCubit: Deleting all versions for client $clientName in firmId: $firmId',
    );

    try {
      final result = await deleteAllClientVersionsUseCase.call(
        firmId,
        clientName,
      );

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientsCubit: deleteAllClientVersions failed with error:',
            failure.message,
          );
          NetworkLogger.printError(
            'ClientsCubit: Full failure object:',
            failure,
          );
          emit(state.copyWith(isLoading: false, error: failure.message));
        },
        (_) {
          NetworkLogger.printSuccess(
            'ClientsCubit: deleteAllClientVersions success',
          );
          fetchClients(firmId); // Обновляем список
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientsCubit: Unexpected error in deleteAllClientVersions:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }
}
