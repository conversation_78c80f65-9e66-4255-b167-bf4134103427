import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';
import '../../domain/entities/task_entity.dart';
import '../cubit/task_create_cubit.dart';
import 'package:balansoved_enterprise/injection_container.dart' as di;
import '../cubit/task_create_state.dart';
import '../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import '../../../profile/presentation/cubit/profile_cubit.dart';
import 'task_create/dialogs.dart';
import 'task_create/form_sections.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';
import 'task_create/widgets/seamless_description_field.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../router.dart';

class TaskCreateCard extends StatelessWidget {
  final TaskEntity? initialTask;
  final void Function(TaskEntity)? onSaved;
  final VoidCallback? onCancel;
  final void Function(TaskEntity)? onCreated;

  const TaskCreateCard({
    super.key,
    this.initialTask,
    this.onSaved,
    this.onCancel,
    this.onCreated,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => TaskCreateCubit(
            saveTaskUseCase: di.sl(),
            profileCubit: context.read<ProfileCubit>(),
          )..initialize(initialTask: initialTask),
      child: _TaskCreateCardContent(
        initialTask: initialTask,
        onSaved: onSaved,
        onCancel: onCancel,
        onCreated: onCreated,
      ),
    );
  }
}

class _TaskCreateCardContent extends StatefulWidget {
  final TaskEntity? initialTask;
  final void Function(TaskEntity)? onSaved;
  final VoidCallback? onCancel;
  final void Function(TaskEntity)? onCreated;

  const _TaskCreateCardContent({
    this.initialTask,
    this.onSaved,
    this.onCancel,
    this.onCreated,
  });

  @override
  State<_TaskCreateCardContent> createState() => _TaskCreateCardContentState();
}

class _TaskCreateCardContentState extends State<_TaskCreateCardContent> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _titleController.text = widget.initialTask?.title ?? '';
    _descriptionController.text = widget.initialTask?.description ?? '';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TaskCreateCubit, TaskCreateState>(
      listener: (context, state) {
        if (state is TaskCreateLoaded) {
          // Обновляем контроллеры при изменении состояния
          if (_titleController.text != state.title) {
            _titleController.text = state.title;
          }
          if (_descriptionController.text != state.description) {
            _descriptionController.text = state.description;
          }
        } else if (state is TaskCreateSaved) {
          // Обрабатываем успешное сохранение
          if (widget.onCreated != null) {
            widget.onCreated!(state.savedTask);
          } else if (widget.onSaved != null) {
            widget.onSaved!(state.savedTask);
          }
        } else if (state is TaskCreateError) {
          // Показываем ошибку
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      child: BlocBuilder<TaskCreateCubit, TaskCreateState>(
        builder: (context, state) {
          if (state is TaskCreateInitial || state is TaskCreateLoading) {
            return const Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: EdgeInsets.all(24.0),
                child: Center(child: CircularProgressIndicator()),
              ),
            );
          }

          if (state is! TaskCreateLoaded && state is! TaskCreateSaving) {
            return const Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: EdgeInsets.all(24.0),
                child: Center(child: Text('Ошибка загрузки')),
              ),
            );
          }

          final loadedState =
              state is TaskCreateLoaded
                  ? state
                  : (state as TaskCreateSaving).currentState;

          return Card(
            margin: EdgeInsets.zero,
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Заголовок
                  Text(
                    widget.initialTask == null
                        ? 'Создать задачу'
                        : 'Редактировать задачу',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Форма
                  Flexible(
                    fit: FlexFit.loose,
                    child: Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Название задачи
                            TextFormField(
                              controller: _titleController,
                              decoration: const InputDecoration(
                                labelText: 'Название задачи *',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Введите название задачи';
                                }
                                return null;
                              },
                              onChanged:
                                  (value) => context
                                      .read<TaskCreateCubit>()
                                      .updateTitle(value),
                            ),
                            const SizedBox(height: 24),

                            // Seamless editor описания с блоками текста и картинок
                            SeamlessDescriptionField(
                              initialText: loadedState.description,
                              attachments: loadedState.cloudFiles,
                              onChanged: (value) {
                                _descriptionController.text = value;
                                context
                                    .read<TaskCreateCubit>()
                                    .updateDescription(value);
                              },
                              onTaskAutoSave: () => _autoSaveTask(context),
                              onFileAdded:
                                  (file) => context
                                      .read<TaskCreateCubit>()
                                      .addCloudFileFromDescription(file),
                              onFileUpdated:
                                  (file) => context
                                      .read<TaskCreateCubit>()
                                      .updateCloudFile(file),
                            ),
                            const SizedBox(height: 24),

                            // Клиенты
                            ClientSelector(
                              selectedClientIds: loadedState.selectedClientIds,
                              onClientChanged: (clientId, selected) {
                                context
                                    .read<TaskCreateCubit>()
                                    .updateSelectedClients(clientId, selected);
                              },
                            ),
                            const SizedBox(height: 24),

                            // Исполнители
                            EmployeeSelector(
                              title: 'Исполнители',
                              selectedIds: loadedState.selectedAssigneeIds,
                              onChanged:
                                  (ids) => context
                                      .read<TaskCreateCubit>()
                                      .updateAssignees(ids),
                            ),
                            const SizedBox(height: 16),

                            // Соисполнители
                            EmployeeSelector(
                              title: 'Соисполнители',
                              selectedIds: loadedState.selectedCoAssigneeIds,
                              onChanged:
                                  (ids) => context
                                      .read<TaskCreateCubit>()
                                      .updateCoAssignees(ids),
                            ),
                            const SizedBox(height: 24),

                            // Наблюдатели
                            EmployeeSelector(
                              title: 'Наблюдатели',
                              selectedIds: loadedState.selectedObserverIds,
                              onChanged:
                                  (ids) => context
                                      .read<TaskCreateCubit>()
                                      .updateObservers(ids),
                            ),
                            const SizedBox(height: 16),

                            // Постановщики
                            EmployeeSelector(
                              title: 'Постановщики',
                              selectedIds: loadedState.selectedCreatorIds,
                              onChanged:
                                  (ids) => context
                                      .read<TaskCreateCubit>()
                                      .updateCreators(ids),
                            ),
                            const SizedBox(height: 24),

                            // Приоритет и дата
                            Row(
                              children: [
                                Expanded(
                                  child: DropdownButtonFormField<String>(
                                    value: loadedState.priority,
                                    decoration: const InputDecoration(
                                      labelText: 'Приоритет',
                                      border: OutlineInputBorder(),
                                    ),
                                    items: const [
                                      DropdownMenuItem(
                                        value: 'low',
                                        child: Text('Низкий'),
                                      ),
                                      DropdownMenuItem(
                                        value: 'medium',
                                        child: Text('Средний'),
                                      ),
                                      DropdownMenuItem(
                                        value: 'high',
                                        child: Text('Высокий'),
                                      ),
                                      DropdownMenuItem(
                                        value: 'critical',
                                        child: Text('Критический'),
                                      ),
                                    ],
                                    onChanged:
                                        (value) => context
                                            .read<TaskCreateCubit>()
                                            .updatePriority(value!),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: InkWell(
                                    onTap:
                                        () => _selectDueDate(
                                          context,
                                          loadedState.dueDate,
                                        ),
                                    child: InputDecorator(
                                      decoration: InputDecoration(
                                        labelText: 'Срок выполнения',
                                        border: const OutlineInputBorder(),
                                        suffixIcon:
                                            loadedState.dueDate != null
                                                ? IconButton(
                                                  icon: const Icon(
                                                    Icons.clear,
                                                    size: 16,
                                                  ),
                                                  onPressed:
                                                      () => context
                                                          .read<
                                                            TaskCreateCubit
                                                          >()
                                                          .updateDueDate(
                                                            null,
                                                            isNewTask:
                                                                widget
                                                                    .initialTask ==
                                                                null,
                                                          ),
                                                  tooltip: 'Сделать бессрочной',
                                                )
                                                : const Icon(
                                                  Icons.calendar_today,
                                                  size: 16,
                                                ),
                                      ),
                                      child: Text(
                                        loadedState.dueDate == null
                                            ? 'Не указан (бессрочная)'
                                            : '${loadedState.dueDate!.day}.${loadedState.dueDate!.month}.${loadedState.dueDate!.year}',
                                        style: TextStyle(
                                          color:
                                              loadedState.dueDate == null
                                                  ? Colors.grey
                                                  : null,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Дополнительные параметры
                            AdditionalOptionsSection(
                              allowAssigneeToChangeDueDate:
                                  loadedState.allowAssigneeToChangeDueDate,
                              onAllowAssigneeChanged:
                                  (value) => context
                                      .read<TaskCreateCubit>()
                                      .updateAllowAssigneeToChangeDueDate(
                                        value,
                                      ),
                            ),
                            const SizedBox(height: 24),

                            // Файловые вложения
                            UnifiedAttachmentsSection(
                              cloudFiles: loadedState.cloudFiles,
                              onAddCloudFile:
                                  (file) => _handleAddCloudFile(context, file),
                              onRemoveCloudFile:
                                  (file) => context
                                      .read<TaskCreateCubit>()
                                      .removeCloudFile(file),
                              onUpdateCloudFile:
                                  (file) => context
                                      .read<TaskCreateCubit>()
                                      .updateCloudFile(file),
                              onTaskAutoSave: () => _autoSaveTask(context),
                            ),
                            const SizedBox(height: 24),

                            // Чек-лист
                            ChecklistSection(
                              checklist: loadedState.checklist,
                              onAddItem: () => _addChecklistItem(context),
                              onRemoveItem:
                                  (item) => context
                                      .read<TaskCreateCubit>()
                                      .removeChecklistItem(item),
                              onToggleItem:
                                  (item, completed) => context
                                      .read<TaskCreateCubit>()
                                      .toggleChecklistItem(item, completed),
                              onReorder:
                                  (oldIndex, newIndex) => context
                                      .read<TaskCreateCubit>()
                                      .reorderChecklist(oldIndex, newIndex),
                            ),
                            const SizedBox(height: 24),

                            // Напоминания
                            RemindersSection(
                              reminders: loadedState.reminders,
                              onAddReminder: () => _addReminder(context),
                              onRemoveReminder:
                                  (reminder) => context
                                      .read<TaskCreateCubit>()
                                      .removeReminder(reminder),
                            ),
                            const SizedBox(height: 24),

                            // Повторение
                            RecurrenceSection(
                              recurrenceType: loadedState.recurrenceType,
                              recurrenceInterval:
                                  loadedState.recurrenceInterval,
                              executionDates:
                                  loadedState.recurrenceExecutionDates,
                              isAnnual: loadedState.recurrenceIsAnnual,
                              onRecurrenceTypeChanged:
                                  (value) => context
                                      .read<TaskCreateCubit>()
                                      .updateRecurrenceType(value),
                              onRecurrenceIntervalChanged:
                                  (value) => context
                                      .read<TaskCreateCubit>()
                                      .updateRecurrenceInterval(value),
                              onExecutionDatesChanged: (dates) => context
                                  .read<TaskCreateCubit>()
                                  .updateRecurrenceExecutionDates(dates),
                              onIsAnnualChanged: (val) => context
                                  .read<TaskCreateCubit>()
                                  .updateRecurrenceIsAnnual(val),
                            ),
                            const SizedBox(height: 24),

                            // Перенос праздников
                            HolidayTransferSection(
                              holidayTransferRule:
                                  loadedState.holidayTransferRule,
                              onHolidayTransferRuleChanged:
                                  (value) => context
                                      .read<TaskCreateCubit>()
                                      .updateHolidayTransferRule(value),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Кнопки
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed:
                            loadedState.hasActiveFileOperations ||
                                    state is TaskCreateSaving
                                ? null
                                : widget.onCancel,
                        child: const Text('Отмена'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed:
                            loadedState.hasActiveFileOperations ||
                                    state is TaskCreateSaving
                                ? null
                                : () => _createTask(context),
                        child:
                            loadedState.hasActiveFileOperations ||
                                    state is TaskCreateSaving
                                ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                                : Text(
                                  widget.initialTask == null && !loadedState.wasAutoCreated
                                      ? 'Создать'
                                      : 'Сохранить',
                                ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _selectDueDate(
    BuildContext context,
    DateTime? currentDueDate,
  ) async {
    final date = await SmartDatePickerDialog.show(
      context: context,
      initialDate: currentDueDate,
      firstDate: DateTime(2020, 1, 1), // Разрешаем выбор дат в прошлом
      lastDate: DateTime(2030, 12, 31), // Расширенный диапазон будущих дат
      helpText: 'Выберите крайний срок',
      allowClear: true,
    );

    if (date != null) {
      context.read<TaskCreateCubit>().updateDueDate(
        date,
        isNewTask: widget.initialTask == null,
      );
    } else {
      context.read<TaskCreateCubit>().updateDueDate(
        null,
        isNewTask: widget.initialTask == null,
      );
    }
  }

  // Эти методы теперь не нужны, так как логика перенесена в Cubit

  void _addChecklistItem(BuildContext context) {
    final cubit = context.read<TaskCreateCubit>();
    showDialog(
      context: context,
      builder: (dialogContext) => ChecklistItemDialog(
        onAdd: (item) {
          cubit.addChecklistItem(item);
          Navigator.of(dialogContext).pop();
        },
      ),
    );
  }

  void _addReminder(BuildContext context) {
    final cubit = context.read<TaskCreateCubit>();
    showDialog(
      context: context,
      builder: (dialogContext) => ReminderDialog(
        onAdd: (reminder) {
          cubit.addReminder(reminder);
          Navigator.of(dialogContext).pop();
        },
      ),
    );
  }

  // Эти методы теперь не нужны, так как логика перенесена в Cubit

  void _createTask(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    final activeFirmState = context.read<ActiveFirmCubit>().state;
    if (activeFirmState.selectedFirm == null) return;

    await context.read<TaskCreateCubit>().saveTask(
      activeFirmState.selectedFirm!.id,
      initialTask: widget.initialTask,
    );
  }

  void _handleAddCloudFile(BuildContext context, FileAttachmentItem file) {
    final activeFirmState = context.read<ActiveFirmCubit>().state;
    if (activeFirmState.selectedFirm == null) {
      context.read<TaskCreateCubit>().addCloudFile(file);
      return;
    }

    context.read<TaskCreateCubit>().addCloudFile(
      file,
      firmId: activeFirmState.selectedFirm!.id,
      initialTask: widget.initialTask,
      onAutoSaved: (taskId) {
        // Навигация к TasksEditRoute после автосохранения
        context.router.navigate(TasksEditRoute(taskId: taskId));
      },
    );
  }

  /// Автосохранение задачи в фоне (например, при загрузке/удалении файлов)
  void _autoSaveTask(BuildContext context) async {
    final activeFirmState = context.read<ActiveFirmCubit>().state;
    if (activeFirmState.selectedFirm == null) return;

    await context.read<TaskCreateCubit>().autoSaveTask(
      activeFirmState.selectedFirm!.id,
      initialTask: widget.initialTask,
    );
  }

  // Эти методы теперь не нужны, так как логика перенесена в Cubit
}
