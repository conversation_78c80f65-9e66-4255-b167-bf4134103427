# pdf_generator.py

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import Paragraph, Spacer, Frame, PageTemplate, BaseDocTemplate
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.colors import HexColor, black
from reportlab.lib.utils import simpleSplit
import datetime
import os
import re


def format_due_date(raw_due):
    """Приводит значение due_date из API к формату DD.MM.YYYY."""
    if raw_due is None:
        return "N/A"

    # Числовое значение (timestamp в micro/milli/seconds)
    try:
        due_int = int(raw_due)
        # Определяем порядок: microsec > 1e12, millisec > 1e10
        if due_int > 1e12:
            seconds = due_int / 1_000_000
        elif due_int > 1e10:
            seconds = due_int / 1_000
        else:
            seconds = due_int
        dt = datetime.datetime.fromtimestamp(seconds)
        return dt.strftime("%d.%m.%Y")
    except (ValueError, TypeError):
        pass

    # Строковое значение
    if isinstance(raw_due, str):
        # Если это строка цифр
        if raw_due.isdigit():
            return format_due_date(int(raw_due))

        # Пытаемся разобрать ISO 8601
        try:
            # Заменяем Z на +00:00 для fromisoformat
            dt = datetime.datetime.fromisoformat(raw_due.replace("Z", "+00:00"))
            return dt.strftime("%d.%m.%Y")
        except Exception:
            pass

        # Пытаемся через strptime (без миллисекунд)
        try:
            dt = datetime.datetime.strptime(raw_due[:19], "%Y-%m-%dT%H:%M:%S")
            return dt.strftime("%d.%m.%Y")
        except Exception:
            return raw_due  # Возвращаем как есть, если не смогли разобрать

    # Fall-back
    return str(raw_due)


# --- Настройка шрифтов для поддержки кириллицы ---
def register_fonts():
    """Регистрирует кириллические шрифты в ReportLab."""
    # Попытка зарегистрировать системный шрифт Windows
    try:
        pdfmetrics.registerFont(TTFont('Verdana', 'Verdana.ttf'))
        pdfmetrics.registerFont(TTFont('Verdana-Bold', 'Verdanab.ttf'))
        return "Verdana", "Verdana-Bold"
    except Exception:
        # Fallback на шрифт DejaVu, который нужно положить рядом со скриптом
        # Скачать можно отсюда: https://dejavu-fonts.github.io/
        try:
            dejavu_path = os.path.join(os.path.dirname(__file__), 'DejaVuSans.ttf')
            dejavu_bold_path = os.path.join(os.path.dirname(__file__), 'DejaVuSans-Bold.ttf')
            pdfmetrics.registerFont(TTFont('DejaVuSans', dejavu_path))
            pdfmetrics.registerFont(TTFont('DejaVuSans-Bold', dejavu_bold_path))
            print("Шрифт Verdana не найден, используется DejaVuSans.")
            return "DejaVuSans", "DejaVuSans-Bold"
        except Exception:
            print("ПРЕДУПРЕЖДЕНИЕ: Кириллические шрифты не найдены. Текст может отображаться некорректно.")
            return "Helvetica", "Helvetica-Bold"


FONT_REGULAR, FONT_BOLD = register_fonts()

# --- Стили ---
styles = getSampleStyleSheet()
style_normal = styles['Normal']
style_normal.fontName = FONT_REGULAR
style_normal.fontSize = 11
style_normal.leading = 14  # Межстрочный интервал

style_h1 = styles['h1']
style_h1.fontName = FONT_BOLD
style_h1.fontSize = 18
style_h1.alignment = 1  # Центрирование

style_h2 = styles['h2']
style_h2.fontName = FONT_BOLD
style_h2.fontSize = 14
style_h2.spaceBefore = 10
style_h2.spaceAfter = 5


# --- Вспомогательные функции ---

def footer_canvas(canvas, doc):
    """Функция для отрисовки колонтитула (номера страницы)."""
    canvas.saveState()
    canvas.setFont(FONT_REGULAR, 9)
    page_number_text = f"Страница {doc.page}"
    canvas.drawCentredString(A4[0] / 2, 1.5 * cm, page_number_text)

    timestamp = datetime.datetime.now().strftime("%d.%m.%Y %H:%M:%S")
    canvas.drawString(A4[0] - 5 * cm, 1.5 * cm, f"Сформировано: {timestamp}")
    canvas.restoreState()


def parse_and_replace_links(description: str, attachments_map: dict) -> str:
    """
    Находит плейсхолдеры [file:file_key] в тексте и заменяет их на HTML-ссылки.
    """
    if not description:
        return ""

    def replace_match(match):
        file_key = match.group(1)
        # Ищем вложение по ключу в словаре
        attachment_info = attachments_map.get(file_key)
        if attachment_info:
            # Формируем HTML-ссылку для ReportLab
            return f'<link href="{attachment_info["public_url"]}"><font color="blue"><u>{attachment_info["name"]}</u></font></link>'
        else:
            # Если файл не найден, возвращаем плейсхолдер как есть, но без скобок
            return f"(файл '{file_key}' не найден)"

    # Заменяем все вхождения плейсхолдера
    return re.sub(r'\[file:([\w\d\./_-]+)\]', replace_match, description)


# --- Основная функция генерации ---

def generate_task_pdf(
        output_path: str,
        task_data: dict,
        participants_map: dict,  # {'assignee': ['Иванов (id1)', 'Петров (id2)'], ...}
        clients_map: list,  # ['ООО Ромашка (id1)', 'ИП Сидоров (id2)']
        attachments_with_urls: list  # [{'name': 'file.pdf', 'public_url': 'http://...', 'file_key': '...'}]
):
    """
    Создает PDF-документ с детальной информацией о задаче.
    """
    doc = BaseDocTemplate(output_path, pagesize=A4,
                          leftMargin=2 * cm, rightMargin=2 * cm,
                          topMargin=2 * cm, bottomMargin=2 * cm)

    frame = Frame(doc.leftMargin, doc.bottomMargin, doc.width, doc.height, id='normal')
    template = PageTemplate(id='main_template', frames=[frame], onPage=footer_canvas)
    doc.addPageTemplates([template])

    story = []

    # 1. Заголовок
    story.append(Paragraph(task_data.get('title', 'Задача без названия'), style_h1))
    story.append(Spacer(1, 0.5 * cm))

    # 2. Мета-информация
    meta_info = (
        f"<b>Статус:</b> {task_data.get('status', 'N/A')} | "
        f"<b>Приоритет:</b> {task_data.get('priority', 'N/A')}"
    )
    if task_data.get('due_date'):
        meta_info += f" | <b>Срок:</b> {format_due_date(task_data.get('due_date'))}"
    story.append(Paragraph(meta_info, style_normal))
    story.append(Spacer(1, 1 * cm))

    # 3. Описание с заменой ссылок
    story.append(Paragraph("Описание", style_h2))

    # Создаем словарь {file_key: {'name': name, 'public_url': url}} для быстрого поиска
    attachments_map = {att['file_key']: att for att in attachments_with_urls if att.get('file_key')}

    parsed_description = parse_and_replace_links(task_data.get('description', 'Нет описания.'), attachments_map)
    story.append(Paragraph(parsed_description, style_normal))
    story.append(Spacer(1, 1 * cm))

    # 4. Участники
    if participants_map:
        story.append(Paragraph("Участники", style_h2))
        role_names = {
            'assignee': 'Исполнители', 'creator': 'Постановщики', 'observer': 'Наблюдатели'
        }
        for role, names in participants_map.items():
            if names:
                story.append(
                    Paragraph(f"<b>{role_names.get(role, role.capitalize())}:</b> {', '.join(names)}", style_normal))
        story.append(Spacer(1, 1 * cm))

    # 5. Клиенты
    if clients_map:
        story.append(Paragraph("Связанные клиенты", style_h2))
        for client_name in clients_map:
            story.append(Paragraph(f"• {client_name}", style_normal))
        story.append(Spacer(1, 1 * cm))

    # 6. Вложения
    if attachments_with_urls:
        story.append(Paragraph("Вложения", style_h2))
        for att in attachments_with_urls:
            link = f'<link href="{att["public_url"]}"><u>{att["name"]}</u></link>'
            story.append(Paragraph(f"• <font color='blue'>{link}</font>", style_normal))

    # Сборка документа
    try:
        doc.build(story)
        return True
    except Exception as e:
        print(f"Ошибка при создании PDF: {e}")
        return False


# --- Пример использования (для тестирования модуля) ---
if __name__ == '__main__':
    print("Запуск тестовой генерации PDF...")

    # Пример данных, которые будут приходить из основного скрипта
    mock_task_data = {
        'title': 'Проверить и утвердить годовой отчет',
        'status': 'in_progress',
        'priority': 'high',
        'due_date': '2025-07-31T15:00:00Z',
        'description': 'Необходимо сверить все данные с бухгалтерией. Особое внимание уделить разделу 4. '
                       'Прикрепленный файл [file:firm_id/2025/07/scan_01.pdf] содержит скан-копию прошлогоднего отчета для сравнения.',
    }

    mock_participants = {
        'assignee': ['Иванов Иван (user-id-123)'],
        'creator': ['Сидоров Петр (user-id-456)']
    }

    mock_clients = ['ООО "Ромашка" (client-id-abc)']

    mock_attachments = [
        {'name': 'scan_01.pdf', 'public_url': 'https://disk.yandex.ru/i/example1',
         'file_key': 'firm_id/2025/07/scan_01.pdf'},
        {'name': 'инструкция.docx', 'public_url': 'https://disk.yandex.ru/i/example2',
         'file_key': 'firm_id/2025/07/instr.docx'}
    ]

    output_filename = "example_task_report.pdf"

    success = generate_task_pdf(
        output_path=output_filename,
        task_data=mock_task_data,
        participants_map=mock_participants,
        clients_map=mock_clients,
        attachments_with_urls=mock_attachments
    )

    if success:
        print(f"Тестовый PDF '{output_filename}' успешно создан.")
        # Попытка открыть файл (работает на Windows, macOS, Linux)
        try:
            os.startfile(output_filename)
        except AttributeError:
            os.system(f'open "{output_filename}"')  # для macOS
        except:
            print(
                f"Не удалось автоматически открыть файл. Пожалуйста, откройте его вручную: {os.path.abspath(output_filename)}")

    else:
        print("Не удалось создать тестовый PDF.")