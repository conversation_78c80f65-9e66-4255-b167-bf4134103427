import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/backup_integration_entity.dart';
import '../repositories/integrations_repository.dart';

class GetBackupStatusUseCase {
  final IntegrationsRepository repository;
  const GetBackupStatusUseCase(this.repository);

  Future<Either<Failure, BackupIntegrationEntity>> call(String firmId) {
    return repository.getBackupStatus(firmId);
  }
}
