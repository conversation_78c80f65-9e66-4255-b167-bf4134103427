
Идентификатор - d5dq1774ef3d58c82i0p
Имя - auth-api
Служебный домен - https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Authentication API
  version: 1.1.0 # Версия обновлена, т.к. добавлен новый метод

x-yc-apigateway:
  cors:
    origin: "*" 
    methods:
      - "POST"
      - "OPTIONS"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    allowCredentials: true
    maxAge: 3600

servers:
- url: https://d5dq1774ef312b646.laqt4bj7.apigw.yandexcloud.net

paths:
  /register-request:
    post:
      summary: Запросить код для регистрации нового пользователя
      operationId: registerRequest
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                password: { type: string }
                user_name: { type: string }
              required: [email, password, user_name]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4er76v5l270502p7qu2
        service_account_id: ajeqgf1b9i412b1cqngu

  /register-confirm:
    post:
      summary: Подтвердить регистрацию с помощью кода
      operationId: registerConfirm
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                code: { type: string }
              required: [email, code]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4eu095gn2tga52no22p
        service_account_id: ajeqgf1b9i412b1cqngu

  /accept-invitation: # <<< НОВЫЙ ЭНДПОИНТ
    post:
      summary: Активировать приглашение сотрудника с помощью ключа
      operationId: acceptInvitation
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                invitation_key: { type: string }
              required: [invitation_key]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ek5l50tl7lsh5kartj # <<< ИСПРАВЛЕНО: Указан реальный ID функции accept-invitation
        service_account_id: ajeqgf1b9i412b1cqngu 
      responses:
        '200':
          description: Приглашение успешно принято.
        '400':
          description: Неверные параметры запроса.
        '404':
          description: Ключ приглашения не найден или уже использован.

  /login:
    post:
      summary: Войти в систему и получить/вернуть существующий токен
      operationId: login
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                password: { type: string }
              required: [email, password]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e11h046o2u1eihlgf0
        service_account_id: ajeqgf1b9i412b1cqngu

  /refresh-token:
    post:
      summary: Принудительно пересоздать и получить новый токен
      operationId: refreshToken
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                password: { type: string }
              required: [email, password]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4elom5l31834a68a8r7
        service_account_id: ajeqgf1b9i412b1cqngu
```