part of 'active_firm_cubit.dart';

class ActiveFirmState extends Equatable {
  final List<FirmEntity> firms;
  final FirmEntity? selectedFirm;
  final bool isLoading;

  const ActiveFirmState({
    required this.firms,
    required this.selectedFirm,
    required this.isLoading,
  });

  const ActiveFirmState.initial()
    : this(firms: const [], selectedFirm: null, isLoading: true);

  ActiveFirmState copyWith({
    List<FirmEntity>? firms,
    FirmEntity? selectedFirm,
    bool? isLoading,
  }) {
    return ActiveFirmState(
      firms: firms ?? this.firms,
      selectedFirm: selectedFirm ?? this.selectedFirm,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props => [firms, selectedFirm, isLoading];
}
