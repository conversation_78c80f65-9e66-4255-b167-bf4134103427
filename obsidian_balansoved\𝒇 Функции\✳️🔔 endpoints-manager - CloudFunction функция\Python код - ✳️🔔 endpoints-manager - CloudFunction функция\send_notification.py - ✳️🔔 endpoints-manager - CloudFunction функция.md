
```python
# send_notification.py

import os
import json
import logging
import hashlib
import datetime
import pytz
import uuid
import boto3
import ydb
from botocore.exceptions import ClientError
from custom_errors import LogicError
from utils import rustore_push_utils

def _create_notices_table_if_not_exists(driver, user_id):
    table_path = os.path.join(os.environ["YDB_DATABASE_NOTICES"], f"notices_{user_id}")
    try:
        session = driver.table_client.session().create()
        session.describe_table(table_path)
    except ydb.SchemeError:
        logging.warning(f"Table {table_path} does not exist. Creating with full schema...")
        session.create_table(
            table_path,
            ydb.TableDescription().with_primary_key("notice_id")
            .with_columns(
                ydb.Column("notice_id", ydb.PrimitiveType.Utf8),
                ydb.Column("title", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                # --- НАЧАЛО ИЗМЕНЕНИЙ: Полная схема из документации ---
                ydb.Column("provider", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("tags_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("additional_info_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("action_url", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("created_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
                ydb.Column("is_delivered", ydb.OptionalType(ydb.PrimitiveType.Bool)),
                ydb.Column("delivered_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
                ydb.Column("is_archived", ydb.OptionalType(ydb.PrimitiveType.Bool))
                # --- КОНЕЦ ИЗМЕНЕНИЙ ---
            )
        )
        logging.info(f"Table {table_path} created successfully.")

def handle_send_notification(endpoints_pool, notices_pool, notices_driver, user_id, payload):
    if not all([user_id, payload, payload.get('title'), payload.get('body')]):
        raise LogicError("user_id_to_notify, payload.title and payload.body are required.")

    _create_notices_table_if_not_exists(notices_driver, user_id)
    
    title = payload.get("title")
    body = payload.get("body")

    def create_notice_record(session):
        tx = session.transaction(ydb.SerializableReadWrite())
        table_name = f"notices_{user_id}"
        # --- ИСПРАВЛЕННЫЙ YQL ЗАПРОС ---
        query = session.prepare(f"""
            DECLARE $id AS Utf8;
            DECLARE $title AS Utf8;
            DECLARE $provider AS Utf8;
            DECLARE $additional_info AS Json;
            DECLARE $created AS Timestamp;
            DECLARE $is_delivered AS Bool;
            
            UPSERT INTO `{table_name}` (notice_id, title, provider, additional_info_json, created_at, is_delivered)
            VALUES ($id, $title, $provider, $additional_info, $created, $is_delivered);
        """)
        
        # Собираем JSON с телом уведомления
        additional_info_payload = {
            "body": body,
            # Можно добавить любую другую мета-информацию
            "source": "internal_trigger" 
        }

        tx.execute(query, {
            "$id": str(uuid.uuid4()),
            "$title": title,
            "$provider": "приложение", # Указываем, что это внутреннее уведомление
            "$additional_info": json.dumps(additional_info_payload), # Сохраняем body внутри JSON
            "$created": datetime.datetime.now(pytz.utc),
            "$is_delivered": False
        })
        # --- КОНЕЦ ИСПРАВЛЕНИЙ ---
        tx.commit()
    notices_pool.retry_operation_sync(create_notice_record)

    def get_active_subscriptions(session):
        query = session.prepare("""
            DECLARE $uid AS Utf8;
            SELECT push_token, platform, endpoint_arn FROM UserEndpoints VIEW user_id_index
            WHERE user_id = $uid AND is_enabled = true;
        """)
        res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id}, commit_tx=True)
        return res[0].rows
    
    subscriptions = endpoints_pool.retry_operation_sync(get_active_subscriptions)
    if not subscriptions:
        return {"statusCode": 200, "body": json.dumps({"message": "Notification saved, but no active devices found."})}

    cns_client = boto3.client(
        'sns',
        region_name=os.environ['CNS_REGION'],
        endpoint_url=os.environ.get('CNS_ENDPOINT_URL', 'https://notifications.yandexcloud.net'),
        aws_access_key_id=os.environ.get('STATIC_ACCESS_KEY_ID'),
        aws_secret_access_key=os.environ.get('STATIC_SECRET_ACCESS_KEY')
    )
    rustore_project_id = os.environ['RUSTORE_PROJECT_ID']
    rustore_service_token = os.environ['RUSTORE_SERVICE_TOKEN']
    
    success_count = 0
    disabled_tokens = []

    for sub in subscriptions:
        is_sent = False
        error_reason = ""
        
        if sub.platform == "WEB" and sub.endpoint_arn:
            web_payload = {"notification": {"title": title, "body": body}}
            message_to_publish = {"default": body, "WEB": json.dumps(web_payload)}
            try:
                cns_client.publish(
                    TargetArn=sub.endpoint_arn,
                    Message=json.dumps(message_to_publish),
                    MessageStructure="json"
                )
                is_sent = True
            except ClientError as e:
                if e.response.get("Error", {}).get("Code") == 'EndpointDisabled':
                    error_reason = "EndpointDisabled"
                logging.error(f"CNS Error for {sub.endpoint_arn}: {e}")

        elif sub.platform == "RUSTORE":
            is_sent, error_reason = rustore_push_utils.send_rustore_notification(
                project_id=rustore_project_id, service_token=rustore_service_token,
                device_token=sub.push_token, title=title, body=body
            )

        if is_sent:
            success_count += 1
        elif error_reason in ["EndpointDisabled", "UNREGISTERED"]:
            disabled_tokens.append(sub.push_token)
            logging.warning(f"Marking token for deactivation: ...{sub.push_token[-10:]} Reason: {error_reason}")

    if disabled_tokens:
        logging.info(f"Deactivating {len(disabled_tokens)} disabled subscriptions in the database.")
        disabled_hashes = [hashlib.sha256(t.encode('utf-8')).hexdigest() for t in disabled_tokens]
        
        def disable_subscriptions_batch(session):
            query = session.prepare("""
                DECLARE $hashes AS List<Utf8>;
                UPDATE UserEndpoints SET is_enabled = false, updated_at = CurrentUtcTimestamp()
                WHERE push_token_hash IN $hashes;
            """)
            session.transaction(ydb.SerializableReadWrite()).execute(
                query, {"$hashes": disabled_hashes}, commit_tx=True
            )
        try:
            endpoints_pool.retry_operation_sync(disable_subscriptions_batch)
            logging.info("Successfully deactivated subscriptions in batch.")
        except Exception as db_error:
            logging.error(f"Failed to deactivate subscriptions in batch: {db_error}", exc_info=True)

    message = f"Notification sent to {success_count} of {len(subscriptions)} devices."
    logging.info(message)
    return {"statusCode": 200, "body": json.dumps({"message": message})}
```