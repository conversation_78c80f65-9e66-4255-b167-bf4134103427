import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../cubit/one_time_services_cubit.dart';
import '../../cubit/one_time_services_state.dart';
import 'service_item_widgets.dart';

/// Основной виджет калькулятора разовых услуг
class OneTimeServicesBody extends StatelessWidget {
  const OneTimeServicesBody({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OneTimeServicesCubit, OneTimeServicesState>(
      builder: (context, state) {
        final cubit = context.read<OneTimeServicesCubit>();
        final numberFormat = NumberFormat.currency(
          locale: 'ru_RU',
          symbol: '₽',
        );

        return Stack(
          children: [
            // Основное содержимое
            Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Center(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 800),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton(
                                onPressed: () => cubit.clearAll(),
                                child: const Text('Очистить'),
                              ),
                            ),
                            const SizedBox(height: 8),

                            // Категории услуг
                            ...servicesByCategory.entries.map((entry) {
                              final category = entry.key;
                              final services = entry.value;

                              return ServiceCategoryCard(
                                category: category,
                                services: services,
                                initiallyExpanded:
                                    category ==
                                    OneTimeServiceCategory.reporting,
                              );
                            }),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                // отступ, чтобы контент не перекрывался футером
                const SizedBox(height: 100),
              ],
            ),

            // Плашка с итогом
            Positioned(
              bottom: 16,
              right: 16,
              child: const _OneTimeServicesTotalFooter(),
            ),
          ],
        );
      },
    );
  }

  /// Карточка с детализацией расчета
  Widget _buildBreakdownCard(
    BuildContext context,
    OneTimeServicesCubit cubit,
    NumberFormat numberFormat,
  ) {
    final breakdown = cubit.getBreakdown();

    if (breakdown.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Детализация расчета',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Divider(height: 1),
            const SizedBox(height: 8),
            ...breakdown.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        entry.key,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    Text(
                      numberFormat.format(entry.value),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}

class _OneTimeServicesTotalFooter extends StatefulWidget {
  const _OneTimeServicesTotalFooter();

  @override
  State<_OneTimeServicesTotalFooter> createState() =>
      _OneTimeServicesTotalFooterState();
}

class _OneTimeServicesTotalFooterState
    extends State<_OneTimeServicesTotalFooter>
    with SingleTickerProviderStateMixin {
  bool _expanded = false;
  static const double _width = 420;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OneTimeServicesCubit, OneTimeServicesState>(
      builder: (context, state) {
        final cubit = context.read<OneTimeServicesCubit>();
        final numberFormat = NumberFormat.currency(
          locale: 'ru_RU',
          symbol: '₽',
        );
        final breakdown = cubit.getBreakdown();

        return SizedBox(
          width: _expanded ? 600 : _width,
          child: AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: Card(
              elevation: 8,
              child: InkWell(
                onTap: () => setState(() => _expanded = !_expanded),
                customBorder: const RoundedRectangleBorder(),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  child:
                      _expanded
                          ? Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Итоговая стоимость',
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        numberFormat.format(state.total),
                                        style: Theme.of(
                                          context,
                                        ).textTheme.headlineMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      const Icon(Icons.expand_more),
                                    ],
                                  ),
                                ],
                              ),
                              const Divider(),
                              if (breakdown.isNotEmpty)
                                SizedBox(
                                  height: 200,
                                  child: Scrollbar(
                                    child: ListView(
                                      padding: EdgeInsets.zero,
                                      children:
                                          breakdown.entries.map((entry) {
                                            final valueText = numberFormat
                                                .format(entry.value);
                                            final valueStyle = Theme.of(
                                              context,
                                            ).textTheme.bodyMedium!.copyWith(
                                              color:
                                                  Theme.of(
                                                    context,
                                                  ).colorScheme.primary,
                                              fontWeight: FontWeight.w600,
                                            );
                                            return Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    vertical: 4,
                                                    horizontal: 0,
                                                  ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      entry.key,
                                                      style:
                                                          Theme.of(context)
                                                              .textTheme
                                                              .bodyMedium,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 12),
                                                  Text(
                                                    valueText,
                                                    style: valueStyle,
                                                  ),
                                                ],
                                              ),
                                            );
                                          }).toList(),
                                    ),
                                  ),
                                )
                              else
                                Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Text(
                                    'Выберите услуги для расчета стоимости',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium?.copyWith(
                                      color:
                                          Theme.of(
                                            context,
                                          ).colorScheme.onSurfaceVariant,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                            ],
                          )
                          : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  'Итоговая стоимость разовых услуг:',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                state.total > 0
                                    ? numberFormat.format(state.total)
                                    : '0 ₽',
                                style: Theme.of(
                                  context,
                                ).textTheme.headlineMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      state.total > 0
                                          ? Theme.of(
                                            context,
                                          ).colorScheme.primary
                                          : Theme.of(
                                            context,
                                          ).colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Icon(Icons.expand_less),
                            ],
                          ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
