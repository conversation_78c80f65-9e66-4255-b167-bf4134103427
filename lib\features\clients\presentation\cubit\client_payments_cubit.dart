import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_payment_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/delete_client_payment_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_client_payments_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_clients_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/upsert_client_payment_usecase.dart';

part 'client_payments_state.dart';

class ClientPaymentsCubit extends Cubit<ClientPaymentsState> {
  final GetClientsUseCase getClientsUseCase;
  final GetClientPaymentsUseCase getClientPaymentsUseCase;
  final UpsertClientPaymentUseCase upsertClientPaymentUseCase;
  final DeleteClientPaymentUseCase deleteClientPaymentUseCase;

  List<ClientEntity>? _clientsCache;
  final Map<int, Map<String, List<ClientPaymentEntity>>> _paymentsCacheByYear =
      {};

  ClientPaymentsCubit({
    required this.getClientsUseCase,
    required this.getClientPaymentsUseCase,
    required this.upsertClientPaymentUseCase,
    required this.deleteClientPaymentUseCase,
  }) : super(const ClientPaymentsState.initial());

  Future<void> loadData(
    String firmId,
    int year, {
    bool forceRefresh = false,
  }) async {
    emit(
      state.copyWith(
        isLoading: true,
        selectedYear: year,
        error: null,
        noAccess: false,
      ),
    );

    if (forceRefresh) {
      _clientsCache = null;
      _paymentsCacheByYear.clear();
    }

    try {
      if (_clientsCache == null) {
        final clientsResult = await getClientsUseCase.call(firmId);
        final clients = clientsResult.getOrElse(() => []);

        final failure = clientsResult.fold((l) => l, (r) => null);
        if (failure != null) {
          emit(
            state.copyWith(
              isLoading: false,
              error: _mapFailureToMessage(failure),
              noAccess: failure is AccessDeniedFailure,
            ),
          );
          return;
        }

        if (clients.isEmpty) {
          emit(state.copyWith(isLoading: false, clients: []));
          return;
        }
        _clientsCache = clients;
      }
      final clients = _clientsCache!;

      var paymentsForYear = _paymentsCacheByYear[year];
      if (paymentsForYear == null) {
        final paymentsResult = await getClientPaymentsUseCase(firmId, year);
        paymentsForYear = paymentsResult.fold(
          (failure) {
            emit(
              state.copyWith(
                isLoading: false,
                error: _mapFailureToMessage(failure),
              ),
            );
            return null;
          },
          (payments) {
            final map = <String, List<ClientPaymentEntity>>{};
            for (var p in payments) {
              map.putIfAbsent(p.clientId, () => []).add(p);
            }
            return map;
          },
        );
        if (paymentsForYear == null) return;
        _paymentsCacheByYear[year] = paymentsForYear;
      }

      // Всегда подгружаем предыдущие годы, пока API не вернёт 0 записей либо не пройдём 5 лет назад
      final Map<String, List<ClientPaymentEntity>> mergedPayments = {
        ...paymentsForYear,
      };

      await _mergePastTariffs(firmId, year - 1, mergedPayments, 5);

      paymentsForYear = mergedPayments;

      emit(
        state.copyWith(
          isLoading: false,
          clients: clients,
          paymentsByClient: paymentsForYear,
          selectedYear: year,
        ),
      );

      debugPrint('[CP] loadData finished for year=$year. Payments per client:');
      for (final c in clients) {
        final list = paymentsForYear[c.id] ?? [];
        debugPrint('   • client=${c.name} (${c.id}) payments=${list.length}');
        for (final p in list) {
          debugPrint(
            '       · ${p.period.year}-${p.period.month} tariff=${p.tariffAnnualAmount} actual=${p.actualAmountPaid}',
          );
        }
      }
    } catch (e) {
      emit(
        state.copyWith(isLoading: false, error: 'Непредвиденная ошибка: $e'),
      );
    }
  }

  Future<void> _findTariffsRecursively(
    String firmId,
    int year,
    List<ClientEntity> clientsToFind,
    Map<String, List<ClientPaymentEntity>> allPayments,
    int depth,
  ) async {
    debugPrint(
      '[CP] _findTariffsRecursively year=$year depth=$depth clientsToFind=${clientsToFind.length}',
    );

    if (depth <= 0 || clientsToFind.isEmpty) return;

    Map<String, List<ClientPaymentEntity>>? paymentsFromPast =
        _paymentsCacheByYear[year];
    if (paymentsFromPast == null) {
      final result = await getClientPaymentsUseCase(firmId, year);
      final paymentsList = result.getOrElse(() => []);

      debugPrint('[CP] API response for $year: count=${paymentsList.length}');

      // Если API вернул ноль записей за этот год — прекращаем рекурсию
      if (paymentsList.isEmpty) {
        return;
      }

      final map = <String, List<ClientPaymentEntity>>{};
      for (var p in paymentsList) {
        map.putIfAbsent(p.clientId, () => []).add(p);
      }
      _paymentsCacheByYear[year] = map;
      paymentsFromPast = map;
    }

    debugPrint('[CP] paymentsFromPast size=${paymentsFromPast.length}');

    final List<ClientEntity> stillNeedTariff = [];
    for (final client in clientsToFind) {
      final paymentWithTariff = (paymentsFromPast[client.id] ?? [])
          .firstWhereOrNull((p) => p.tariffAnnualAmount != null);

      if (paymentWithTariff != null) {
        debugPrint('[CP] Found tariff for client ${client.id} in year=$year');
        allPayments.putIfAbsent(client.id, () => []).add(paymentWithTariff);
      } else {
        stillNeedTariff.add(client);
      }
    }

    if (stillNeedTariff.isNotEmpty) {
      await _findTariffsRecursively(
        firmId,
        year - 1,
        stillNeedTariff,
        allPayments,
        depth - 1,
      );
    }
  }

  Future<void> _mergePastTariffs(
    String firmId,
    int year,
    Map<String, List<ClientPaymentEntity>> allPayments,
    int depth,
  ) async {
    if (depth <= 0) return;

    debugPrint('[CP] _mergePastTariffs year=$year depth=$depth');

    Map<String, List<ClientPaymentEntity>>? cached = _paymentsCacheByYear[year];
    if (cached == null) {
      final result = await getClientPaymentsUseCase(firmId, year);
      final list = result.getOrElse(() => []);
      debugPrint('[CP]   fetched ${list.length} payments for $year');
      if (list.isEmpty) {
        return; // остановка по пустому году
      }

      final map = <String, List<ClientPaymentEntity>>{};
      for (var p in list) {
        map.putIfAbsent(p.clientId, () => []).add(p);
      }
      _paymentsCacheByYear[year] = map;
      cached = map;
    } else {
      debugPrint(
        '[CP]   used cache with ${cached.values.fold<int>(0, (a, b) => a + b.length)} payments',
      );
      if (cached.isEmpty) return; // should not happen
    }

    // merge into allPayments
    cached.forEach((clientId, list) {
      final target = allPayments.putIfAbsent(clientId, () => []);
      target.addAll(list);
    });

    await _mergePastTariffs(firmId, year - 1, allPayments, depth - 1);
  }

  void changeYear(String firmId, int newYear) {
    loadData(firmId, newYear);
  }

  Future<void> upsertPayment({
    required String firmId,
    required String clientId,
    required DateTime period,
    double? actualAmount,
    double? tariffAmount,
    DateTime? paymentDate,
  }) async {
    emit(state.copyWith(isLoading: true));
    final params = UpsertClientPaymentParams(
      firmId: firmId,
      clientId: clientId,
      period: period,
      actualAmount: actualAmount,
      tariffAmount: tariffAmount,
      paymentDate: paymentDate,
    );
    final result = await upsertClientPaymentUseCase(params);
    result.fold(
      (failure) => emit(
        state.copyWith(isLoading: false, error: _mapFailureToMessage(failure)),
      ),
      (_) {
        final year = period.year;
        _paymentsCacheByYear.remove(year);
        loadData(firmId, year);
      },
    );
  }

  Future<void> deletePayment({
    required String firmId,
    required String clientId,
    required DateTime period,
  }) async {
    emit(state.copyWith(isLoading: true));
    final params = DeleteClientPaymentParams(
      firmId: firmId,
      clientId: clientId,
      period: period,
    );
    final result = await deleteClientPaymentUseCase(params);
    result.fold(
      (failure) => emit(
        state.copyWith(isLoading: false, error: _mapFailureToMessage(failure)),
      ),
      (_) {
        final year = period.year;
        _paymentsCacheByYear.remove(year);
        loadData(firmId, year);
      },
    );
  }

  String _mapFailureToMessage(Failure failure) {
    String type = '';
    switch (failure.runtimeType) {
      case ServerFailure:
        type = 'Ошибка сервера';
        break;
      case NetworkFailure:
        type = 'Ошибка сети';
        break;
      case ConnectionFailure:
        type = 'Проблема с подключением';
        break;
      case AccessDeniedFailure:
        type = 'Отказано в доступе';
        break;
      default:
        type = 'Неожиданная ошибка';
        break;
    }
    return '$type: ${failure.message}\nДетали: ${failure.details ?? 'отсутствуют'}';
  }
}
