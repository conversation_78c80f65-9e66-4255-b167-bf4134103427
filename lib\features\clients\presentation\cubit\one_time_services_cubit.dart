import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'one_time_services_state.dart';

class OneTimeServicesCubit extends Cubit<OneTimeServicesState> {
  OneTimeServicesCubit() : super(const OneTimeServicesState()) {
    loadFromPrefs();
    _loadCustomPrices();
  }

  static const _prefsKey = 'one_time_services_state_v1';
  static const _pricesKey = 'one_time_services_prices_v1';

  /// Пользовательские расценки для услуг
  final Map<String, int> _customPrices = {};

  /// Загрузка состояния из SharedPreferences
  Future<void> loadFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_prefsKey);
      if (jsonString == null || jsonString.isEmpty) {
        return;
      }

      final map = Map<String, dynamic>.from(
        jsonDecode(jsonString) as Map<dynamic, dynamic>,
      );

      final selectedServices = Map<String, int>.from(
        map['selectedServices'] as Map<String, dynamic>? ?? {},
      );

      emit(
        OneTimeServicesState(
          selectedServices: selectedServices,
          total: map['total'] as int? ?? 0,
        ),
      );

      // Пересчитываем итоговую сумму на случай изменения цен
      _recalculate();
    } catch (e) {
      // В случае ошибки загрузки, используем пустое состояние
      emit(const OneTimeServicesState());
    }
  }

  /// Сохранение состояния в SharedPreferences
  Future<void> _saveToPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final map = {
        'selectedServices': state.selectedServices,
        'total': state.total,
      };
      await prefs.setString(_prefsKey, jsonEncode(map));
    } catch (e) {
      // Игнорируем ошибки сохранения
    }
  }

  /// Обновление количества услуги
  void updateServiceQuantity(String serviceId, int quantity) {
    final updatedServices = Map<String, int>.from(state.selectedServices);

    if (quantity <= 0) {
      updatedServices.remove(serviceId);
    } else {
      updatedServices[serviceId] = quantity;
    }

    emit(state.copyWith(selectedServices: updatedServices));
    _recalculate();
  }

  /// Переключение чекбокса услуги
  void toggleService(String serviceId) {
    final currentQuantity = state.selectedServices[serviceId] ?? 0;
    updateServiceQuantity(serviceId, currentQuantity > 0 ? 0 : 1);
  }

  /// Получение количества услуги
  int getServiceQuantity(String serviceId) {
    return state.selectedServices[serviceId] ?? 0;
  }

  /// Проверка, выбрана ли услуга
  bool isServiceSelected(String serviceId) {
    return getServiceQuantity(serviceId) > 0;
  }

  /// Пересчет итоговой стоимости
  void _recalculate() {
    int total = 0;

    for (final entry in state.selectedServices.entries) {
      final serviceId = entry.key;
      final quantity = entry.value;

      // Находим услугу по ID
      final service = OneTimeServiceType.values.firstWhere(
        (s) => s.id == serviceId,
        orElse: () => OneTimeServiceType.auditServices, // fallback
      );

      // Добавляем к итогу только если услуга активна
      if (service.isActive) {
        final price = getCustomPrice(service);
        total += price * quantity;
      }
    }

    emit(state.copyWith(total: total));
    unawaited(_saveToPrefs());
  }

  /// Получение детализации расчета
  Map<String, int> getBreakdown() {
    final Map<String, int> breakdown = {};

    for (final entry in state.selectedServices.entries) {
      final serviceId = entry.key;
      final quantity = entry.value;

      final service = OneTimeServiceType.values.firstWhere(
        (s) => s.id == serviceId,
        orElse: () => OneTimeServiceType.auditServices,
      );

      if (service.isActive && quantity > 0) {
        final price = getCustomPrice(service);
        final cost = price * quantity;
        final label =
            quantity > 1 ? '${service.title} (×$quantity)' : service.title;
        breakdown[label] = cost;
      }
    }

    return breakdown;
  }

  /// Очистка всех выбранных услуг
  void clearAll() {
    emit(const OneTimeServicesState());
    unawaited(_saveToPrefs());
  }

  /// Получение общего количества выбранных услуг
  int get selectedServicesCount {
    return state.selectedServices.values
        .where((quantity) => quantity > 0)
        .length;
  }

  /// Получение общего количества единиц услуг
  int get totalUnitsCount {
    return state.selectedServices.values
        .where((quantity) => quantity > 0)
        .fold(0, (sum, quantity) => sum + quantity);
  }

  /// Получить пользовательскую цену для услуги
  int getCustomPrice(OneTimeServiceType service) {
    return _customPrices[service.id] ?? service.basePrice;
  }

  /// Обновить пользовательские расценки для категории
  void updateCategoryPrices(
    OneTimeServiceCategory category,
    Map<String, int> prices,
  ) {
    _customPrices.addAll(prices);
    _recalculate(); // Пересчитываем с новыми ценами
    unawaited(_saveCustomPrices());
  }

  /// Загрузить пользовательские расценки из SharedPreferences
  Future<void> _loadCustomPrices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_pricesKey);
      if (jsonString != null && jsonString.isNotEmpty) {
        final Map<String, dynamic> data = jsonDecode(jsonString);
        _customPrices.clear();
        _customPrices.addAll(
          data.map((key, value) => MapEntry(key, value as int)),
        );

        // Пересчитываем после загрузки
        _recalculate();
      }
    } catch (e) {
      // Игнорируем ошибки загрузки
    }
  }

  /// Сохранить пользовательские расценки в SharedPreferences
  Future<void> _saveCustomPrices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_pricesKey, jsonEncode(_customPrices));
    } catch (e) {
      // Игнорируем ошибки сохранения
    }
  }

  /// Сбросить расценки категории к значениям по умолчанию
  void resetCategoryPrices(OneTimeServiceCategory category) {
    final services = servicesByCategory[category] ?? [];
    for (final service in services) {
      _customPrices.remove(service.id);
    }
    _recalculate();
    unawaited(_saveCustomPrices());
  }
}
