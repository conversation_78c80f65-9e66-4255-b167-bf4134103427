import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/utils/file_utils.dart';

class AttachmentPickerDialog extends StatelessWidget {
  final List<FileAttachmentItem> attachments;
  final void Function(FileAttachmentItem) onSelected;

  const AttachmentPickerDialog({
    super.key,
    required this.attachments,
    required this.onSelected,
  });

  static const _imgExt = ['jpg', 'jpeg', 'jpe', 'png', 'webp'];

  @override
  Widget build(BuildContext context) {
    final imageFiles =
        attachments.where((a) {
          final ext = a.name.split('.').last.toLowerCase();
          return a.fileKey != null && _imgExt.contains(ext);
        }).toList();

    return AlertDialog(
      title: const Text('Выберите изображение'),
      content: SizedBox(
        width: 400,
        child:
            imageFiles.isEmpty
                ? const Text('Нет загруженных изображений')
                : Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      imageFiles.map((f) => _buildItem(context, f)).toList(),
                ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Закрыть'),
        ),
      ],
    );
  }

  Widget _buildItem(BuildContext context, FileAttachmentItem file) {
    return GestureDetector(
      onTap: () {
        onSelected(file);
        Navigator.of(context).pop();
      },
      child: SizedBox(
        width: 80,
        height: 80,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FileUtils.getFileIcon(file.name, size: 48),
            const SizedBox(height: 4),
            Text(
              file.name,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }
}
