import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/patent_editor_widget.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/form_widgets.dart';
import 'package:balansoved_enterprise/presentation/widgets/calendar_day_picker.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_edit_controllers.dart';
import 'package:balansoved_enterprise/presentation/widgets/communication_methods_selector.dart';

/// Универсальный виджет для отображения полей взносов
class ContributionFieldWidget extends StatelessWidget {
  final ContributionFieldConfig config;
  final bool isEditing;
  final List<String> selectedTypes;
  final List<int> paymentDates;
  final Function(String) onAdd;
  final Function(String) onRemove;
  final Function(List<int>) onDateChanged;
  final Function(String, String) copyToClipboard;

  const ContributionFieldWidget({
    super.key,
    required this.config,
    required this.isEditing,
    required this.selectedTypes,
    required this.paymentDates,
    required this.onAdd,
    required this.onRemove,
    required this.onDateChanged,
    required this.copyToClipboard,
  });

  @override
  Widget build(BuildContext context) {
    if (isEditing) {
      final shouldShowDatePicker = selectedTypes.isNotEmpty;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MultiSelectWidget(
            label: config.label,
            selected: selectedTypes,
            options: config.availableTypes,
            isEditing: isEditing,
            onAdd: (value) {
              if (selectedTypes.isEmpty) {
                onAdd(value);
              } else if (!selectedTypes.contains(value)) {
                onRemove(selectedTypes.first);
                onAdd(value);
              }
            },
            onRemove: onRemove,
            copyToClipboard: copyToClipboard,
            maxSelection: 1,
            icon: Icons.money_outlined,
          ),
          if (shouldShowDatePicker) ...[
            const SizedBox(height: ClientConstants.sectionSpacing),
            DateInputFormField(
              fieldKey: config.dateKey,
              controller: config.dateController,
              focusNode: config.dateFocusNode,
              labelText: 'Дата платежа',
              prefixIcon: Icons.date_range_outlined,
              isEditing: isEditing,
              onIconTap: () {
                showDialog(
                  context: context,
                  builder:
                      (context) => CalendarDayPicker(
                        selectedDays: paymentDates,
                        paymentType:
                            selectedTypes.isNotEmpty
                                ? selectedTypes.first
                                : 'Ежемесячно',
                        onDaysSelected: onDateChanged,
                      ),
                );
              },
              validator: (value) {
                if (value == null || value.isEmpty) return null;
                final type = selectedTypes.first;
                return ClientConstants.validatePaymentDate(value, type);
              },
              disableAutoFormatting: true,
            ),
          ],
        ],
      );
    }

    // Режим просмотра
    if (selectedTypes.isEmpty) return const SizedBox.shrink();

    return MultiSelectWidget(
      label: config.label,
      selected: selectedTypes,
      options: config.availableTypes,
      isEditing: false,
      onAdd: (_) {},
      onRemove: (_) {},
      copyToClipboard: copyToClipboard,
      icon: Icons.money_outlined,
    );
  }
}

/// Секция контактов
class ContactsSection extends StatelessWidget {
  final bool isEditing;
  final List<ContactEntity> contacts;
  final Function(int) onRemoveContact;
  final Function(int, ContactEntity) onUpdateContact;
  final VoidCallback onAddContact;
  final Function(String, String) copyToClipboard;

  const ContactsSection({
    super.key,
    required this.isEditing,
    required this.contacts,
    required this.onRemoveContact,
    required this.onUpdateContact,
    required this.onAddContact,
    required this.copyToClipboard,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.contacts_outlined,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            const Text(
              'Контакты',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.smallSpacing),
        if (contacts.isEmpty) const Text('Контакты отсутствуют.'),
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children:
              contacts.asMap().entries.map((entry) {
                final index = entry.key;
                final contact = entry.value;
                return SizedBox(
                  width: 360,
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.person_outline,
                                    size: 18,
                                    color:
                                        Theme.of(
                                          context,
                                        ).textTheme.bodySmall?.color,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    contact.fullName?.isNotEmpty == true
                                        ? contact.fullName!
                                        : 'Контакт ${index + 1}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              if (isEditing)
                                IconButton(
                                  icon: const Icon(Icons.delete_outline),
                                  onPressed: () => onRemoveContact(index),
                                ),
                            ],
                          ),
                          const SizedBox(height: ClientConstants.smallSpacing),
                          TextFormField(
                            initialValue: contact.fullName,
                            decoration: const InputDecoration(
                              labelText: 'ФИО',
                              prefixIcon: Icon(Icons.badge_outlined, size: 20),
                            ),
                            readOnly: !isEditing,
                            onTap:
                                isEditing
                                    ? null
                                    : () => copyToClipboard(
                                      contact.fullName ?? '',
                                      'ФИО',
                                    ),
                            onChanged:
                                isEditing
                                    ? (value) => onUpdateContact(
                                      index,
                                      contact.copyWith(fullName: value),
                                    )
                                    : null,
                          ),
                          const SizedBox(height: ClientConstants.smallSpacing),
                          TextFormField(
                            initialValue: contact.phone,
                            decoration: const InputDecoration(
                              labelText: 'Телефон',
                              prefixIcon: Icon(Icons.phone_outlined, size: 20),
                            ),
                            readOnly: !isEditing,
                            onTap:
                                isEditing
                                    ? null
                                    : () => copyToClipboard(
                                      contact.phone ?? '',
                                      'Телефон',
                                    ),
                            onChanged:
                                isEditing
                                    ? (value) => onUpdateContact(
                                      index,
                                      contact.copyWith(phone: value),
                                    )
                                    : null,
                          ),
                          const SizedBox(height: ClientConstants.smallSpacing),
                          // Email addresses section
                          _buildEmailsSection(
                            context,
                            contact,
                            index,
                            isEditing,
                            onUpdateContact,
                            copyToClipboard,
                          ),
                          const SizedBox(height: ClientConstants.smallSpacing),
                          CommunicationMethodsSelector(
                            selectedMethods: contact.communicationMethods,
                            isEditing: isEditing,
                            onChanged:
                                (methods) => onUpdateContact(
                                  index,
                                  contact.copyWith(
                                    communicationMethods: methods,
                                  ),
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
        const SizedBox(height: ClientConstants.smallSpacing),
        if (isEditing)
          TextButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Добавить контакт'),
            onPressed: onAddContact,
          ),
      ],
    );
  }

  Widget _buildEmailsSection(
    BuildContext context,
    ContactEntity contact,
    int index,
    bool isEditing,
    Function(int, ContactEntity) onUpdateContact,
    Function(String, String) copyToClipboard,
  ) {
    final emails = contact.emails;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.email_outlined, size: 20),
            const SizedBox(width: 8),
            const Text(
              'Email адреса',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (emails.isNotEmpty)
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children:
                emails.map((email) {
                  return ChoiceChip(
                    label: Text(email),
                    selected: false,
                    onSelected:
                        isEditing
                            ? (selected) {
                              // Remove email when chip is tapped
                              final updatedEmails = List<String>.from(emails);
                              updatedEmails.remove(email);
                              onUpdateContact(
                                index,
                                contact.copyWith(emails: updatedEmails),
                              );
                            }
                            : (selected) {
                              // Copy to clipboard when not editing
                              copyToClipboard(email, 'Email');
                            },
                    backgroundColor: Colors.blue.shade50,
                    selectedColor: Colors.blue.shade100,
                    labelStyle: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 12,
                    ),

                    showCheckmark: false,
                  );
                }).toList(),
          ),
        if (emails.isEmpty && !isEditing)
          const Text(
            'Email адреса отсутствуют',
            style: TextStyle(color: Colors.grey, fontSize: 12),
          ),
        if (isEditing) ...[
          const SizedBox(height: 8),
          Builder(
            builder: (context) {
              final controller = TextEditingController();
              return TextFormField(
                controller: controller,
                decoration: const InputDecoration(
                  labelText: 'Добавить email',
                  hintText: 'Введите email и нажмите Enter',
                  prefixIcon: Icon(Icons.add_outlined, size: 20),
                  isDense: true,
                ),
                keyboardType: TextInputType.emailAddress,
                onFieldSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    final updatedEmails = List<String>.from(emails);
                    if (!updatedEmails.contains(value.trim())) {
                      updatedEmails.add(value.trim());
                      onUpdateContact(
                        index,
                        contact.copyWith(emails: updatedEmails),
                      );
                    }
                    controller.clear();
                  }
                },
              );
            },
          ),
        ],
      ],
    );
  }
}

/// Секция патентов
class PatentsSection extends StatelessWidget {
  final bool isEditing;
  final List<PatentEntity> patents;
  final Function(int, PatentEntity) onUpdatePatent;
  final Function(int) onRemovePatent;
  final VoidCallback onAddPatent;

  const PatentsSection({
    super.key,
    required this.isEditing,
    required this.patents,
    required this.onUpdatePatent,
    required this.onRemovePatent,
    required this.onAddPatent,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.copyright_outlined, size: 20, color: Colors.purple),
            SizedBox(width: 8),
            Text(
              'Патенты',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.smallSpacing),
        if (patents.isEmpty) const Text('Патенты отсутствуют.'),
        for (int i = 0; i < patents.length; i++)
          PatentEditorWidget(
            patent: patents[i],
            isEditing: isEditing,
            onChanged: (updated) => onUpdatePatent(i, updated),
            onDelete: () => onRemovePatent(i),
          ),
        const SizedBox(height: ClientConstants.smallSpacing),
        if (isEditing)
          TextButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Добавить патент'),
            onPressed: onAddPatent,
          ),
      ],
    );
  }
}

/// Дополнительная секция
class AdditionalSection extends StatelessWidget {
  final bool isEditing;
  final List<String> additionalTags;
  final List<String> activityTypes;

  // Унифицированные поля взносов
  final List<String> fixedContributionsIP;
  final List<int> fixedContributionsPaymentDate;
  final List<String> contributionsIP1Percent;
  final List<int> contributionsIP1PercentPaymentDate;
  final TextEditingController fixedContributionsDateCtrl;
  final GlobalKey fixedContributionsDateKey;
  final FocusNode fixedContributionsDateFN;
  final TextEditingController contributionsIP1PercentDateCtrl;
  final GlobalKey contributionsIP1PercentDateKey;
  final FocusNode contributionsIP1PercentDateFN;

  final Function(String) onAddTag;
  final Function(String) onRemoveTag;
  final Function(String) onActivityTypeAdd;
  final Function(String) onActivityTypeRemove;
  final Function(String) onFixedContributionAdd;
  final Function(String) onFixedContributionRemove;
  final Function(List<int>) onFixedContributionDateChanged;
  final Function(String) onContributionsIP1PercentAdd;
  final Function(String) onContributionsIP1PercentRemove;
  final Function(List<int>) onContributionsIP1PercentDateChanged;
  final Function(String, String) copyToClipboard;

  const AdditionalSection({
    super.key,
    required this.isEditing,
    required this.additionalTags,
    required this.activityTypes,
    required this.fixedContributionsIP,
    required this.fixedContributionsPaymentDate,
    required this.contributionsIP1Percent,
    required this.contributionsIP1PercentPaymentDate,
    required this.fixedContributionsDateCtrl,
    required this.fixedContributionsDateKey,
    required this.fixedContributionsDateFN,
    required this.contributionsIP1PercentDateCtrl,
    required this.contributionsIP1PercentDateKey,
    required this.contributionsIP1PercentDateFN,
    required this.onAddTag,
    required this.onRemoveTag,
    required this.onActivityTypeAdd,
    required this.onActivityTypeRemove,
    required this.onFixedContributionAdd,
    required this.onFixedContributionRemove,
    required this.onFixedContributionDateChanged,
    required this.onContributionsIP1PercentAdd,
    required this.onContributionsIP1PercentRemove,
    required this.onContributionsIP1PercentDateChanged,
    required this.copyToClipboard,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isEditing) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.more_horiz_outlined,
                size: 20,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Text(
                'Дополнительно',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: ClientConstants.smallSpacing),

          MultiSelectWidget(
            label: 'Виды деятельности',
            selected: activityTypes,
            options: ClientConstants.activityTypes,
            isEditing: isEditing,
            onAdd: onActivityTypeAdd,
            onRemove: onActivityTypeRemove,
            copyToClipboard: copyToClipboard,
            icon: Icons.business_outlined,
          ),
          const SizedBox(height: ClientConstants.fieldSpacing),

          // Группа «Важно»
          MultiSelectWidget(
            label: 'Важно',
            selected:
                additionalTags
                    .where((tag) => ClientConstants.importantTags.contains(tag))
                    .toList(),
            options: ClientConstants.importantTags,
            isEditing: isEditing,
            onAdd: onAddTag,
            onRemove: onRemoveTag,
            copyToClipboard: copyToClipboard,
            icon: Icons.warning_amber_rounded,
          ),
          const SizedBox(height: ClientConstants.fieldSpacing),

          MultiSelectWidget(
            label: 'Дополнительные теги',
            selected:
                additionalTags
                    .where(
                      (tag) => ClientConstants.predefinedAdditionalTags
                          .contains(tag),
                    )
                    .toList(),
            options: ClientConstants.predefinedAdditionalTags,
            isEditing: isEditing,
            onAdd: onAddTag,
            onRemove: onRemoveTag,
            copyToClipboard: copyToClipboard,
            icon: Icons.tag_outlined,
          ),

          TagsFieldWidget(
            label: 'Пользовательские теги',
            tags:
                additionalTags
                    .where(
                      (tag) =>
                          !ClientConstants.importantTags.contains(tag) &&
                          !ClientConstants.predefinedAdditionalTags.contains(
                            tag,
                          ),
                    )
                    .toList(),
            isEditing: isEditing,
            onAddTag: onAddTag,
            onRemoveTag: onRemoveTag,
            copyToClipboard: copyToClipboard,
            icon: Icons.local_offer_outlined,
          ),

          // Поля взносов
          const SizedBox(height: ClientConstants.fieldSpacing),
          _buildContributionField(
            context,
            'Фиксированные взносы ИП',
            fixedContributionsIP,
            fixedContributionsPaymentDate,
            ClientConstants.fixedContributionsIP,
            fixedContributionsDateCtrl,
            fixedContributionsDateKey,
            fixedContributionsDateFN,
            onFixedContributionAdd,
            onFixedContributionRemove,
            onFixedContributionDateChanged,
          ),
          const SizedBox(height: ClientConstants.fieldSpacing),
          _buildContributionField(
            context,
            'Взносы ИП 1%',
            contributionsIP1Percent,
            contributionsIP1PercentPaymentDate,
            ClientConstants.contributionsIP1Percent,
            contributionsIP1PercentDateCtrl,
            contributionsIP1PercentDateKey,
            contributionsIP1PercentDateFN,
            onContributionsIP1PercentAdd,
            onContributionsIP1PercentRemove,
            onContributionsIP1PercentDateChanged,
          ),
        ],
      );
    }

    final List<Widget> visibleWidgets = [];
    final important = ClientConstants.importantTags;
    final predefined = ClientConstants.predefinedAdditionalTags;

    final selectedImportant =
        additionalTags.where((t) => important.contains(t)).toList();
    final selectedPredefined =
        additionalTags.where((t) => predefined.contains(t)).toList();
    final customTags =
        additionalTags
            .where((t) => !predefined.contains(t) && !important.contains(t))
            .toList();

    if (selectedImportant.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Важно',
          selected: selectedImportant,
          options: important,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.warning_amber_rounded,
        ),
      );
    }
    if (selectedPredefined.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Дополнительные теги',
          selected: selectedPredefined,
          options: predefined,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.label_outline,
        ),
      );
    }
    if (customTags.isNotEmpty) {
      visibleWidgets.add(
        TagsFieldWidget(
          label: 'Пользовательские теги',
          tags: customTags,
          isEditing: isEditing,
          onAddTag: (_) {},
          onRemoveTag: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.local_offer_outlined,
        ),
      );
    }
    if (activityTypes.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Вид деятельности',
          selected: activityTypes,
          options: ClientConstants.activityTypes,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.work_outline,
        ),
      );
    }

    if (visibleWidgets.isEmpty) {
      return const SizedBox.shrink();
    }

    // Добавим поля взносов в режим просмотра
    final List<Widget> contributionWidgets = [];

    if (fixedContributionsIP.isNotEmpty) {
      contributionWidgets.add(
        _buildContributionField(
          context,
          'Фиксированные взносы ИП',
          fixedContributionsIP,
          fixedContributionsPaymentDate,
          ClientConstants.fixedContributionsIP,
          fixedContributionsDateCtrl,
          fixedContributionsDateKey,
          fixedContributionsDateFN,
          (_) {},
          (_) {},
          (_) {},
        ),
      );
    }

    if (contributionsIP1Percent.isNotEmpty) {
      if (contributionWidgets.isNotEmpty) {
        contributionWidgets.add(
          const SizedBox(height: ClientConstants.fieldSpacing),
        );
      }
      contributionWidgets.add(
        _buildContributionField(
          context,
          'Взносы ИП 1%',
          contributionsIP1Percent,
          contributionsIP1PercentPaymentDate,
          ClientConstants.contributionsIP1Percent,
          contributionsIP1PercentDateCtrl,
          contributionsIP1PercentDateKey,
          contributionsIP1PercentDateFN,
          (_) {},
          (_) {},
          (_) {},
        ),
      );
    }

    // Объединяем все виджеты
    final allWidgets = [...visibleWidgets];
    if (contributionWidgets.isNotEmpty) {
      if (allWidgets.isNotEmpty) {
        allWidgets.add(const SizedBox(height: ClientConstants.fieldSpacing));
      }
      allWidgets.addAll(contributionWidgets);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.more_horiz_outlined,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            const Text(
              'Дополнительно',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.smallSpacing),
        for (int i = 0; i < allWidgets.length; i++) ...[
          allWidgets[i],
          if (i < allWidgets.length - 1)
            const SizedBox(height: ClientConstants.fieldSpacing),
        ],
      ],
    );
  }

  Widget _buildContributionField(
    BuildContext context,
    String label,
    List<String> selectedTypes,
    List<int> paymentDates,
    List<String> availableTypes,
    TextEditingController dateController,
    GlobalKey dateKey,
    FocusNode dateFocusNode,
    Function(String) onAdd,
    Function(String) onRemove,
    Function(List<int>) onDateChanged,
  ) {
    if (isEditing) {
      final shouldShowDatePicker = selectedTypes.isNotEmpty;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MultiSelectWidget(
            label: label,
            selected: selectedTypes,
            options: availableTypes,
            isEditing: isEditing,
            onAdd: (value) {
              if (selectedTypes.isEmpty) {
                onAdd(value);
              } else if (!selectedTypes.contains(value)) {
                onRemove(selectedTypes.first);
                onAdd(value);
              }
            },
            onRemove: onRemove,
            copyToClipboard: copyToClipboard,
            maxSelection: 1,
            icon: Icons.money_outlined,
          ),
          if (shouldShowDatePicker) ...[
            const SizedBox(height: ClientConstants.sectionSpacing),
            DateInputFormField(
              fieldKey: dateKey,
              controller: dateController,
              focusNode: dateFocusNode,
              labelText: 'Дата платежа',
              prefixIcon: Icons.date_range_outlined,
              isEditing: isEditing,
              onIconTap: () {
                showDialog(
                  context: context,
                  builder:
                      (context) => CalendarDayPicker(
                        selectedDays: paymentDates,
                        paymentType:
                            selectedTypes.isNotEmpty
                                ? selectedTypes.first
                                : 'Ежемесячно',
                        onDaysSelected: onDateChanged,
                      ),
                );
              },
              validator: (value) {
                if (value == null || value.isEmpty) return null;
                final type = selectedTypes.first;
                return ClientConstants.validatePaymentDate(value, type);
              },
              disableAutoFormatting: true,
            ),
          ],
        ],
      );
    }

    // Режим просмотра
    if (selectedTypes.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MultiSelectWidget(
          label: label,
          selected: selectedTypes,
          options: availableTypes,
          isEditing: false,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.money_outlined,
        ),
        if (paymentDates.isNotEmpty) ...[
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 28),
            child: Row(
              children: [
                Icon(
                  Icons.date_range_outlined,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  'Даты: ${ClientConstants.formatPaymentDates(paymentDates)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap:
                      () => copyToClipboard(
                        ClientConstants.formatPaymentDates(paymentDates),
                        'даты платежей',
                      ),
                  child: Icon(
                    Icons.copy,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}

/// Секция дополнительной информации (теги и т.д.)
class AdditionalInfoSection extends StatelessWidget {
  final bool isEditing;
  final ClientEntity client;
  final Function(String) onAddTag;
  final Function(String) onRemoveTag;
  final Function(String, String) copyToClipboard;
  final TextEditingController newTagController;

  const AdditionalInfoSection({
    super.key,
    required this.isEditing,
    required this.client,
    required this.onAddTag,
    required this.onRemoveTag,
    required this.copyToClipboard,
    required this.newTagController,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.more_horiz, size: 20, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            const Text(
              'Дополнительно',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),
        ...ClientConstants.tagGroups.entries.map((entry) {
          final groupName = entry.key;
          final groupTags = entry.value;

          final selectedInGroup =
              client.additionalTags
                  .where((tag) => groupTags.contains(tag))
                  .toList();

          if (selectedInGroup.isEmpty && !isEditing) {
            return const SizedBox.shrink();
          }

          return Padding(
            padding: const EdgeInsets.only(
              bottom: ClientConstants.sectionSpacing,
            ),
            child: MultiSelectWidget(
              label: groupName,
              selected: selectedInGroup,
              options: groupTags,
              isEditing: isEditing,
              onAdd: onAddTag,
              onRemove: onRemoveTag,
              copyToClipboard: copyToClipboard,
              icon: _getGroupIcon(groupName),
            ),
          );
        }),
        _buildUserTagsSection(context),
      ],
    );
  }

  IconData _getGroupIcon(String groupName) {
    switch (groupName) {
      case 'Важно':
        return Icons.warning_amber_rounded;
      case 'Виды деятельности':
        return Icons.business_center_outlined;
      case 'Дополнительные теги':
        return Icons.label_outline;
      default:
        return Icons.sell_outlined;
    }
  }

  Widget _buildUserTagsSection(BuildContext context) {
    final theme = Theme.of(context);
    final userTags =
        client.additionalTags
            .where(
              (tag) =>
                  !ClientConstants.tagGroups.values.any(
                    (list) => list.contains(tag),
                  ),
            )
            .toList();

    if (userTags.isEmpty && !isEditing) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.sell_outlined,
              size: 18,
              color: theme.textTheme.bodySmall?.color,
            ),
            const SizedBox(width: 8),
            Text(
              'Пользовательские теги',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: theme.textTheme.bodyMedium?.color,
              ),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.smallSpacing),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: [
            ...userTags.map(
              (tag) => Chip(
                label: Text(tag),
                onDeleted: isEditing ? () => onRemoveTag(tag) : null,
              ),
            ),
            if (isEditing)
              SizedBox(
                width: 200,
                child: TextField(
                  controller: newTagController,
                  decoration: InputDecoration(
                    hintText: 'Добавить тег...',
                    isDense: true,
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.add),
                      onPressed: () {
                        if (newTagController.text.isNotEmpty) {
                          onAddTag(newTagController.text);
                          newTagController.clear();
                        }
                      },
                    ),
                  ),
                  onSubmitted: (value) {
                    if (value.isNotEmpty) {
                      onAddTag(value);
                      newTagController.clear();
                    }
                  },
                ),
              ),
          ],
        ),
      ],
    );
  }
}
