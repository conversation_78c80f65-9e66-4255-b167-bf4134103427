**1. `get_sns_client() -> boto3.client`**
- **Назначение**: Создает и возвращает клиент `boto3` для работы с Yandex Cloud Notifications. Использует переменные окружения для получения ключей доступа и конфигурации.
- **На выходе**: Объект клиента `boto3`.

**2. `send_web_push_notification(client: boto3.client, platform_app_arn: str, push_subscription_json: str, title: str, body: str) -> tuple[bool, str]`**
- **Назначение**: Отправляет Web Push уведомление, используя предоставленный `boto3` клиент. Инкапсулирует логику создания временного эндпоинта, отправки сообщения и последующего удаления этого эндпоинта.
- **На входе**:
	-> `client`: Инициализированный `boto3` клиент.
	-> `platform_app_arn`: ARN приложения в Yandex Cloud Notifications.
	-> `push_subscription_json`: JSON-строка PushSubscription, полученная из браузера.
	-> `title`: Заголовок уведомления.
	-> `body`: Текст уведомления.
- **На выходе**:
	-> Кортеж `(True, Message ID)` в случае успеха.
	-> Кортеж `(False, Текст ошибки)` в случае неудачи.

---

```python
# utils/web_push_utils.py

import boto3
import json
import os
import logging
from botocore.exceptions import ClientError

def get_sns_client() -> boto3.client:
    """
    Создает и возвращает клиент boto3 для работы с Yandex Cloud Notifications.
    Использует переменные окружения для конфигурации.
    """
    try:
        yc_key_id = os.environ['YC_KEY_ID']
        yc_secret_key = os.environ['YC_SECRET_KEY']
        region = os.environ.get('YC_REGION', 'ru-central1')
        endpoint_url = os.environ.get('YC_ENDPOINT_URL', 'https://notifications.yandexcloud.net')

        return boto3.client(
            "sns",
            region_name=region,
            endpoint_url=endpoint_url,
            aws_access_key_id=yc_key_id,
            aws_secret_access_key=yc_secret_key
        )
    except KeyError as e:
        logging.error(f"Ошибка конфигурации клиента SNS: не найдена переменная окружения {e}")
        raise
    except Exception as e:
        logging.error(f"Не удалось инициализировать Boto3 клиент: {e}", exc_info=True)
        raise

def send_web_push_notification(client: boto3.client, platform_app_arn: str, push_subscription_json: str, title: str, body: str) -> tuple[bool, str]:
    """
    Отправляет Web Push уведомление, используя предоставленный клиент boto3.
    """
    endpoint_arn = None
    try:
        # 1. Создание эндпоинта
        response_create = client.create_platform_endpoint(
            PlatformApplicationArn=platform_app_arn,
            Token=push_subscription_json,
        )
        endpoint_arn = response_create["EndpointArn"]
        logging.info(f"Временный эндпоинт успешно создан: {endpoint_arn}")

        # 2. Формирование и отправка уведомления
        web_payload = {"notification": {"title": title, "body": body}}
        message_to_publish = {"default": body, "WEB": json.dumps(web_payload)}

        response_publish = client.publish(
            TargetArn=endpoint_arn,
            Message=json.dumps(message_to_publish),
            MessageStructure="json",
        )
        message_id = response_publish['MessageId']
        logging.info(f"Уведомление успешно отправлено. Message ID: {message_id}")
        
        return True, message_id

    except ClientError as e:
        error_msg = f"Ошибка API при работе с эндпоинтом: {e.response['Error']['Message']}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg
    except Exception as e:
        error_msg = f"Непредвиденная ошибка: {e}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg
    finally:
        # 3. Удаление временного эндпоинта в любом случае
        if endpoint_arn:
            try:
                client.delete_endpoint(EndpointArn=endpoint_arn)
                logging.info(f"Временный эндпоинт {endpoint_arn} удален.")
            except ClientError as e:
                logging.warning(f"Не критично: не удалось удалить эндпоинт {endpoint_arn}: {e}")

```