import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:file_saver/file_saver.dart';
import 'package:http/http.dart' as http;

import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class FileUtils {
  /// Форматирует размер файла в человекочитаемый вид
  static String formatFileSize(int bytes) {
    if (bytes == 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    int unitIndex = 0;
    double size = bytes.toDouble();

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(unitIndex == 0 ? 0 : 1)} ${units[unitIndex]}';
  }

  /// Получает иконку для файла на основе расширения
  static Widget getFileIcon(String fileName, {double size = 32}) {
    final extension = fileName.split('.').last.toLowerCase();

    return Builder(
      builder: (context) {
        final theme = Theme.of(context);

        IconData iconData;
        Color iconColor;

        switch (extension) {
          case 'pdf':
            iconData = Icons.picture_as_pdf;
            iconColor = theme.colorScheme.error;
            break;
          case 'doc':
          case 'docx':
            iconData = Icons.description;
            iconColor = theme.colorScheme.primary;
            break;
          case 'xls':
          case 'xlsx':
            iconData = Icons.table_chart;
            iconColor = theme.colorScheme.tertiary;
            break;
          case 'ppt':
          case 'pptx':
            iconData = Icons.slideshow;
            iconColor = theme.colorScheme.secondary;
            break;
          case 'jpg':
          case 'jpeg':
          case 'png':
          case 'gif':
            iconData = Icons.image;
            iconColor = theme.colorScheme.secondary;
            break;
          case 'zip':
          case 'rar':
          case '7z':
            iconData = Icons.archive;
            iconColor = theme.colorScheme.outline;
            break;
          case 'txt':
            iconData = Icons.text_snippet;
            iconColor = theme.colorScheme.onSurfaceVariant;
            break;
          case 'xml':
            iconData = Icons.code;
            iconColor = theme.colorScheme.primary;
            break;
          default:
            iconData = Icons.insert_drive_file;
            iconColor = theme.colorScheme.onSurfaceVariant;
        }

        return Icon(iconData, size: size, color: iconColor);
      },
    );
  }

  /// Скачивает файл по fileKey
  static Future<void> downloadFile(
    BuildContext context,
    String fileKey,
    String fileName, {
    VoidCallback? onStart,
    VoidCallback? onSuccess,
    VoidCallback? onError,
  }) async {
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Не выбрана фирма')));
      }
      onError?.call();
      return;
    }

    onStart?.call();

    final storageCubit = context.read<TariffsAndStorageCubit>();
    late StreamSubscription sub;

    try {
      sub = storageCubit.stream.listen((state) async {
        if (state is FileDownloadUrlReady && state.fileKey == fileKey) {
          try {
            final response = await http.get(Uri.parse(state.downloadUrl));
            if (response.statusCode == 200) {
              await FileSaver.instance.saveFile(
                name: fileName,
                bytes: response.bodyBytes,
              );
              onSuccess?.call();
            } else {
              throw Exception('Ошибка загрузки файла: ${response.statusCode}');
            }
          } catch (e) {
            debugPrint('Ошибка скачивания файла: $e');
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Ошибка скачивания файла: $e'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
            onError?.call();
          }
          sub.cancel();
        } else if (state is TariffsAndStorageError) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Ошибка скачивания: ${state.message}'),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
          onError?.call();
          sub.cancel();
        }
      });

      await storageCubit.getDownloadUrl(
        firmId: firmState.selectedFirm!.id,
        fileKey: fileKey,
      );

      // Таймаут безопасности
      Future.delayed(const Duration(seconds: 30), () {
        sub.cancel();
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Превышено время ожидания скачивания'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
        onError?.call();
      });
    } catch (e) {
      sub.cancel();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка запроса: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      onError?.call();
    }
  }

  /// Проверяет, поддерживается ли предпросмотр для данного типа файла
  static bool isPreviewSupported(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    const supportedImages = ['png', 'jpeg', 'jpg', 'jpe', 'webp'];
    return extension == 'pdf' ||
        extension == 'doc' ||
        extension == 'docx' ||
        extension == 'xls' ||
        extension == 'xlsx' ||
        extension == 'xml' ||
        supportedImages.contains(extension);
  }
}
