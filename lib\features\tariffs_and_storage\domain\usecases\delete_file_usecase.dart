import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../repositories/tariffs_and_storage_repository.dart';

/// Use case для удаления файлов
class DeleteFileUseCase {
  final ITariffsAndStorageRepository repository;

  const DeleteFileUseCase({required this.repository});

  /// Удалить файл по ключу
  Future<Either<Failure, void>> call({
    required String firmId,
    required String fileKey,
  }) async {
    return await repository.deleteFile(firmId: firmId, fileKey: fileKey);
  }
}
