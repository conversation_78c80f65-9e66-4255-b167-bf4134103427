
Идентификатор - d5dln4usaaktunc0kvia
Имя - scheduler-api
Служебный домен - https://d5dln4usaaktunc0kvia.8wihnuyr.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Scheduler API
  version: 1.0.0
  description: API для управления запланированными событиями и их ручного запуска.
servers:
  - url: https://d5dln4usaaktunc0kvia.8wihnuyr.apigw.yandexcloud.net

x-yc-apigateway:
  cors:
    origin: "*"
    methods:
      - "POST"
      - "OPTIONS"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    allowCredentials: true
    maxAge: 3600

# Аутентификация для этого API не требуется, так как он предназначен
# для внутреннего использования другими сервисами.
# Безопасность обеспечивается на уровне сети или через IAM.

paths:
  /manage:
    post:
      summary: Универсальный метод для управления запланированными событиями
      description: |
        Позволяет выполнять следующие операции:
        - GET: Получить одно или все события.
        - UPSERT: Создать новое или обновить существующее событие.
        - DELETE: Удалить событие.
      operationId: manageScheduledEvents
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4eh9o1sau3l2mv3g09n # ID функции scheduler-manager
        service_account_id: ajeqgf1b9i412b1cqngu # SA с доступом к YDB
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action:
                  type: string
                  enum: [GET, UPSERT, DELETE]
                event_id:
                  type: string
                  description: "ID события. Обязателен для DELETE и для обновления через UPSERT."
                payload:
                  type: object
                  description: "Данные события. Обязателен для UPSERT."
              required:
                - action
      responses:
        '200':
          description: Успешное выполнение (GET, UPDATE, DELETE).
        '201':
          description: Событие успешно создано (UPSERT).
        '400':
          description: Неверные параметры в запросе.
        '404':
          description: Событие не найдено.
        '500':
          description: Внутренняя ошибка сервера.

  /trigger:
    post:
      summary: Запустить проверку и выполнение запланированных событий
      description: |
        Вызывает функцию-триггер для проверки всех активных событий. 
        Имитирует срабатывание таймера. Предназначен для отладки и принудительного запуска.
      operationId: triggerScheduler
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ed209l95con54mj89a # ID функции scheduler-trigger
        service_account_id: ajeqgf1b9i412b1cqngu # SA с доступом к YDB и вызову функций
      responses:
        '200':
          description: Проверка успешно запущена.
        '500':
          description: Внутренняя ошибка сервера при запуске проверки.
```