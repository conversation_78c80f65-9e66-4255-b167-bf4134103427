part of 'create_firm_cubit.dart';

class CreateFirmState extends Equatable {
  final bool isLoading;
  final bool success;
  final String? errorMessage;

  const CreateFirmState({
    required this.isLoading,
    required this.success,
    this.errorMessage,
  });

  const CreateFirmState.initial() : this(isLoading: false, success: false);

  CreateFirmState copyWith({
    bool? isLoading,
    bool? success,
    String? errorMessage,
  }) {
    return CreateFirmState(
      isLoading: isLoading ?? this.isLoading,
      success: success ?? this.success,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [isLoading, success, errorMessage];
}
