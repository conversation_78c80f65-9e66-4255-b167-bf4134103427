
```python
# index.py

import json, os, logging, sys
import ydb
from utils import auth_utils, ydb_utils, request_parser
import get_logic
import update_logic
import storage_logic
from custom_errors import AuthError, LogicError, NotFoundError, QuotaExceededError

# Устанавливаем уровень логирования
logging.getLogger().setLevel(logging.INFO)

def check_permissions(session, user_id, firm_id):
    logging.info(f"Checking permissions for user_id: {user_id} in firm_id: {firm_id}")
    query_text = "DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;"
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$user_id": user_id, "$firm_id": firm_id}, commit_tx=True)
    if not result[0].rows:
        logging.warning(f"Permission check failed: User {user_id} is not a member of firm {firm_id}.")
        return (False, False)
    roles = json.loads(result[0].rows[0].roles)
    is_admin_or_owner = "OWNER" in roles or "ADMIN" in roles
    logging.info(f"Permission check successful: User is member, is_admin_or_owner={is_admin_or_owner}.")
    return (True, is_admin_or_owner)

def handler(event, context):
    logging.info("--- NEW INVOCATION ---")
    
    # Очищаем кеш драйверов в начале каждого вызова для предотвращения ошибок с "протухшими" соединениями
    if 'ydb_utils' in sys.modules and hasattr(sys.modules['ydb_utils'], 'clear_drivers_cache'):
        ydb_utils.clear_drivers_cache()
    
    logging.info(f"RAW EVENT: {event}")
    try:
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing or invalid Bearer token format.")
        token = auth_header.split(' ', 1)[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired token.")
        
        requesting_user_id = user_payload['user_id']
        logging.info(f"Request authorized for user_id: {requesting_user_id}")
        
        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        logging.info(f"Parsed action: '{action}' for firm_id: '{firm_id}'")
        
        if not all([firm_id, action]):
            raise LogicError("firm_id and action are required parameters.")

        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        is_member, is_admin_or_owner = firms_pool.retry_operation_sync(
            lambda s: check_permissions(s, requesting_user_id, firm_id)
        )

        if not is_member:
            raise AuthError("User is not a member of the specified firm.")

        tariffs_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_TARIFFS"], os.environ["YDB_DATABASE_TARIFFS"])
        tariffs_pool = ydb.SessionPool(tariffs_driver)
        logging.info(f"Successfully connected to DB pools. Routing action '{action}'...")

        if action == 'GET_RECORD':
            if not is_admin_or_owner: raise AuthError("Admin or Owner rights required for GET_RECORD.")
            return tariffs_pool.retry_operation_sync(lambda s: get_logic.get_or_create_record(s, firm_id))

        elif action == 'UPDATE_JSON':
            if not is_admin_or_owner: raise AuthError("Admin or Owner rights required for UPDATE_JSON.")
            target_field = data.get('target_json_field')
            updates = data.get('updates')
            return tariffs_pool.retry_operation_sync(lambda s: update_logic.update_json_fields(s, firm_id, target_field, updates))

        elif action == 'CLEAR_JSON':
            if not is_admin_or_owner: raise AuthError("Admin or Owner rights required for CLEAR_JSON.")
            fields = data.get('fields_to_clear')
            return tariffs_pool.retry_operation_sync(lambda s: update_logic.clear_json_fields(s, firm_id, fields))
        
        elif action == 'GET_UPLOAD_URL':
            filename = data.get('filename')
            filesize = data.get('filesize')
            return storage_logic.handle_get_upload_url(tariffs_pool, firm_id, filename, filesize)

        elif action == 'GET_DOWNLOAD_URL':
            file_key = data.get('file_key')
            return storage_logic.handle_get_download_url(firm_id, file_key)

        elif action == 'CONFIRM_UPLOAD':
            file_key = data.get('file_key')
            return storage_logic.handle_confirm_upload(tariffs_pool, firm_id, file_key)

        elif action == 'DELETE_FILE':
            file_key = data.get('file_key')
            return storage_logic.handle_delete_file(tariffs_pool, firm_id, file_key)

        else:
            raise LogicError(f"Invalid action specified: '{action}'")

    except (AuthError, PermissionError) as e:
        logging.warning(f"Authorization error: {e}", exc_info=True)
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        logging.warning(f"Business logic error: {e}", exc_info=True)
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        logging.warning(f"Not found error: {e}")
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except QuotaExceededError as e:
        logging.warning(f"Quota exceeded error: {e}", exc_info=True)
        return {"statusCode": 413, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical unhandled error in handler: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```