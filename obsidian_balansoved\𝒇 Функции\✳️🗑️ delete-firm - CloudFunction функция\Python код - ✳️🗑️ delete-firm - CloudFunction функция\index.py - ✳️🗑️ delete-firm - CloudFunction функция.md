
```python
import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

# Импорт логических модулей
import precondition_checks
import deletion_logic
from custom_errors import AuthError, LogicError, PreconditionFailedError

# Настройка логирования
logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    try:
        # 1. Авторизация и парсинг
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing Bearer token.")
        
        user_jwt = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(user_jwt)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired token.")
        
        user_id = user_payload['user_id']
        
        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        if not firm_id:
            raise LogicError("`firm_id` is required in the request body.")

        logging.info(f"--- STARTING DELETION PROCESS FOR FIRM {firm_id} BY OWNER {user_id} ---")

        # 2. Подключение к основной БД для транзакций
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)

        # 3. Фаза 1: Проверка всех предусловий
        logging.info("--- PHASE 1: PRECONDITION CHECKS ---")
        precondition_checks.run_all_checks(firms_pool, user_jwt, user_id, firm_id)
        logging.info("All precondition checks passed successfully.")

        # 4. Фаза 2: Поэтапное удаление
        logging.info("--- PHASE 2: STAGED DELETION ---")
        deletion_logic.run_all_deletions(firms_pool, user_jwt, user_id, firm_id)
        
        logging.info(f"--- FIRM {firm_id} DELETED SUCCESSFULLY ---")
        return {"statusCode": 200, "body": json.dumps({"message": "Firm and all associated data successfully deleted."})}

    except AuthError as e:
        logging.error(f"Authorization error: {e}", exc_info=True)
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        logging.error(f"Logic error: {e}", exc_info=True)
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except PreconditionFailedError as e:
        logging.error(f"Precondition failed: {e}", exc_info=True)
        return {"statusCode": 412, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical unhandled error: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```