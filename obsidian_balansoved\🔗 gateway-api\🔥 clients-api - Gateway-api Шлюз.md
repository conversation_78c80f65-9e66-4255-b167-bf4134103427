Идентификатор - d5domf1ev0daigtm42of
Имя - clients-api
Служебный домен - https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Clients API
  version: 1.1.0 # Версия обновлена, т.к. добавлен новый функционал
servers:
  - url: https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net

x-yc-apigateway:
  cors:
    origin: "*"
    methods:
      - "POST"
      - "OPTIONS"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    allowCredentials: true
    maxAge: 3600

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - bearerAuth: []

paths:
  /manage:
    post:
      summary: Универсальный метод для управления профилями клиентов
      operationId: manageClients
      tags: [Clients]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e0nco8ka4c1me3qdrt
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firm_id:
                  type: string
                action:
                  type: string
                  enum: [GET, UPSERT, DELETE]
                client_id:
                  type: string
                creation_date: # <<< НОВОЕ ПОЛЕ
                  type: string
                  format: date
                  description: "Дата версии в формате 'YYYY-MM-DD'."
                payload:
                  type: object
              required:
                - firm_id
                - action
      responses:
        '200':
          description: Успешное выполнение.
        '201':
          description: Клиент успешно создан.
        '400':
          description: Неверные параметры.
        '403':
          description: Ошибка авторизации.
        '404':
          description: Клиент не найден.
        '500':
          description: Внутренняя ошибка.

  # --- НОВЫЙ РАЗДЕЛ ДЛЯ УПРАВЛЕНИЯ ОПЛАТАМИ ---
  /payments/manage:
    post:
      summary: Универсальный метод для управления оплатами клиентов
      operationId: manageClientPayments
      tags: [Client Payments]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4evkcnph6jfho1qlcfa # <<< ID новой функции client-payments-manager
        service_account_id: ajek4l2ql5b2e77uo3vb # <<< Тот же сервисный аккаунт
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firm_id:
                  type: string
                  description: "ID фирмы, в контексте которой выполняется операция."
                action:
                  type: string
                  enum: [GET, UPSERT, DELETE]
                  description: "Тип выполняемой операции."
                
                # Параметры для GET
                year:
                  type: integer
                  description: "Год для фильтрации. Обязателен для action: GET."
                
                # Параметры для GET (опционально), DELETE (обязательно)
                client_id:
                  type: string
                  description: "ID клиента. Опционален для GET, обязателен для DELETE."
                
                # Параметры для UPSERT, DELETE
                period:
                  type: string
                  description: "Период в формате 'YYYY-MM'. Обязателен для DELETE и в payload для UPSERT."
                
                # Параметры для UPSERT
                payload:
                  type: object
                  description: "Данные для создания/обновления записи. Обязателен для action: UPSERT."
              required:
                - firm_id
                - action
      responses:
        '200':
          description: Успешное выполнение (GET, UPDATE, DELETE).
        '201':
          description: Запись об оплате успешно создана (UPSERT).
        '400':
          description: Неверные параметры в запросе.
        '403':
          description: Ошибка авторизации или недостаточно прав.
        '404':
          description: Ресурс не найден.
        '500':
          description: Внутренняя ошибка сервера.
```