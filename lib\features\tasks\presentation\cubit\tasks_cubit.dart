import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/system_task_uid_entity.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/tasks/domain/repositories/tasks_repository.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/get_tasks_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/get_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/save_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/delete_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/features/calendar/presentation/cubit/calendar_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';

/// Результат создания системной задачи
class SystemTaskCreationResult {
  final bool success;
  final bool taskAlreadyExists;
  final String message;

  SystemTaskCreationResult({
    required this.success,
    required this.taskAlreadyExists,
    required this.message,
  });
}

/// Результат удаления системной задачи
class SystemTaskDeletionResult {
  final bool success;
  final String message;

  SystemTaskDeletionResult({
    required this.success,
    required this.message,
  });
}

class TasksCubit extends Cubit<TasksState> {
  final GetTasksUseCase getTasksUseCase;
  final GetTaskUseCase getTaskUseCase;
  final SaveTaskUseCase saveTaskUseCase;
  final DeleteTaskUseCase deleteTaskUseCase;
  final CalendarCubit calendarCubit;
  final ClientsCubit clientsCubit;

  TasksCubit({
    required this.getTasksUseCase,
    required this.getTaskUseCase,
    required this.saveTaskUseCase,
    required this.deleteTaskUseCase,
    required this.calendarCubit,
    required this.clientsCubit,
  }) : super(TasksInitial());

  TaskRequestParams? _currentParams;
  int? _urgentSearchYear;
  int? _urgentSearchMonth;

  List<TaskEntity> get tasks {
    final currentState = state;
    if (currentState is TasksLoaded) {
      return currentState.tasks;
    } else if (currentState is TasksLoadingMore) {
      return currentState.tasks;
    }
    return [];
  }

  TaskRequestParams? get currentParams => _currentParams;

  TaskPaginationMeta? get paginationMeta {
    final currentState = state;
    if (currentState is TasksLoaded) {
      return currentState.paginationMeta;
    } else if (currentState is TasksLoadingMore) {
      return currentState.paginationMeta;
    }
    return null;
  }

  bool get canLoadMore {
    final currentState = state;
    if (currentState is TasksLoaded) {
      return currentState.canLoadMore;
    }
    return false;
  }

  bool get isLoadingMore => state is TasksLoadingMore;

  bool get isLoading => state is TasksLoading;
  String? get error =>
      state is TasksError ? (state as TasksError).message : null;

  /// Загрузить задачи по заданным параметрам
  Future<void> fetchTasks(String firmId, TaskRequestParams params) async {
    final currentState = state;
    if (currentState is TasksLoaded &&
        currentState.currentParams == params &&
        !params.force) {
      return;
    }
    if (currentState is TasksLoading &&
        currentState.currentParams == params &&
        !params.force) {
      return;
    }

    _currentParams = params;
    emit(TasksLoading(currentParams: params));

    // Сбрасываем пагинацию для срочных задач при новом запросе
    if (params.viewType == TaskViewType.dated) {
      _urgentSearchYear = params.year;
      _urgentSearchMonth = params.month;
    }

    final result = await getTasksUseCase(firmId, params);

    result.fold(
      (failure) {
        if (failure is AccessDeniedFailure) {
          emit(TasksNoAccess(message: failure.message));
        } else {
          emit(TasksError(message: failure.message));
        }
      },
      (tasksResult) {
        // Определяем, можно ли загружать ещё
        // Для бессрочных задач - если загружено 100 задач
        // Для срочных задач без фильтра по месяцу - если мы в текущем году и есть предыдущие месяцы
        bool canLoadMore = false;
        if (params.viewType == TaskViewType.timeless) {
          canLoadMore = tasksResult.tasks.length == 100;
        } else if (params.viewType == TaskViewType.dated &&
            !params.filterByMonth) {
          final currentYear = DateTime.now().year;
          final taskYear = params.year ?? currentYear;
          final taskMonth = params.month ?? DateTime.now().month;
          canLoadMore = taskYear == currentYear && taskMonth > 1;
        }

        emit(
          TasksLoaded(
            result: tasksResult,
            currentParams: params,
            canLoadMore: canLoadMore,
          ),
        );
      },
    );
  }

  /// Загрузить бессрочные задачи (по умолчанию первая страница)
  Future<void> fetchTimelessTasks(
    String firmId, {
    int page = 0,
    bool force = false,
  }) async {
    final params = TaskRequestParams.timeless(
      page: page,
      filterByMonth: false,
      force: force,
    );
    await fetchTasks(firmId, params);
  }

  /// Загрузить задачи с крайним сроком за указанный месяц
  Future<void> fetchDatedTasks(
    String firmId, {
    required int month,
    required int year,
    bool filterByMonth = false,
    bool force = false,
  }) async {
    final params = TaskRequestParams.dated(
      month: month,
      year: year,
      filterByMonth: filterByMonth,
      force: force,
    );
    await fetchTasks(firmId, params);
  }

  /// Загрузить задачи за текущий месяц
  Future<void> fetchCurrentMonthTasks(
    String firmId, {
    bool filterByMonth = false,
    bool force = false,
  }) async {
    final params = TaskRequestParams.currentMonth(
      filterByMonth: filterByMonth,
      force: force,
    );
    await fetchTasks(firmId, params);
  }

  /// Перейти к следующей странице (только для бессрочных задач)
  Future<void> nextPage(String firmId) async {
    if (_currentParams?.viewType != TaskViewType.timeless) return;

    final meta = paginationMeta;
    if (meta == null || !meta.hasNextPage) return;

    final newParams = _currentParams!.copyWith(page: meta.currentPage + 1);
    await fetchTasks(firmId, newParams);
  }

  /// Перейти к предыдущей странице (только для бессрочных задач)
  Future<void> previousPage(String firmId) async {
    if (_currentParams?.viewType != TaskViewType.timeless) return;

    final meta = paginationMeta;
    if (meta == null || !meta.hasPreviousPage) return;

    final newParams = _currentParams!.copyWith(page: meta.currentPage - 1);
    await fetchTasks(firmId, newParams);
  }

  /// Переключиться на другой месяц (только для срочных задач)
  Future<void> switchToMonth(String firmId, int month, int year) async {
    final params = TaskRequestParams.dated(
      month: month,
      year: year,
      filterByMonth: true, // Включаем фильтр по месяцу при переключении
      force: true,
    );
    await fetchTasks(firmId, params);
  }

  /// Загрузить одну задачу по ID

  /// Загрузить задачи из предыдущих месяцев (для срочных задач без фильтра по месяцу)
  Future<void> loadMoreUrgentTasks(String firmId) async {
    final currentState = state;

    if (currentState is! TasksLoaded) return;

    if (currentState.currentParams.viewType != TaskViewType.dated ||
        currentState.currentParams.filterByMonth) {
      return;
    }

    emit(
      TasksLoadingMore(
        currentResult: currentState.result,
        currentParams: currentState.currentParams,
      ),
    );

    var searchYear = _urgentSearchYear ?? DateTime.now().year;
    var searchMonth = _urgentSearchMonth ?? DateTime.now().month;

    List<TaskEntity>? foundTasks;
    bool errorOccurred = false;
    final currentYear = DateTime.now().year;

    for (int i = 0; i < 24 && foundTasks == null && !errorOccurred; i++) {
      searchMonth--;
      if (searchMonth == 0) {
        searchMonth = 12;
        searchYear--;
      }

      // Прекращаем поиск, если вышли за пределы текущего года
      if (searchYear < currentYear) {
        break;
      }

      // Прекращаем поиск, если вышли за пределы разумного диапазона
      if (searchYear < 2020) {
        break;
      }

      final searchParams = currentState.currentParams.copyWith(
        month: searchMonth,
        year: searchYear,
      );

      final result = await getTasksUseCase(firmId, searchParams);

      result.fold(
        (failure) {
          errorOccurred = true;
        },
        (tasksResult) {
          if (tasksResult.tasks.isNotEmpty) {
            foundTasks = tasksResult.tasks;
          }
        },
      );
    }

    if (errorOccurred) {
      emit(
        TasksLoaded(
          result: currentState.result,
          currentParams: currentState.currentParams, // НЕ МЕНЯЕМ ПАРАМЕТРЫ
          canLoadMore: false,
        ),
      );
      return;
    }

    if (foundTasks != null) {
      // Обновляем месяц и год для следующего поиска
      _urgentSearchYear = searchYear;
      _urgentSearchMonth = searchMonth;

      final allTasks = [...currentState.result.tasks, ...foundTasks!];

      final combinedResult = TasksResult(
        tasks: allTasks,
        paginationMeta: currentState.result.paginationMeta,
      );

      // Проверяем, можно ли грузить еще (есть ли еще месяцы в текущем году)
      final canLoadMore = searchYear == currentYear && searchMonth > 1;

      emit(
        TasksLoaded(
          result: combinedResult,
          currentParams: currentState.currentParams, // НЕ МЕНЯЕМ ПАРАМЕТРЫ
          canLoadMore: canLoadMore,
        ),
      );
    } else {
      // Задачи не найдены, отключаем кнопку
      emit(
        TasksLoaded(
          result: currentState.result,
          currentParams: currentState.currentParams, // НЕ МЕНЯЕМ ПАРАМЕТРЫ
          canLoadMore: false,
        ),
      );
    }
  }

  /// Загрузить ещё задач (добавить к существующим)
  Future<void> loadMoreTasks(String firmId) async {
    final currentState = state;

    // Проверяем, что мы в состоянии с загруженными задачами
    if (currentState is! TasksLoaded) return;

    // Проверяем, что это бессрочные задачи и можно загружать ещё
    if (!currentState.canLoadMore ||
        currentState.currentParams.viewType != TaskViewType.timeless) {
      return;
    }

    // Переходим в состояние загрузки дополнительных задач
    emit(
      TasksLoadingMore(
        currentResult: currentState.result,
        currentParams: currentState.currentParams,
      ),
    );

    // Загружаем следующую страницу
    final currentPage = currentState.currentParams.page ?? 0;
    final nextPage = currentPage + 1;
    final nextParams = currentState.currentParams.copyWith(page: nextPage);

    final result = await getTasksUseCase(firmId, nextParams);

    result.fold(
      (failure) {
        // При ошибке возвращаемся к предыдущему состоянию без canLoadMore
        emit(
          TasksLoaded(
            result: currentState.result,
            currentParams: currentState.currentParams,
            canLoadMore: false, // Отключаем кнопку при ошибке
          ),
        );
      },
      (newTasksResult) {
        // Объединяем старые и новые задачи
        final allTasks = [
          ...currentState.result.tasks,
          ...newTasksResult.tasks,
        ];

        // Создаём объединённый результат
        final combinedResult = TasksResult(
          tasks: allTasks,
          paginationMeta: newTasksResult.paginationMeta,
        );

        // Определяем, можно ли загружать ещё
        final canLoadMore = newTasksResult.tasks.length == 100;

        emit(
          TasksLoaded(
            result: combinedResult,
            currentParams: nextParams,
            canLoadMore: canLoadMore,
          ),
        );
      },
    );
  }

  /// Сохранить задачу. Возвращает `true`, если операция прошла успешно.
  Future<bool> saveTask(String firmId, TaskEntity task) async {
    final prevState = state;

    final result = await saveTaskUseCase(firmId, task);

    bool isSuccess = false;

    result.fold(
      (failure) {
        emit(
          TasksError(
            message:
                '${failure.message}${failure.details != null ? '\n${failure.details}' : ''}',
          ),
        );
        // Восстанавливаем предыдущее состояние, если было
        if (prevState is TasksLoaded) {
          emit(prevState);
        }
      },
      (taskId) {
        isSuccess = true;
        // Обновляем задачу локально вместо полной перезагрузки
        final currentState = state;
        if (currentState is TasksLoaded) {
          final updatedTask = task.copyWith(id: taskId);

          // Проверяем, существует ли задача (редактирование) или это новая задача
          final existingIndex = currentState.result.tasks.indexWhere(
            (t) => t.id == task.id,
          );

          if (existingIndex != -1) {
            // Обновляем существующую задачу
            final updatedTasks = List<TaskEntity>.from(
              currentState.result.tasks,
            );
            updatedTasks[existingIndex] = updatedTask;

            final updatedResult = TasksResult(
              tasks: updatedTasks,
              paginationMeta: currentState.result.paginationMeta,
            );

            emit(
              TasksLoaded(
                result: updatedResult,
                currentParams: currentState.currentParams,
                canLoadMore: currentState.canLoadMore,
              ),
            );
          } else {
            // Для новой задачи перезагружаем список, чтобы учесть фильтры
            if (_currentParams != null) {
              fetchTasks(firmId, _currentParams!);
            }
          }
        } else {
          // Если состояние не TasksLoaded, делаем полную перезагрузку
          if (_currentParams != null) {
            fetchTasks(firmId, _currentParams!);
          }
        }
      },
    );

    return isSuccess;
  }

  /// Удалить задачу и обновить локальное состояние
  Future<void> deleteTask(String firmId, String taskId) async {
    final result = await deleteTaskUseCase(firmId, taskId);
    result.fold((failure) => emit(TasksError(message: failure.message)), (_) {
      // Удаляем задачу из локального состояния вместо полной перезагрузки
      final currentState = state;
      if (currentState is TasksLoaded) {
        final updatedTasks =
            currentState.result.tasks
                .where((task) => task.id != taskId)
                .toList();

        final updatedResult = TasksResult(
          tasks: updatedTasks,
          paginationMeta: currentState.result.paginationMeta,
        );

        emit(
          TasksLoaded(
            result: updatedResult,
            currentParams: currentState.currentParams,
            canLoadMore: currentState.canLoadMore,
          ),
        );
      } else {
        // Если состояние не TasksLoaded, делаем полную перезагрузку
        if (_currentParams != null) {
          fetchTasks(firmId, _currentParams!);
        }
      }
    });
  }

  /// Удалить системную задачу и обновить клиента
  Future<SystemTaskDeletionResult> deleteSystemTask(
    String firmId,
    String taskId,
    ClientEntity client,
  ) async {
    // Удаляем задачу
    final result = await deleteTaskUseCase(firmId, taskId);
    
    return result.fold(
      (failure) {
        emit(TasksError(message: failure.message));
        return SystemTaskDeletionResult(
          success: false,
          message: failure.message,
        );
      },
      (_) async {
        // Удаляем задачу из systemTaskUids клиента
        final updatedSystemTaskUids = client.systemTaskUids
            .where((systemTask) => systemTask.uid != taskId)
            .toList();
        
        final updatedClient = client.copyWith(
          systemTaskUids: updatedSystemTaskUids,
        );
        
        // Сохраняем обновленного клиента
        final savedClient = await clientsCubit.saveClient(
          firmId,
          updatedClient,
        );
        
        if (savedClient != null) {
          // Обновляем локальное состояние задач
          final currentState = state;
          if (currentState is TasksLoaded) {
            final updatedTasks = currentState.result.tasks
                .where((task) => task.id != taskId)
                .toList();

            final updatedResult = TasksResult(
              tasks: updatedTasks,
              paginationMeta: currentState.result.paginationMeta,
            );

            emit(
              TasksLoaded(
                result: updatedResult,
                currentParams: currentState.currentParams,
                canLoadMore: currentState.canLoadMore,
              ),
            );
          } else {
            if (_currentParams != null) {
              fetchTasks(firmId, _currentParams!);
            }
          }
          
          return SystemTaskDeletionResult(
            success: true,
            message: 'Системная задача успешно удалена',
          );
        } else {
          return SystemTaskDeletionResult(
            success: false,
            message: 'Не удалось обновить клиента после удаления задачи',
          );
        }
      },
    );
  }

  /// Обновить статус конкретной задачи с предварительным получением актуальной информации
  Future<void> updateTaskStatus(
    String firmId,
    String taskId,
    String newStatus,
  ) async {
    // Сначала получаем актуальную информацию о задаче
    final getTaskResult = await getTaskUseCase(firmId, taskId);

    await getTaskResult.fold(
      (failure) async {
        emit(
          TasksError(
            message:
                'Ошибка получения актуальной информации о задаче: ${failure.message}',
          ),
        );
      },
      (actualTask) async {
        // Обновляем только статус у актуальной задачи
        final updatedTask = actualTask.copyWith(status: newStatus);

        // Сохраняем обновленную задачу
        final saveResult = await saveTaskUseCase(firmId, updatedTask);

        saveResult.fold(
          (failure) {
            emit(
              TasksError(
                message: 'Ошибка обновления статуса задачи: ${failure.message}',
              ),
            );
          },
          (taskId) {
            // Обновляем задачу в локальном состоянии без полной перезагрузки
            final currentState = state;
            if (currentState is TasksLoaded) {
              final updatedTasks =
                  currentState.result.tasks.map((task) {
                    return task.id == updatedTask.id ? updatedTask : task;
                  }).toList();

              final updatedResult = TasksResult(
                tasks: updatedTasks,
                paginationMeta: currentState.result.paginationMeta,
              );

              emit(
                TasksLoaded(
                  result: updatedResult,
                  currentParams: currentState.currentParams,
                  canLoadMore: currentState.canLoadMore,
                ),
              );
            } else if (currentState is TasksLoadingMore) {
              final updatedTasks =
                  currentState.tasks.map((task) {
                    return task.id == updatedTask.id ? updatedTask : task;
                  }).toList();

              final updatedResult = TasksResult(
                tasks: updatedTasks,
                paginationMeta: currentState.paginationMeta!,
              );

              emit(
                TasksLoaded(
                  result: updatedResult,
                  currentParams: currentState.currentParams,
                  canLoadMore: true,
                ),
              );
            }

            // Уведомляем календарь об изменении задачи
            calendarCubit.updateTaskInCurrentState(updatedTask);
          },
        );
      },
    );
  }

  void clearError() {
    if (state is TasksError) {
      emit(TasksInitial());
    }
  }

  /// Сбрасывает состояние кубита при смене фирмы
  void resetState() {
    _currentParams = null;
    emit(TasksInitial());
  }

  /// Поиск существующей системной задачи
  Future<TaskEntity?> _findExistingSystemTask(String firmId, String title, DateTime dueDate, String clientId) async {
    // Сначала проверяем в локальном состоянии
    final currentState = state;
    if (currentState is TasksLoaded) {
      try {
        final localTask = currentState.result.tasks.firstWhere(
          (task) => 
            task.title == title &&
            task.dueDate?.day == dueDate.day &&
            task.dueDate?.month == dueDate.month &&
            task.dueDate?.year == dueDate.year &&
            task.clientIds.contains(clientId) &&
            (task.options['isSystem'] == true),
        );
        return localTask;
      } catch (e) {
        // Продолжаем поиск через API
      }
    }
    
    // Поиск через API - проверяем бессрочные задачи
    final timelessParams = TaskRequestParams.timeless(page: 0, filterByMonth: false, force: true);
    final timelessResult = await getTasksUseCase(firmId, timelessParams);
    
    final foundTask = timelessResult.fold(
      (failure) => null,
      (tasksResult) {
        try {
          return tasksResult.tasks.firstWhere(
            (task) => 
              task.title == title &&
              task.dueDate?.day == dueDate.day &&
              task.dueDate?.month == dueDate.month &&
              task.dueDate?.year == dueDate.year &&
              task.clientIds.contains(clientId) &&
              (task.options['isSystem'] == true),
          );
        } catch (e) {
          return null;
        }
      },
    );
    
    if (foundTask != null) {
      return foundTask;
    }
    
    // Поиск в срочных задачах на следующий год
    final nextYear = dueDate.year;
    final datedParams = TaskRequestParams.dated(
      month: dueDate.month,
      year: nextYear,
      filterByMonth: false,
      force: true,
    );
    
    final datedResult = await getTasksUseCase(firmId, datedParams);
    
    return datedResult.fold(
      (failure) => null,
      (tasksResult) {
        try {
          return tasksResult.tasks.firstWhere(
            (task) => 
              task.title == title &&
              task.dueDate?.day == dueDate.day &&
              task.dueDate?.month == dueDate.month &&
              task.dueDate?.year == dueDate.year &&
              task.clientIds.contains(clientId) &&
              (task.options['isSystem'] == true),
          );
        } catch (e) {
          return null;
        }
      },
    );
  }

  Future<SystemTaskCreationResult> createSystemTaskForClient(
    String firmId,
    ClientEntity client,
  ) async {
    final now = DateTime.now();
    final dueDate = now.add(const Duration(days: 365));
    
    final expectedTitle = "Обновление электронной подписи для клиента: ${client.name}";
    final expectedDescription = "Необходимо обновить электронную подпись для клиента ${client.name} до ${dueDate.toLocal().toString().split(' ')[0]}";
    
    // Проверяем существование задачи с таким же названием и датой
    final existingTask = await _findExistingSystemTask(firmId, expectedTitle, dueDate, client.id);
    if (existingTask != null) {
      return SystemTaskCreationResult(
        success: true,
        taskAlreadyExists: true,
        message: 'Системная задача для клиента ${client.name} уже существует',
      );
    }

    final task = TaskEntity(
      id: '',
      title: expectedTitle,
      description: expectedDescription,
      clientIds: [client.id],
      assigneeIds: const [],
      observerIds: const [],
      creatorIds: const [],
      status: 'new',
      priority: 'medium',
      dueDate: dueDate,
      attachments: const [],
      checklist: const [],
      reminders: const [],
      recurrence: null,
      options: const {'isSystem': true},
      holidayTransferRule: null,
      originTaskId: null,
      createdAt: now,
      updatedAt: now,
    );

    final result = await saveTaskUseCase(firmId, task);

    return result.fold(
      (failure) {
        emit(TasksError(message: failure.message));
        return SystemTaskCreationResult(
          success: false,
          taskAlreadyExists: false,
          message: failure.message,
        );
      },
      (taskId) async {
        final newSystemTask = SystemTaskUidEntity(
          uid: taskId,
          name: task.title,
          description: task.description ?? '',
          dueDate: task.dueDate,
        );
        final updatedClient = client.copyWith(
          systemTaskUids: [...client.systemTaskUids, newSystemTask],
        );
        final savedClient = await clientsCubit.saveClient(
          firmId,
          updatedClient,
        );
        if (savedClient != null) {
          if (_currentParams != null) {
            fetchTasks(firmId, _currentParams!);
          }
          return SystemTaskCreationResult(
            success: true,
            taskAlreadyExists: false,
            message: 'Системная задача успешно создана для клиента ${client.name}',
          );
        } else {
          emit(
            TasksError(
              message: 'Не удалось обновить клиента с новой системной задачей.',
            ),
          );
          // Попытка удалить созданную задачу, чтобы избежать несоответствий
          await deleteTaskUseCase(firmId, taskId);
          return SystemTaskCreationResult(
            success: false,
            taskAlreadyExists: false,
            message: 'Не удалось обновить клиента с новой системной задачей.',
          );
        }
      },
    );
  }
}
