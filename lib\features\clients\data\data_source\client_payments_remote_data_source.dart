import 'package:balansoved_enterprise/features/clients/domain/entities/client_payment_entity.dart';

/// Абстракция удалённого источника данных для платежей клиентов.
/// Реализует универсальный endpoint /payments/manage на clients-api.
abstract class IClientPaymentsRemoteDataSource {
  /// Получить список платежей клиентов фирмы за указанный год.
  /// Если [clientId] указан, то возвращаются записи только по этому клиенту.
  Future<List<ClientPaymentEntity>> fetchPayments(
    String token,
    String firmId,
    int year, {
    String? clientId,
  });

  /// Создать/обновить запись об оплате.
  Future<void> upsertPayment(
    String token,
    String firmId,
    String clientId,
    DateTime period,
    double? actualAmount,
    double? tariffAmount,
    DateTime? paymentDate,
  );

  /// Удалить запись об оплате.
  Future<void> deletePayment(
    String token,
    String firmId,
    String clientId,
    DateTime period,
  );
}
