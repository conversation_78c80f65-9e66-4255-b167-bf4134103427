import '../models/tariffs_and_storage_model.dart';
import '../../domain/entities/file_upload_entity.dart';

/// Абстракция удалённого источника данных для тарифов и хранилища
abstract class ITariffsAndStorageRemoteDataSource {
  /// Получить данные о тарифах и хранилище фирмы
  Future<TariffsAndStorageModel> getTariffsAndStorage(
    String token,
    String firmId,
  );

  /// Обновить JSON поля
  Future<void> updateJsonFields({
    required String token,
    required String firmId,
    required String targetJsonField,
    required Map<String, dynamic> updates,
  });

  /// Очистить JSON поля
  Future<void> clearJsonFields({
    required String token,
    required String firmId,
    required List<String> fieldsToClear,
  });

  /// Получить URL для загрузки файла
  Future<FileUploadEntity> getUploadUrl({
    required String token,
    required String firmId,
    required String fileName,
    required int fileSize,
  });

  /// Получить URL для скачивания файла
  Future<FileDownloadEntity> getDownloadUrl({
    required String token,
    required String firmId,
    required String fileKey,
  });

  /// Загрузить файл через HTTP PUT
  Stream<FileUploadProgressEntity> uploadFileViaHttp({
    required String uploadUrl,
    required List<int> fileBytes,
    required String fileName,
  });

  /// Подтвердить загрузку файла
  Future<void> confirmUpload({
    required String token,
    required String firmId,
    required String fileKey,
  });

  /// Удалить файл
  Future<void> deleteFile({
    required String token,
    required String firmId,
    required String fileKey,
  });
}
