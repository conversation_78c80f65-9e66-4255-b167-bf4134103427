import 'package:flutter/material.dart';

class ManualMatch {
  final int start;
  final int end;
  final String content;

  ManualMatch(this.start, this.end, this.content);

  String? group(int index) {
    if (index == 0) return '[[$content]]';
    if (index == 1) return content;
    return null;
  }
}

enum BlockType { text, image }

class Block {
  BlockType type;
  // For text blocks
  final TextEditingController? controller;
  final FocusNode? focusNode;
  // For images
  String? fileKey;
  String? fileName;
  int width;
  int height;

  Block.text(String text)
    : type = BlockType.text,
      controller = TextEditingController(text: text),
      focusNode = FocusNode(),
      fileKey = null,
      fileName = null,
      width = 0,
      height = 0;

  Block.image({
    required String fileKey,
    required String fileName,
    required int width,
    required int height,
  }) : type = BlockType.image,
       controller = null,
       focusNode = null,
       fileKey = fileKey,
       fileName = fileName,
       width = width,
       height = height;

  void dispose() {
    controller?.dispose();
    focusNode?.dispose();
  }
}
