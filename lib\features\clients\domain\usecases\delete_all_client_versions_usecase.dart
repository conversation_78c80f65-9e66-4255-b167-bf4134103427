import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/clients_repository.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

class DeleteAllClientVersionsUseCase {
  final IClientsRepository repository;

  DeleteAllClientVersionsUseCase(this.repository);

  Future<Either<Failure, Unit>> call(String firmId, String clientName) async {
    NetworkLogger.printInfo(
      'DeleteAllClientVersionsUseCase: Starting deletion of all versions for client: $clientName in firmId: $firmId',
    );

    try {
      final result = await repository.deleteAllClientVersions(firmId, clientName);

      return result.fold(
        (failure) {
          NetworkLogger.printError(
            'DeleteAllClientVersionsUseCase: Repository call failed:',
            failure.message,
          );
          return Left(failure);
        },
        (_) {
          NetworkLogger.printSuccess(
            'DeleteAllClientVersionsUseCase: Successfully deleted all versions for client $clientName',
          );
          return const Right(unit);
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'DeleteAllClientVersionsUseCase: Unexpected error:',
        e,
        stackTrace,
      );
      return Left(
        UnexpectedFailure(
          message: 'Неожиданная ошибка при удалении всех версий клиента: $e',
        ),
      );
    }
  }
}