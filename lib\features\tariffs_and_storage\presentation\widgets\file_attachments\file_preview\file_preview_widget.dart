import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:pdfx/pdfx.dart';
import 'package:vector_math/vector_math_64.dart' as vmath;

import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class PdfPreviewWidget extends StatefulWidget {
  final String fileKey;
  final String fileName;

  const PdfPreviewWidget({
    super.key,
    required this.fileKey,
    required this.fileName,
  });

  @override
  State<PdfPreviewWidget> createState() => _PdfPreviewWidgetState();
}

class _PdfPreviewWidgetState extends State<PdfPreviewWidget> {
  PdfControllerPinch? _pdfController;
  bool _isLoading = true;
  String? _errorMessage;
  bool _isProgrammaticScroll = false;

  // Для отслеживания перетаскивания скроллбара
  double? _dragStartY;
  double? _dragStartScrollY;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  @override
  void dispose() {
    _pdfController?.removeListener(_constrainMovement);
    _pdfController?.dispose();
    super.dispose();
  }

  Future<void> _loadPdf() async {
    try {
      final downloadUrl = await _getDownloadUrl();
      if (downloadUrl == null) {
        setState(() {
          _errorMessage = 'Не удалось получить ссылку для предпросмотра';
          _isLoading = false;
        });
        return;
      }

      final controller = PdfControllerPinch(
        document: PdfDocument.openData(
          http.get(Uri.parse(downloadUrl)).then((r) => r.bodyBytes),
        ),
      );

      controller.loadingState.addListener(() {
        if (controller.loadingState.value == PdfLoadingState.success) {
          final scale = 0.5;
          final width = MediaQuery.of(context).size.width;
          final dx = (width - width * scale) / 2;
          final matrix =
              Matrix4.identity()
                ..translate(dx)
                ..scale(scale);
          controller.goTo(destination: matrix);
        }
      });

      setState(() {
        _pdfController = controller;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Ошибка загрузки PDF: $e';
        _isLoading = false;
      });
    }
  }

  Future<String?> _getDownloadUrl() async {
    final storageCubit = context.read<TariffsAndStorageCubit>();
    final firmState = context.read<ActiveFirmCubit>().state;

    if (firmState.selectedFirm == null) return null;

    final firmId = firmState.selectedFirm!.id;
    final completer = Completer<String?>();
    late StreamSubscription sub;

    sub = storageCubit.stream.listen((state) {
      if (state is FileDownloadUrlReady && state.fileKey == widget.fileKey) {
        if (!completer.isCompleted) completer.complete(state.downloadUrl);
        sub.cancel();
      } else if (state is TariffsAndStorageError) {
        if (!completer.isCompleted) completer.complete(null);
        sub.cancel();
      }
    });

    storageCubit.getDownloadUrl(firmId: firmId, fileKey: widget.fileKey);

    Future.delayed(const Duration(seconds: 15), () {
      if (!completer.isCompleted) {
        completer.complete(null);
        sub.cancel();
      }
    });

    return completer.future;
  }

  void _constrainMovement() {
    if (_pdfController == null || _isProgrammaticScroll) return;

    final currentMatrix = _pdfController!.value;
    final currentZoom = _pdfController!.zoomRatio;
    final width = MediaQuery.of(context).size.width;
    final desiredDx = (width - width * currentZoom) / 2;

    final vmath.Vector3 t = currentMatrix.getTranslation();
    final currentDx = t.x;

    if ((currentDx - desiredDx).abs() > 5.0) {
      final newMatrix =
          Matrix4.identity()
            ..translate(desiredDx, t.y)
            ..scale(currentZoom);

      _isProgrammaticScroll = true;
      _pdfController!.goTo(destination: newMatrix);
      _isProgrammaticScroll = false;
    }
  }

  void _zoom(double factor) {
    if (_pdfController == null) return;

    final currentZoom = _pdfController!.zoomRatio;
    final newZoom = (currentZoom * factor).clamp(0.25, 5.0);

    final viewRect = _pdfController!.viewRect;
    final centerY = viewRect.center.dy;

    final width = MediaQuery.of(context).size.width;
    final dx = (width - width * newZoom) / 2;

    final scaleFactor = newZoom / currentZoom;
    final newCenterY = centerY * scaleFactor;
    final dy = viewRect.height / 2 - newCenterY;

    final matrix =
        Matrix4.identity()
          ..translate(dx, dy)
          ..scale(newZoom);

    _programmaticGoTo(matrix);
  }

  void _goToInstant(Matrix4 destination) {
    if (_pdfController == null) return;
    _isProgrammaticScroll = true;
    _pdfController!.goTo(destination: destination);
    _isProgrammaticScroll = false;
  }

  Future<void> _programmaticGoTo(Matrix4 destination) async {
    if (_pdfController == null) return;
    if (!mounted) return;

    _isProgrammaticScroll = true;
    _pdfController!.goTo(destination: destination);
    await Future.delayed(const Duration(milliseconds: 20));
    if (mounted) {
      _isProgrammaticScroll = false;
    }
  }

  void _recenter() {
    if (_pdfController == null) return;
    final currentMatrix = _pdfController!.value.clone();
    final currentZoom = _pdfController!.zoomRatio;
    final width = MediaQuery.of(context).size.width;
    final dx = (width - width * currentZoom) / 2;

    final vmath.Vector3 t = currentMatrix.getTranslation();
    final dy = t.y;

    final matrix =
        Matrix4.identity()
          ..translate(dx, dy)
          ..scale(currentZoom);
    _programmaticGoTo(matrix);
  }

  // Вычисляем параметры скроллбара с учетом текущего масштаба
  Map<String, double>? _calculateScrollbarParams() {
    if (_pdfController == null) return null;

    final pages = _pdfController!.pagesCount ?? 1;
    if (pages <= 1) return null;

    final currentZoom = _pdfController!.zoomRatio;
    final matrix = _pdfController!.value;
    final currentTranslation = matrix.getTranslation();
    final currentY = -currentTranslation.y;

    final viewRect = _pdfController!.viewRect;
    final viewportHeight = viewRect.height;

    // Получаем границы документа в текущем масштабе
    final firstPageRect = _pdfController!.getPageRect(1);
    final lastPageRect = _pdfController!.getPageRect(pages);

    if (firstPageRect == null || lastPageRect == null) return null;

    // Учитываем текущий масштаб при расчете границ
    final scaledDocumentTop = firstPageRect.top * currentZoom;
    final scaledDocumentBottom = lastPageRect.bottom * currentZoom;
    final scaledDocumentHeight = scaledDocumentBottom - scaledDocumentTop;

    // Минимальная позиция прокрутки (верх документа)
    final minY = scaledDocumentTop;
    // Максимальная позиция прокрутки (низ документа минус высота viewport)
    final maxY = scaledDocumentBottom - viewportHeight;

    // Если документ полностью помещается в viewport
    if (maxY <= minY) return null;

    // Диапазон прокрутки
    final scrollRange = maxY - minY;

    // Прогресс прокрутки (0.0 - 1.0)
    final scrollProgress = ((currentY - minY) / scrollRange).clamp(0.0, 1.0);

    // Отношение видимой области к общей высоте документа
    final visibleRatio = viewportHeight / scaledDocumentHeight;

    return {
      'scrollProgress': scrollProgress,
      'visibleRatio': visibleRatio,
      'minY': minY,
      'maxY': maxY,
      'scrollRange': scrollRange,
    };
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_pdfController == null) {
      return const Center(child: Text('Не удалось загрузить PDF'));
    }

    return Stack(
      children: [
        Positioned.fill(
          child: Center(
            child: GestureDetector(
              onScaleEnd: (_) => _recenter(),
              child: PdfViewPinch(
                controller: _pdfController!,
                scrollDirection: Axis.vertical,
                padding: 0,
                minScale: 0.25,
                maxScale: 5,
              ),
            ),
          ),
        ),
        Positioned(
          right: 16,
          bottom: 32,
          child: Column(
            children: [
              FloatingActionButton(
                mini: true,
                heroTag: 'zoom_in_${widget.fileKey}',
                onPressed: () => _zoom(1.25),
                child: const Icon(Icons.add),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                mini: true,
                heroTag: 'zoom_out_${widget.fileKey}',
                onPressed: () => _zoom(0.8),
                child: const Icon(Icons.remove),
              ),
            ],
          ),
        ),
        // Кастомная вертикальная полоса прокрутки
        ValueListenableBuilder<Matrix4>(
          valueListenable: _pdfController!,
          builder: (context, matrix, _) {
            final params = _calculateScrollbarParams();
            if (params == null) return const SizedBox.shrink();

            final scrollProgress = params['scrollProgress']!;
            final visibleRatio = params['visibleRatio']!;
            final minY = params['minY']!;
            final maxY = params['maxY']!;
            final scrollRange = params['scrollRange']!;

            return Positioned(
              top: 60,
              right: 16,
              bottom: 140,
              width: 12,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final trackHeight = constraints.maxHeight;
                  final thumbHeight = (trackHeight * visibleRatio).clamp(
                    30.0, // Минимальная высота для удобного захвата
                    trackHeight,
                  );

                  if (thumbHeight >= trackHeight) {
                    return const SizedBox.shrink();
                  }

                  final maxThumbPosition = trackHeight - thumbHeight;
                  final thumbTop = (maxThumbPosition * scrollProgress).clamp(
                    0.0,
                    maxThumbPosition,
                  );

                  return GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onVerticalDragStart: (details) {
                      _dragStartY = details.localPosition.dy;
                      final currentY = -matrix.getTranslation().y;
                      _dragStartScrollY = currentY;
                    },
                    onVerticalDragUpdate: (details) {
                      if (_dragStartY == null || _dragStartScrollY == null) {
                        return;
                      }

                      final dragDelta = details.localPosition.dy - _dragStartY!;
                      final progressDelta = dragDelta / maxThumbPosition;
                      final scrollDelta = progressDelta * scrollRange;
                      final newY = (_dragStartScrollY! + scrollDelta).clamp(
                        minY,
                        maxY,
                      );

                      final currentMatrix = _pdfController!.value.clone();
                      final v = currentMatrix.getTranslation();
                      currentMatrix.setTranslation(
                        vmath.Vector3(v.x, -newY, v.z),
                      );
                      _goToInstant(currentMatrix);
                    },
                    onVerticalDragEnd: (_) {
                      _dragStartY = null;
                      _dragStartScrollY = null;
                    },
                    onTapDown: (details) {
                      // При клике на трек перемещаемся к этой позиции
                      final clickY = details.localPosition.dy;
                      final clickProgress = ((clickY - thumbHeight / 2) /
                              maxThumbPosition)
                          .clamp(0.0, 1.0);
                      final newY = minY + (clickProgress * scrollRange);

                      final currentMatrix = _pdfController!.value.clone();
                      final v = currentMatrix.getTranslation();
                      currentMatrix.setTranslation(
                        vmath.Vector3(v.x, -newY, v.z),
                      );
                      _programmaticGoTo(currentMatrix);
                    },
                    child: Stack(
                      children: [
                        // Трек
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white30,
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        // Ползунок
                        Positioned(
                          top: thumbTop,
                          left: 0,
                          right: 0,
                          height: thumbHeight,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }
}
