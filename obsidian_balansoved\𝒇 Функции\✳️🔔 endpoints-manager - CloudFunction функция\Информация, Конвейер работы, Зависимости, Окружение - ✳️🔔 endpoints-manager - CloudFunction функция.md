
Идентификатор - d4euafv5ijaums363e84
Описание - 🔔 Управляет подписками и отправляет Push-уведомления. **Использует гибридный подход**: Web Push через Yandex CNS, RuStore Push - напрямую.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`
	-> `action` (string, **обязательно**): `ADD`, `DELETE`, `GET_LIST`, `SEND`.
	-> **Для `ADD`**:
		- `push_token` (string, **обязательно**): JSON-строка для Web или токен устройства для RuStore.
		- `device_info` (object, необязательно): Дополнительная информация об устройстве.
	-> **Для `DELETE`**:
		- `push_token` (string, **обязательно**): Токен подписки, которую нужно удалить. **Передается в теле запроса.**
	-> **Для `SEND`**:
		- `user_id_to_notify` (string, **обязательно**): ID пользователя для отправки.
		- `payload` (object, **обязательно**): `{"title": "...", "body": "..."}`.

Внутренняя работа:
    -> Логирование события и контекста.
    -> Получение 'action' из контекста API Gateway или тела запроса.
    -> Парсинг тела запроса.
    -> Авторизация:
        -> Извлечение заголовка авторизации (учитывая внутренние вызовы через X-Forwarded-Authorization).
        -> Проверка JWT и извлечение user_id для пользовательских запросов; для внутренних вызовов user_id не требуется.
    -> Инициализация драйвера и пула сессий YDB для базы endpoints.
    -> Маршрутизация по 'action':
        -> **ADD** (требует авторизации):
            -> Получение push_token и device_info.
            -> Определение платформы (WEB или RUSTORE) по формату токена.
            -> Вычисление хэша токена.
            -> Если WEB: Создание или активация endpoint в Yandex CNS, получение endpoint_arn.
            -> UPSERT записи в UserEndpoints с данными (хэш, user_id, платформа, токен, ARN если WEB, enabled=true, device_info, timestamps).
        -> **DELETE** (требует авторизации):
            -> Получение push_token.
            -> Вычисление хэша и поиск записи в UserEndpoints.
            -> Проверка, что user_id совпадает с запрашивающим.
            -> Если WEB и есть ARN: Удаление endpoint из CNS (игнорируя NotFound).
            -> Удаление записи из UserEndpoints.
        -> **GET_LIST** (требует авторизации):
            -> Запрос всех записей для user_id из UserEndpoints (с индексом).
            -> Парсинг device_info_json и возврат списка в JSON.
        -> **SEND** (не требует пользовательской авторизации, для внутренних вызовов):
            -> Инициализация драйвера и пула для базы notices.
            -> Получение user_id_to_notify и payload (title, body).
            -> Создание таблицы notices_{user_id} если не существует (с полной схемой).
            -> UPSERT уведомления в notices_{user_id} (notice_id, title, provider='приложение', additional_info_json с body, created_at, is_delivered=false).
            -> Получение активных подписок (is_enabled=true) для user_id.
            -> Для каждой подписки:
                -> Если WEB: Формирование payload и публикация в CNS через endpoint_arn.
                -> Если RUSTORE: Отправка через rustore_push_utils.
                -> Сбор невалидных токенов (EndpointDisabled или UNREGISTERED).
            -> Если есть невалидные: Пакетное обновление UserEndpoints, установка is_enabled=false для их хэшей.
    -> Обработка исключений: AuthError (403), LogicError (400), NotFoundError (404), другие (500).

На выходе:
    -> 201 Created для ADD с сообщением успеха.
    -> 200 OK для DELETE, GET_LIST (с данными), SEND (с сообщением о количестве отправленных).
    -> 400 Bad Request для логических ошибок (например, отсутствие параметров).
    -> 403 Forbidden для ошибок авторизации.
    -> 404 Not Found если подписка не найдена.
    -> 500 Internal Server Error для непредвиденных ошибок.

---
#### Зависимости и окружение
-   **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `utils/rustore_push_utils.py`
-   **Переменные окружения**:
    -   `YDB_ENDPOINT_ENDPOINTS`, `YDB_DATABASE_ENDPOINTS`
    -   `YDB_ENDPOINT_NOTICES`, `YDB_DATABASE_NOTICES`
    -   `JWT_SECRET`, `SA_KEY_FILE`
    -   `CNS_PLATFORM_APP_ARN_WEB`: ARN **только** для Web Push-приложения в Yandex Notification Service.
    -   `RUSTORE_PROJECT_ID`: ID проекта из консоли RuStore.
    -   `RUSTORE_SERVICE_TOKEN`: Сервисный токен для авторизации в RuStore.
    -   `CNS_REGION`: Регион, в котором создан сервис (`ru-central1`).
    -   `CNS_ENDPOINT_URL`: URL эндпоинта Yandex Notification Service (`https://notifications.yandexcloud.net`).
    -   `STATIC_ACCESS_KEY_ID`
    -   `STATIC_SECRET_ACCESS_KEY`