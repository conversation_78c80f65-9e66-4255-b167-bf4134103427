
```python
import json
import ydb
import math
import datetime
import calendar
from custom_errors import NotFoundError, LogicError

PAGE_SIZE = 100

def get_task(session, table_name, task_id=None, page=0, get_dated_tasks=False, month=None, year=None, client_id=None):
    """
    Обрабатывает GET запросы с опциональной фильтрацией по client_id.
    """
    print(f"GET_TASK: Starting with params - task_id: {task_id}, page: {page}, get_dated_tasks: {get_dated_tasks}, month: {month}, year: {year}, client_id: {client_id}")
    
    tx = session.transaction(ydb.SerializableReadWrite())
    
    if task_id:
        print(f"GET_TASK: Fetching specific task with id: {task_id}")
        query_text = f"DECLARE $task_id AS Utf8; SELECT * FROM `{table_name}` WHERE task_id = $task_id;"
        res = tx.execute(session.prepare(query_text), {"$task_id": task_id})
        if not res[0].rows:
            print(f"GET_TASK: Task with id {task_id} not found")
            raise NotFoundError(f"Task with id {task_id} not found.")
        data = {c.name: res[0].rows[0][c.name] for c in res[0].columns}
        tx.commit()
        print(f"GET_TASK: Successfully retrieved task {task_id}")
        return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}

    params = {}
    declare_clauses = []
    where_clauses = []
    
    if client_id:
        declare_clauses.append("DECLARE $client_id_str AS Utf8;")
        where_clauses.append("String::Contains(CAST(client_ids_json AS String), $client_id_str)")
        params["$client_id_str"] = f'"{client_id}"'
    
    if get_dated_tasks:
        if not all([month, year]):
            raise LogicError("`month` and `year` are required when `get_dated_tasks` is true.")
        
        try:
            _, num_days = calendar.monthrange(year, month)
            start_date = datetime.datetime(year, month, 1, tzinfo=datetime.timezone.utc)
            end_date = datetime.datetime(year, month, num_days, 23, 59, 59, 999999, tzinfo=datetime.timezone.utc)
        except (ValueError, TypeError):
            raise LogicError("Invalid month or year provided.")
        
        declare_clauses.extend(["DECLARE $start_date AS Timestamp;", "DECLARE $end_date AS Timestamp;"])
        where_clauses.extend(["due_date IS NOT NULL", "due_date >= $start_date", "due_date <= $end_date"])
        params.update({"$start_date": start_date, "$end_date": end_date})
        
        full_where_clause = " AND ".join(where_clauses) if where_clauses else "1=1"
        full_declare_clause = " ".join(declare_clauses)
        
        query_text = f"{full_declare_clause} SELECT * FROM `{table_name}` WHERE {full_where_clause} ORDER BY due_date ASC;"
        res = tx.execute(session.prepare(query_text), params)
        data = [{c.name: r[c.name] for c in res[0].columns} for r in res[0].rows]
        tx.commit()
        return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}

    where_clauses.append("due_date IS NULL")
    full_where_clause = " AND ".join(where_clauses)
    
    try:
        page = int(page or 0)
        if page < 0: page = 0
    except (ValueError, TypeError):
        page = 0
        
    print(f"GET_TASK: Processing paginated undated tasks. Page: {page}, client_id filter: {client_id}")
    
    count_declare_clause = " ".join(declare_clauses)
    count_query_text = f"{count_declare_clause} SELECT COUNT(task_id) AS total_count FROM `{table_name}` WHERE {full_where_clause};"
    print(f"GET_TASK: Executing count query: {count_query_text}")
    count_res = tx.execute(session.prepare(count_query_text), params)
    total_tasks = count_res[0].rows[0].total_count if count_res[0].rows else 0
    print(f"GET_TASK: Total undated tasks found: {total_tasks}")
    
    if total_tasks == 0:
        metadata = {"total_tasks": 0, "current_page": 0, "page_size": PAGE_SIZE, "total_pages": 0}
        tx.commit()
        print(f"GET_TASK: No undated tasks found, returning empty result")
        return {"statusCode": 200, "body": json.dumps({"metadata": metadata, "data": []})}

    total_pages = math.ceil(total_tasks / PAGE_SIZE)
    if page >= total_pages:
        print(f"GET_TASK: Page {page} exceeds total pages {total_pages}")
        raise NotFoundError(f"Page {page} does not exist. Total pages: {total_pages}.")

    offset = page * PAGE_SIZE
    print(f"GET_TASK: Calculating pagination - offset: {offset}, limit: {PAGE_SIZE}")
    
    declare_clauses.extend(["DECLARE $limit AS Uint64;", "DECLARE $offset AS Uint64;"])
    params.update({"$limit": PAGE_SIZE, "$offset": offset})
    
    full_declare_clause = " ".join(declare_clauses)
    
    select_query_text = f"""
        {full_declare_clause}
        SELECT * FROM `{table_name}`
        WHERE {full_where_clause}
        ORDER BY created_at DESC
        LIMIT $limit OFFSET $offset;
    """
    print(f"GET_TASK: Executing paginated select query")
    select_res = tx.execute(session.prepare(select_query_text), params)
    data = [{c.name: r[c.name] for c in select_res[0].columns} for r in select_res[0].rows]
    print(f"GET_TASK: Retrieved {len(data)} undated tasks for page {page}")
    
    metadata = {"total_tasks": total_tasks, "current_page": page, "page_size": PAGE_SIZE, "total_pages": total_pages}
    
    tx.commit()
    print(f"GET_TASK: Successfully completed paginated undated tasks query")
    return {"statusCode": 200, "body": json.dumps({"metadata": metadata, "data": data}, default=str)}
```