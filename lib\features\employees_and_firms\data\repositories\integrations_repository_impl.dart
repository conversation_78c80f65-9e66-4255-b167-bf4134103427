import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_local_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/data_source/integrations_remote_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/data_source/scheduler_remote_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/backup_integration_entity.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/integrations_repository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class IntegrationsRepositoryImpl implements IntegrationsRepository {
  final IIntegrationsRemoteDataSource remoteDataSource;
  final IAuthLocalDataSource authLocalDataSource;
  final SharedPreferences prefs;
  final ISchedulerRemoteDataSource schedulerRemoteDataSource;

  IntegrationsRepositoryImpl({
    required this.remoteDataSource,
    required this.authLocalDataSource,
    required this.prefs,
    required this.schedulerRemoteDataSource,
  });

  static String _prefsKey(String firmId) => 'yadisk_last_sync_utc_$firmId';

  /// Универсальный парсер поля `enabled`, корректно обрабатывающий
  /// null, bool и строковые значения ('true' / 'false').
  bool _parseEnabled(Map<String, dynamic>? map) {
    if (map == null) return false;
    final val = map['enabled'];
    if (val == null) return true; // если ключ отсутствует, считаем включённым
    if (val is bool) {
      return val;
    }
    final stringResult = val.toString().toLowerCase() == 'true';
    return stringResult;
  }

  @override
  Future<Either<Failure, BackupIntegrationEntity>> getBackupStatus(
    String firmId,
  ) async {
    try {
      final token = await authLocalDataSource.getAccessToken();
      if (token == null) {
        return left(const AccessDeniedFailure(message: 'Токен не найден'));
      }
      final integrations = await remoteDataSource.getIntegrations(
        token,
        firmId,
      );
      final ydisk = integrations['yandex_disk'] as Map<String, dynamic>?;

      // --- Надёжное определение флага enabled ---
      final enabled = _parseEnabled(ydisk);

      // Безопасное извлечение last_sync_utc
      final lastVal0 = ydisk?['last_sync_utc'];
      final lastSync =
          lastVal0 != null ? DateTime.tryParse(lastVal0.toString()) : null;
      final tokenValue = ydisk?['token']?.toString();
      // Обновляем SharedPreferences
      if (lastSync != null) {
        prefs.setString(_prefsKey(firmId), lastSync.toIso8601String());
      }
      return right(
        BackupIntegrationEntity(
          enabled: enabled,
          lastSyncUtc: lastSync,
          token: tokenValue,
        ),
      );
    } on NetworkException catch (e) {
      return left(NetworkFailure(message: e.message));
    } on ServerException catch (e) {
      return left(ServerFailure(message: e.message));
    } catch (e) {
      return left(UnexpectedFailure(details: e.toString()));
    }
  }

  @override
  Future<Either<Failure, BackupIntegrationEntity>> setBackupEnabled({
    required String firmId,
    required bool enabled,
  }) async {
    try {
      final token = await authLocalDataSource.getAccessToken();
      if (token == null) {
        return left(const AccessDeniedFailure(message: 'Токен не найден'));
      }
      // --- Создание/удаление события планировщика ---
      String? schedulerEventId;
      if (enabled) {
        // генерируем список дат на год вперёд, каждую неделю
        final now = DateTime.now().toUtc();
        final dates = List.generate(
          52,
          (i) => now.add(Duration(days: 7 * (i + 1))),
        );
        final isoDates = dates.map((d) => d.toIso8601String()).toList();

        final payload = {
          'function_id': 'd4e3f6917icsff9hikrh', // TODO: вынести в константы
          'custom_identifier': 'backup-yadisk-$firmId',
          'is_annual': true,
          'execution_dates_json': jsonEncode(isoDates),
          'request_body_json': jsonEncode({'firm_id': firmId}),
          'is_active': true,
        };
        schedulerEventId = await schedulerRemoteDataSource.upsertEvent(
          token,
          payload,
        );
      } else {
        // попытка удалить
        final currentIntegrations = await remoteDataSource.getIntegrations(
          token,
          firmId,
        );
        final ydisk0 =
            currentIntegrations['yandex_disk'] as Map<String, dynamic>?;
        final existingEventId = ydisk0?['scheduler_event_id']?.toString();
        if (existingEventId != null) {
          await schedulerRemoteDataSource.deleteEvent(token, existingEventId);
        }
      }

      final nowUtc = DateTime.now().toUtc().toIso8601String();
      // Обновляем поля на сервере (сервер выполняет глубокий мердж)
      await remoteDataSource.upsertIntegration(token, firmId, {
        'yandex_disk': {
          'enabled': enabled,
          if (enabled) 'last_sync_utc': nowUtc,
          if (schedulerEventId != null) 'scheduler_event_id': schedulerEventId,
          if (!enabled) 'scheduler_event_id': null,
        },
      });
      // Обновляем локальный кэш даты
      if (enabled) prefs.setString(_prefsKey(firmId), nowUtc);
      // Получаем актуальное состояние интеграций
      final updated = await remoteDataSource.getIntegrations(token, firmId);
      final ydisk = updated['yandex_disk'] as Map<String, dynamic>?;
      final lastVal = ydisk?['last_sync_utc'];
      final parsedLast =
          lastVal != null ? DateTime.tryParse(lastVal.toString()) : null;
      final parsedToken = ydisk?['token']?.toString();
      final parsedEnabled = _parseEnabled(ydisk);
      return right(
        BackupIntegrationEntity(
          enabled: parsedEnabled,
          lastSyncUtc: parsedLast,
          token: parsedToken,
        ),
      );
    } on NetworkException catch (e) {
      return left(NetworkFailure(message: e.message));
    } on ServerException catch (e) {
      return left(ServerFailure(message: e.message));
    } catch (e) {
      return left(UnexpectedFailure(details: e.toString()));
    }
  }

  @override
  Future<Either<Failure, BackupIntegrationEntity>> saveToken({
    required String firmId,
    required String token,
  }) async {
    try {
      final jwt = await authLocalDataSource.getAccessToken();
      if (jwt == null) {
        return left(const AccessDeniedFailure(message: 'Токен не найден'));
      }

      // Отправляем только поле token, сервер объединит глубоко
      await remoteDataSource.upsertIntegration(jwt, firmId, {
        'yandex_disk': {'token': token},
      });

      // Получаем обновлённое состояние после сохранения token
      final updated = await remoteDataSource.getIntegrations(jwt, firmId);
      final ydisk = updated['yandex_disk'] as Map<String, dynamic>?;
      final lastVal2 = ydisk?['last_sync_utc'];
      final parsedLast2 =
          lastVal2 != null ? DateTime.tryParse(lastVal2.toString()) : null;
      final parsedToken = ydisk?['token']?.toString();
      final parsedEnabled = _parseEnabled(ydisk);
      // Сохраняем дату последней синхронизации в prefs, если есть
      if (parsedLast2 != null) {
        prefs.setString(_prefsKey(firmId), parsedLast2.toIso8601String());
      }
      return right(
        BackupIntegrationEntity(
          enabled: parsedEnabled,
          lastSyncUtc: parsedLast2,
          token: parsedToken,
        ),
      );
    } on NetworkException catch (e) {
      return left(NetworkFailure(message: e.message));
    } on ServerException catch (e) {
      return left(ServerFailure(message: e.message));
    } catch (e) {
      return left(UnexpectedFailure(details: e.toString()));
    }
  }
}
