import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'price_calculator_state.dart';

class PriceCalculatorCubit extends Cubit<PriceCalculatorState> {
  PriceCalculatorCubit() : super(_initialState) {
    _loadAllRates();
  }

  static const _prefsKey = 'price_calculator_state_v1';

  static PriceCalculatorState get _initialState => PriceCalculatorState(
    entityType: EntityType.ooo,
    businessActivityType: BusinessActivityType.services,
    taxSystem: TaxSystem.usn6,
    baseRate: _getBaseRate(BusinessActivityType.services, TaxSystem.usn6),
    operationsQty: 0,
    generatePrimaryDocs: false,
    employeesQty: 0,
    paymentsQty: 0,
    vedOption: VedOption.none,
    kkmBso: false,
    extraAccountQty: 0,
    currencyAccountQty: 0,
    nomenclatureQty: 0,
    taxSystemMix: false,
    ndsRates: false,
    pbu18: false,
    osNma: false,
    // Новые опции ВЭД и акцизов
    ftsEaes: false,
    exciseGoods: false,
    alcoholDeclaration: false,
    // Обособленные подразделения
    subdivisionsQty: 0,
    // Сложные операции
    complexOperations: false,
    // Фиксированные услуги
    our1C: false,
    separate1CAccess: false,
    vpn: false,
    documentResigning: false,
    courierPayment: false,
    accountUnblocking: false,
    rates: _defaultRates[EntityType.ooo]!,
    total: 0,
  );

  /// Таблица базовых тарифов
  static Map<BusinessActivityType, Map<TaxSystem, int>> _baseTariffs = {
    BusinessActivityType.services: {
      TaxSystem.osno: 10000,
      TaxSystem.usn6: 6000,
      TaxSystem.usn15: 8000,
      TaxSystem.patent: 5000,
      TaxSystem.eskhn: 7000,
      TaxSystem.patentUsn6: 7000,
      TaxSystem.patentUsn15: 8500,
    },
    BusinessActivityType.trade: {
      TaxSystem.osno: 12000,
      TaxSystem.usn6: 8000,
      TaxSystem.usn15: 10000,
      TaxSystem.patent: 7000,
      TaxSystem.eskhn: 9000,
      TaxSystem.patentUsn6: 9000,
      TaxSystem.patentUsn15: 10500,
    },
    BusinessActivityType.production: {
      TaxSystem.osno: 15000,
      TaxSystem.usn6: 10000,
      TaxSystem.usn15: 12000,
      TaxSystem.patent: 9000,
      TaxSystem.eskhn: 11000,
      TaxSystem.patentUsn6: 11500,
      TaxSystem.patentUsn15: 13000,
    },
  };

  /// Получить базовый тариф по виду деятельности и системе налогообложения
  static int _getBaseRate(BusinessActivityType activity, TaxSystem taxSystem) {
    return _baseTariffs[activity]?[taxSystem] ?? 6000;
  }

  static Map<BusinessActivityType, Map<TaxSystem, int>> getBaseTariffs() {
    return _baseTariffs;
  }

  void updateBaseTariffs(
    Map<BusinessActivityType, Map<TaxSystem, int>> newTariffs,
  ) {
    _baseTariffs = newTariffs;
    // Пересчитать текущий базовый тариф в state
    final newBaseRate = _getBaseRate(
      state.businessActivityType,
      state.taxSystem,
    );
    emit(state.copyWith(baseRate: newBaseRate));
    _recalc();
  }

  // Храним тарифы для всех типов
  static final Map<EntityType, Map<String, double>> _ratesByEntity = {
    for (var e in EntityType.values)
      e: Map<String, double>.from(_defaultRates[e]!),
  };

  Future<void> loadFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_prefsKey);
    if (jsonString == null || jsonString.isEmpty) {
      _recalc();
      return;
    }
    try {
      final map = Map<String, dynamic>.from(
        jsonDecode(jsonString) as Map<dynamic, dynamic>,
      );
      final entityType = EntityType.fromString(map['entityType'] as String);
      emit(_stateFromMap(map, entityType));
    } catch (_) {
      _recalc();
    }
  }

  PriceCalculatorState _stateFromMap(
    Map<String, dynamic> map,
    EntityType entityType,
  ) {
    final businessActivity = BusinessActivityType.fromString(
      map['businessActivityType'] as String? ??
          BusinessActivityType.services.name,
    );
    final taxSystem = TaxSystem.fromString(
      map['taxSystem'] as String? ?? TaxSystem.usn6.name,
    );
    final baseRate =
        map['baseRate'] as int? ?? _getBaseRate(businessActivity, taxSystem);

    return PriceCalculatorState(
      entityType: entityType,
      businessActivityType: businessActivity,
      taxSystem: taxSystem,
      baseRate: baseRate,
      operationsQty: map['operationsQty'] as int? ?? 0,
      generatePrimaryDocs: map['generatePrimaryDocs'] as bool? ?? false,
      employeesQty: map['employeesQty'] as int? ?? 0,
      paymentsQty: map['paymentsQty'] as int? ?? 0,
      vedOption:
          VedOption.values[map['vedOption'] as int? ?? VedOption.none.index],
      kkmBso: map['kkmBso'] as bool? ?? false,
      extraAccountQty: map['extraAccountQty'] as int? ?? 0,
      currencyAccountQty: map['currencyAccountQty'] as int? ?? 0,
      nomenclatureQty: map['nomenclatureQty'] as int? ?? 0,
      taxSystemMix: map['taxSystemMix'] as bool? ?? false,
      ndsRates: map['ndsRates'] as bool? ?? false,
      pbu18: map['pbu18'] as bool? ?? false,
      osNma: map['osNma'] as bool? ?? false,
      ftsEaes: map['ftsEaes'] as bool? ?? false,
      exciseGoods: map['exciseGoods'] as bool? ?? false,
      alcoholDeclaration: map['alcoholDeclaration'] as bool? ?? false,
      subdivisionsQty: map['subdivisionsQty'] as int? ?? 0,
      complexOperations: map['complexOperations'] as bool? ?? false,
      our1C: map['our1C'] as bool? ?? false,
      separate1CAccess: map['separate1CAccess'] as bool? ?? false,
      vpn: map['vpn'] as bool? ?? false,
      documentResigning: map['documentResigning'] as bool? ?? false,
      courierPayment: map['courierPayment'] as bool? ?? false,
      accountUnblocking: map['accountUnblocking'] as bool? ?? false,
      rates: Map<String, double>.from(
        map['rates'] as Map<String, dynamic>? ?? _defaultRates[entityType]!,
      ),
      total: map['total'] as int? ?? 0,
    );
  }

  Future<void> _saveToPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString(_prefsKey, jsonEncode(_stateToMap(state)));
  }

  Map<String, dynamic> _stateToMap(PriceCalculatorState s) => {
    'entityType': s.entityType.name,
    'businessActivityType': s.businessActivityType.name,
    'taxSystem': s.taxSystem.name,
    'baseRate': s.baseRate,
    'operationsQty': s.operationsQty,
    'generatePrimaryDocs': s.generatePrimaryDocs,
    'employeesQty': s.employeesQty,
    'paymentsQty': s.paymentsQty,
    'vedOption': s.vedOption.index,
    'kkmBso': s.kkmBso,
    'extraAccountQty': s.extraAccountQty,
    'currencyAccountQty': s.currencyAccountQty,
    'nomenclatureQty': s.nomenclatureQty,
    'taxSystemMix': s.taxSystemMix,
    'ndsRates': s.ndsRates,
    'pbu18': s.pbu18,
    'osNma': s.osNma,
    'ftsEaes': s.ftsEaes,
    'exciseGoods': s.exciseGoods,
    'alcoholDeclaration': s.alcoholDeclaration,
    'subdivisionsQty': s.subdivisionsQty,
    'complexOperations': s.complexOperations,
    'our1C': s.our1C,
    'separate1CAccess': s.separate1CAccess,
    'vpn': s.vpn,
    'documentResigning': s.documentResigning,
    'courierPayment': s.courierPayment,
    'accountUnblocking': s.accountUnblocking,
    'rates': s.rates,
    'total': s.total,
  };

  Future<void> _loadAllRates() async {
    final prefs = await SharedPreferences.getInstance();
    for (final e in EntityType.values) {
      final jsonStr = prefs.getString('$_prefsKey${'_${e.name}'}');
      if (jsonStr != null && jsonStr.isNotEmpty) {
        try {
          final Map<String, dynamic> parsed = jsonDecode(jsonStr);
          _ratesByEntity[e] = parsed.map(
            (k, v) => MapEntry(k, (v as num).toDouble()),
          );
        } catch (_) {}
      }
    }
    // Обновляем state текущими тарифами
    emit(state.copyWith(rates: _ratesByEntity[state.entityType]!));
  }

  Future<void> _saveEntityRates(EntityType type) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString(
      '$_prefsKey${'_${type.name}'}',
      jsonEncode(_ratesByEntity[type]),
    );
  }

  // Удалено переопределение onChange (вызывало бесконечную рекурсию emit → onChange → emit)

  void changeEntity(EntityType type) {
    emit(state.copyWith(entityType: type, rates: _ratesByEntity[type]!));
    _recalc();
  }

  void updateField<T>(String field, T value) {
    switch (field) {
      case 'businessActivityType':
        final activity = value as BusinessActivityType;
        final newBaseRate = _getBaseRate(activity, state.taxSystem);
        emit(
          state.copyWith(businessActivityType: activity, baseRate: newBaseRate),
        );
        break;
      case 'taxSystem':
        final taxSystem = value as TaxSystem;
        final newBaseRate = _getBaseRate(state.businessActivityType, taxSystem);
        emit(state.copyWith(taxSystem: taxSystem, baseRate: newBaseRate));
        break;
      case 'baseRate':
        emit(state.copyWith(baseRate: value as int));
        break;
      case 'operationsQty':
        emit(state.copyWith(operationsQty: value as int));
        break;
      case 'generatePrimaryDocs':
        emit(state.copyWith(generatePrimaryDocs: value as bool));
        break;
      case 'employeesQty':
        emit(state.copyWith(employeesQty: value as int));
        break;
      case 'paymentsQty':
        emit(state.copyWith(paymentsQty: value as int));
        break;
      case 'vedOption':
        emit(state.copyWith(vedOption: value as VedOption));
        break;
      case 'kkmBso':
        emit(state.copyWith(kkmBso: value as bool));
        break;
      case 'extraAccountQty':
        emit(state.copyWith(extraAccountQty: value as int));
        break;
      case 'currencyAccountQty':
        emit(state.copyWith(currencyAccountQty: value as int));
        break;
      case 'nomenclatureQty':
        emit(state.copyWith(nomenclatureQty: value as int));
        break;
      case 'taxSystemMix':
        emit(state.copyWith(taxSystemMix: value as bool));
        break;
      case 'ndsRates':
        emit(state.copyWith(ndsRates: value as bool));
        break;
      case 'pbu18':
        emit(state.copyWith(pbu18: value as bool));
        break;
      case 'osNma':
        emit(state.copyWith(osNma: value as bool));
        break;
      case 'ftsEaes':
        emit(state.copyWith(ftsEaes: value as bool));
        break;
      case 'exciseGoods':
        emit(state.copyWith(exciseGoods: value as bool));
        break;
      case 'alcoholDeclaration':
        emit(state.copyWith(alcoholDeclaration: value as bool));
        break;
      case 'subdivisionsQty':
        emit(state.copyWith(subdivisionsQty: value as int));
        break;
      case 'complexOperations':
        emit(state.copyWith(complexOperations: value as bool));
        break;
      case 'our1C':
        emit(state.copyWith(our1C: value as bool));
        break;
      case 'separate1CAccess':
        emit(state.copyWith(separate1CAccess: value as bool));
        break;
      case 'vpn':
        emit(state.copyWith(vpn: value as bool));
        break;
      case 'documentResigning':
        emit(state.copyWith(documentResigning: value as bool));
        break;
      case 'courierPayment':
        emit(state.copyWith(courierPayment: value as bool));
        break;
      case 'accountUnblocking':
        emit(state.copyWith(accountUnblocking: value as bool));
        break;
      default:
        return;
    }
    _recalc();
  }

  void updateRate(String key, double value) {
    final current = Map<String, double>.from(_ratesByEntity[state.entityType]!);
    current[key] = value;
    _ratesByEntity[state.entityType] = current;
    emit(state.copyWith(rates: current));
    _recalc();
    unawaited(_saveEntityRates(state.entityType));
  }

  void updateRates(Map<String, double> ratesToUpdate) {
    final current = Map<String, double>.from(_ratesByEntity[state.entityType]!);
    current.addAll(ratesToUpdate);
    _ratesByEntity[state.entityType] = current;
    emit(state.copyWith(rates: current));
    _recalc();
    unawaited(_saveEntityRates(state.entityType));
  }

  void _recalc() {
    final s = state;
    final r = s.rates;
    int total = s.baseRate;
    int f(double v) => v.round();

    // operations cost
    int opsCost = 0;
    if (s.operationsQty > 200) {
      opsCost = ((s.operationsQty - 200) * (r['operations_t4'] ?? 0.0)).round();
    } else if (s.operationsQty > 150) {
      opsCost = f(s.baseRate * (r['operations_t3'] ?? 0.0));
    } else if (s.operationsQty > 100) {
      opsCost = f(s.baseRate * (r['operations_t2'] ?? 0.0));
    } else if (s.operationsQty > 50) {
      opsCost = f(s.baseRate * (r['operations_t1'] ?? 0.0));
    }
    total += opsCost;

    // primary docs cost
    int primaryDocsCost = 0;
    if (s.generatePrimaryDocs) {
      if (s.operationsQty > 200) {
        primaryDocsCost =
            ((s.operationsQty - 200) * (r['primary_docs_t4'] ?? 0.0)).round();
      } else if (s.operationsQty > 100) {
        primaryDocsCost = f(s.baseRate * (r['primary_docs_t3'] ?? 0.0));
      } else if (s.operationsQty > 50) {
        primaryDocsCost = f(s.baseRate * (r['primary_docs_t2'] ?? 0.0));
      } else if (s.operationsQty > 0) {
        primaryDocsCost = f(s.baseRate * (r['primary_docs_t1'] ?? 0.0));
      }
    }
    total += primaryDocsCost;

    // employees
    final chargeableQty =
        (s.employeesQty - (r['employees_free'] ?? 0.0).round())
            .clamp(0, 9999)
            .toInt();
    final empCost = chargeableQty * (r['employees_cost'] ?? 0.0).round();
    total += empCost;

    // auto discount
    if ((r['employees_discount'] ?? 0.0) > 0 && s.employeesQty == 0) {
      total -= f(s.baseRate * (r['employees_discount'] ?? 0.0));
    }

    // bank payments cost
    int paymentsCost = 0;
    if (s.paymentsQty > 500) {
      paymentsCost = (s.paymentsQty * (r['bank_p7'] ?? 0.0)).round();
    } else if (s.paymentsQty > 100) {
      paymentsCost = (s.paymentsQty * (r['bank_p6'] ?? 0.0)).round();
    } else if (s.paymentsQty > 80) {
      paymentsCost = (r['bank_p5'] ?? 0.0).round();
    } else if (s.paymentsQty > 60) {
      paymentsCost = (r['bank_p4'] ?? 0.0).round();
    } else if (s.paymentsQty > 40) {
      paymentsCost = (r['bank_p3'] ?? 0.0).round();
    } else if (s.paymentsQty > 20) {
      paymentsCost = (r['bank_p2'] ?? 0.0).round();
    } else if (s.paymentsQty > 0) {
      paymentsCost = (r['bank_p1'] ?? 0.0).round();
    }
    total += paymentsCost;

    // ved cost
    int vedCost = 0;
    if (s.vedOption == VedOption.importExport) {
      vedCost = f(s.baseRate * (r['bank_ved_ie'] ?? 0.0));
    } else if (s.vedOption == VedOption.both) {
      vedCost = f(s.baseRate * (r['bank_ved_both'] ?? 0.0));
    }
    total += vedCost;

    // nomenclature
    int nomCost = 0;
    if (s.nomenclatureQty > 250) {
      nomCost = f(s.baseRate * (r['features_nom_t3'] ?? 0.0));
    } else if (s.nomenclatureQty > 100) {
      nomCost = f(s.baseRate * (r['features_nom_t2'] ?? 0.0));
    } else if (s.nomenclatureQty > 50) {
      nomCost = f(s.baseRate * (r['features_nom_t1'] ?? 0.0));
    }
    total += nomCost;

    int percentCost(bool flag, String rateKey) =>
        flag ? f(s.baseRate * (r[rateKey] ?? 0.0)) : 0;

    int perUnitCost(int qty, String rateKey) =>
        (qty * (r[rateKey] ?? 0.0)).round();

    total += percentCost(s.kkmBso, 'bank_kkm');
    total += perUnitCost(s.extraAccountQty, 'bank_extra_acc');
    total += perUnitCost(s.currencyAccountQty, 'bank_curr_acc');
    total += percentCost(s.taxSystemMix, 'features_tax_mix');
    total += percentCost(s.ndsRates, 'features_nds_rates');
    total += percentCost(s.pbu18, 'features_pbu18');
    total += percentCost(s.osNma, 'features_os_nma');

    // Новые опции ВЭД и акцизов
    total += percentCost(s.ftsEaes, 'features_fts_eaes');
    total += percentCost(s.exciseGoods, 'features_excise_goods');
    total += percentCost(s.alcoholDeclaration, 'features_alcohol_declaration');

    // Обособленные подразделения
    total += perUnitCost(s.subdivisionsQty, 'features_subdivision_cost');

    // Сложные операции
    total += percentCost(s.complexOperations, 'features_complex_ops');

    // Фиксированные услуги
    total += s.our1C ? (r['services_our_1c'] ?? 0.0).round() : 0;
    total +=
        s.separate1CAccess
            ? (r['services_separate_1c_access'] ?? 0.0).round()
            : 0;
    total += s.vpn ? (r['services_vpn'] ?? 0.0).round() : 0;
    total +=
        s.documentResigning
            ? (r['services_document_resigning'] ?? 0.0).round()
            : 0;
    total +=
        s.courierPayment ? (r['services_courier_payment'] ?? 0.0).round() : 0;
    total +=
        s.accountUnblocking
            ? (r['services_account_unblocking'] ?? 0.0).round()
            : 0;

    emit(state.copyWith(total: total));
    unawaited(_saveToPrefs());
  }

  /// Возвращает детализацию расчёта (ключ-метка → сумма).
  Map<String, int> breakdown() {
    final Map<String, int> map = {'Базовый тариф': state.baseRate};

    int f(double v) => v.round();
    final s = state;
    final r = s.rates;

    // operations cost
    int opsCost = 0;
    if (s.operationsQty > 200) {
      opsCost = ((s.operationsQty - 200) * r['operations_t4']!).round();
      map['Операции >200'] = opsCost;
    } else if (s.operationsQty > 150) {
      opsCost = f(s.baseRate * r['operations_t3']!);
      map['Операции 151-200'] = opsCost;
    } else if (s.operationsQty > 100) {
      opsCost = f(s.baseRate * r['operations_t2']!);
      map['Операции 101-150'] = opsCost;
    } else if (s.operationsQty > 50) {
      opsCost = f(s.baseRate * r['operations_t1']!);
      map['Операции 51-100'] = opsCost;
    }

    // primary docs
    int primCost = 0;
    if (s.generatePrimaryDocs) {
      if (s.operationsQty > 200) {
        primCost = ((s.operationsQty - 200) * r['primary_docs_t4']!).round();
        map['Первичка >200'] = primCost;
      } else if (s.operationsQty > 100) {
        primCost = f(s.baseRate * r['primary_docs_t3']!);
        map['Первичка 101-200'] = primCost;
      } else if (s.operationsQty > 50) {
        primCost = f(s.baseRate * r['primary_docs_t2']!);
        map['Первичка 51-100'] = primCost;
      } else if (s.operationsQty > 0) {
        primCost = f(s.baseRate * r['primary_docs_t1']!);
        map['Первичка 0-50'] = primCost;
      }
    }

    // employees cost
    final chargeableQty =
        (s.employeesQty - (r['employees_free'] ?? 0.0).round())
            .clamp(0, 9999)
            .toInt();
    final empCost = chargeableQty * (r['employees_cost'] ?? 0.0).round();
    if (empCost > 0) map['Сотрудники'] = empCost;

    // discount employees zero
    if ((r['employees_discount'] ?? 0.0) > 0 && s.employeesQty == 0) {
      final disc = f(s.baseRate * (r['employees_discount'] ?? 0.0));
      map['Скидка без сотрудников'] = -disc;
    }

    // payments cost
    int paymentsCost = 0;
    if (s.paymentsQty > 500) {
      paymentsCost = (s.paymentsQty * r['bank_p7']!).round();
    } else if (s.paymentsQty > 100) {
      paymentsCost = (s.paymentsQty * r['bank_p6']!).round();
    } else if (s.paymentsQty > 80) {
      paymentsCost = r['bank_p5']!.round();
    } else if (s.paymentsQty > 60) {
      paymentsCost = r['bank_p4']!.round();
    } else if (s.paymentsQty > 40) {
      paymentsCost = r['bank_p3']!.round();
    } else if (s.paymentsQty > 20) {
      paymentsCost = r['bank_p2']!.round();
    } else if (s.paymentsQty > 0) {
      paymentsCost = r['bank_p1']!.round();
    }
    if (paymentsCost > 0) map['Банк/платежи'] = paymentsCost;

    int vedCost = 0;
    if (s.vedOption == VedOption.importExport) {
      vedCost = f(s.baseRate * r['bank_ved_ie']!);
      map['ВЭД И/Э'] = vedCost;
    } else if (s.vedOption == VedOption.both) {
      vedCost = f(s.baseRate * r['bank_ved_both']!);
      map['ВЭД И+Э'] = vedCost;
    }

    int nomCost = 0;
    if (s.nomenclatureQty > 250) {
      nomCost = f(s.baseRate * r['features_nom_t3']!);
      map['Номенклатура >250'] = nomCost;
    } else if (s.nomenclatureQty > 100) {
      nomCost = f(s.baseRate * r['features_nom_t2']!);
      map['Номенклатура 101-250'] = nomCost;
    } else if (s.nomenclatureQty > 50) {
      nomCost = f(s.baseRate * r['features_nom_t1']!);
      map['Номенклатура 51-100'] = nomCost;
    }

    void addPercent(bool flag, String rateKey, String label) {
      if (flag) map[label] = f(s.baseRate * (r[rateKey] ?? 0.0));
    }

    void addPerUnit(int qty, String rateKey, String label) {
      if (qty > 0) map[label] = (qty * (r[rateKey] ?? 0.0)).round();
    }

    addPercent(s.kkmBso, 'bank_kkm', 'ККМ/БСО');
    addPerUnit(s.extraAccountQty, 'bank_extra_acc', 'Доп. р/с');
    addPerUnit(s.currencyAccountQty, 'bank_curr_acc', 'Валютный счёт');
    addPercent(s.taxSystemMix, 'features_tax_mix', 'Смешанные СНО');
    addPercent(s.ndsRates, 'features_nds_rates', 'Разные ставки НДС');
    addPercent(s.pbu18, 'features_pbu18', 'ПБУ 18');
    addPercent(s.osNma, 'features_os_nma', 'ОС/НМА');

    // Новые опции ВЭД и акцизов
    addPercent(s.ftsEaes, 'features_fts_eaes', 'ФТС ЕАЭС');
    addPercent(
      s.exciseGoods,
      'features_excise_goods',
      'Торговля акцизными товарами',
    );
    addPercent(
      s.alcoholDeclaration,
      'features_alcohol_declaration',
      'Алкогольная декларация',
    );

    // Обособленные подразделения
    addPerUnit(
      s.subdivisionsQty,
      'features_subdivision_cost',
      'Обособленные подразделения',
    );

    // Сложные операции
    addPercent(s.complexOperations, 'features_complex_ops', 'Сложные операции');

    // Фиксированные услуги
    if (s.our1C) {
      map['Ведение в нашей 1С'] = (r['services_our_1c'] ?? 0.0).round();
    }
    if (s.separate1CAccess) {
      map['Отдельный доступ в 1С'] =
          (r['services_separate_1c_access'] ?? 0.0).round();
    }
    if (s.vpn) map['VPN'] = (r['services_vpn'] ?? 0.0).round();
    if (s.documentResigning) {
      map['Повторное подписание документов'] =
          (r['services_document_resigning'] ?? 0.0).round();
    }
    if (s.courierPayment) {
      map['Оплата взносов курьером'] =
          (r['services_courier_payment'] ?? 0.0).round();
    }
    if (s.accountUnblocking) {
      map['Снятие блоков р/с'] =
          (r['services_account_unblocking'] ?? 0.0).round();
    }

    return map;
  }

  // Predefined default rates
  static final Map<EntityType, Map<String, double>> _defaultRates = {
    EntityType.ooo: {
      'operations_t1': 0.3,
      'operations_t2': 0.5,
      'operations_t3': 0.6,
      'operations_t4': 45,
      'primary_docs_t1': 0.1,
      'primary_docs_t2': 0.3,
      'primary_docs_t3': 0.5,
      'primary_docs_t4': 45,
      'employees_cost': 500,
      'employees_free': 0,
      'employees_discount': 0,
      'bank_p1': 3000,
      'bank_p2': 6000,
      'bank_p3': 8000,
      'bank_p4': 10000,
      'bank_p5': 11000,
      'bank_p6': 80,
      'bank_p7': 50,
      'bank_ved_ie': 0.25,
      'bank_ved_both': 0.5,
      'bank_kkm': 0.25,
      'bank_extra_acc': 1500,
      'bank_curr_acc': 1500,
      'features_nom_t1': 0.1,
      'features_nom_t2': 0.25,
      'features_nom_t3': 0.4,
      'features_tax_mix': 0.2,
      'features_nds_rates': 0.2,
      'features_pbu18': 0.2,
      'features_os_nma': 0.26,
      // Новые опции ВЭД и акцизов
      'features_fts_eaes': 0.1,
      'features_excise_goods': 0.2,
      'features_alcohol_declaration': 0.2,
      // Обособленные подразделения
      'features_subdivision_cost': 1500,
      // Сложные операции
      'features_complex_ops': 0.2,
      // Фиксированные услуги
      'services_our_1c': 2000,
      'services_separate_1c_access': 1300,
      'services_vpn': 1000,
      'services_document_resigning': 150,
      'services_courier_payment': 2000,
      'services_account_unblocking': 2500,
    },
    EntityType.ao: {..._aoRates},
    EntityType.ip: {..._ipRates},
    EntityType.kfh: {..._kfhRates},
    EntityType.kh: {..._kfhRates},
  };

  // --- отдельные карты ставок (сокращённо) ---
  static const Map<String, double> _aoRates = {
    // скопированы значения из HTML allRates. (Полный список)
    'operations_t1': 0.3,
    'operations_t2': 0.5,
    'operations_t3': 0.6,
    'operations_t4': 45,
    'primary_docs_t1': 0.1,
    'primary_docs_t2': 0.3,
    'primary_docs_t3': 0.5,
    'primary_docs_t4': 45,
    'employees_cost': 550,
    'employees_free': 0,
    'employees_discount': 0,
    'bank_p1': 3000,
    'bank_p2': 6000,
    'bank_p3': 8000,
    'bank_p4': 10000,
    'bank_p5': 11000,
    'bank_p6': 80,
    'bank_p7': 50,
    'bank_ved_ie': 0.25,
    'bank_ved_both': 0.5,
    'bank_kkm': 0.25,
    'bank_extra_acc': 1500,
    'bank_curr_acc': 1500,
    'features_nom_t1': 0.1,
    'features_nom_t2': 0.25,
    'features_nom_t3': 0.4,
    'features_tax_mix': 0.2,
    'features_nds_rates': 0.2,
    'features_pbu18': 0.2,
    'features_os_nma': 0.26,
    // Новые опции ВЭД и акцизов
    'features_fts_eaes': 0.1,
    'features_excise_goods': 0.2,
    'features_alcohol_declaration': 0.2,
    // Обособленные подразделения
    'features_subdivision_cost': 1500,
    // Сложные операции
    'features_complex_ops': 0.2,
    // Фиксированные услуги
    'services_our_1c': 2000,
    'services_separate_1c_access': 1300,
    'services_vpn': 1000,
    'services_document_resigning': 150,
    'services_courier_payment': 2000,
    'services_account_unblocking': 2500,
  };

  static const Map<String, double> _ipRates = {
    'operations_t1': 0.25,
    'operations_t2': 0.45,
    'operations_t3': 0.55,
    'operations_t4': 40,
    'primary_docs_t1': 0.1,
    'primary_docs_t2': 0.25,
    'primary_docs_t3': 0.45,
    'primary_docs_t4': 40,
    'employees_cost': 450,
    'employees_free': 1,
    'employees_discount': 0.2,
    'bank_p1': 2500,
    'bank_p2': 5000,
    'bank_p3': 7000,
    'bank_p4': 9000,
    'bank_p5': 10000,
    'bank_p6': 70,
    'bank_p7': 45,
    'bank_ved_ie': 0.2,
    'bank_ved_both': 0.4,
    'bank_kkm': 0.2,
    'bank_extra_acc': 1200,
    'bank_curr_acc': 1200,
    'features_nom_t1': 0.08,
    'features_nom_t2': 0.2,
    'features_nom_t3': 0.35,
    'features_tax_mix': 0.18,
    'features_nds_rates': 0.18,
    'features_pbu18': 0.18,
    'features_os_nma': 0.22,
    // Новые опции ВЭД и акцизов
    'features_fts_eaes': 0.1,
    'features_excise_goods': 0.2,
    'features_alcohol_declaration': 0.2,
    // Обособленные подразделения
    'features_subdivision_cost': 1500,
    // Сложные операции
    'features_complex_ops': 0.2,
    // Фиксированные услуги
    'services_our_1c': 2000,
    'services_separate_1c_access': 1300,
    'services_vpn': 1000,
    'services_document_resigning': 150,
    'services_courier_payment': 2000,
    'services_account_unblocking': 2500,
  };

  static const Map<String, double> _kfhRates = {
    'operations_t1': 0.2,
    'operations_t2': 0.4,
    'operations_t3': 0.5,
    'operations_t4': 35,
    'primary_docs_t1': 0.08,
    'primary_docs_t2': 0.2,
    'primary_docs_t3': 0.4,
    'primary_docs_t4': 35,
    'employees_cost': 400,
    'employees_free': 2,
    'employees_discount': 0.25,
    'bank_p1': 2000,
    'bank_p2': 4500,
    'bank_p3': 6500,
    'bank_p4': 8500,
    'bank_p5': 9500,
    'bank_p6': 60,
    'bank_p7': 40,
    'bank_ved_ie': 0.15,
    'bank_ved_both': 0.3,
    'bank_kkm': 0.18,
    'bank_extra_acc': 1000,
    'bank_curr_acc': 1000,
    'features_nom_t1': 0.06,
    'features_nom_t2': 0.18,
    'features_nom_t3': 0.3,
    'features_tax_mix': 0.15,
    'features_nds_rates': 0.15,
    'features_pbu18': 0.15,
    'features_os_nma': 0.18,
    // Новые опции ВЭД и акцизов
    'features_fts_eaes': 0.1,
    'features_excise_goods': 0.2,
    'features_alcohol_declaration': 0.2,
    // Обособленные подразделения
    'features_subdivision_cost': 1500,
    // Сложные операции
    'features_complex_ops': 0.2,
    // Фиксированные услуги
    'services_our_1c': 2000,
    'services_separate_1c_access': 1300,
    'services_vpn': 1000,
    'services_document_resigning': 150,
    'services_courier_payment': 2000,
    'services_account_unblocking': 2500,
  };
}
