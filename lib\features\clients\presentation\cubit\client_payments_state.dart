part of 'client_payments_cubit.dart';

class ClientPaymentsState extends Equatable {
  final bool isLoading;
  final List<ClientEntity> clients;
  final Map<String, List<ClientPaymentEntity>> paymentsByClient;
  final int selectedYear;
  final String? error;
  final bool noAccess;

  const ClientPaymentsState({
    required this.isLoading,
    required this.clients,
    required this.paymentsByClient,
    required this.selectedYear,
    this.error,
    this.noAccess = false,
  });

  const ClientPaymentsState.initial()
    : this(
        isLoading: false,
        clients: const [],
        paymentsByClient: const {},
        selectedYear: 0,
      );

  ClientPaymentsState copyWith({
    bool? isLoading,
    List<ClientEntity>? clients,
    Map<String, List<ClientPaymentEntity>>? paymentsByClient,
    int? selectedYear,
    String? error,
    bool? noAccess,
  }) {
    return ClientPaymentsState(
      isLoading: isLoading ?? this.isLoading,
      clients: clients ?? this.clients,
      paymentsByClient: paymentsByClient ?? this.paymentsByClient,
      selectedYear: selectedYear ?? this.selectedYear,
      error: error,
      noAccess: noAccess ?? this.noAccess,
    );
  }

  @override
  List<Object?> get props => [
    isLoading,
    clients,
    paymentsByClient,
    selectedYear,
    error,
    noAccess,
  ];
}
