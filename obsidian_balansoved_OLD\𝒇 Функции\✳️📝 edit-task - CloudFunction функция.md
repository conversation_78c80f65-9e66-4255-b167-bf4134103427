
Идентификатор - d4e4mpjj10hka0o8ct9d
Описание - 📝 Управляет задачами. Позволяет получать, создавать, обновлять и удалять задачи, а также автоматически синхронизирует напоминания.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы.
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
 		- `task_id` (string, необязательно): ID задачи для `GET` (одна), `UPSERT` (обновление), `DELETE`.
		- `payload` (object, необязательно): Данные для `UPSERT`. **Может содержать `main_task_id`, `subtasks_json`, `periodic_parent_id`, `periodic_children_json`, `reminders_json`**.
		- **Параметры для `action: GET`**:
			- **Для бессрочных задач:**
				- `page` (integer, необязательно): Номер страницы (с 0). По умолчанию `0`.
			- **Для задач с крайним сроком:**
				- `get_dated_tasks` (boolean, **обязательно**): Установить в `true`.
				- `month` (integer, **обязательно**): Месяц (1-12).
				- `year` (integer, **обязательно**): Год.
			- **Дополнительный фильтр (для любого списка):**
				- `client_id` (string, необязательно): ID клиента для фильтрации списка задач.

Внутренняя работа:
    -> Логирование начала запроса и авторизация:
        -> Проверка JWT токена, извлечение user_id.
        -> Проверка членства в фирме и минимальной роли EDITOR для UPSERT/DELETE.
    -> Парсинг запроса:
        -> Извлечение firm_id, action (GET, UPSERT, DELETE), task_id, payload и других параметров.
    -> Подключение к YDB базам фирм и задач.
    -> Маршрутизация по action:
        -> GET:
            -> Если task_id: Получение одной задачи по ID, включая подзадачи и напоминания.
            -> Если month/year: Получение срочных задач за период, с фильтром по client_id если указан.
            -> Иначе: Пагинированное получение бессрочных задач, с фильтром по client_id.
        -> UPSERT:
            -> Проверка прав на изменение (создатель или исполнитель).
            -> Создание новой задачи если нет task_id, иначе обновление.
            -> Обработка подзадач: Обновление subtasks_json в главной, main_task_id в подзадачах.
            -> Обработка периодических: Обновление periodic_children_json в родителе, periodic_parent_id в детях.
            -> Синхронизация напоминаний: Сравнение существующих и желаемых, создание/удаление событий в scheduler_manager.
            -> Транзакционная запись в YDB.
        -> DELETE:
            -> Проверка прав на удаление.
            -> Удаление напоминаний и событий планировщика.
            -> Если подзадача: Удаление из subtasks_json родителя, установка main_task_id в null.
            -> Если периодический экземпляр: Удаление из periodic_children_json родителя.
            -> Если периодический родитель: Рекурсивное удаление всех детей.
            -> Удаление задачи из YDB.
    -> Обработка ошибок и возврат ответа.

На выходе:
    -> Успешные ответы:
        -> GET: 200 OK с JSON данными (одна задача, пагинированный список бессрочных с next_page_token, или список срочных).
        -> UPSERT: 201 Created для создания (с task_id), 200 OK для обновления (с сообщением).
        -> DELETE: 200 OK с сообщением об удалении.
    -> Ошибки:
        -> 400 Bad Request: Неверные параметры, парсинг ошибок.
        -> 401 Unauthorized: Недействительный токен.
        -> 403 Forbidden: Недостаточно прав, не член фирмы.
        -> 404 Not Found: Задача/фирма не найдена.
        -> 500 Internal Server Error: Внутренние ошибки, проблемы с DB или вызовами функций.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `reminders_processor.py`, `invoke_utils.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`
	- `FUNCTION_ID_SCHEDULER_MANAGER`
	- `FUNCTION_ID_ENDPOINTS_MANAGER`
	- `FUNCTION_ID_EDIT_TASK` ([[✳️📝 edit-task - CloudFunction функция]])

---
#### index.py
```python
import json, os, logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

from get import get_task
from upsert import upsert_task
from delete import delete_task
from custom_errors import AuthError, LogicError, NotFoundError

logging.basicConfig(level=logging.INFO)

def _log_event_context(event, context):
    """Выводит в лог подробную информацию о входящем событии и контексте вызова."""
    try:
        print("RAW EVENT: %s" % json.dumps(event, default=str, ensure_ascii=False)[:10000])
    except Exception:
        print("RAW EVENT (non-json serialisable): %s" % event)
    
    if context is not None:
        print(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s" %
            (getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None))
        )

def check_membership_and_role(session, user_id, firm_id):
    query_text = "DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;"
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$user_id": user_id, "$firm_id": firm_id}, commit_tx=True)
    if not result[0].rows:
        return (False, False)
    roles = json.loads(result[0].rows[0].roles)
    is_admin_or_owner = "OWNER" in roles or "ADMIN" in roles
    return (True, is_admin_or_owner)

def handler(event, context):
    _log_event_context(event, context)
    
    user_id = "unknown"
    action = "unknown"
    firm_id = "unknown"
    try:
        # 1. Авторизация
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing Bearer token")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload: 
            raise AuthError("Invalid or expired token")
        requesting_user_id = user_payload['user_id']
        user_id = requesting_user_id
        print(f"Authorized request for user_id: {user_id}")

        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        
        if not all([firm_id, action]): 
            raise LogicError("firm_id and action are required.")
        
        print(f"Processing action: {action} for user_id: {user_id}, firm_id: {firm_id}")
        print(f"Parsed request data: {data}")

        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        print(f"Connected to firms database for firm_id: {firm_id}")
        firms_pool = ydb.SessionPool(firms_driver)
        print(f"Checking membership and role for user_id: {user_id}, firm_id: {firm_id}")
        is_member, is_admin_or_owner = firms_pool.retry_operation_sync(
            lambda s: check_membership_and_role(s, requesting_user_id, firm_id)
        )

        if not is_member: 
            print(f"Access denied: User {user_id} is not a member of firm {firm_id}")
            raise AuthError("User is not a member of the specified firm.")
        
        print(f"User {user_id} membership verified. Admin/Owner: {is_admin_or_owner}")

        tasks_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_TASKS"], os.environ["YDB_DATABASE_TASKS"])
        print(f"Connected to tasks database")
        tasks_pool = ydb.SessionPool(tasks_driver)
        table_name = f"tasks_{firm_id}"

        def task_transaction_router(session):
            task_id = data.get('task_id')
            payload = data.get('payload', {})
            page = data.get('page')
            get_dated_tasks = data.get('get_dated_tasks', False)
            month = data.get('month')
            year = data.get('year')
            client_id = data.get('client_id') # Новый параметр

            print(f"Routing to action handler: {action}")
            if action == "GET":
                print(f"Executing GET action for user_id: {user_id}")
                # Передаем client_id в функцию get_task
                result = get_task(session, table_name, task_id, page, get_dated_tasks, month, year, client_id)
                print(f"GET action completed successfully for user_id: {user_id}")
                return result
            
            elif action == "UPSERT":
                print(f"Executing UPSERT action for user_id: {user_id}")
                # ИЗМЕНЕНИЕ: Передаем firm_id в функцию upsert_task
                result = upsert_task(session, table_name, payload, task_id, requesting_user_id, is_admin_or_owner, token, firm_id)
                print(f"UPSERT action completed successfully for user_id: {user_id}")
                return result
            
            elif action == "DELETE":
                print(f"Executing DELETE action for user_id: {user_id}")
                result = delete_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner, token)
                print(f"DELETE action completed successfully for user_id: {user_id}")
                return result
            
            else:
                raise LogicError(f"Invalid action.")

        return tasks_pool.retry_operation_sync(task_transaction_router)

    except AuthError as e:
        print(f"AUTH ERROR for user_id: {user_id}, action: {action}, firm_id: {firm_id} - {str(e)}")
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        print(f"LOGIC ERROR for user_id: {user_id}, action: {action}, firm_id: {firm_id} - {str(e)}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        print(f"NOT FOUND ERROR for user_id: {user_id}, action: {action}, firm_id: {firm_id} - {str(e)}")
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        print(f"UNEXPECTED ERROR for user_id: {user_id}, action: {action}, firm_id: {firm_id} - {str(e)}")
        logging.error(f"Error processing task request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```
#### get.py
```python
import json
import ydb
import math
import datetime
import calendar
from custom_errors import NotFoundError, LogicError

PAGE_SIZE = 100

def get_task(session, table_name, task_id=None, page=0, get_dated_tasks=False, month=None, year=None, client_id=None):
    """
    Обрабатывает GET запросы с опциональной фильтрацией по client_id.
    """
    print(f"GET_TASK: Starting with params - task_id: {task_id}, page: {page}, get_dated_tasks: {get_dated_tasks}, month: {month}, year: {year}, client_id: {client_id}")
    
    tx = session.transaction(ydb.SerializableReadWrite())
    
    if task_id:
        print(f"GET_TASK: Fetching specific task with id: {task_id}")
        query_text = f"DECLARE $task_id AS Utf8; SELECT * FROM `{table_name}` WHERE task_id = $task_id;"
        res = tx.execute(session.prepare(query_text), {"$task_id": task_id})
        if not res[0].rows:
            print(f"GET_TASK: Task with id {task_id} not found")
            raise NotFoundError(f"Task with id {task_id} not found.")
        data = {c.name: res[0].rows[0][c.name] for c in res[0].columns}
        tx.commit()
        print(f"GET_TASK: Successfully retrieved task {task_id}")
        return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}

    params = {}
    declare_clauses = []
    where_clauses = []
    
    if client_id:
        declare_clauses.append("DECLARE $client_id_str AS Utf8;")
        where_clauses.append("String::Contains(CAST(client_ids_json AS String), $client_id_str)")
        params["$client_id_str"] = f'"{client_id}"'
    
    if get_dated_tasks:
        if not all([month, year]):
            raise LogicError("`month` and `year` are required when `get_dated_tasks` is true.")
        
        try:
            _, num_days = calendar.monthrange(year, month)
            start_date = datetime.datetime(year, month, 1, tzinfo=datetime.timezone.utc)
            end_date = datetime.datetime(year, month, num_days, 23, 59, 59, 999999, tzinfo=datetime.timezone.utc)
        except (ValueError, TypeError):
            raise LogicError("Invalid month or year provided.")
        
        declare_clauses.extend(["DECLARE $start_date AS Timestamp;", "DECLARE $end_date AS Timestamp;"])
        where_clauses.extend(["due_date IS NOT NULL", "due_date >= $start_date", "due_date <= $end_date"])
        params.update({"$start_date": start_date, "$end_date": end_date})
        
        full_where_clause = " AND ".join(where_clauses) if where_clauses else "1=1"
        full_declare_clause = " ".join(declare_clauses)
        
        query_text = f"{full_declare_clause} SELECT * FROM `{table_name}` WHERE {full_where_clause} ORDER BY due_date ASC;"
        res = tx.execute(session.prepare(query_text), params)
        data = [{c.name: r[c.name] for c in res[0].columns} for r in res[0].rows]
        tx.commit()
        return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}

    where_clauses.append("due_date IS NULL")
    full_where_clause = " AND ".join(where_clauses)
    
    try:
        page = int(page or 0)
        if page < 0: page = 0
    except (ValueError, TypeError):
        page = 0
        
    print(f"GET_TASK: Processing paginated undated tasks. Page: {page}, client_id filter: {client_id}")
    
    count_declare_clause = " ".join(declare_clauses)
    count_query_text = f"{count_declare_clause} SELECT COUNT(task_id) AS total_count FROM `{table_name}` WHERE {full_where_clause};"
    print(f"GET_TASK: Executing count query: {count_query_text}")
    count_res = tx.execute(session.prepare(count_query_text), params)
    total_tasks = count_res[0].rows[0].total_count if count_res[0].rows else 0
    print(f"GET_TASK: Total undated tasks found: {total_tasks}")
    
    if total_tasks == 0:
        metadata = {"total_tasks": 0, "current_page": 0, "page_size": PAGE_SIZE, "total_pages": 0}
        tx.commit()
        print(f"GET_TASK: No undated tasks found, returning empty result")
        return {"statusCode": 200, "body": json.dumps({"metadata": metadata, "data": []})}

    total_pages = math.ceil(total_tasks / PAGE_SIZE)
    if page >= total_pages:
        print(f"GET_TASK: Page {page} exceeds total pages {total_pages}")
        raise NotFoundError(f"Page {page} does not exist. Total pages: {total_pages}.")

    offset = page * PAGE_SIZE
    print(f"GET_TASK: Calculating pagination - offset: {offset}, limit: {PAGE_SIZE}")
    
    declare_clauses.extend(["DECLARE $limit AS Uint64;", "DECLARE $offset AS Uint64;"])
    params.update({"$limit": PAGE_SIZE, "$offset": offset})
    
    full_declare_clause = " ".join(declare_clauses)
    
    select_query_text = f"""
        {full_declare_clause}
        SELECT * FROM `{table_name}`
        WHERE {full_where_clause}
        ORDER BY created_at DESC
        LIMIT $limit OFFSET $offset;
    """
    print(f"GET_TASK: Executing paginated select query")
    select_res = tx.execute(session.prepare(select_query_text), params)
    data = [{c.name: r[c.name] for c in select_res[0].columns} for r in select_res[0].rows]
    print(f"GET_TASK: Retrieved {len(data)} undated tasks for page {page}")
    
    metadata = {"total_tasks": total_tasks, "current_page": page, "page_size": PAGE_SIZE, "total_pages": total_pages}
    
    tx.commit()
    print(f"GET_TASK: Successfully completed paginated undated tasks query")
    return {"statusCode": 200, "body": json.dumps({"metadata": metadata, "data": data}, default=str)}
```
#### upsert.py
```python
import json, os, uuid, datetime, pytz, logging
from datetime import timedelta
import ydb
from custom_errors import LogicError, AuthError, NotFoundError
import reminders_processor

def json_datetime_serializer(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime.datetime, datetime.date)):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")

def _get_declare_for_task(payload):
    declare_clauses, params = "", {}
    type_map = {
        'task_id': ydb.PrimitiveType.Utf8, 
        'title': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'description': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'client_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'assignee_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'observer_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'creator_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'status': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'priority': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'due_date': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 
        'completed_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 
        'attachments_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'checklist_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'reminders_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'recurrence_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'options_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'holiday_transfer_rule': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'origin_task_id': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'is_system_task': ydb.OptionalType(ydb.PrimitiveType.Bool), 
        'created_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 
        'updated_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
        'periodic_parent_id': ydb.OptionalType(ydb.PrimitiveType.Utf8) # Добавлено: периодическая родительская задача
    }
    for key, value in payload.items():
        if key in type_map:
            if isinstance(type_map[key], ydb.OptionalType) and type_map[key].item == ydb.PrimitiveType.Timestamp and isinstance(value, str):
                try: params[f"${key}"] = datetime.datetime.strptime(value.replace('Z', ''), "%Y-%m-%dT%H:%M:%S").replace(tzinfo=pytz.utc)
                except (ValueError, TypeError): raise LogicError(f"Invalid timestamp format for '{key}'. Use YYYY-MM-DDTHH:MM:SSZ.")
            else: params[f"${key}"] = value
            type_name_str = str(type_map[key]); type_name = type_name_str.replace("Optional[", "").replace("]", "") if "Optional" in type_name_str else type_name_str; declare_clauses += f"DECLARE ${key} AS {type_name}; "
    return declare_clauses, params

def _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
    print(f"PERMISSIONS: Checking modify permissions for task {task_id}, user {requesting_user_id}, admin/owner: {is_admin_or_owner}")
    if is_admin_or_owner: 
        print(f"PERMISSIONS: User {requesting_user_id} has admin/owner access to task {task_id}")
        return True
    
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    res = session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(f"DECLARE $task_id AS Utf8; SELECT creator_ids_json, assignee_ids_json, options_json FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    if not res[0].rows: 
        print(f"PERMISSIONS: Task {task_id} not found when checking permissions")
        raise NotFoundError(f"Task with id {task_id} not found.")
    
    task_data = res[0].rows[0]
    creator_ids = json.loads(task_data.get('creator_ids_json', '[]') or '[]')
    assignee_ids = json.loads(task_data.get('assignee_ids_json', '[]') or '[]')
    options = json.loads(task_data.get('options_json', '{}') or '{}')
    
    print(f"PERMISSIONS: Task {task_id} - creators: {len(creator_ids)}, assignees: {len(assignee_ids)}")
    
    if requesting_user_id in creator_ids: 
        print(f"PERMISSIONS: User {requesting_user_id} is creator of task {task_id}")
        return True
    
    if requesting_user_id in assignee_ids:
        allow_assignee_edit = options.get('allow_assignee_to_edit', False)
        print(f"PERMISSIONS: User {requesting_user_id} is assignee of task {task_id}, allow_assignee_to_edit: {allow_assignee_edit}")
        if allow_assignee_edit: 
            return True
    
    print(f"PERMISSIONS: User {requesting_user_id} has no modify permissions for task {task_id}")
    return False

# ИСПРАВЛЕНО: Добавлен 'session' в аргументы
def _update_main_task_subtasks(session, tx, table_name, main_task_id, subtask_id, action='add'):
    print(f"UPDATE_SUBTASKS: Starting {action} operation for main_task {main_task_id}, subtask {subtask_id}")
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    query = session.prepare(f"DECLARE $task_id AS Utf8; SELECT subtasks_json FROM `{table_name}` WHERE task_id = $task_id;")
    res = tx.execute(query, {"$task_id": main_task_id})

    if not res[0].rows:
        print(f"UPDATE_SUBTASKS: Main task {main_task_id} not found when trying to update subtasks")
        logging.warning(f"Main task {main_task_id} not found when trying to update subtasks.")
        return
    
    subtasks = json.loads(res[0].rows[0].subtasks_json or '[]')
    print(f"UPDATE_SUBTASKS: Current subtasks for main_task {main_task_id}: {len(subtasks)} items")
    
    if action == 'add' and subtask_id not in subtasks:
        subtasks.append(subtask_id)
        print(f"UPDATE_SUBTASKS: Added subtask {subtask_id} to main_task {main_task_id}")
    elif action == 'remove' and subtask_id in subtasks:
        subtasks.remove(subtask_id)
        print(f"UPDATE_SUBTASKS: Removed subtask {subtask_id} from main_task {main_task_id}")
    else: 
        print(f"UPDATE_SUBTASKS: No changes needed for main_task {main_task_id}, subtask {subtask_id}")
        return
    
    new_subtasks_json = json.dumps(subtasks)
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    update_query = session.prepare(f"DECLARE $task_id AS Utf8; DECLARE $subtasks_json AS Json; UPDATE `{table_name}` SET subtasks_json = $subtasks_json WHERE task_id = $task_id;")
    tx.execute(update_query, {"$task_id": main_task_id, "$subtasks_json": new_subtasks_json})
    print(f"UPDATE_SUBTASKS: Successfully updated subtasks for main_task {main_task_id}, new count: {len(subtasks)}")

# ИСПРАВЛЕНО: Добавлен 'session' в аргументы
def _update_periodic_parent_children(session, tx, table_name, parent_id, child_id, action='add'):
    print(f"UPDATE_PERIODIC_CHILDREN: Starting {action} operation for parent {parent_id}, child {child_id}")
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    query = session.prepare(f"DECLARE $task_id AS Utf8; SELECT periodic_children_json FROM `{table_name}` WHERE task_id = $task_id;")
    res = tx.execute(query, {"$task_id": parent_id})

    if not res[0].rows:
        print(f"UPDATE_PERIODIC_CHILDREN: Periodic parent task {parent_id} not found when trying to update children")
        logging.warning(f"Periodic parent task {parent_id} not found when trying to update children.")
        return
    
    children = json.loads(res[0].rows[0].periodic_children_json or '[]')
    print(f"UPDATE_PERIODIC_CHILDREN: Current children for parent {parent_id}: {len(children)} items")
    
    if action == 'add' and child_id not in children:
        children.append(child_id)
        print(f"UPDATE_PERIODIC_CHILDREN: Added child {child_id} to parent {parent_id}")
    elif action == 'remove' and child_id in children:
        children.remove(child_id)
        print(f"UPDATE_PERIODIC_CHILDREN: Removed child {child_id} from parent {parent_id}")
    else: 
        print(f"UPDATE_PERIODIC_CHILDREN: No changes needed for parent {parent_id}, child {child_id}")
        return
    
    new_children_json = json.dumps(children)
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    update_query = session.prepare(f"DECLARE $task_id AS Utf8; DECLARE $periodic_children_json AS Json; UPDATE `{table_name}` SET periodic_children_json = $periodic_children_json WHERE task_id = $task_id;")
    tx.execute(update_query, {"$task_id": parent_id, "$periodic_children_json": new_children_json})
    print(f"UPDATE_PERIODIC_CHILDREN: Successfully updated children for parent {parent_id}, new count: {len(children)}")


def upsert_task(session, table_name, payload, task_id, requesting_user_id, is_admin_or_owner, user_jwt, firm_id):
    print(f"UPSERT_TASK: Starting upsert operation for task_id: {task_id}, user: {requesting_user_id}")
    print(f"UPSERT_TASK: Payload keys: {list(payload.keys()) if payload else 'None'}")
    
    # ИСПРАВЛЕНО: Добавлен firm_id в сигнатуру вложенной функции
    def manage_scheduler_event(current_task_id, new_payload, old_recurrence_json_str, user_jwt_token, db_session, db_table_name, firm_id_for_child):
        print(f"UPSERT_TASK: Managing scheduler event for task {current_task_id}")
        new_recurrence_json_str = new_payload.get('recurrence_json')
        print(f"UPSERT_TASK: Old recurrence: {bool(old_recurrence_json_str)}, New recurrence: {bool(new_recurrence_json_str)}")
        
        if new_recurrence_json_str and new_recurrence_json_str != 'null':
            print(f"UPSERT_TASK: Creating/updating scheduler event for task {current_task_id}")
            logging.info(f"[{current_task_id}] Upserting scheduler event due to new/modified recurrence rule.")
            
            try:
                recurrence_data = json.loads(new_recurrence_json_str)
                is_annual = recurrence_data.get('is_annual', False)
                execution_dates_json = recurrence_data.get('execution_dates_json', [])
                print(f"UPSERT_TASK: Parsed recurrence data for task {current_task_id}: is_annual={is_annual}, dates_count={len(execution_dates_json)}")
                
                if not execution_dates_json and not is_annual:
                     logging.warning(f"[{current_task_id}] No execution dates provided. Aborting scheduler event creation.")
                     print(f"UPSERT_TASK: Warning - No execution dates for task {current_task_id}")
                     return

            except (json.JSONDecodeError, TypeError) as e:
                logging.error(f"[{current_task_id}] Invalid recurrence_json format: {e}")
                print(f"UPSERT_TASK: Error parsing recurrence data for task {current_task_id}: {e}")
                return
            
            child_task_payload = new_payload.copy()
            child_task_payload.pop('task_id', None); child_task_payload.pop('recurrence_json', None); child_task_payload.pop('periodic_children_json', None); child_task_payload.pop('created_at', None); child_task_payload.pop('updated_at', None)

            # Наследование полей от родительской задачи
            fields_to_inherit = [
                'creator_ids_json', 'assignee_ids_json', 'observer_ids_json',
                'description', 'attachments_json', 'title',
                'client_ids_json', 'priority', 'checklist_json',
                'options_json', 'holiday_transfer_rule', 'is_system_task'
            ]
            for field in fields_to_inherit:
                if field in new_payload:
                    child_task_payload[field] = new_payload[field]

            child_task_payload['periodic_parent_id'] = current_task_id
            print(f"UPSERT_TASK: Prepared child task payload for task {current_task_id}")

            scheduler_payload = {
                "action": "UPSERT",
                "payload": {
                    "function_id": os.environ.get("FUNCTION_ID_EDIT_TASK"),
                    "custom_identifier": current_task_id,
                    "is_annual": is_annual,
                    "execution_dates_json": json.dumps(execution_dates_json),
                    "request_body_json": json.dumps({
                        "action": "UPSERT",
                        "firm_id": firm_id_for_child,
                        "payload": child_task_payload
                    }, default=json_datetime_serializer),
                    "request_headers_json": json.dumps({"Authorization": f"Bearer {user_jwt_token}"}),
                    "is_active": True
                }
            }
            
            try:
                print(f"UPSERT_TASK: Invoking scheduler-manager for task {current_task_id}")
                logging.info(f"[{current_task_id}] Invoking scheduler-manager to upsert event.")
                scheduler_response = reminders_processor.invoke_utils.invoke_function(
                    function_id=os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER"),
                    payload=scheduler_payload,
                    user_jwt=user_jwt_token
                )
                print(f"UPSERT_TASK: Successfully created/updated scheduler event for task {current_task_id}")
                logging.info(f"[{current_task_id}] Successfully upserted scheduler event.")
                
                # ИСПРАВЛЕНО: Корректно парсим ответ и извлекаем event_id
                if scheduler_response and scheduler_response.get('statusCode') == 201 and scheduler_response.get('body'):
                    try:
                        response_body = json.loads(scheduler_response['body'])
                        event_id = response_body.get('event_id')
                    except (json.JSONDecodeError, TypeError):
                        event_id = None
                        logging.warning(f"[{current_task_id}] Could not parse event_id from scheduler response body.")
                    
                    if event_id:
                        print(f"UPSERT_TASK: Saving event_id {event_id} to recurrence_json for task {current_task_id}")
                        logging.info(f"[{current_task_id}] Saving event_id {event_id} to recurrence_json.")
                        
                        updated_recurrence_data = recurrence_data.copy()
                        updated_recurrence_data['event_id'] = event_id
                        updated_recurrence_json = json.dumps(updated_recurrence_data)
                        
                        update_tx = db_session.transaction(ydb.SerializableReadWrite())
                        update_tx.execute(
                            db_session.prepare(f"DECLARE $task_id AS Utf8; DECLARE $recurrence_json AS Json; UPDATE `{db_table_name}` SET recurrence_json = $recurrence_json WHERE task_id = $task_id;"),
                            {"$task_id": current_task_id, "$recurrence_json": updated_recurrence_json}
                        )
                        update_tx.commit()
                        print(f"UPSERT_TASK: Successfully saved event_id {event_id} to recurrence_json for task {current_task_id}")
                        logging.info(f"[{current_task_id}] Successfully saved event_id {event_id} to recurrence_json.")

            except Exception as e:
                error_details = str(e);_ = getattr(e, 'details', None); error_details = _ if _ else error_details
                print(f"UPSERT_TASK: Warning - Failed to manage scheduler event for task {current_task_id}: {error_details}")
                logging.error(f"[{current_task_id}] Failed to upsert scheduler event. Details: {error_details}", exc_info=True)

        elif (old_recurrence_json_str and old_recurrence_json_str != 'null') and (not new_recurrence_json_str or new_recurrence_json_str == 'null'):
            print(f"UPSERT_TASK: Recurrence removed for task {current_task_id}, attempting to delete scheduler event")
            logging.info(f"[{current_task_id}] Recurrence removed, attempting to delete scheduler event.")
            
            try:
                old_recurrence_data = json.loads(old_recurrence_json_str)
                event_id = old_recurrence_data.get('event_id')
                
                if event_id:
                    print(f"UPSERT_TASK: Found event_id {event_id} in old recurrence_json for task {current_task_id}")
                    logging.info(f"[{current_task_id}] Found event_id {event_id} in old recurrence_json, deleting event.")
                    
                    delete_payload = {"action": "DELETE", "event_id": event_id}
                    reminders_processor.invoke_utils.invoke_function(
                        function_id=os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER"),
                        payload=delete_payload,
                        user_jwt=user_jwt_token
                    )
                    print(f"UPSERT_TASK: Successfully deleted scheduler event {event_id} for task {current_task_id}")
                    logging.info(f"[{current_task_id}] Successfully deleted scheduler event {event_id}.")
                else:
                    print(f"UPSERT_TASK: No event_id found in old recurrence_json for task {current_task_id}")
                    logging.warning(f"[{current_task_id}] No event_id found in old recurrence_json, cannot delete scheduler event.")
                    
            except (json.JSONDecodeError, TypeError) as e:
                print(f"UPSERT_TASK: Error parsing old recurrence_json for task {current_task_id}: {e}")
                logging.error(f"[{current_task_id}] Error parsing old recurrence_json: {e}")
            except Exception as e:
                print(f"UPSERT_TASK: Error deleting scheduler event for task {current_task_id}: {e}")
                logging.error(f"[{current_task_id}] Error deleting scheduler event: {e}", exc_info=True)

    if not payload: raise LogicError("payload is required for UPSERT action.")
    tx = session.transaction(ydb.SerializableReadWrite())
    payload['updated_at'] = datetime.datetime.now(pytz.utc)

    main_task_id = payload.get('main_task_id')
    periodic_parent_id = payload.get('periodic_parent_id')
    original_main_task_id = None
    original_periodic_parent_id = None
    original_recurrence_json = None

    if task_id:
        # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
        res = tx.execute(session.prepare(f"DECLARE $task_id AS Utf8; SELECT main_task_id, periodic_parent_id, recurrence_json FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
        if res[0].rows:
            row = res[0].rows[0]
            original_main_task_id = row.main_task_id
            original_periodic_parent_id = row.periodic_parent_id
            original_recurrence_json = row.recurrence_json
        else:
            raise NotFoundError(f"Task with id {task_id} not found.")

        if not _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
             raise AuthError("You do not have permission to modify this task.")

        payload['task_id'] = task_id
        declare_clauses, params = _get_declare_for_task(payload)
        set_clauses = ", ".join([f"`{k}` = ${k}" for k in payload if k != 'task_id'])
        # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
        tx.execute(session.prepare(f"{declare_clauses} UPDATE `{table_name}` SET {set_clauses} WHERE task_id = $task_id;"), params)

        # ИСПРАВЛЕНО: Передача 'session' в вызовы вспомогательных функций
        if main_task_id != original_main_task_id:
            if original_main_task_id: _update_main_task_subtasks(session, tx, table_name, original_main_task_id, task_id, action='remove')
            if main_task_id: _update_main_task_subtasks(session, tx, table_name, main_task_id, task_id, action='add')

        # ИСПРАВЛЕНО: Передача 'session' в вызовы вспомогательных функций
        if periodic_parent_id != original_periodic_parent_id:
            if original_periodic_parent_id: _update_periodic_parent_children(session, tx, table_name, original_periodic_parent_id, task_id, action='remove')
            if periodic_parent_id: _update_periodic_parent_children(session, tx, table_name, periodic_parent_id, task_id, action='add')

        tx.commit()
        print(f"UPSERT_TASK: Task {task_id} updated successfully in database")

        try:
            print(f"UPSERT_TASK: Managing scheduler events for updated task {task_id}")
            manage_scheduler_event(task_id, payload, original_recurrence_json, user_jwt, session, table_name, firm_id)
            print(f"UPSERT_TASK: Scheduler events managed successfully for task {task_id}")
        except Exception as e:
            print(f"UPSERT_TASK: Error managing scheduler event for task {task_id}: {e}")
            logging.error(f"[{task_id}] Error managing scheduler event after update: {e}", exc_info=True)

        try:
            if 'reminders_json' in payload:
                print(f"UPSERT_TASK: Syncing reminders for updated task {task_id}")
                reminders_processor.sync_reminders(task_id, payload, user_jwt)
                print(f"UPSERT_TASK: Reminders synced successfully for task {task_id}")
        except Exception as e:
            print(f"UPSERT_TASK: Error syncing reminders for task {task_id}: {e}")
            logging.error(f"[{task_id}] Error syncing reminders after update: {e}", exc_info=True)
        
        print(f"UPSERT_TASK: Task update operation completed successfully for task {task_id}")
        return {"statusCode": 200, "body": json.dumps({"message": "Task updated", "task_id": task_id})}
    else: # Create new task
        new_task_id = str(uuid.uuid4())
        payload['task_id'] = new_task_id
        payload['created_at'] = payload['updated_at']
        if 'creator_ids_json' not in payload: payload['creator_ids_json'] = json.dumps([requesting_user_id])
        if 'is_system_task' not in payload: payload['is_system_task'] = False
        
        declare_clauses, params = _get_declare_for_task(payload)
        columns = ", ".join([f"`{k}`" for k in payload.keys()]); placeholders = ", ".join([f"${k}" for k in payload.keys()])
        # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
        tx.execute(session.prepare(f"{declare_clauses} UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"), params)

        # ИСПРАВЛЕНО: Передача 'session' в вызовы вспомогательных функций
        if main_task_id: _update_main_task_subtasks(session, tx, table_name, main_task_id, new_task_id, action='add')
        if periodic_parent_id: _update_periodic_parent_children(session, tx, table_name, periodic_parent_id, new_task_id, action='add')

        tx.commit()
        print(f"UPSERT_TASK: New task {new_task_id} created successfully in database")

        try:
            print(f"UPSERT_TASK: Managing scheduler events for new task {new_task_id}")
            manage_scheduler_event(new_task_id, payload, None, user_jwt, session, table_name, firm_id)
            print(f"UPSERT_TASK: Scheduler events managed successfully for task {new_task_id}")
        except Exception as e:
            print(f"UPSERT_TASK: Error managing scheduler event for task {new_task_id}: {e}")
            logging.error(f"[{new_task_id}] Error managing scheduler event after create: {e}", exc_info=True)

        try:
            if 'reminders_json' in payload:
                print(f"UPSERT_TASK: Syncing reminders for new task {new_task_id}")
                reminders_processor.sync_reminders(new_task_id, payload, user_jwt)
                print(f"UPSERT_TASK: Reminders synced successfully for task {new_task_id}")
        except Exception as e:
            print(f"UPSERT_TASK: Error syncing reminders for task {new_task_id}: {e}")
            logging.error(f"[{new_task_id}] Error syncing reminders after create: {e}", exc_info=True)
        
        print(f"UPSERT_TASK: Task creation operation completed successfully for task {new_task_id}")
        return {"statusCode": 201, "body": json.dumps({"message": "Task created", "task_id": new_task_id})}
```
#### delete.py
```python
import json, logging, os
import ydb
from custom_errors import LogicError, AuthError, NotFoundError
import reminders_processor
# Импортируем хелперы из upsert.py, они нам все еще нужны
from upsert import _update_main_task_subtasks, _update_periodic_parent_children

def _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
    print(f"DELETE_PERMISSIONS: Checking modify permissions for task {task_id}, user {requesting_user_id}, admin/owner: {is_admin_or_owner}")
    if is_admin_or_owner: 
        print(f"DELETE_PERMISSIONS: User {requesting_user_id} has admin/owner access to task {task_id}")
        return True
    
    res = session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(f"DECLARE $task_id AS Utf8; SELECT creator_ids_json, assignee_ids_json, options_json FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    if not res[0].rows: 
        print(f"DELETE_PERMISSIONS: Task {task_id} not found when checking permissions")
        raise NotFoundError(f"Task with id {task_id} not found.")
    
    task_data = res[0].rows[0]
    creator_ids = json.loads(task_data.get('creator_ids_json', '[]') or '[]')
    assignee_ids = json.loads(task_data.get('assignee_ids_json', '[]') or '[]')
    options = json.loads(task_data.get('options_json', '{}') or '{}')
    
    print(f"DELETE_PERMISSIONS: Task {task_id} - creators: {len(creator_ids)}, assignees: {len(assignee_ids)}")
    
    if requesting_user_id in creator_ids: 
        print(f"DELETE_PERMISSIONS: User {requesting_user_id} is creator of task {task_id}")
        return True
    
    if requesting_user_id in assignee_ids:
        allow_assignee_edit = options.get('allow_assignee_to_edit', False)
        print(f"DELETE_PERMISSIONS: User {requesting_user_id} is assignee of task {task_id}, allow_assignee_to_edit: {allow_assignee_edit}")
        if allow_assignee_edit: 
            return True
    
    print(f"DELETE_PERMISSIONS: User {requesting_user_id} has no modify permissions for task {task_id}")
    return False

def delete_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner, user_jwt):
    print(f"DELETE_TASK: Starting deletion process for task_id: {task_id}, user: {requesting_user_id}")
    
    if not task_id: 
        print(f"DELETE_TASK: Error - task_id is required")
        raise LogicError("task_id is required for DELETE action.")
    
    print(f"DELETE_TASK: Checking deletion permissions for task {task_id}")
    if not _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner): 
        print(f"DELETE_TASK: Permission denied for user {requesting_user_id} to delete task {task_id}")
        raise AuthError("You do not have permission to delete this task.")
    print(f"DELETE_TASK: Permission granted for task {task_id} deletion")
    
    # Сначала всегда удаляем напоминания, эта логика не меняется.
    print(f"DELETE_TASK: Deleting reminders for task {task_id}")
    try:
        reminders_processor.sync_reminders(task_id, {"reminders_json": "[]"}, user_jwt)
        print(f"DELETE_TASK: Successfully deleted reminders for task {task_id}")
    except Exception as e:
        print(f"DELETE_TASK: Error deleting reminders for task {task_id}: {e}")
        logging.error(f"[{task_id}] Error deleting reminders before task removal: {e}", exc_info=True)

    tx = session.transaction(ydb.SerializableReadWrite())
    
    # Получаем информацию о задаче, включая recurrence_json для извлечения event_id
    print(f"DELETE_TASK: Fetching task data from database for task {task_id}")
    res = tx.execute(
        session.prepare(f"DECLARE $task_id AS Utf8; SELECT main_task_id, periodic_parent_id, periodic_children_json, recurrence_json FROM `{table_name}` WHERE task_id = $task_id;"), 
        {"$task_id": task_id}
    )

    if res[0].rows:
        task_data = res[0].rows[0]
        main_task_id = task_data.main_task_id
        periodic_parent_id = task_data.periodic_parent_id
        periodic_children_str = task_data.periodic_children_json
        recurrence_json_str = task_data.recurrence_json
        is_periodic_parent = periodic_children_str and json.loads(periodic_children_str)
        
        print(f"DELETE_TASK: Task data retrieved - main_task_id: {main_task_id}, periodic_parent_id: {periodic_parent_id}, is_periodic_parent: {bool(is_periodic_parent)}, has_recurrence: {bool(recurrence_json_str)}")

        # Если задача имеет recurrence_json, пытаемся удалить соответствующее событие планировщика
        if recurrence_json_str and recurrence_json_str != 'null':
            print(f"DELETE_TASK: Task {task_id} has recurrence_json, attempting to delete scheduler event")
            logging.info(f"[{task_id}] Task has recurrence_json, attempting to delete scheduler event.")
            
            try:
                recurrence_data = json.loads(recurrence_json_str)
                event_id = recurrence_data.get('event_id')
                
                if event_id:
                    print(f"DELETE_TASK: Found event_id {event_id} in recurrence_json for task {task_id}")
                    logging.info(f"[{task_id}] Found event_id {event_id} in recurrence_json, deleting scheduler event.")
                    
                    delete_event_payload = {
                        "action": "DELETE",
                        "event_id": event_id
                    }
                    
                    reminders_processor.invoke_utils.invoke_function(
                        function_id=os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER"),
                        payload=delete_event_payload,
                        user_jwt=user_jwt
                    )
                    print(f"DELETE_TASK: Successfully deleted scheduler event {event_id} for task {task_id}")
                    logging.info(f"[{task_id}] Successfully deleted scheduler event {event_id}.")
                else:
                    print(f"DELETE_TASK: No event_id found in recurrence_json for task {task_id}")
                    logging.warning(f"[{task_id}] No event_id found in recurrence_json, cannot delete scheduler event.")
                    
            except (json.JSONDecodeError, TypeError) as e:
                print(f"DELETE_TASK: Error parsing recurrence_json for task {task_id}: {e}")
                logging.error(f"[{task_id}] Error parsing recurrence_json: {e}")
            except Exception as e:
                print(f"DELETE_TASK: Error deleting scheduler event for task {task_id}: {e}")
                logging.error(f"[{task_id}] Failed to delete scheduler event, but continuing with task deletion. Error: {e}", exc_info=True)

        # Обновляем связи для подзадач и родительских периодических задач (если применимо)
        if main_task_id:
            print(f"DELETE_TASK: Removing task {task_id} from subtasks of main task {main_task_id}")
            _update_main_task_subtasks(tx, table_name, main_task_id, task_id, action='remove')
        
        if periodic_parent_id:
            print(f"DELETE_TASK: Removing task {task_id} from children of periodic parent {periodic_parent_id}")
            _update_periodic_parent_children(tx, table_name, periodic_parent_id, task_id, action='remove')

    # Удаляем саму задачу из базы данных задач
    print(f"DELETE_TASK: Deleting task {task_id} from database table {table_name}")
    tx.execute(session.prepare(f"DECLARE $task_id AS Utf8; DELETE FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    
    tx.commit()
    print(f"DELETE_TASK: Successfully deleted task {task_id} from database")
    print(f"DELETE_TASK: Task deletion operation completed successfully for task {task_id}")
    return {"statusCode": 200, "body": json.dumps({"message": "Task and all associated reminders deleted"})}
```
#### custom_errors.py
```python
# ФАЙЛ БЕЗ ИЗМЕНЕНИЙ
class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass
class FunctionInvokeError(Exception): pass
```
#### invoke_utils.py
```python
# utils/invoke_utils.py

import os
import json
import logging
import time
import urllib.request
import urllib.error

# Настраиваем логирование, чтобы видеть информационные сообщения
logging.basicConfig(level=logging.INFO)

IAM_TOKEN_CACHE = {"token": None, "expires_at": 0}
METADATA_URL = "http://169.254.169.254/computeMetadata/v1/instance/service-accounts/default/token"

def _get_iam_token() -> str:
    """Возвращает актуальный IAM-токен, кешируя его."""
    now_ts = time.time()
    if IAM_TOKEN_CACHE["token"] and now_ts < IAM_TOKEN_CACHE["expires_at"]:
        return IAM_TOKEN_CACHE["token"]
    try:
        req = urllib.request.Request(METADATA_URL, headers={"Metadata-Flavor": "Google"})
        with urllib.request.urlopen(req, timeout=2) as resp:
            data = json.loads(resp.read())
            IAM_TOKEN_CACHE["token"] = data["access_token"]
            IAM_TOKEN_CACHE["expires_at"] = now_ts + data.get("expires_in", 300) - 60
            return IAM_TOKEN_CACHE["token"]
    except Exception as e:
        logging.error(f"Не удалось получить IAM-токен: {e}", exc_info=True)
        return ""

class FunctionInvokeError(Exception):
    """Исключение для ошибок при вызове облачных функций."""
    def __init__(self, message, status_code=None, details=None):
        super().__init__(message)
        self.status_code = status_code
        self.details = details

def invoke_function(function_id: str, payload: dict, user_jwt: str) -> dict:
    """
    Вызывает облачную функцию и возвращает ее ответ в виде словаря,
    имитирующего стандартный ответ Yandex Cloud Function.
    """
    if not function_id:
        raise FunctionInvokeError("ID функции не указан для вызова")

    function_url = f"https://functions.yandexcloud.net/{function_id}"
    iam_token = _get_iam_token()
    if not iam_token:
        raise FunctionInvokeError("Не удалось получить IAM-токен для вызова функции")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {iam_token}",
        "X-Forwarded-Authorization": f"Bearer {user_jwt}"
    }

    req_data = json.dumps(payload).encode('utf-8')
    req = urllib.request.Request(function_url, data=req_data, headers=headers, method="POST")

    try:
        with urllib.request.urlopen(req, timeout=15) as resp:
            # Читаем тело ответа и декодируем его в строку
            resp_body_str = resp.read().decode('utf-8')
            
            # --- ФИНАЛЬНОЕ ИСПРАВЛЕНИЕ ---
            # Собираем словарь, который имитирует стандартный ответ функции.
            # Теперь вызывающий код получит именно то, что ожидает.
            response_dict = {
                "statusCode": resp.status,
                "body": resp_body_str
            }
            # --- КОНЕЦ ИСПРАВЛЕНИЯ ---
            
            logging.info(f"Successfully invoked function {function_id}, status: {resp.status}")
            return response_dict

    except urllib.error.HTTPError as e:
        details = e.read().decode(errors='ignore')
        logging.error(f"Ошибка вызова {function_id}. Статус: {e.code}. Детали: {details[:500]}")
        raise FunctionInvokeError(f"Ошибка вызова функции {function_id}", status_code=e.code, details=details)
    except Exception as e:
        logging.error(f"Исключение при вызове {function_id}: {e}", exc_info=True)
        raise FunctionInvokeError(f"Непредвиденное исключение при вызове {function_id}: {e}")
```

#### reminders_processor.py
```python
import os, json, logging
import invoke_utils

FUNCTION_ID_SCHEDULER_MANAGER = os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER")
FUNCTION_ID_ENDPOINTS_MANAGER = os.environ.get("FUNCTION_ID_ENDPOINTS_MANAGER")

def _get_existing_reminders(task_id: str, user_jwt: str) -> dict: 
     print(f"REMINDERS: Fetching existing reminders from scheduler for task {task_id}") 
     payload = {"action": "GET", "custom_identifier": task_id} 
     try: 
         response = invoke_utils.invoke_function(FUNCTION_ID_SCHEDULER_MANAGER, payload, user_jwt) 
         print(f"REMINDERS: Received response from scheduler for task {task_id}") 
     except invoke_utils.FunctionInvokeError as e: 
         if e.status_code == 404: 
             print(f"REMINDERS: No existing events found for task {task_id} (404)") 
             return {} 
         print(f"REMINDERS: Error fetching existing reminders for task {task_id}: {e}") 
         raise e 
 
     existing = {} 
     
     # --- НАЧАЛО ИСПРАВЛЕНИЙ --- 
     # Корректно парсим тело ответа, которое является JSON-строкой 
     events = [] 
     if response and response.get("statusCode") == 200 and response.get("body"): 
         try: 
             response_data = json.loads(response["body"]) 
             events = response_data.get("data", []) 
         except (json.JSONDecodeError, TypeError): 
             print(f"REMINDERS: Could not parse JSON from scheduler response body for task {task_id}") 
             events = [] 
     # --- КОНЕЦ ИСПРАВЛЕНИЙ --- 
 
     if not isinstance(events, list): 
         events = [events] 
     
     print(f"REMINDERS: Processing {len(events)} events for task {task_id}") 
     for event in events: 
         try: 
             # Проверяем, что это событие - напоминание, а не периодическая задача 
             raw_body = event.get("request_body_json", "{}") 
             body = json.loads(raw_body or "{}") if isinstance(raw_body, str) else raw_body 
             if body.get("action") != "SEND": 
                 continue # Это не напоминание 
 
             raw_dates = event.get("execution_dates_json", "[]") 
             dates = json.loads(raw_dates or "[]") if isinstance(raw_dates, str) else raw_dates 
 
             user_id = body.get("user_id_to_notify") 
             if user_id and dates: 
                 existing[(dates[0], user_id)] = event["event_id"] 
                 print(f"REMINDERS: Found existing reminder for task {task_id}, user {user_id}, datetime {dates[0]}") 
         except (json.JSONDecodeError, KeyError, IndexError) as e: 
             print(f"REMINDERS: Error parsing event for task {task_id}: {e}") 
             continue 
     
     print(f"REMINDERS: Found {len(existing)} valid existing reminders for task {task_id}") 
     return existing

def _get_desired_reminders(task_payload: dict) -> set:
    print(f"REMINDERS: Calculating desired reminders from task payload")
    desired = set()
    try:
        reminders_str = task_payload.get("reminders_json", "[]")
        reminders = json.loads(reminders_str) if isinstance(reminders_str, str) else reminders_str
        print(f"REMINDERS: Found {len(reminders)} reminder definitions in payload")
        
        for i, rem in enumerate(reminders):
            role = rem.get("role")
            dt = rem.get("datetime")
            if not role or not dt:
                print(f"REMINDERS: Skipping reminder {i} - missing role or datetime")
                continue
            
            print(f"REMINDERS: Processing reminder {i} for role {role}, datetime {dt}")
            user_ids_str = task_payload.get(f"{role}_ids_json", "[]")
            user_ids = json.loads(user_ids_str) if isinstance(user_ids_str, str) else user_ids_str
            print(f"REMINDERS: Found {len(user_ids)} users for role {role}")
            
            for uid in user_ids:
                desired.add((dt, uid))
                print(f"REMINDERS: Added desired reminder for user {uid}, datetime {dt}")
    except (json.JSONDecodeError, TypeError) as e:
        print(f"REMINDERS: Error parsing reminders_json or user IDs: {e}")
        logging.error(f"Could not parse reminders_json or associated user IDs: {e}")
    
    print(f"REMINDERS: Calculated {len(desired)} total desired reminders")
    return desired

def sync_reminders(task_id: str, task_payload: dict, user_jwt: str):
    print(f"REMINDERS: Starting sync for task {task_id}")
    logging.info(f"[{task_id}] Синхронизация напоминаний…")
    try:
        print(f"REMINDERS: Getting existing reminders for task {task_id}")
        existing = _get_existing_reminders(task_id, user_jwt)
        print(f"REMINDERS: Found {len(existing)} existing reminders for task {task_id}")
        
        print(f"REMINDERS: Calculating desired reminders for task {task_id}")
        desired = _get_desired_reminders(task_payload)
        print(f"REMINDERS: Calculated {len(desired)} desired reminders for task {task_id}")
        
        to_delete = [eid for key, eid in existing.items() if key not in desired]
        to_create = [key for key in desired if key not in existing]

        print(f"REMINDERS: Task {task_id} - to create: {len(to_create)}, to delete: {len(to_delete)}")
        logging.info(f"[{task_id}] Reminders to create: {len(to_create)}, to delete: {len(to_delete)}")

        print(f"REMINDERS: Deleting {len(to_delete)} obsolete reminders for task {task_id}")
        for eid in to_delete:
            print(f"REMINDERS: Deleting reminder event {eid} for task {task_id}")
            invoke_utils.invoke_function(FUNCTION_ID_SCHEDULER_MANAGER, {"action": "DELETE", "event_id": eid}, user_jwt)
        
        print(f"REMINDERS: Creating {len(to_create)} new reminders for task {task_id}")
        title = task_payload.get("title", "Без названия")
        for dt, uid in to_create:
            print(f"REMINDERS: Creating reminder for task {task_id}, user {uid}, datetime {dt}")
            scheduler_payload = {
                "action": "UPSERT",
                "payload": {
                    "function_id": FUNCTION_ID_ENDPOINTS_MANAGER,
                    "custom_identifier": task_id,
                    "is_annual": False,
                    "execution_dates_json": json.dumps([dt]),
                    "request_body_json": json.dumps({
                        "action": "SEND", # Важно для фильтрации в _get_existing_reminders
                        "user_id_to_notify": uid,
                        "payload": {
                            "title": "Напоминание по задаче",
                            "body": f"Не забудьте про задачу: «{title}»"
                        }
                    }),
                    "request_headers_json": {"Authorization": f"Bearer {user_jwt}"},
                    "is_active": True
                }
            }
            invoke_utils.invoke_function(FUNCTION_ID_SCHEDULER_MANAGER, scheduler_payload, user_jwt)
            print(f"REMINDERS: Successfully created reminder for task {task_id}, user {uid}")
        
        print(f"REMINDERS: Sync completed successfully for task {task_id}")
        logging.info(f"[{task_id}] Синхронизация напоминаний завершена")
    except Exception as e:
        print(f"REMINDERS: Error syncing reminders for task {task_id}: {e}")
        logging.error(f"[{task_id}] Ошибка синхронизации напоминаний: {e}", exc_info=True)
```
