import 'package:flutter/material.dart';

// ------------------ Повторение задачи ------------------
class RecurrenceSection extends StatefulWidget {
  final String recurrenceType;
  final String recurrenceInterval;
  final Function(String) onRecurrenceTypeChanged;
  final Function(String) onRecurrenceIntervalChanged;
  final List<DateTime> executionDates;
  final Function(List<DateTime>) onExecutionDatesChanged;
  final bool isAnnual;
  final Function(bool) onIsAnnualChanged;

  const RecurrenceSection({
    super.key,
    required this.recurrenceType,
    required this.recurrenceInterval,
    required this.onRecurrenceTypeChanged,
    required this.onRecurrenceIntervalChanged,
    required this.executionDates,
    required this.onExecutionDatesChanged,
    required this.isAnnual,
    required this.onIsAnnualChanged,
  });

  @override
  State<RecurrenceSection> createState() => _RecurrenceSectionState();
}

class _RecurrenceSectionState extends State<RecurrenceSection> {
  late List<DateTime> _executionDates;
  late TimeOfDay _executionTime;

  @override
  void initState() {
    super.initState();
    _executionDates = List.from(widget.executionDates);
    _executionTime = widget.executionDates.isNotEmpty
        ? TimeOfDay(hour: widget.executionDates.first.hour, minute: widget.executionDates.first.minute)
        : const TimeOfDay(hour: 0, minute: 0);
  }

  Future<void> _selectExecutionTime() async {
    final picked = await showTimePicker(
      context: context,
      initialTime: _executionTime,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child ?? const SizedBox.shrink(),
        );
      },
    );
    if (picked != null && picked != _executionTime) {
      setState(() {
        _executionTime = picked;
        // обновляем время у всех выбранных дат
        _executionDates = _executionDates
            .map((d) => DateTime(d.year, d.month, d.day, _executionTime.hour, _executionTime.minute))
            .toList();
        widget.onExecutionDatesChanged(_executionDates);
      });
    }
  }

  void _handleDaySelection(int day, int calendarIndex) {
    setState(() {
      final month = calendarIndex + 1;
      final value = DateTime(DateTime.now().year, month, day, _executionTime.hour, _executionTime.minute);
      final exists = _executionDates.any((d) => d.month == month && d.day == day);
      if (exists) {
        _executionDates = _executionDates.where((d) => !(d.month == month && d.day == day)).toList();
      } else {
        _executionDates = [..._executionDates, value];
      }
      widget.onExecutionDatesChanged(_executionDates);
    });
  }

  Widget _buildMonthCalendar(String name, int index) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              width: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Text(name, textAlign: TextAlign.center, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
            ),
            Padding(
              padding: const EdgeInsets.all(4),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  childAspectRatio: 1,
                  crossAxisSpacing: 2,
                  mainAxisSpacing: 2,
                ),
                itemCount: 31,
                itemBuilder: (ctx, i) {
                  final day = i + 1;
                  final selected = _executionDates.any((d) => d.month == index + 1 && d.day == day);
                  return InkWell(
                    onTap: () => _handleDaySelection(day, index),
                    child: Container(
                      decoration: BoxDecoration(
                        color: selected ? Theme.of(context).colorScheme.primaryContainer : Colors.transparent,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: selected ? Theme.of(context).colorScheme.primary : Theme.of(context).dividerColor.withOpacity(0.4),
                          width: selected ? 2 : 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          day.toString(),
                          style: TextStyle(fontSize: 10, fontWeight: selected ? FontWeight.bold : FontWeight.normal, color: selected ? Theme.of(context).colorScheme.onPrimaryContainer : Theme.of(context).textTheme.bodyMedium?.color),
                        ),
                      ),
                    ),
                  );
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildYearlyCalendars() {
    const months = ['Январь','Февраль','Март','Апрель','Май','Июнь','Июль','Август','Сентябрь','Октябрь','Ноябрь','Декабрь'];
    return Column(children: [
      for (int row = 0; row < 3; row++) ...[
        Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
          for (int col = 0; col < 4; col++) _buildMonthCalendar(months[row*4+col], row*4+col),
        ]),
        if (row != 2) const SizedBox(height: 12),
      ],
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final isRepeating = widget.recurrenceType != 'none';
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      SwitchListTile(
        title: const Text('Задача повторяется'),
        value: isRepeating,
        onChanged: (v) => widget.onRecurrenceTypeChanged(v ? 'arbitrary' : 'none'),
      ),
      if (isRepeating) ...[
        const SizedBox(height: 8),
        Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 1024),
            child: InteractiveViewer(
              panEnabled: false,
              minScale: 1.0,
              maxScale: 1.5,
              child: _buildYearlyCalendars(),
            ),
          ),
        ),
        const SizedBox(height: 8),
        ListTile(
          title: const Text('Время исполнения'),
          subtitle: Text('${_executionTime.hour.toString().padLeft(2, '0')}:${_executionTime.minute.toString().padLeft(2, '0')}'),
          trailing: IconButton(
            icon: const Icon(Icons.access_time),
            onPressed: _selectExecutionTime,
          ),
        ),
        const SizedBox(height: 8),
        SwitchListTile(
          title: const Text('Повторять ежегодно'),
          value: widget.isAnnual,
          onChanged: widget.onIsAnnualChanged,
        ),
      ],
    ]);
  }

}
