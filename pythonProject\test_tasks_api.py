import requests
import json
import sys
from colorama import init, Fore, Style

# Инициализируем colorama (autoreset=True сбрасывает цвет после каждого print)
init(autoreset=True)

# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
API_TASKS_URL = "https://d5d7ct0sul274igh4vij.aqkd4clz.apigw.yandexcloud.net/manage"

FIRM_ID = "9a33483b-dfad-44a3-a36d-102b498ec0ef"
LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL


def run_test_step(title: str, url: str, payload: dict, headers: dict, expected_status: int):
    """Выполняет один шаг теста, выводит результат и возвращает ответ в случае успеха."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ")

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=15)

        if response.status_code == expected_status:
            print(f"{TICK} (Статус: {response.status_code})")
            return response
        else:
            print(f"{CROSS} (Ожидался статус {expected_status}, получен {response.status_code})")
            print(Fore.RED + "  Текст ошибки:")
            try:
                error_json = response.json()
                print(Fore.RED + json.dumps(error_json, indent=4, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        return None


if __name__ == "__main__":
    print("\n--- Начало комплексного тестирования Tasks API ---\n")

    # --- Шаг 1: Аутентификация ---
    login_response = run_test_step("Шаг 1: Получение JWT токена", API_AUTH_URL, LOGIN_PAYLOAD, DEFAULT_HEADERS, 200)
    if not login_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось войти в систему. Тестирование прервано.")

    jwt_token = login_response.json().get("token")
    auth_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {jwt_token}"}

    # --- Шаг 2: Создание задачи ---
    create_payload = {
        "firm_id": FIRM_ID,
        "action": "UPSERT",
        "payload": {
            "title": "Новая задача из автоматического теста",
            "priority": "medium",
            "status": "new"
        }
    }
    create_response = run_test_step("Шаг 2: Создание новой задачи", API_TASKS_URL, create_payload, auth_headers, 201)
    if not create_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось создать задачу. Тестирование прервано.")

    task_id = create_response.json().get("task_id")

    # --- Последующие шаги ---
    get_one_payload = {"firm_id": FIRM_ID, "action": "GET", "task_id": task_id}
    run_test_step("Шаг 3: Получение созданной задачи по ID", API_TASKS_URL, get_one_payload, auth_headers, 200)

    update_payload = {
        "firm_id": FIRM_ID, "action": "UPSERT", "task_id": task_id,
        "payload": {
            "status": "in_progress",
            "description": "Описание задачи было успешно обновлено."
        }
    }
    run_test_step("Шаг 4: Обновление данных задачи", API_TASKS_URL, update_payload, auth_headers, 200)

    get_all_payload = {"firm_id": FIRM_ID, "action": "GET"}
    run_test_step("Шаг 5: Получение списка всех задач", API_TASKS_URL, get_all_payload, auth_headers, 200)

    delete_payload = {"firm_id": FIRM_ID, "action": "DELETE", "task_id": task_id}
    run_test_step("Шаг 6: Удаление задачи", API_TASKS_URL, delete_payload, auth_headers, 200)

    run_test_step("Шаг 7: Проверка удаления (ожидается 404)", API_TASKS_URL, get_one_payload, auth_headers, 404)

    print("\n--- Тестирование успешно завершено ---")