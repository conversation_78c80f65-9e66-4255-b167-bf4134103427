import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:balansoved_enterprise/router.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/dialogs/user_not_found_dialog.dart';

class AppContent extends StatelessWidget {
  const AppContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProfileCubit, ProfileState>(
      listener: (context, state) {
        if (state is ProfileLoaded) {
          context.read<ActiveFirmCubit>().setFirms(state.profile.firms);
        } else if (state is ProfileError) {
          // Проверяем, является ли ошибка "пользователь не найден" (HTTP 404)
          final errorMessage = state.message.toLowerCase();
          if (errorMessage.contains('404') && 
              (errorMessage.contains('user') || errorMessage.contains('пользователь')) &&
              (errorMessage.contains('not found') || errorMessage.contains('не найден'))) {
            // Показываем диалог с предложением выйти из аккаунта
            UserNotFoundDialog.show(
              context,
              errorMessage: state.message,
            );
          }
        }
      },
      child: Router.withConfig(
        config: GetIt.I<AppRouter>().config(),
      ),
    );
  }
}