import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/backup_integration_entity.dart';

abstract class IntegrationsRepository {
  /// Получить информацию о резервном копировании Я.Диск для фирмы
  Future<Either<Failure, BackupIntegrationEntity>> getBackupStatus(
    String firmId,
  );

  /// Включить или выключить резервное копирование
  Future<Either<Failure, BackupIntegrationEntity>> setBackupEnabled({
    required String firmId,
    required bool enabled,
  });

  /// Сохранить/обновить токен Яндекс Диска
  Future<Either<Failure, BackupIntegrationEntity>> saveToken({
    required String firmId,
    required String token,
  });
}
