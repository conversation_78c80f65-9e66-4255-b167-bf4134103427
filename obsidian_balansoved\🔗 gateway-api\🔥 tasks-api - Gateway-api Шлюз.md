
Идентификатор - d5d7ct0sul274igh4vij
Имя - tasks-api
Служебный домен - https://d5d7ct0sul274igh4vij.aqkd4clz.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Tasks API
  version: 1.1.0 # Обновляем версию, так как изменился контракт
servers:
  - url: https://d5d7ct0sul274igh4vij.aqkd4clz.apigw.yandexcloud.net

x-yc-apigateway:
  cors:
    origin: "*"
    methods:
      - "POST"
      - "OPTIONS"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    allowCredentials: true
    maxAge: 3600

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - bearerAuth: []

paths:
  /manage:
    post:
      summary: Универсальный метод для управления задачами
      description: |
        Позволяет создавать, обновлять, удалять и получать задачи.
        Метод GET имеет два режима:
        1. Получение бессрочных задач (постранично).
        2. Получение задач с крайним сроком (за указанный месяц).
      operationId: manageTasks
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e4mpjj10hka0o8ct9d
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firm_id:
                  type: string
                  description: "ID фирмы, в контексте которой выполняется операция."
                action:
                  type: string
                  enum: [GET, UPSERT, DELETE]
                  description: "Тип выполняемой операции."
                task_id:
                  type: string
                  description: "ID задачи. Обязателен для GET (одна задача), UPSERT (обновление), DELETE."
                payload:
                  type: object
                  description: "Данные для создания/обновления задачи. Обязателен для UPSERT."
                
                # --- ПАРАМЕТРЫ ДЛЯ GET ---
                page:
                  type: integer
                  description: "Для бессрочных задач: номер страницы для пагинации (начиная с 0). По умолчанию 0."
                get_dated_tasks:
                  type: boolean
                  description: "Для срочных задач: установить в 'true', чтобы получить задачи с крайним сроком."
                month:
                  type: integer
                  description: "Для срочных задач: месяц (1-12). Обязателен, если get_dated_tasks=true."
                year:
                  type: integer
                  description: "Для срочных задач: год. Обязателен, если get_dated_tasks=true."

              required:
                - firm_id
                - action
      responses:
        '200':
          description: Успешное выполнение.
        '201':
          description: Задача успешно создана.
        '400':
          description: Неверные параметры.
        '403':
          description: Ошибка авторизации или недостаточно прав.
        '404':
          description: Задача или страница не найдена.
        '500':
          description: Внутренняя ошибка сервера.
```