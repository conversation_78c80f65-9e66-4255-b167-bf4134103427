
```python
import json
import os
import logging
import sys
import ydb

from utils import auth_utils, ydb_utils, request_parser
from get import get_payments
from upsert import upsert_payment
from delete import delete_payment
from custom_errors import AuthError, LogicError, NotFoundError

logging.getLogger().setLevel(logging.INFO)

def check_permissions(session, user_id, firm_id):
    """Проверяет, является ли пользователь OWNER или ADMIN в фирме."""
    query = session.prepare("""
        DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8;
        SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;
    """)
    result = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$user_id": user_id, "$firm_id": firm_id}, commit_tx=True)
    if not result[0].rows:
        raise AuthError("User is not a member of the specified firm.")
    
    roles = json.loads(result[0].rows[0].roles or '[]')
    if "OWNER" not in roles and "ADMIN" not in roles:
        raise AuthError("Insufficient permissions. Owner or Admin role required.")
    
    logging.info(f"User {user_id} has required permissions for firm {firm_id}.")
    return True

def _ensure_table_exists(driver, database_path, table_name):
    """Проверяет существование таблицы и создает ее при необходимости."""
    full_table_path = os.path.join(database_path, table_name)
    logging.info(f"Checking for existence of table at full path: {full_table_path}")
    session = driver.table_client.session().create()
    try:
        desc = session.describe_table(full_table_path)
        logging.info(f"Table {table_name} already exists.")
        # Check for actual_payment_date column
        column_names = [col.name for col in desc.columns]
        if 'actual_payment_date' not in column_names:
            logging.info(f"Adding missing column actual_payment_date to {table_name}")
            session.execute_scheme(f"ALTER TABLE `{full_table_path}` ADD COLUMN actual_payment_date Date?;")
    except ydb.SchemeError:
        logging.warning(f"Table {table_name} does not exist. Creating...")
        session.create_table(
            full_table_path,
            ydb.TableDescription().with_primary_keys("client_id", "period_start_date")
            .with_columns(
                ydb.Column("client_id", ydb.PrimitiveType.Utf8),
                ydb.Column("period_start_date", ydb.PrimitiveType.Date),
                ydb.Column("actual_amount_kopeks", ydb.OptionalType(ydb.PrimitiveType.Int64)),
                ydb.Column("tariff_annual_amount_kopeks", ydb.OptionalType(ydb.PrimitiveType.Int64)),
                ydb.Column("actual_payment_date", ydb.OptionalType(ydb.PrimitiveType.Date)),
                ydb.Column("created_at", ydb.PrimitiveType.Timestamp),
                ydb.Column("updated_at", ydb.PrimitiveType.Timestamp),
            )
        )
        logging.info(f"Table {table_name} created successfully.")
    except Exception as e:
        logging.error(f"Failed to check or create table {table_name}: {e}", exc_info=True)
        raise RuntimeError(f"Could not ensure table {table_name} exists.")


def handler(event, context):
    try:
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '): raise AuthError("Unauthorized")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload: raise AuthError("Invalid token")
        requesting_user_id = user_payload['user_id']

        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        if not all([firm_id, action]):
            raise LogicError("firm_id and action are required.")

        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        firms_pool.retry_operation_sync(lambda s: check_permissions(s, requesting_user_id, firm_id))

        payments_db_path = os.environ["YDB_DATABASE_CLIENT_PAYMENTS"]
        payments_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_CLIENT_PAYMENTS"], payments_db_path)
        payments_pool = ydb.SessionPool(payments_driver)
        table_name = f"client_payments_{firm_id}"
        
        _ensure_table_exists(payments_driver, payments_db_path, table_name)

        def payments_transaction_router(session):
            if action == "GET":
                year = data.get('year')
                client_id = data.get('client_id')
                return get_payments(session, table_name, year, client_id)
            
            elif action == "UPSERT":
                payload = data.get('payload')
                return upsert_payment(session, table_name, payload)
            
            elif action == "DELETE":
                client_id = data.get('client_id')
                period = data.get('period')
                return delete_payment(session, table_name, client_id, period)
            
            else:
                raise LogicError(f"Invalid action specified: '{action}'.")

        return payments_pool.retry_operation_sync(payments_transaction_router)

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing payment request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```