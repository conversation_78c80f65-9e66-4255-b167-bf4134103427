import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';

/// Состояния кубита контроля entity клиента
abstract class ClientEntityControllerState extends Equatable {
  const ClientEntityControllerState();

  @override
  List<Object?> get props => [];
}

/// Начальное состояние - клиент не загружен
class ClientEntityControllerInitial extends ClientEntityControllerState {}

/// Состояние с загруженным клиентом
class ClientEntityControllerLoaded extends ClientEntityControllerState {
  final ClientEntity client;

  const ClientEntityControllerLoaded({required this.client});

  @override
  List<Object?> get props => [client];
}