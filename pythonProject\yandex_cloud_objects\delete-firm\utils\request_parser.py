# utils/request_parser.py

import json
import base64
import logging

def parse_request_body(event: dict) -> dict:
    """
    Безопасно извлекает, декодирует (если необходимо) и парсит JSON-тело
    из входящего event от Yandex API Gateway.
    
    :param event: Словарь события от API Gateway.
    :raises ValueError: Если тело отсутствует, не может быть декодировано или распарсено.
    :return: Распарсенный словарь из тела запроса.
    """
    logging.info("Parsing request body...")
    
    body_str = event.get('body')
    if not body_str:
        raise ValueError("Request body is empty or missing.")

    if event.get('isBase64Encoded', False):
        logging.info("Body is Base64 encoded. Decoding...")
        try:
            body_str = base64.b64decode(body_str).decode('utf-8')
            logging.info(f"Decoded body: {body_str}")
        except Exception as e:
            raise ValueError(f"Failed to decode Base64 body: {e}")
    else:
        logging.info("Body is not Base64 encoded.")

    try:
        data = json.loads(body_str)
        logging.info(f"Successfully parsed JSON data: {data}")
        return data
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON format in request body. Error: {e}. Body was: '{body_str}'")