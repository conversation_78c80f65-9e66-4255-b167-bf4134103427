import 'package:dartz/dartz.dart';

import '../../../../core/error/failure.dart';
import '../repositories/client_payments_repository.dart';

class UpsertClientPaymentUseCase {
  final IClientPaymentsRepository repository;

  UpsertClientPaymentUseCase({required this.repository});

  Future<Either<Failure, Unit>> call(UpsertClientPaymentParams params) {
    return repository.upsertPayment(
      firmId: params.firmId,
      clientId: params.clientId,
      period: params.period,
      actualAmount: params.actualAmount,
      tariffAmount: params.tariffAmount,
      paymentDate: params.paymentDate,
    );
  }
}

class UpsertClientPaymentParams {
  final String firmId;
  final String clientId;
  final DateTime period;
  final double? actualAmount;
  final double? tariffAmount;
  final DateTime? paymentDate;

  UpsertClientPaymentParams({
    required this.firmId,
    required this.clientId,
    required this.period,
    this.actualAmount,
    this.tariffAmount,
    this.paymentDate,
  });
}
