
```python
import logging
import os
import ydb
import invoke_utils
from utils import ydb_utils

def _delete_all_tasks(user_jwt, firm_id):
    """Удаляет все задачи фирмы."""
    logging.info("Starting deletion of all tasks...")
    tasks_driver = ydb_utils.get_driver_for_db(invoke_utils.YDB_ENDPOINT_TASKS, invoke_utils.YDB_DATABASE_TASKS)
    tasks_pool = ydb.SessionPool(tasks_driver)
    table_name = f"tasks_{firm_id}"

    def get_task_ids(session):
        try:
            res = session.transaction(ydb.SerializableReadWrite()).execute(f"SELECT task_id FROM `{table_name}`;", commit_tx=True)
            return [row.task_id for row in res[0].rows]
        except ydb.BadQuery:
            return [] # Таблицы нет - задач нет

    task_ids = tasks_pool.retry_operation_sync(get_task_ids)
    if not task_ids:
        logging.info("No tasks found to delete.")
        return

    logging.info(f"Found {len(task_ids)} tasks to delete.")
    for i, task_id in enumerate(task_ids):
        invoke_utils.invoke_function(
            invoke_utils.FUNCTION_ID_EDIT_TASK,
            {"firm_id": firm_id, "action": "DELETE", "task_id": task_id},
            user_jwt
        )
        logging.info(f"  - Deleted task {i+1}/{len(task_ids)} ({task_id})")
    logging.info("All tasks deleted.")

def _delete_all_clients(user_jwt, firm_id):
    """Удаляет все версии всех клиентов фирмы."""
    logging.info("Starting deletion of all client versions...")
    clients_driver = ydb_utils.get_driver_for_db(invoke_utils.YDB_ENDPOINT_CLIENTS, invoke_utils.YDB_DATABASE_CLIENTS)
    clients_pool = ydb.SessionPool(clients_driver)
    table_name = f"clients_{firm_id}"

    def get_all_client_versions(session):
        """Получает все версии (client_id, manual_creation_date) всех клиентов."""
        try:
            query = f"SELECT client_id, manual_creation_date FROM `{table_name}`"
            res = session.transaction(ydb.SerializableReadWrite()).execute(query, commit_tx=True)
            return [(row.client_id, row.manual_creation_date) for row in res[0].rows]
        except ydb.SchemeError:
            logging.warning(f"Table {table_name} not found. Assuming no clients to delete.")
            return []

    client_versions = clients_pool.retry_operation_sync(get_all_client_versions)

    if not client_versions:
        logging.info("No client versions found to delete.")
        return

    logging.info(f"Found {len(client_versions)} client versions to delete.")
    for i, (client_id, creation_date) in enumerate(client_versions):
        try:
            invoke_utils.invoke_function(
                invoke_utils.FUNCTION_ID_EDIT_CLIENT,
                {
                    "firm_id": firm_id,
                    "action": "DELETE",
                    "client_id": client_id,
                    "creation_date": creation_date.isoformat()
                },
                user_jwt
            )
            logging.info(f"  - Deleted client version {i+1}/{len(client_versions)} ({client_id}, {creation_date.isoformat()})")
        except Exception as e:
            logging.error(f"Failed to delete version for client {client_id} (date: {creation_date.isoformat()}): {e}", exc_info=True)
            continue

    logging.info("All client versions deleted.")

def _delete_all_employees(user_jwt, firm_id, owner_id):
    """Удаляет всех сотрудников, кроме владельца."""
    logging.info("Starting deletion of all employees...")
    employees_response = invoke_utils.invoke_function(
        invoke_utils.FUNCTION_ID_EDIT_EMPLOYEE,
        {"firm_id": firm_id, "action": "GET_INFO"},
        user_jwt
    )
    if not employees_response or not employees_response.get('data'):
        logging.warning("Could not retrieve employee list.")
        return

    employees_to_delete = [emp for emp in employees_response['data'] if emp['user_id'] != owner_id]
    if not employees_to_delete:
        logging.info("No other employees found to delete.")
        return
    
    logging.info(f"Found {len(employees_to_delete)} employees to delete.")
    for i, emp in enumerate(employees_to_delete):
        invoke_utils.invoke_function(
            invoke_utils.FUNCTION_ID_DELETE_EMPLOYEE,
            {"firm_id": firm_id, "user_id_to_delete": emp['user_id']},
            user_jwt
        )
        logging.info(f"  - Deleted employee {i+1}/{len(employees_to_delete)} ({emp['user_id']})")
    logging.info("All employees deleted.")

def _drop_personal_tables(firm_id):
    """Удаляет персональные таблицы фирмы из всех баз данных."""
    logging.info("Starting deletion of personal YDB tables...")
    databases = {
        "tasks": (invoke_utils.YDB_ENDPOINT_TASKS, invoke_utils.YDB_DATABASE_TASKS),
        "clients": (invoke_utils.YDB_ENDPOINT_CLIENTS, invoke_utils.YDB_DATABASE_CLIENTS),
        "client_payments": (invoke_utils.YDB_ENDPOINT_CLIENT_PAYMENTS, invoke_utils.YDB_DATABASE_CLIENT_PAYMENTS)
    }

    for db_alias, (endpoint, db_path) in databases.items():
        table_name = f"{db_alias}_{firm_id}" if db_alias != "client_payments" else f"client_payments_{firm_id}"
        full_table_path = os.path.join(db_path, table_name)
        try:
            driver = ydb_utils.get_driver_for_db(endpoint, db_path)
            session = driver.table_client.session().create()
            session.drop_table(full_table_path)
            logging.info(f"  - Dropped table: {full_table_path}")
        except ydb.SchemeError:
            logging.warning(f"  - Table not found, skipping drop: {full_table_path}")
        except Exception as e:
            logging.error(f"  - FAILED to drop table {full_table_path}: {e}")
    logging.info("Personal YDB tables deleted.")

def _delete_tariffs_and_storage_record(firm_id):
    """Удаляет запись о тарифах и хранилище фирмы."""
    logging.info("Starting deletion of tariffs and storage record...")
    try:
        tariffs_driver = ydb_utils.get_driver_for_db(invoke_utils.YDB_ENDPOINT_TARIFFS_AND_STORAGE, invoke_utils.YDB_DATABASE_TARIFFS_AND_STORAGE)
        tariffs_pool = ydb.SessionPool(tariffs_driver)
        
        def delete_record(session):
            query = session.prepare("DECLARE $fid AS Utf8; DELETE FROM tariffs_and_storage WHERE firm_id = $fid;")
            session.transaction(ydb.SerializableReadWrite()).execute(query, {"$fid": firm_id}, commit_tx=True)
        
        tariffs_pool.retry_operation_sync(delete_record)
        logging.info("Tariffs and storage record deleted successfully.")
    except Exception as e:
        logging.error(f"Failed to delete tariffs and storage record: {e}")
        # Не прерываем процесс удаления, так как это не критично

def _delete_firm_records(session, firm_id, owner_id):
    """Удаляет финальные записи о фирме и владении."""
    logging.info("Starting final cleanup of Firms and Users tables...")
    tx = session.transaction(ydb.SerializableReadWrite())
    
    # Удаляем запись о фирме
    tx.execute(session.prepare("DECLARE $fid AS Utf8; DELETE FROM Firms WHERE firm_id = $fid;"), {"$fid": firm_id})
    # Удаляем запись о владении
    tx.execute(session.prepare("DECLARE $uid AS Utf8; DECLARE $fid AS Utf8; DELETE FROM Users WHERE user_id = $uid AND firm_id = $fid;"), {"$uid": owner_id, "$fid": firm_id})
    
    tx.commit()
    logging.info("Final records from Firms and Users tables deleted.")

def run_all_deletions(pool, user_jwt, owner_id, firm_id):
    """Запускает все шаги по удалению."""
    _delete_all_tasks(user_jwt, firm_id)
    _delete_all_clients(user_jwt, firm_id)
    _delete_all_employees(user_jwt, firm_id, owner_id)
    _drop_personal_tables(firm_id)
    _delete_tariffs_and_storage_record(firm_id)
    pool.retry_operation_sync(lambda s: _delete_firm_records(s, firm_id, owner_id))
```