import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../cubit/one_time_services_cubit.dart';
import '../../cubit/one_time_services_state.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';

class OneTimeServicesSettingsDialog extends StatefulWidget {
  const OneTimeServicesSettingsDialog({super.key, required this.category});

  final OneTimeServiceCategory category;

  @override
  State<OneTimeServicesSettingsDialog> createState() =>
      _OneTimeServicesSettingsDialogState();
}

class _OneTimeServicesSettingsDialogState
    extends State<OneTimeServicesSettingsDialog> {
  late final OneTimeServicesCubit _cubit;
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _cubit = context.read<OneTimeServicesCubit>();

    // Получаем услуги для данной категории
    final services = servicesByCategory[widget.category] ?? [];

    // Инициализируем контроллеры для каждой услуги
    for (final service in services) {
      final currentPrice = _cubit.getCustomPrice(service);
      _controllers[service.id] = TextEditingController(
        text: NumberFormat('#,##0', 'ru_RU').format(currentPrice),
      );
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _save() {
    final Map<String, int> newPrices = {};

    for (var entry in _controllers.entries) {
      final serviceId = entry.key;
      final controller = entry.value;

      // Парсим цену, убирая пробелы и запятые
      final cleanText = controller.text.replaceAll(RegExp(r'[^\d]'), '');
      final price = int.tryParse(cleanText) ?? 0;
      if (price > 0) {
        newPrices[serviceId] = price;
      }
    }

    _cubit.updateCategoryPrices(widget.category, newPrices);
    Navigator.of(context).pop();
  }

  void _resetToDefaults() {
    final services = servicesByCategory[widget.category] ?? [];
    for (final service in services) {
      _controllers[service.id]?.text = NumberFormat(
        '#,##0',
        'ru_RU',
      ).format(service.basePrice);
    }
  }

  @override
  Widget build(BuildContext context) {
    final services = servicesByCategory[widget.category] ?? [];

    return AlertDialog(
      title: Text('Настройка расценок: ${widget.category.title}'),
      content: SizedBox(
        width: 600,
        height: 400,
        child:
            services.isEmpty
                ? const Center(child: Text('Нет услуг в данной категории'))
                : Scrollbar(
                  child: ListView.builder(
                    itemCount: services.length,
                    itemBuilder: (context, index) {
                      final service = services[index];
                      final controller = _controllers[service.id];

                      if (controller == null) return const SizedBox.shrink();

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: UniversalNumberField(
                          controller: controller,
                          labelText: service.title,
                          fieldType: NumberFieldType.integer,
                          suffixText: service.isFromPrice ? 'от ₽' : '₽',
                          isEditing: service.isActive,
                          helperText:
                              service.isActive
                                  ? 'Базовая цена: ${service.priceLabel}'
                                  : 'Услуга неактивна',
                          onChanged: (value) {
                            if (!service.isActive) return;

                            // Автоматическое форматирование числа
                            final cleanValue = value.replaceAll(
                              RegExp(r'[^\d]'),
                              '',
                            );
                            if (cleanValue.isNotEmpty) {
                              final number = int.tryParse(cleanValue);
                              if (number != null) {
                                final formatted = NumberFormat(
                                  '#,##0',
                                  'ru_RU',
                                ).format(number);
                                if (formatted != value) {
                                  controller.value = TextEditingValue(
                                    text: formatted,
                                    selection: TextSelection.collapsed(
                                      offset: formatted.length,
                                    ),
                                  );
                                }
                              }
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
      ),
      actions: [
        TextButton(onPressed: _resetToDefaults, child: const Text('Сбросить')),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        FilledButton(onPressed: _save, child: const Text('Сохранить')),
      ],
    );
  }
}
