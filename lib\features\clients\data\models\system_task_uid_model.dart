import 'package:balansoved_enterprise/features/clients/domain/entities/system_task_uid_entity.dart';

class SystemTaskUidModel extends SystemTaskUidEntity {
  const SystemTaskUidModel({
    required super.uid,
    required super.name,
    required super.description,
    super.dueDate,
  });

  factory SystemTaskUidModel.fromJson(Map<String, dynamic> json) {
    return SystemTaskUidModel(
      uid: json['uid'],
      name: json['name'],
      description: json['description'],
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'name': name,
      'description': description,
      'dueDate': dueDate?.toIso8601String(),
    };
  }

  factory SystemTaskUidModel.fromEntity(SystemTaskUidEntity entity) {
    return SystemTaskUidModel(
      uid: entity.uid,
      name: entity.name,
      description: entity.description,
      dueDate: entity.dueDate,
    );
  }
}