
Идентификатор - d4eu095gn2tga52no22p
Описание - Подтвердить регистрацию с помощью кода, активировать пользователя и связанные приглашения.

Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `email`: Email пользователя.
	-> `code`: 6-значный код из email.

Внутренняя работа:
	-> Парсинг тела запроса для извлечения `email` и `code`.
	-> Проверка наличия `email` и `code`; если отсутствуют, возврат 400 с сообщением.
	-> Получение драйверов YDB для `jwt-database` и `firms-database`.
	-> Транзакционная обработка в `jwt-database`:
		-> Поиск неактивного пользователя по `email`.
		-> Если пользователь не найден или уже активен, возврат 404.
		-> Проверка предоставленного `code` на совпадение и срока действия (`code_expires_at`), с учетом возможных форматов даты (datetime или микросекунды).
		-> Если код неверный или просрочен, возврат 400.
		-> Обновление пользователя: установка `is_active = true`, очистка `verification_code` и `code_expires_at`.
		-> Коммит транзакции.
	-> Обновление в `firms-database` (отдельная операция с retry):
		-> Обновление `user_id` для всех записей в таблице `Users`, где `email` совпадает и `is_active = false`.
		-> Если обновление fails, логирование ошибки, но продолжение (пользователь активирован).
	-> Генерация JWT-токена на основе `user_id` и `email`.
	-> Обработка ошибок: возврат 500 при внутренних сбоях.

На выходе:
	-> `200 OK`: {"token": "<jwt_token>"}
	-> `400 Bad Request`: {"message": "Invalid or expired code."} или `{"message": "Email and code are required."}`
	-> `404 Not Found`: `{"message": "User not found or already active."}`
	-> `500 Internal Server Error`: {"message": "Internal Server Error"}

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
    - `YDB_ENDPOINT`, `YDB_DATABASE` ([[💾 jwt-database - База данных YandexDatabase]])
    - `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
    - SA_KEY_FILE ([[ydb_sa_key.json]])
    - JWT_SECRET 