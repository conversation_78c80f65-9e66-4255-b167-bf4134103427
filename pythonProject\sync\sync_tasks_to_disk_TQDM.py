import requests
import json
import sys
import os
import re
import datetime
import time
import tempfile
from functools import wraps
from colorama import init, Fore, Style
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from io import BytesIO

# Импортируем наши утилиты
from tkinter_progress import TkinterProgressWindow
import yandex_disk_uploader
import pdf_generator
import importlib

# На случай, если модуль был закеширован без новой функции
if not hasattr(yandex_disk_uploader, "upload_and_get_link_from_bytes"):
    importlib.reload(yandex_disk_uploader)

# --- Инициализация ---
init(autoreset=True)

# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
API_TASKS_URL = "https://d5d7ct0sul274igh4vij.aqkd4clz.apigw.yandexcloud.net/manage"
API_CLIENTS_URL = "https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net/manage"
API_EMPLOYEES_URL = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net/employees/edit"
API_GATEWAY_BASE = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net"
API_STORAGE_URL = "https://d5d5vaj6bd49bcvhnfbh.sk0vql13.apigw.yandexcloud.net/manage"

FIRM_ID = "9a33483b-dfad-44a3-a36d-102b498ec0ef"
LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

YADISK_BASE_FOLDER = "balansoved enterprise"
PAGE_SIZE = 100
MAX_RETRIES = 3
RETRY_BASE_DELAY_SEC = 5

# --- Символы и кеши ---
TICK, CROSS, INFO, WARN = Fore.GREEN + "✓", Fore.RED + "✗", Fore.YELLOW + "→", Fore.MAGENTA + "!"
CLIENT_NAME_CACHE, EMPLOYEE_NAME_CACHE = {}, {}


def populate_employee_cache(auth_headers: dict):
    """Заполняет кеш сотрудников одним запросом к /get-user-data."""
    # 1. Пробуем специальный Gateway-эндпоинт (может вернуть связи фирм/задач)
    try:
        resp = requests.get(f"{API_GATEWAY_BASE}/get-user-data", headers=auth_headers, timeout=20)
        if resp.status_code == 200:
            data = resp.json().get("data") or resp.json()
            employees = []
            for key in ("employees", "users", "team", "staff"):
                if key in data and isinstance(data[key], list):
                    employees = data[key]
                    break
            for emp in employees:
                uid = emp.get("user_id") or emp.get("id") or emp.get("uid")
                name = emp.get("full_name") or emp.get("fullName") or emp.get("name")
                if uid and name:
                    EMPLOYEE_NAME_CACHE[uid] = name
            print_with_progress(f"  {INFO} Кеш сотрудников пополнен из get-user-data: {len(employees)} записей.")
        else:
            print_with_progress(f"  {WARN} get-user-data ответил статус {resp.status_code}.")
    except requests.exceptions.RequestException as e:
        print_with_progress(f"  {WARN} Ошибка сети get-user-data: {e}")

    # 2. Запрашиваем полный список сотрудников фирмы через /employees/edit GET_INFO
    try:
        payload = {"firm_id": FIRM_ID, "action": "GET_INFO"}
        resp = requests.post(API_EMPLOYEES_URL, json=payload, headers=auth_headers, timeout=20)
        if resp.status_code == 200:
            employees = resp.json().get("data", [])
            if isinstance(employees, list):
                for emp in employees:
                    uid = emp.get("user_id") or emp.get("id") or emp.get("uid")
                    name = emp.get("full_name") or emp.get("fullName") or emp.get("name")
                    if uid and name:
                        EMPLOYEE_NAME_CACHE[uid] = name
                print_with_progress(f"  {INFO} Кеш сотрудников пополнен из employees/edit: {len(employees)} записей.")
            else:
                print_with_progress(f"  {WARN} employees/edit ответил статус {resp.status_code}.")
        else:
            print_with_progress(f"  {WARN} employees/edit ответил статус {resp.status_code}.")
    except requests.exceptions.RequestException as e:
        print_with_progress(f"  {WARN} Ошибка сети employees/edit: {e}")


# --- Глобальный экземпляр окна прогресса ---
global_progress = TkinterProgressWindow()


# =================================================================================
# ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
# =================================================================================

def print_with_progress(message, end="\n"):
    """Печать сообщения в консоль."""
    print(message, end=end)


def retry_network_operation(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        for attempt in range(MAX_RETRIES):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.RequestException as e:
                wait_time = RETRY_BASE_DELAY_SEC * (2 ** attempt)
                print_with_progress(f"\n{WARN} Попытка {attempt + 1}/{MAX_RETRIES} провалена. Ошибка сети: {e}.")
                if attempt + 1 < MAX_RETRIES:
                    print_with_progress(f"{WARN} Повторная попытка через {wait_time} секунд...")
                    time.sleep(wait_time)
                else:
                    print_with_progress(f"{CROSS} Все попытки исчерпаны. Операция не удалась.")
                    return None
        return None

    return wrapper


@retry_network_operation
def make_api_request(url: str, payload: dict, headers: dict):
    return requests.post(url, json=payload, headers=headers, timeout=20)


def run_api_step(title: str, url: str, payload: dict, headers: dict, expected_status: int, increment: bool = True):
    print_with_progress(f"{Style.BRIGHT}► {title}", end=" ... ")
    # Динамически увеличиваем общий объём работ
    global_progress.add_to_total(1)
    global_progress.set_description(title)
    response = make_api_request(url, payload, headers)
    if increment: global_progress.increment(1)

    if response is None:
        print_with_progress(f"{CROSS} (Операция не удалась после {MAX_RETRIES} попыток)")
        return None
    if response.status_code == expected_status:
        print_with_progress(f"{TICK} (Статус: {response.status_code})")
        return response
    else:
        print_with_progress(f"{CROSS} (Ожидался статус {expected_status}, получен {response.status_code})")
        print_with_progress(Fore.RED + f"  Текст ошибки: {response.text}")
        return None


@retry_network_operation
def download_file_to_memory(url: str):
    """Скачивает файл и возвращает bytes."""
    global_progress.add_to_total(1)
    with requests.get(url, stream=True) as r:
        r.raise_for_status()
        buffer = BytesIO()
        for chunk in r.iter_content(chunk_size=8192):
            buffer.write(chunk)
        global_progress.increment(1)
        return buffer.getvalue()


def sanitize_filename(name: str) -> str:
    return re.sub(r'[\\/*?:"<>|]', '_', name).strip()


def get_client_name(client_id: str, auth_headers: dict) -> str:
    if client_id in CLIENT_NAME_CACHE: return CLIENT_NAME_CACHE[client_id]
    payload = {"firm_id": FIRM_ID, "action": "GET", "client_id": client_id}
    try:
        response = requests.post(API_CLIENTS_URL, json=payload, headers=auth_headers, timeout=10)
        if response.status_code == 200:
            name = response.json().get('data', {}).get('client_name', client_id)
            CLIENT_NAME_CACHE[client_id] = name
            return name
    except requests.exceptions.RequestException:
        pass
    CLIENT_NAME_CACHE[client_id] = client_id
    return client_id


def get_employee_name(employee_id: str, auth_headers: dict) -> str:
    if employee_id in EMPLOYEE_NAME_CACHE: return EMPLOYEE_NAME_CACHE[employee_id]
    payload = {"firm_id": FIRM_ID, "action": "GET_INFO", "user_id_to_edit": employee_id}
    try:
        response = requests.post(API_EMPLOYEES_URL, json=payload, headers=auth_headers, timeout=10)
        if response.status_code == 200:
            data = response.json().get('data', {})

            # --- Универсальная рекурсивная функция поиска ФИО ---
            def find_name_in_obj(obj):
                if isinstance(obj, str):
                    # Эвристика: строка с пробелом и длиной > 3 — вероятно ФИО
                    if " " in obj and len(obj) > 3:
                        return obj
                elif isinstance(obj, dict):
                    for k, v in obj.items():
                        # Сначала проверяем ключи, которые явно могут содержать имя
                        if k in ("full_name", "fullName", "name", "display_name", "user_name") and isinstance(v, str) and v:
                            return v
                        res = find_name_in_obj(v)
                        if res:
                            return res
                elif isinstance(obj, list):
                    for item in obj:
                        res = find_name_in_obj(item)
                        if res:
                            return res
                return None

            candidate = find_name_in_obj(data)
            if candidate:
                EMPLOYEE_NAME_CACHE[employee_id] = candidate
                return candidate
            # Имя не найдено — возможно нет прав / пустой ответ
            # Оставляем fallback ниже
        elif response.status_code == 403:
            # Недостаточно прав — возвращаем ID, но помечаем в кэше
            EMPLOYEE_NAME_CACHE[employee_id] = employee_id
            return employee_id
    except requests.exceptions.RequestException:
        pass
    EMPLOYEE_NAME_CACHE[employee_id] = employee_id
    return employee_id


def get_client_folder_name(client_ids_json: str, auth_headers: dict) -> str:
    try:
        client_ids = json.loads(client_ids_json or '[]')
        if not client_ids: return "unassigned"
        folder_parts = [f"{sanitize_filename(get_client_name(cid, auth_headers))} ({cid})" for cid in client_ids]
        return "-".join(sorted(folder_parts))
    except (json.JSONDecodeError, TypeError):
        return "invalid_client_id_format"


def count_task_operations(task_data: dict) -> int:
    """Подсчитывает количество операций для одной задачи."""
    # 1 (детали) + N*(1(ссылка)+1(скачать)+1(загрузить)) + 1(pdf) + 1(json)
    attachments_count = len(json.loads(task_data.get('attachments_json', '[]')))
    return 1 + (attachments_count * 3) + 2


# =================================================================================
# ОСНОВНАЯ ЛОГИКА
# =================================================================================

def process_single_task(task_summary: dict, base_path_on_disk: str, auth_headers: dict, yadisk_token: str,
                        temp_dir: str):
    task_id = task_summary['task_id']
    task_title_raw = task_summary.get('title', 'Без_названия')
    task_title_sanitized = sanitize_filename(task_title_raw)
    print_with_progress(f"\n{Fore.CYAN}--- Обработка задачи: {task_title_raw} ---")

    get_task_payload = {"firm_id": FIRM_ID, "action": "GET", "task_id": task_id}
    task_response = run_api_step(f"Детали: {task_title_raw[:30]}...", API_TASKS_URL, get_task_payload, auth_headers,
                                 200)
    if not task_response: return

    task_data = task_response.json().get('data', {})

    client_folder_name = get_client_folder_name(task_data.get('client_ids_json'), auth_headers)
    task_folder_name = f"{task_title_sanitized} ({task_id})"
    task_folder_on_disk = f"{base_path_on_disk}/{client_folder_name}/{task_folder_name}"

    print_with_progress(f"  {INFO} Путь на Диске: {task_folder_on_disk}")
    if not yandex_disk_uploader.ensure_path_recursively(yadisk_token, task_folder_on_disk): return

    new_attachments_with_urls = []
    attachments_from_task = json.loads(task_data.get('attachments_json', '[]'))
    if attachments_from_task:
        files_folder_on_disk = f"{task_folder_on_disk}/файлы"
        total_files = len(attachments_from_task)
        print_with_progress(f"  {INFO} Найдено вложений: {total_files}. Загрузка файлов...")
        # Для подпапки "файлы" достаточно один раз убедиться, что она существует,
        # чтобы избежать повторных проверок каждого уровня пути (что порождает лишние API-запросы).
        if not yandex_disk_uploader.ensure_folder_exists(yadisk_token, files_folder_on_disk):
            return

        counter_lock = threading.Lock()
        completed = {"cnt": 0}

        def update_counter():
            with counter_lock:
                completed["cnt"] += 1
                msg = f"  {INFO} Вложения: {completed['cnt']}/{total_files}"
                print_with_progress(msg)
                global_progress.set_description(msg)

        def handle_attachment(attachment: dict):
            raw_key = attachment.get('file_key') or attachment.get('fileKey')
            file_key = str(raw_key) if raw_key else ""
            filename = attachment.get('name', os.path.basename(file_key))

            if not file_key:
                print_with_progress(f"  {WARN} Пропуск вложения (нет key): {filename}")
                return None

            for attempt in range(1, MAX_RETRIES + 1):
                try:
                    # --- 1. Получаем download URL ---
                    payload = {"firm_id": FIRM_ID, "action": "GET_DOWNLOAD_URL", "file_key": file_key}
                    resp = make_api_request(API_STORAGE_URL, payload, auth_headers)
                    if not resp or resp.status_code != 200:
                        raise RuntimeError(f"Storage API status {resp.status_code if resp else 'N/A'}")
                    download_url = resp.json().get('download_url')
                    if not download_url:
                        raise RuntimeError("download_url отсутствует в ответе")

                    # --- 2. Скачиваем файл в память ---
                    global_progress.set_description(f"Скачивание: {filename[:25]}...")
                    file_bytes = download_file_to_memory(download_url)
                    if file_bytes is None:
                        raise RuntimeError("Download returned None")

                    # --- 3. Загружаем в Я.Диск ---
                    global_progress.set_description(f"Загрузка: {filename[:25]}...")
                    target_path_on_disk = f"{files_folder_on_disk}/{filename}"
                    if hasattr(yandex_disk_uploader, "upload_and_get_link_from_bytes"):
                        yadisk_link = yandex_disk_uploader.upload_and_get_link_from_bytes(
                            yadisk_token, file_bytes, target_path_on_disk)
                    else:
                        temp_path = os.path.join(temp_dir, filename)
                        with open(temp_path, "wb") as f:
                            f.write(file_bytes)
                        yadisk_link = yandex_disk_uploader.upload_and_get_link(yadisk_token, temp_path, target_path_on_disk)

                    global_progress.add_to_total(1)
                    global_progress.increment(1)

                    if not yadisk_link:
                        raise RuntimeError("upload returned None")

                    update_counter()
                    return {"name": filename, "public_url": yadisk_link, "file_key": file_key}

                except Exception as e:
                    print_with_progress(f"  {WARN} Ошибка обработки '{filename}' (попытка {attempt}/{MAX_RETRIES}): {e}")
                    if attempt == MAX_RETRIES:
                        print_with_progress(f"  {CROSS} Файл '{filename}' пропущен после {MAX_RETRIES} попыток.")
                        return None

        # Ограничиваем количество потоков, чтобы не создавать слишком много одновременных загрузок
        max_workers = min(4, len(attachments_from_task))
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_map = {executor.submit(handle_attachment, att): att for att in attachments_from_task}
            for future in as_completed(future_map):
                result = future.result()
                if result:
                    new_attachments_with_urls.append(result)
    else:
        print_with_progress(f"  {INFO} Вложения отсутствуют.")

    print_with_progress(f"  {INFO} Подготовка данных для PDF...")
    participant_roles = ['assignee_ids_json', 'creator_ids_json', 'observer_ids_json']
    participants_map = {role.split('_')[0]: [f"{get_employee_name(eid, auth_headers)} ({eid})" for eid in
                                             json.loads(task_data.get(role, '[]'))] for role in participant_roles}
    clients_map = [f"{get_client_name(cid, auth_headers)} ({cid})" for cid in
                   json.loads(task_data.get('client_ids_json', '[]'))]

    global_progress.set_description(f"Создание PDF: {task_title_raw[:25]}...")
    pdf_path = os.path.join(temp_dir, 'task_report.pdf')
    if pdf_generator.generate_task_pdf(pdf_path, task_data, participants_map, clients_map, new_attachments_with_urls):
        final_pdf_path_on_disk = f"{task_folder_on_disk}/Отчет по задаче.pdf"
        yandex_disk_uploader.upload_and_get_link(yadisk_token, pdf_path, final_pdf_path_on_disk)
        print_with_progress(f"  {TICK} Отчет PDF сохранен.")
    else:
        print_with_progress(f"  {CROSS} Не удалось сгенерировать PDF.")
    global_progress.add_to_total(1)
    global_progress.increment(1)

    task_data_for_json = task_data.copy()
    task_data_for_json['attachments_json'] = json.dumps(new_attachments_with_urls, ensure_ascii=False)
    task_json_path = os.path.join(temp_dir, 'task_info_raw.json')
    with open(task_json_path, 'w', encoding='utf-8') as f:
        json.dump(task_data_for_json, f, ensure_ascii=False, indent=4)
    final_json_path_on_disk = f"{task_folder_on_disk}/task_info_raw.json"
    yandex_disk_uploader.upload_and_get_link(yadisk_token, task_json_path, final_json_path_on_disk)
    print_with_progress(f"  {TICK} Файл JSON сохранен.")
    global_progress.add_to_total(1)
    global_progress.increment(1)


def ask_yes_no(question: str, default_yes: bool = True) -> bool:
    default_hint = "[Y/n]" if default_yes else "[y/N]"
    while True:
        try:
            response = input(f"{question} {default_hint}: ").strip().lower()
            if response == "": return default_yes
            if response in ["y", "yes", "да", "д"]: return True
            if response in ["n", "no", "нет", "н"]: return False
            print("Пожалуйста, введите 'y' или 'n'.")
        except (EOFError, KeyboardInterrupt):
            print("\nВвод прерван.")
            return False


def main():
    script_start = time.time()
    print_with_progress(Fore.CYAN + "\n--- Запуск скрипта синхронизации с Яндекс Диском ---\n")

    initial_ops = 2  # Авторизация + получение интеграций
    global_progress.initialize(initial_ops, "Инициализация...")

    login_response = run_api_step("Авторизация", API_AUTH_URL, LOGIN_PAYLOAD, DEFAULT_HEADERS, 200)
    if not login_response: global_progress.close(); sys.exit()
    auth_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {login_response.json()['token']}"}

    # ---- Кеш всех сотрудников фирмы ----
    populate_employee_cache(auth_headers)

    integrations_response = run_api_step("Получение интеграций",
                                         "https://d5d5vaj6bd49bcvhnfbh.sk0vql13.apigw.yandexcloud.net/integrations",
                                         {"firm_id": FIRM_ID, "action": "GET"}, auth_headers, 200)
    yadisk_token = integrations_response.json().get('integrations', {}).get('yandex_disk', {}).get(
        'token') if integrations_response else None
    if not yadisk_token: global_progress.close(); sys.exit(
        f"\n{CROSS} Критическая ошибка: токен Яндекс Диска не найден.")
    print_with_progress(f"{TICK} Токен Яндекс Диска получен.")

    time.sleep(1)

    process_timeless = ask_yes_no("Обрабатывать бессрочные задачи?")
    process_dated = ask_yes_no("Обрабатывать задачи со сроком?")
    if not process_timeless and not process_dated: global_progress.close(); print_with_progress(
        f"{INFO} Завершение работы."); return

    tasks_to_process = []
    global_progress.set_description("Получение списка задач...")
    if process_timeless:
        page = 0
        while True:
            resp = make_api_request(API_TASKS_URL, {"firm_id": FIRM_ID, "action": "GET", "page": page}, auth_headers)
            if not resp or resp.status_code != 200 or not resp.json().get('data'): break
            tasks = resp.json()['data']
            tasks_to_process.extend([{'type': 'timeless', 'data': t} for t in tasks])
            if len(tasks) < PAGE_SIZE: break
            page += 1

    if process_dated:
        current_year = datetime.datetime.now().year
        for month in range(1, 13):
            resp = make_api_request(API_TASKS_URL,
                                    {"firm_id": FIRM_ID, "action": "GET", "get_dated_tasks": True, "year": current_year,
                                     "month": month}, auth_headers)
            if resp and resp.status_code == 200 and resp.json().get('data'):
                tasks_to_process.extend(
                    [{'type': 'dated', 'data': t, 'year': current_year, 'month': month} for t in resp.json()['data']])

    if not tasks_to_process:
        global_progress.close()
        print_with_progress(f"\n{INFO} Актуальных задач для обработки не найдено. Завершение работы.")
        return

    global_progress.set_description("Подготовка к обработке...")

    with tempfile.TemporaryDirectory() as temp_dir:
        print_with_progress(f"\n{INFO} Временная папка: {temp_dir}")
        print_with_progress(
            f"{INFO} Всего задач для обработки: {len(tasks_to_process)}.\n")

        for task_info in tasks_to_process:
            task_data = task_info['data']
            if task_info['type'] == 'timeless':
                base_path = f"{YADISK_BASE_FOLDER}/бессрочные задачи"
            else:
                base_path = f"{YADISK_BASE_FOLDER}/задачи/{task_info['year']}/{task_info['month']:02d}"

            yandex_disk_uploader.ensure_path_recursively(yadisk_token, base_path)
            process_single_task(task_data, base_path, auth_headers, yadisk_token, temp_dir)
            time.sleep(0.2)

    global_progress.set_description("Завершение...")
    print_with_progress(Fore.CYAN + "\n--- Синхронизация успешно завершена! ---\n")
    elapsed = time.time() - script_start
    print_with_progress(f"{INFO} Время выполнения скрипта: {elapsed:.2f} сек.")
    time.sleep(3)
    global_progress.close()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n" + Fore.YELLOW + "Процесс прерван. Завершение работы...")
        if global_progress: global_progress.close()
        sys.exit(1)
    except Exception as e:
        print(f"\n\n{Fore.RED}Произошла непредвиденная ошибка: {e}")
        import traceback

        traceback.print_exc()
        if global_progress: global_progress.close()
        sys.exit(1)