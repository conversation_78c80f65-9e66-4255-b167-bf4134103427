
Идентификатор - d4ek5l50tl7lsh5kartj
Описание - ✅ Активировать приглашение сотрудника с помощью ключа.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> Тело запроса:
		- `invitation_key` (string, **обязательно**): Уникальный ключ-приглашение, полученный из email.

Внутренняя работа:
	-> Парсинг запроса: Извлекается `invitation_key` из тела запроса с использованием `request_parser.parse_request_body`.
	-> Проверка наличия `invitation_key`: Если ключ отсутствует, выбрасывается `LogicError` с сообщением "invitation_key is required.".
	-> Логирование: Записывается отладочная информация о полученном ключе.
	-> Инициализация YDB: Создается драйвер и пул сессий с использованием `ydb_utils.get_driver_for_db` и переменных окружения `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`.
	-> Транзакция в `firms-database`:
		-> Подготовка и выполнение запроса на поиск: Ищется `user_id` в таблице `Users` по `invitation_key`, где `is_active` false или NULL.
		-> Логирование количества найденных строк.
		-> Если запись не найдена: Откат транзакции и возврат None.
		-> Если запись найдена: Логирование активации, подготовка и выполнение обновления - установка `is_active` в true и `invitation_key` в NULL.
		-> Коммит транзакции и возврат True.
	-> Выполнение транзакции с ретраем: Используется `pool.retry_operation_sync`.
	-> Обработка результата: Если True, логирование успеха и возврат 200 OK; иначе выбрасывается `NotFoundError`.
	-> Обработка исключений: `LogicError` -> 400, `NotFoundError` -> 404, другие -> 500 с соответствующими логами.

На выходе:
	-> `200 OK`: `{"message": "Employee activated successfully."}`
	-> `400 Bad Request`: `{"message": "invitation_key is required."}`
	-> `404 Not Found`: `{"message": "Invalid or expired invitation key."}`
	-> `500 Internal Server Error`: `{"message": "Internal Server Error"}`

---
#### Зависимости и окружение
- **Необходимые утилиты**: [[📄 utils - ydb_utils.md]], [[📄 utils - request_parser.md]]
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` [[💾 firms-database - База данных YandexDatabase.md]]
	- `SA_KEY_FILE` [[🗝️ auth-service-acc - Статический ключ доступа.md]]

---
#### Код функции

```python
import json
import os
import logging
import ydb
from utils import ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)
# Повышаем уровень до DEBUG для подробного логирования
logging.getLogger().setLevel(logging.DEBUG)

# Кастомные ошибки для четкого разделения логики в блоке try-except
class LogicError(Exception): pass
class NotFoundError(Exception): pass

def handler(event, context):
    try:
        # 1. Парсинг запроса
        data = request_parser.parse_request_body(event)
        invitation_key = data.get('invitation_key')

        if not invitation_key:
            raise LogicError("invitation_key is required.")

        # Логируем полученный ключ и его длину (для проверки пробелов/символов)
        logging.debug(f"[accept-invitation] Received invitation_key='" + invitation_key + f"' (len={len(invitation_key)})")

        driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_FIRMS"],
            os.environ["YDB_DATABASE_FIRMS"]
        )
        pool = ydb.SessionPool(driver)

        def accept_invitation_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())

            # 2. Ищем неактивного пользователя с таким ключом
            find_query = session.prepare("""
                DECLARE $key AS Utf8;
                SELECT user_id FROM Users
                WHERE invitation_key = $key AND (is_active = false OR is_active IS NULL);
            """)
            res = tx.execute(find_query, {'$key': invitation_key})

            # Логируем количество найденных строк
            logging.debug(f"[accept-invitation] Rows found for key={invitation_key}: {len(res[0].rows)}")

            if not res[0].rows:
                tx.rollback()
                logging.debug("[accept-invitation] Key not found or user already active, rolling back transaction")
                # Возвращаем None, чтобы сигнализировать, что ключ не найден
                return None

            user_id_to_activate = res[0].rows[0].user_id

            logging.debug(f"[accept-invitation] Activating user_id={user_id_to_activate} for key={invitation_key}")

            # 3. Активируем пользователя и обнуляем ключ
            update_query = session.prepare("""
                DECLARE $user_id AS Utf8;
                UPDATE Users
                SET is_active = true, invitation_key = NULL
                WHERE user_id = $user_id;
            """)
            tx.execute(update_query, {'$user_id': user_id_to_activate})

            tx.commit()
            return True # Сигнал об успехе

        # Выполняем транзакцию
        result = pool.retry_operation_sync(accept_invitation_transaction)

        if result:
            # Если транзакция вернула True, значит все успешно
            logging.info(f"Successfully activated user with invitation key: ...{invitation_key[-4:]}")
            return {
                "statusCode": 200,
                "body": json.dumps({"message": "Employee activated successfully."})
            }
        else:
            # Если транзакция вернула None, ключ не найден
            raise NotFoundError("Invalid or expired invitation key.")

    except LogicError as e:
        # Ошибка в самом запросе (отсутствует ключ)
        logging.warning(f"Bad Request: {e}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        # Ключ не найден в БД
        logging.warning(f"Invitation key not found: {e}")
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        # Любая другая ошибка (БД, и т.д.)
        logging.error(f"Error accepting invitation: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}

```