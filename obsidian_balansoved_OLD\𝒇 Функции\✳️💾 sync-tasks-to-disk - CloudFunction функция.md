
Идентификатор: d4e3f6917icsff9hikrh
Описание: 💾 Синхронизирует задачи и связанные с ними данные (вложения, отчеты) в Yandex Disk. Вызывается по запросу владельца фирмы.
Точка входа: index.handler
Таймаут: 10 мин

---

На входе:
	-> `Authorization: Bearer <jwt_token>` или `X-Forwarded-Authorization: Bearer <jwt_token>`: **(Обязательно)** JWT токен пользователя, инициирующего синхронизацию.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы для синхронизации.
Конвейер работы:
    -> Авторизация и парсинг:
        -> Извлечение firm_id из тела запроса.
        -> Извлечение и верификация JWT-токена из заголовков.
        -> Проверка, что пользователь - OWNER в firms-database.
    -> Получение токена Я.Диска:
        -> Вызов edit-integrations (GET) для получения интеграций.
        -> Проверка квоты по last_sync_utc (пропуск в тестовом режиме).
    -> Обновление времени синхронизации:
        -> Вызов edit-integrations (UPSERT) с новым last_sync_utc.
    -> Кеширование данных:
        -> Вызов get-user-data для базового списка сотрудников.
        -> Вызов edit-employee (GET_INFO) для полного списка, объединение в кеш.
    -> Получение списка задач:
        -> Множественные вызовы edit-task (GET) для бессрочных и датированных задач.
    -> Обработка каждой задачи:
        -> Умный пропуск:
            -> Проверка наличия и возраста task_info_raw.json.
            -> Полная синхронизация, если отсутствует или старше 30 дней; обновление метаданных для 7-30 дней; пропуск для <7 дней.
        -> Получение деталей: Вызов edit-task (GET).
        -> Получение имен: Вызов edit-client (GET, is_actual: true); из кеша для сотрудников, с fallback-запросом.
        -> Обработка вложений:
            -> Для каждого: GET_DOWNLOAD_URL из tariffs-and-storage-manager.
            -> Скачивание и загрузка на Я.Диск.
        -> Генерация артефактов: PDF с pdf_generator, JSON с данными.
        -> Загрузка PDF и JSON на Я.Диск.
    -> Обработка исключений и возврат статусов.
На выходе:
    -> 200 OK: {"message": "Successfully processed X tasks."} или {"message": "No tasks to process."}
    -> 400 Bad Request: Неверные параметры (например, отсутствие firm_id).
    -> 403 Forbidden: Ошибка авторизации или недостаточно прав.
    -> 500 Internal Server Error: Внутренняя ошибка.

---
### Зависимости и окружение
-   **Необходимые утилиты**:
    -   `utils/auth_utils.py`
    -   `utils/ydb_utils.py`
    -   `utils/request_parser.py`
    -   `yandex_disk_uploader.py`
    -   `pdf_generator.py`
-   **Переменные окружения**:
    -   `SYNC_FREQUENCY_DAYS`: Квота частоты запуска в днях.
    -   `YADISK_BASE_FOLDER`: Корневая папка на Яндекс.Диске.
    -   `YC_REGION`: Регион для вызова функций (`ru-central1`).
    -   `JWT_SECRET`
    -   `SYNC_TASKS_TEST_MODE`: Если 'true', функция всегда работает в тестовом режиме без ограничения по частоте синхронизации.
    -   `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
    -   **ID целевых функций**:
        -   `FUNCTION_ID_EDIT_INTEGRATIONS` ([[✳️🔗 edit-integrations - CloudFunction функция]])
        -   `FUNCTION_ID_EDIT_EMPLOYEE` ([[✳️📎 edit-employee - CloudFunction функция]])
        -   `FUNCTION_ID_EDIT_TASK` ([[✳️📝 edit-task - CloudFunction функция]])
        -   `FUNCTION_ID_EDIT_CLIENT` ([[✳️👤 edit-client - CloudFunction функция]])
        -   `FUNCTION_ID_TARIFFS_AND_STORAGE_MANAGER` ([[✳️💰 tariffs-and-storage-manager - CloudFunction функция]])
        -   `FUNCTION_ID_GET_USER_DATA` ([[✳️ get-user-data - CloudFunction функция]])

---
### index.py
```python
import json
import os
import time
import tempfile
from datetime import timezone, timedelta
import datetime as dt
import logging
import traceback, sys
# --- Защищённый импорт локальных модулей ---
try:
    import runtime_utils
    import data_fetchers
    import task_processor
    from custom_errors import AuthError, LogicError
    from utils import auth_utils, request_parser, ydb_utils
    import ydb
except Exception:  # Логируем любые фатальные ошибки ещё до инициализации runtime_utils
    logging.basicConfig(level=logging.ERROR)
    logging.error("🔥 FATAL import/initialization error 🔥\n%s", traceback.format_exc())
    sys.exit(1)

# --- Включаем немедленный вывод каждой лог-записи ---
orig_log_message = runtime_utils.log_message

def log_message_live(message: str, level: str = "INFO"):
    """Proxy для runtime_utils.log_message с одновременным выводом в stdout."""
    orig_log_message(message, level)
    # Отображаем сразу в Cloud Logs
    level_up = level.upper()
    log_level = {
        "ERROR": logging.ERROR,
        "WARN": logging.WARNING,
        "WARNING": logging.WARNING,
        "INFO": logging.INFO,
        "PROGRESS": logging.INFO,
        "DEBUG": logging.DEBUG,
    }.get(level_up, logging.INFO)
    logging.log(log_level, message)

# Подменяем функцию во всём модуле
runtime_utils.log_message = log_message_live

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def _verify_owner_permissions(user_id: str, firm_id: str):
    """Проверяет, является ли пользователь владельцем указанной фирмы."""
    runtime_utils.log_progress(f"► Проверка прав владельца для пользователя {user_id} в фирме {firm_id}...")
    
    firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
    firms_pool = ydb.SessionPool(firms_driver)
    
    def check_role_in_db(session):
        query = session.prepare("DECLARE $uid AS Utf8; DECLARE $fid AS Utf8; SELECT roles FROM Users WHERE user_id = $uid AND firm_id = $fid;")
        res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id, "$fid": firm_id}, commit_tx=True)
        
        if not res[0].rows:
            raise AuthError("User is not a member of the specified firm.")
        
        roles = json.loads(res[0].rows[0].roles or '[]')
        if "OWNER" not in roles:
            raise AuthError("Access denied. Only the firm owner can initiate the sync.")
        return True
    
    firms_pool.retry_operation_sync(check_role_in_db)
    runtime_utils.log_progress(f"  {runtime_utils.TICK} Права владельца подтверждены.")

def handler(event, context):
    script_start = time.time()
    runtime_utils.log_message("--- Запуск функции синхронизации с Яндекс Диском ---")

    try:
        # --- ДЕТАЛЬНЫЙ ЛОГ ВХОДНОГО СОБЫТИЯ ---
        raw_headers = event.get('headers', {}) or {}
        # Маскируем длинные токены для безопасности
        masked_headers = {
            k: (v[:10] + '...' + v[-4:] if isinstance(v, str) and 'authorization' in k.lower() and len(v) > 20 else v)
            for k, v in raw_headers.items()
        }
        body_preview = (event.get('body') or '')
        if isinstance(body_preview, (bytes, bytearray)):
            body_preview = body_preview.decode('utf-8', errors='ignore')
        body_preview = body_preview[:1000] + ('...' if len(body_preview) > 1000 else '')
        runtime_utils.log_message(f"RAW HEADERS: {json.dumps(masked_headers, ensure_ascii=False)}")
        runtime_utils.log_message(f"RAW BODY PREVIEW (first 1k chars): {body_preview}")

        # 1. Авторизация и проверка прав
        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        if not firm_id:
            raise LogicError("`firm_id` is required in the request body.")

        headers = event.get('headers', {}) or {}
        auth_header = headers.get('Authorization') or headers.get('authorization')
        xfwd_header = headers.get('X-Forwarded-Authorization') or headers.get('x-forwarded-authorization')

        token_header = None
        if xfwd_header and xfwd_header.lower().startswith('bearer '):
            token_header = xfwd_header  # Приоритет user JWT, проброшенного планировщиком
        elif auth_header and auth_header.lower().startswith('bearer '):
            token_header = auth_header  # Фоллбэк — прямой вызов из клиента

        if not token_header:
            raise AuthError("Bearer token required in Authorization или X-Forwarded-Authorization header.")

        user_jwt = token_header.split(' ', 1)[1]
        
        user_payload = auth_utils.verify_jwt(user_jwt)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired JWT.")
        
        user_id = user_payload['user_id']
        _verify_owner_permissions(user_id, firm_id)

        # 2. Получение токена Я.Диска и проверка квоты
        def _to_bool(val):
            """Нормализует различные формы true/false к bool."""
            if isinstance(val, bool):
                return val
            if isinstance(val, (int, float)):
                return val != 0
            if isinstance(val, str):
                return val.strip().lower() in ("true", "1", "yes")
            return False

        env_test_mode = _to_bool(os.environ.get("SYNC_TASKS_TEST_MODE", "false"))
        is_test_mode = env_test_mode
        if is_test_mode:
            runtime_utils.log_message("РЕЖИМ ТЕСТИРОВАНИЯ АКТИВЕН", "WARN")

        runtime_utils.log_progress("► Получение интеграций Я.Диска...")
        integrations_response = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_INTEGRATIONS, {"firm_id": firm_id, "action": "GET"}, user_jwt)
        if not integrations_response:
            raise RuntimeError("Failed to get integrations.")

        yadisk_data = integrations_response.get('integrations', {}).get('yandex_disk', {})
        yadisk_token = yadisk_data.get('token')
        if not yadisk_token:
            raise RuntimeError("Yandex Disk token not found in integrations.")
        runtime_utils.log_progress(f"  {runtime_utils.TICK} Токен Яндекс Диска получен.")

        if not is_test_mode:
            last_sync_str = yadisk_data.get('last_sync_utc')
            if last_sync_str:
                try:
                    last_sync_dt = dt.datetime.fromisoformat(last_sync_str.replace("Z", "+00:00"))
                    if dt.datetime.now(timezone.utc) - last_sync_dt < timedelta(days=runtime_utils.SYNC_FREQUENCY_DAYS):
                        raise LogicError(f"Sync was performed less than {runtime_utils.SYNC_FREQUENCY_DAYS} days ago. Skipping.")
                except ValueError:
                    runtime_utils.log_message(f"Неверный формат даты '{last_sync_str}'. Игнорирование.", "WARN")

        # 3. Обновление времени синхронизации
        now_utc_iso = dt.datetime.now(timezone.utc).isoformat()
        yadisk_data['last_sync_utc'] = now_utc_iso
        update_payload = {"firm_id": firm_id, "action": "UPSERT", "payload": {"yandex_disk": yadisk_data}}
        if not runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_INTEGRATIONS, update_payload, user_jwt):
            raise RuntimeError("Failed to update sync time.")
        runtime_utils.log_progress(f"  {runtime_utils.TICK} Время последней синхронизации обновлено.")

        # 4. Получение задач
        data_fetchers.populate_employee_cache(user_jwt, firm_id)
        tasks_to_process = data_fetchers.get_all_tasks(user_jwt, firm_id)
        
        if not tasks_to_process:
            runtime_utils.log_message("Актуальных задач для обработки не найдено.")
            return {"statusCode": 200, "body": json.dumps({"message": "No tasks to process."})}

        # 5. Обработка задач
        with tempfile.TemporaryDirectory() as temp_dir:
            runtime_utils.log_progress(f"Всего задач для обработки: {len(tasks_to_process)}.")
            for i, task_info in enumerate(tasks_to_process):
                runtime_utils.log_progress(f"\n[ЗАДАЧА {i+1}/{len(tasks_to_process)}]")
                task_processor.process_single_task(task_info, user_jwt, firm_id, yadisk_token, temp_dir)
                time.sleep(0.1)

        runtime_utils.log_message(f"Синхронизация успешно завершена! Время выполнения: {time.time() - script_start:.2f} сек.")
        return {"statusCode": 200, "body": json.dumps({"message": f"Successfully processed {len(tasks_to_process)} tasks."})}

    except AuthError as e:
        runtime_utils.log_message(f"Ошибка прав доступа: {e}", "ERROR")
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        runtime_utils.log_message(f"Ошибка логики: {e}", "ERROR")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        runtime_utils.log_message(f"Критическая ошибка: {e}", "ERROR")
        import traceback
        runtime_utils.log_message(f"Трассировка: {traceback.format_exc()}", "ERROR")
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
    finally:
        runtime_utils.print_final_log()
```
### runtime_utils.py
```python
import json
import os
import re
import time
import threading
import datetime as dt
import requests
import logging

# --- Инициализация и Константы ---
LOG_MESSAGES = []
LOG_LOCK = threading.Lock()
TICK, CROSS, INFO, WARN = "✓", "✗", "→", "!"

MAX_RETRIES = 3
RETRY_BASE_DELAY_SEC = 3
SYNC_FREQUENCY_DAYS = int(os.environ.get("SYNC_FREQUENCY_DAYS", 7))
YADISK_BASE_FOLDER = os.environ.get("YADISK_BASE_FOLDER", "balansoved enterprise")

# --- Получение IAM-токена для вызова приватных функций ---
IAM_TOKEN_CACHE = {"token": None, "expires_at": 0}
METADATA_URL = "http://169.254.169.254/computeMetadata/v1/instance/service-accounts/default/token"

def get_iam_token() -> str:
    """Возвращает действующий IAM-токен, кешируя его до истечения."""
    now_ts = time.time()
    if IAM_TOKEN_CACHE["token"] and now_ts < IAM_TOKEN_CACHE["expires_at"]:
        return IAM_TOKEN_CACHE["token"]

    try:
        resp = requests.get(METADATA_URL, headers={"Metadata-Flavor": "Google"}, timeout=2)
        resp.raise_for_status()
        data = resp.json()
        IAM_TOKEN_CACHE["token"] = data["access_token"]
        IAM_TOKEN_CACHE["expires_at"] = now_ts + data.get("expires_in", 300) - 60
        return IAM_TOKEN_CACHE["token"]
    except Exception as e:
        log_message(f"Не удалось получить IAM-токен: {e}", "WARN")
        return ""

# --- ID Целевых функций ---
FUNCTION_ID_EDIT_INTEGRATIONS = os.environ.get("FUNCTION_ID_EDIT_INTEGRATIONS")
FUNCTION_ID_EDIT_EMPLOYEE = os.environ.get("FUNCTION_ID_EDIT_EMPLOYEE")
FUNCTION_ID_EDIT_TASK = os.environ.get("FUNCTION_ID_EDIT_TASK")
FUNCTION_ID_EDIT_CLIENT = os.environ.get("FUNCTION_ID_EDIT_CLIENT")
FUNCTION_ID_TARIFFS_AND_STORAGE_MANAGER = os.environ.get("FUNCTION_ID_TARIFFS_AND_STORAGE_MANAGER")
FUNCTION_ID_GET_USER_DATA = os.environ.get("FUNCTION_ID_GET_USER_DATA")

# --- Логирование ---
def log_message(message: str, level: str = "INFO"):
    timestamp = dt.datetime.now().strftime("%H:%M:%S")
    with LOG_LOCK:
        LOG_MESSAGES.append(f"[{timestamp}] [{level.upper()}] {message}")

def print_final_log():
    log_message("="*80)
    log_message("ПОЛНЫЙ ЛОГ ВЫПОЛНЕНИЯ:")
    for msg in LOG_MESSAGES:
        logging.info(msg) # Выводим в стандартный лог функции
    log_message("="*80)

def log_progress(message: str):
    clean_message = re.sub(r'\x1b\[[0-9;]*m', '', message).rstrip()
    if clean_message:
        log_message(clean_message, level="PROGRESS")

# --- Вызов функций ---
def invoke_function(function_id: str, payload: dict, user_jwt: str) -> dict | None:
    """Вызывает облачную функцию по HTTPS. Требует, чтобы функция была публичной
    либо авторизована через переданный JWT (Authorization header)."""

    if not function_id:
        log_message(f"ID функции не указан для вызова с payload: {str(payload)[:100]}", "ERROR")
        return None

    # Составляем URL облачной функции
    function_url = f"https://functions.yandexcloud.net/{function_id}"

    iam_token = get_iam_token()

    # HTTP-заголовки для вызова облачной функции
    http_headers = {"Content-Type": "application/json"}
    if iam_token:
        http_headers["Authorization"] = f"Bearer {iam_token}"

    # Добавляем пользовательский JWT прямо в HTTP-заголовок, как это делает scheduler-trigger
    http_headers["X-Forwarded-Authorization"] = f"Bearer {user_jwt}"

    invoke_event = json.dumps(payload)

    for attempt in range(MAX_RETRIES):
        try:
            resp = requests.post(function_url, data=invoke_event, headers=http_headers, timeout=30)

            if 200 <= resp.status_code < 300:
                try:
                    return resp.json()
                except ValueError:
                    log_message(f"Вызов {function_id} завершился без JSON-тела.", "WARN")
                    return {}
            else:
                # Логируем тело ответа, если возможно
                body_preview = resp.text[:200] if resp.text else "<empty>"
                log_message(f"Ошибка вызова {function_id}. Статус: {resp.status_code}. Тело: {body_preview}", "WARN")

        except requests.exceptions.RequestException as e:
            log_message(f"Исключение при вызове {function_id}: {e}", "WARN")

        # back-off перед повтором
        if attempt + 1 < MAX_RETRIES:
            time.sleep(RETRY_BASE_DELAY_SEC * (2 ** attempt))

    log_message(f"Все попытки вызова {function_id} провалены.", "ERROR")
    return None
```
### data_fetchers.py
```python
import json
import datetime as dt
import runtime_utils

CLIENT_NAME_CACHE, EMPLOYEE_NAME_CACHE = {}, {}
PAGE_SIZE = 100

def populate_employee_cache(user_jwt: str, firm_id: str):
    """(ИСПРАВЛЕНО) Заполняет кеш сотрудников из двух источников для полноты данных."""
    runtime_utils.log_message("Заполнение кеша сотрудников...")
    
    # 1. Вызов get-user-data для получения общего списка
    user_data = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_GET_USER_DATA, {}, user_jwt)
    if user_data:
        # Ищем список сотрудников в разных возможных ключах
        employees = []
        for key in ("employees", "users", "team", "staff"):
             if key in user_data and isinstance(user_data[key], list):
                employees = user_data[key]
                break
        for emp in employees:
            uid = emp.get("user_id") or emp.get("id") or emp.get("uid")
            name = emp.get("full_name") or emp.get("fullName") or emp.get("name")
            if uid and name:
                EMPLOYEE_NAME_CACHE[uid] = name
        runtime_utils.log_message(f"Кеш сотрудников пополнен из get-user-data: {len(employees)} записей.")

    # 2. Вызов edit-employee для получения полного списка по фирме
    emp_data = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_EMPLOYEE, {"firm_id": firm_id, "action": "GET_INFO"}, user_jwt)
    if emp_data and isinstance(emp_data.get('data'), list):
        employees = emp_data['data']
        for emp in employees:
            uid = emp.get("user_id")
            name = emp.get("full_name")
            if uid and name:
                EMPLOYEE_NAME_CACHE[uid] = name
        runtime_utils.log_message(f"Кеш сотрудников дополнен из edit-employee: {len(employees)} записей.")
    
    runtime_utils.log_message(f"Итоговый размер кеша сотрудников: {len(EMPLOYEE_NAME_CACHE)}.")

def get_client_name(client_id: str, user_jwt: str, firm_id: str) -> str:
    if client_id in CLIENT_NAME_CACHE: return CLIENT_NAME_CACHE[client_id]
    # Запрашиваем только актуальную версию клиента
    payload = {"firm_id": firm_id, "action": "GET", "client_id": client_id, "is_actual": True}
    response = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_CLIENT, payload, user_jwt)
    if response:
        # Поскольку is_actual=true, мы ожидаем список из одного элемента
        client_data = response.get('data')
        if isinstance(client_data, list) and client_data:
            name = client_data[0].get('client_name', client_id)
            CLIENT_NAME_CACHE[client_id] = name
            return name
        # Фоллбэк для старых версий или ошибок
        elif isinstance(client_data, dict):
            name = client_data.get('client_name', client_id)
            CLIENT_NAME_CACHE[client_id] = name
            return name

    CLIENT_NAME_CACHE[client_id] = client_id
    return client_id

def get_employee_name(employee_id: str, user_jwt: str, firm_id: str) -> str:
    """(ИСПРАВЛЕНО) Получает имя сотрудника из кеша, с фоллбэком на API-запрос."""
    if employee_id in EMPLOYEE_NAME_CACHE:
        return EMPLOYEE_NAME_CACHE[employee_id]
    
    runtime_utils.log_message(f"Имя для {employee_id} не найдено в кеше. Выполняю доп. запрос...", "WARN")
    payload = {"firm_id": firm_id, "action": "GET_INFO", "user_id_to_edit": employee_id}
    response = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_EMPLOYEE, payload, user_jwt)
    
    if response and isinstance(response.get('data'), dict):
        name = response['data'].get('full_name', employee_id)
        EMPLOYEE_NAME_CACHE[employee_id] = name
        return name
        
    EMPLOYEE_NAME_CACHE[employee_id] = employee_id
    return employee_id

def get_client_folder_name(client_ids_json: str, user_jwt: str, firm_id: str) -> str:
    from yandex_disk_uploader import sanitize_filename
    try:
        client_ids = json.loads(client_ids_json or '[]')
        if not client_ids: return "unassigned"
        folder_parts = [f"{sanitize_filename(get_client_name(cid, user_jwt, firm_id))} ({cid})" for cid in client_ids]
        return "-".join(sorted(folder_parts))
    except (json.JSONDecodeError, TypeError):
        return "invalid_client_id_format"

def get_all_tasks(user_jwt: str, firm_id: str) -> list:
    tasks_to_process = []
    runtime_utils.log_message("Получение списка задач...")
    page = 0
    while True:
        resp = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_TASK, {"firm_id": firm_id, "action": "GET", "page": page}, user_jwt)
        if not resp or not resp.get('data'): break
        tasks = resp['data']
        tasks_to_process.extend([{'type': 'timeless', 'data': t} for t in tasks])
        if len(tasks) < PAGE_SIZE: break
        page += 1

    current_year = dt.datetime.now().year
    for month in range(1, 13):
        resp = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_TASK, {"firm_id": firm_id, "action": "GET", "get_dated_tasks": True, "year": current_year, "month": month}, user_jwt)
        if resp and resp.get('data'):
            tasks_to_process.extend([{'type': 'dated', 'data': t, 'year': current_year, 'month': month} for t in resp['data']])
    
    return tasks_to_process
```
### task_processor.py
```python
import json
import os
import requests
from io import BytesIO
from concurrent.futures import ThreadPoolExecutor, as_completed

import runtime_utils
import data_fetchers
import yandex_disk_uploader
import pdf_generator
import datetime as dt

# Повышаем максимальную общую задержку backoff в retry до 30 секунд
requests.packages.urllib3.util.retry.Retry.BACKOFF_MAX = 30
def download_file_to_memory(url: str):
    session = requests.Session()
    retries = requests.packages.urllib3.util.retry.Retry(total=5, backoff_factor=1, status_forcelist=[500, 502, 503, 504])
    session.mount('https://', requests.adapters.HTTPAdapter(max_retries=retries))
    try:
        with session.get(url, stream=True, timeout=60) as r:
            r.raise_for_status()
            buffer = BytesIO()
            for chunk in r.iter_content(chunk_size=8192):
                buffer.write(chunk)
            return buffer.getvalue()
    except requests.exceptions.RequestException as e:
        runtime_utils.log_message(f"Ошибка скачивания файла с {url}: {e}", "ERROR")
        return None

def process_single_task(task_info: dict, user_jwt: str, firm_id: str, yadisk_token: str, temp_dir: str):
    task_summary = task_info['data']
    task_id = task_summary['task_id']
    task_title_raw = task_summary.get('title', 'Без_названия')
    task_title_sanitized = yandex_disk_uploader.sanitize_filename(task_title_raw)
    runtime_utils.log_progress(f"--- Обработка задачи: {task_title_raw} ---")

    get_task_payload = {"firm_id": firm_id, "action": "GET", "task_id": task_id}
    task_response = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_EDIT_TASK, get_task_payload, user_jwt)
    if not task_response: return
    task_data = task_response.get('data', {})

    base_path_on_disk = f"{runtime_utils.YADISK_BASE_FOLDER}/бессрочные задачи" if task_info['type'] == 'timeless' else f"{runtime_utils.YADISK_BASE_FOLDER}/задачи/{task_info['year']}/{task_info['month']:02d}"
    
    client_folder_name = data_fetchers.get_client_folder_name(task_data.get('client_ids_json'), user_jwt, firm_id)
    task_folder_name = f"{task_title_sanitized} ({task_id})"
    task_folder_on_disk = f"{base_path_on_disk}/{client_folder_name}/{task_folder_name}"

    runtime_utils.log_progress(f"  {runtime_utils.INFO} Путь на Диске: {task_folder_on_disk}")
    
    # ► Умный пропуск с еженедельным обновлением в течение 30 дней
    marker_file_path = f"{task_folder_on_disk}/task_info_raw.json"
    disk_info = yandex_disk_uploader.get_resource_info(yadisk_token, marker_file_path)

    now_utc = dt.datetime.now(dt.timezone.utc)
    skip_attachments = False

    if disk_info:
        try:
            created_dt = dt.datetime.fromisoformat(disk_info.get('created').replace("Z", "+00:00"))
            modified_dt = dt.datetime.fromisoformat(disk_info.get('modified').replace("Z", "+00:00"))
        except Exception:
            # Если не удаётся распарсить даты, выполняем полную синхронизацию
            created_dt = modified_dt = None

        if created_dt and modified_dt:
            age_days = (now_utc - created_dt).days
            days_since_mod = (now_utc - modified_dt).days

            if age_days > 30:
                # Папка старше 30 дней – дальнейшие обновления не требуются
                runtime_utils.log_progress(f"  {runtime_utils.INFO} Задача старше 30 дней, пропуск обновления.")
                return

            if days_since_mod < 7:
                # Последнее обновление было менее недели назад – рано обновлять
                runtime_utils.log_progress(f"  {runtime_utils.INFO} Задача обновлялась менее недели назад, пропуск.")
                return

            # Требуется обновление без перезагрузки вложений
            runtime_utils.log_progress(f"  {runtime_utils.WARN} Выполняется плановое недельное обновление задачи (без вложений)...")
            skip_attachments = True
        else:
            runtime_utils.log_progress(f"  {runtime_utils.WARN} Не удалось определить даты ресурса, выполняется полная синхронизация...")

    if not yandex_disk_uploader.ensure_path_recursively(yadisk_token, task_folder_on_disk): return

    new_attachments_with_urls = []
    if not skip_attachments:
        attachments_from_task = json.loads(task_data.get('attachments_json', '[]'))
        if attachments_from_task:
            files_folder_on_disk = f"{task_folder_on_disk}/файлы"
            if not yandex_disk_uploader.ensure_folder_exists(yadisk_token, files_folder_on_disk): return
            runtime_utils.log_progress(f"  {runtime_utils.INFO} Найдено вложений: {len(attachments_from_task)}. Загрузка...")

            def handle_attachment(attachment: dict):
                file_key = attachment.get('file_key') or attachment.get('fileKey')
                filename = attachment.get('name', os.path.basename(file_key or 'unknown_file'))
                if not file_key: return None
                
                payload = {"firm_id": firm_id, "action": "GET_DOWNLOAD_URL", "file_key": file_key}
                resp = runtime_utils.invoke_function(runtime_utils.FUNCTION_ID_TARIFFS_AND_STORAGE_MANAGER, payload, user_jwt)
                if not resp or not resp.get('download_url'): return None
                
                file_bytes = download_file_to_memory(resp['download_url'])
                if not file_bytes: return None

                target_path = f"{files_folder_on_disk}/{filename}"
                yadisk_link = yandex_disk_uploader.upload_and_get_link_from_bytes(yadisk_token, file_bytes, target_path)
                if not yadisk_link: return None

                return {"name": filename, "public_url": yadisk_link, "file_key": file_key}

            with ThreadPoolExecutor(max_workers=4) as executor:
                future_map = {executor.submit(handle_attachment, att): att for att in attachments_from_task}
                for future in as_completed(future_map):
                    result = future.result()
                    if result: new_attachments_with_urls.append(result)
        else:
            runtime_utils.log_progress(f"  {runtime_utils.INFO} Вложения отсутствуют.")
    else:
        runtime_utils.log_progress(f"  {runtime_utils.INFO} Пропуск загрузки вложений для еженедельного обновления.")

    runtime_utils.log_progress(f"  {runtime_utils.INFO} Подготовка данных для PDF...")
    participant_roles = ['assignee_ids_json', 'creator_ids_json', 'observer_ids_json']
    participants_map = {role.split('_')[0]: [f"{data_fetchers.get_employee_name(eid, user_jwt, firm_id)} ({eid})" for eid in json.loads(task_data.get(role, '[]'))] for role in participant_roles}
    clients_map = [f"{data_fetchers.get_client_name(cid, user_jwt, firm_id)} ({cid})" for cid in json.loads(task_data.get('client_ids_json', '[]'))]

    pdf_path = os.path.join(temp_dir, 'task_report.pdf')
    if pdf_generator.generate_task_pdf(pdf_path, task_data, participants_map, clients_map, new_attachments_with_urls):
        yandex_disk_uploader.upload_and_get_link(yadisk_token, pdf_path, f"{task_folder_on_disk}/Отчет по задаче.pdf")
        runtime_utils.log_progress(f"  {runtime_utils.TICK} Отчет PDF сохранен.")
    
    if not skip_attachments:
        task_data['attachments_json'] = json.dumps(new_attachments_with_urls, ensure_ascii=False)

    task_json_path = os.path.join(temp_dir, 'task_info_raw.json')
    with open(task_json_path, 'w', encoding='utf-8') as f:
        json.dump(task_data, f, ensure_ascii=False, indent=4)
    yandex_disk_uploader.upload_and_get_link(yadisk_token, task_json_path, f"{task_folder_on_disk}/task_info_raw.json")
    runtime_utils.log_progress(f"  {runtime_utils.TICK} Файл JSON сохранен.")
```
### custom_errors.py
```python
class AuthError(Exception):
    """Ошибка аутентификации или прав доступа."""
    pass

class LogicError(Exception):
    """Ошибка в логике запроса (неверные параметры)."""
    pass

```
