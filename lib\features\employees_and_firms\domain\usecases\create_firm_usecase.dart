import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/employees_repository.dart';
import 'package:dartz/dartz.dart';

class CreateFirmUseCase {
  final IEmployeesRepository repository;
  const CreateFirmUseCase(this.repository);

  Future<Either<Failure, Unit>> call(String firmName) {
    return repository.createFirm(firmName);
  }
}
