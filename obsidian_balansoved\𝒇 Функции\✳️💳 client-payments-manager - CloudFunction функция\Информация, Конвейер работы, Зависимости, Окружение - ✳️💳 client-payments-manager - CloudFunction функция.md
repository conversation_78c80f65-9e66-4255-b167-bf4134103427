
Идентификатор - d4evkcnph6jfho1qlcfa
Описание - 💳 Управляет записями об оплатах услуг от клиентов фирмы.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя с правами `OWNER` или `ADMIN`.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы, в контексте которой выполняется операция.
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
		- **Для `GET`**:
			- `year` (integer, **обязательно**): Год, за который запрашиваются данные.
			- `client_id` (string, необязательно): Если указан, вернутся данные только по этому клиенту.
		- **Для `UPSERT`**:
			- `payload` (object, **обязательно**): Объект с данными для записи. **Суммы передаются в рублях.**
                ```json
                {
                  "client_id": "...",
                  "period": "2025-07",
                  "actual_amount_paid": 5800.50,
                  "tariff_annual_amount": 70000.00,
                  "actual_payment_date": "2025-07-15"
                }
                ```
		- **Для `DELETE`**:
			- `client_id` (string, **обязательно**): ID клиента.
			- `period` (string, **обязательно**): Период в формате "YYYY-MM".

Внутренняя работа:
    -> Авторизация:
        -> Извлечение и проверка JWT-токена.
        -> Верификация и получение user_id.
    -> Парсинг тела запроса.
    -> Проверка обязательных параметров: firm_id и action.
    -> Проверка прав: OWNER или ADMIN в firms-database.
    -> Инициализация подключения к client-payments-database.
    -> Обеспечение существования таблицы client_payments_{firm_id}:
        -> Если таблицы нет, создание с колонками (client_id, period_start_date, actual_amount_kopeks, tariff_annual_amount_kopeks, actual_payment_date, created_at, updated_at).
    -> Маршрутизация по action в транзакции:
        -> GET:
            -> Проверка года, опционально client_id.
            -> Запрос записей за год, конвертация копеек в рубли (float).
        -> UPSERT:
            -> Валидация payload (client_id, period, суммы, optional actual_payment_date).
            -> Конвертация рублей в копейки (Int64) и даты в Date.
            -> Проверка существования записи для сохранения created_at.
            -> UPSERT с обновлением updated_at.
        -> DELETE:
            -> Валидация client_id и period.
            -> Удаление записи.
    -> Обработка исключений и возврат статусов.

На выходе:
    -> 200 OK (GET): {"data": [{"client_id": "...", "period": "YYYY-MM", "actual_amount_paid": float, "tariff_annual_amount": float, "actual_payment_date": "YYYY-MM-DD" or null, "created_at": str, "updated_at": str}]}
    -> 200/201 OK (UPSERT): {"message": "Payment record updated"} или {"message": "Payment record created"} (201 для новой записи)
    -> 200 OK (DELETE): {"message": "Payment record deleted"}
    -> 400 Bad Request, 403 Forbidden, 404 Not Found, 500 Internal Server Error.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
	- `YDB_ENDPOINT_CLIENT_PAYMENTS`, `YDB_DATABASE_CLIENT_PAYMENTS` ([[💾 client-payments-database - База данных YandexDatabase]])
	- `SA_KEY_FILE` ([[ydb_sa_key.json]])
	- `JWT_SECRET`