import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/notification_entity.dart';
import '../repositories/notifications_repository.dart';

class GetNotifications {
  final NotificationsRepository repository;

  GetNotifications(this.repository);

  Future<Either<Failure, List<NotificationEntity>>> call({
    required int page,
    bool getArchived = false,
  }) async {
    return await repository.getNotifications(
      page: page,
      getArchived: getArchived,
    );
  }
}