import requests
import json
from colorama import init, Fore, Style

# Инициализируем colorama для красивого вывода в консоли
init(autoreset=True)

# URL эндпоинта для ручного запуска триггера.
# Я взял его из вашего файла "🔥 scheduler-api - Gateway-api Шлюз.md"
API_SCHEDULER_TRIGGER_URL = "https://d5dln4usaaktunc0kvia.8wihnuyr.apigw.yandexcloud.net/trigger"

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL
INFO = Fore.CYAN + "→" + Style.RESET_ALL

def trigger_scheduler():
    """
    Отправляет запрос для ручного запуска проверки запланированных событий.
    """
    print(f"{Style.BRIGHT}► Отправка запроса на запуск триггера...")
    print(f"{INFO} URL: {API_SCHEDULER_TRIGGER_URL}")

    try:
        # Отправляем пустой POST-запрос. Тело и заголовки не требуются.
        response = requests.post(API_SCHEDULER_TRIGGER_URL, timeout=30) # Таймаут 30 секунд

        # Проверяем, что ответ успешный (HTTP 200)
        if response.status_code == 200:
            print(f"\n{TICK} Успех! Статус ответа: {response.status_code}")
            try:
                # Пытаемся красиво распечатать JSON-ответ от функции
                response_data = response.json()
                print(f"{INFO} Ответ от сервера:")
                print(Style.DIM + json.dumps(response_data, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                # Если ответ не в формате JSON, просто выводим как текст
                print(f"{INFO} Текстовый ответ от сервера: {response.text}")
        else:
            # Если сервер вернул ошибку
            print(f"\n{CROSS} Ошибка! Сервер вернул статус: {response.status_code}")
            print(f"{INFO} Текст ошибки:")
            try:
                print(Fore.RED + json.dumps(response.json(), indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)

    except requests.exceptions.Timeout:
        print(f"\n{CROSS} Ошибка: Запрос превысил таймаут (30 секунд).")
        print(f"{INFO} Это может означать, что функция-триггер выполняется долго. Проверьте её логи.")
    except requests.exceptions.RequestException as e:
        # В случае проблем с сетью или DNS
        print(f"\n{CROSS} Ошибка сети при отправке запроса.")
        print(f"{INFO} Детали: {e}")

if __name__ == "__main__":
    trigger_scheduler()