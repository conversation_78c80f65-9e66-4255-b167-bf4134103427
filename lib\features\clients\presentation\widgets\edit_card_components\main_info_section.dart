import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/form_widgets.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';

class MainInfoSection extends StatelessWidget {
  final bool isEditing;
  final TextEditingController nameCtrl;
  final TextEditingController shortNameCtrl;
  final TextEditingController innCtrl;
  final TextEditingController commentCtrl;
  final TextEditingController creationDateCtrl;
  final GlobalKey creationDateKey;
  final FocusNode creationDateFN;
  final DateTime? creationDate;
  final String? ownershipForm;
  final String? directorType;
  final String? directorName;
  final TextEditingController? directorNameCtrl;
  final TextEditingController? directorStartDateCtrl;
  final GlobalKey? directorStartDateKey;
  final FocusNode? directorStartDateFN;
  final DateTime? directorStartDate;
  final List<KppInfo> kppInfo; // Новый параметр для информации о КПП
  final DateTime? digitalSignatureExpiryDate;
  final TextEditingController digitalSignatureExpiryDateCtrl;
  final GlobalKey digitalSignatureExpiryDateKey;
  final FocusNode digitalSignatureExpiryDateFN;
  final Function(DateTime?) onDateChanged;
  final Function(DateTime?) onDigitalSignatureExpiryDateChanged;
  final Function(String?) onOwnershipFormChanged;
  final Function(String?) onDirectorTypeChanged;
  final Function(String?) onDirectorNameChanged;
  final Function(DateTime?) onDirectorStartDateChanged;
  final Function(String, String) copyToClipboard;
  final VoidCallback? onCreateSystemTask;

  const MainInfoSection({
    super.key,
    required this.isEditing,
    required this.nameCtrl,
    required this.shortNameCtrl,
    required this.innCtrl,
    required this.commentCtrl,
    required this.creationDate,
    required this.ownershipForm,
    required this.directorType,
    this.directorName,
    this.directorNameCtrl,
    this.directorStartDateCtrl,
    this.directorStartDateKey,
    this.directorStartDateFN,
    this.directorStartDate,
    required this.kppInfo, // Новый обязательный параметр
    this.digitalSignatureExpiryDate,
    required this.digitalSignatureExpiryDateCtrl,
    required this.digitalSignatureExpiryDateKey,
    required this.digitalSignatureExpiryDateFN,
    required this.onDateChanged,
    required this.onDigitalSignatureExpiryDateChanged,
    required this.onOwnershipFormChanged,
    required this.onDirectorTypeChanged,
    required this.onDirectorNameChanged,
    required this.onDirectorStartDateChanged,
    required this.copyToClipboard,
    required this.creationDateCtrl,
    required this.creationDateKey,
    required this.creationDateFN,
    this.onCreateSystemTask,
  });

  bool _isKppInfoRequired() {
    return ownershipForm == 'АО' ||
        ownershipForm == 'ООО' ||
        ownershipForm == 'КХ';
  }

  String? _validateKppInfo() {
    if (_isKppInfoRequired() && kppInfo.isEmpty) {
      return 'Добавьте хотя бы одну запись КПП';
    }
    return null;
  }

  String _getCurrentKpp() {
    if (kppInfo.isNotEmpty) {
      return kppInfo.first.number;
    }
    return '';
  }

  bool _isDirectorTypeRequired() {
    return ownershipForm == 'АО' ||
        ownershipForm == 'ООО' ||
        ownershipForm == 'КХ';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.info_outline, size: 20, color: Colors.blue),
            SizedBox(width: 8),
            Text(
              'Основная информация',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.smallSpacing),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: nameCtrl,
                decoration: const InputDecoration(
                  labelText: 'Наименование *',
                  prefixIcon: Icon(Icons.business, size: 20),
                ),
                readOnly: !isEditing,
                onTap:
                    isEditing
                        ? null
                        : () => copyToClipboard(nameCtrl.text, 'Наименование'),
                validator:
                    (v) =>
                        (v == null || v.trim().isEmpty)
                            ? 'Введите наименование'
                            : null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: shortNameCtrl,
                decoration: const InputDecoration(
                  labelText: 'Сокращённое наименование *',
                  prefixIcon: Icon(Icons.short_text, size: 20),
                ),
                readOnly: !isEditing,
                onTap:
                    isEditing
                        ? null
                        : () => copyToClipboard(
                          shortNameCtrl.text,
                          'Сокращённое наименование',
                        ),
                validator:
                    (v) =>
                        (v == null || v.trim().isEmpty)
                            ? 'Введите сокращённое наименование'
                            : null,
              ),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),
        Row(
          children: [
            Expanded(
              child: UniversalNumberField(
                controller: innCtrl,
                labelText: 'ИНН',
                prefixIcon: Icons.confirmation_number_outlined,
                fieldType: NumberFieldType.integer,
                isRequired: true,
                isEditing: isEditing,
                onTap: () => copyToClipboard(innCtrl.text, 'ИНН'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(child: _buildCurrentKppField(context)),
          ],
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),
        Row(
          children: [
            Expanded(
              child: DateInputFormField(
                fieldKey: creationDateKey,
                controller: creationDateCtrl,
                focusNode: creationDateFN,
                labelText: 'Дата регистрации *',
                prefixIcon: Icons.calendar_today,
                isEditing: isEditing,
                onDateChanged: onDateChanged,
                onIconTap: () async {
                  final date = await SmartDatePickerDialog.show(
                    context: context,
                    initialDate: creationDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime.now(),
                    helpText: 'Выберите дату регистрации',
                    allowClear: true,
                  );
                  if (date != null) {
                    onDateChanged(date);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Введите дату регистрации'; // Обязательное поле
                  }
                  try {
                    DateFormat('dd.MM.yyyy').parseStrict(value);
                    return null;
                  } catch (e) {
                    return 'Формат: дд.мм.гггг';
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(child: _buildOwnershipFormField(context)),
          ],
        ),
        if (_isDirectorTypeRequired()) ...[
          const SizedBox(height: ClientConstants.fieldSpacing),
          _buildDirectorTypeField(context),
          const SizedBox(height: ClientConstants.fieldSpacing),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: directorNameCtrl,
                  decoration: const InputDecoration(
                    labelText: 'ФИО руководителя *',
                    prefixIcon: Icon(Icons.person, size: 20),
                  ),
                  readOnly: !isEditing,
                  onTap:
                      isEditing
                          ? null
                          : () => copyToClipboard(
                            directorNameCtrl?.text ?? '',
                            'ФИО руководителя',
                          ),
                  onChanged: onDirectorNameChanged,
                  validator: (value) {
                    if (_isDirectorTypeRequired() &&
                        (value == null || value.trim().isEmpty)) {
                      return 'Введите ФИО руководителя';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DateInputFormField(
                  controller: directorStartDateCtrl!,
                  focusNode: directorStartDateFN,
                  labelText: 'Дата начала полномочий *',
                  prefixIcon: Icons.calendar_today,
                  isEditing: isEditing,
                  onDateChanged: onDirectorStartDateChanged,
                  onIconTap: () async {
                    final date = await SmartDatePickerDialog.show(
                      context: context,
                      initialDate: directorStartDate,
                      firstDate: DateTime(1900),
                      lastDate: DateTime.now(),
                      helpText: 'Выберите дату начала полномочий',
                      allowClear: true,
                    );
                    if (date != null) {
                      onDirectorStartDateChanged(date);
                    }
                  },
                  validator: (value) {
                    if (_isDirectorTypeRequired() &&
                        (value == null || value.isEmpty)) {
                      return 'Введите дату начала полномочий';
                    }
                    if (value != null && value.isNotEmpty) {
                      try {
                        DateFormat('dd.MM.yyyy').parseStrict(value);
                        return null;
                      } catch (e) {
                        return 'Формат: дд.мм.гггг';
                      }
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
        const SizedBox(height: ClientConstants.fieldSpacing),
        TextFormField(
          controller: commentCtrl,
          decoration: const InputDecoration(
            labelText: 'Комментарий',
            prefixIcon: Icon(Icons.note_alt_outlined, size: 20),
          ),
          maxLines: 3,
          readOnly: !isEditing,
          onTap:
              isEditing
                  ? null
                  : () => copyToClipboard(commentCtrl.text, 'Комментарий'),
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: DateInputFormField(
                fieldKey: digitalSignatureExpiryDateKey,
                controller: digitalSignatureExpiryDateCtrl,
                focusNode: digitalSignatureExpiryDateFN,
                labelText: 'Срок действия ЭЦП',
                prefixIcon: Icons.verified_user,
                isEditing: isEditing,
                onDateChanged: onDigitalSignatureExpiryDateChanged,
                onIconTap: () async {
                  final date = await SmartDatePickerDialog.show(
                    context: context,
                    initialDate: digitalSignatureExpiryDate,
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(
                      const Duration(days: 3650),
                    ), // 10 лет
                    helpText: 'Выберите срок действия ЭЦП',
                    allowClear: true,
                  );
                  if (date != null) {
                    onDigitalSignatureExpiryDateChanged(date);
                  }
                },
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    try {
                      DateFormat('dd.MM.yyyy').parseStrict(value);
                      return null;
                    } catch (e) {
                      return 'Формат: дд.мм.гггг';
                    }
                  }
                  return null;
                },
              ),
            ),
            if (isEditing && onCreateSystemTask != null)
              IconButton(
                icon: const Icon(Icons.add_task),
                tooltip: 'Создать задачу на обновление ЭЦП',
                onPressed: onCreateSystemTask,
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildCurrentKppField(BuildContext context) {
    final currentKpp = _getCurrentKpp();
    final validationError = _validateKppInfo();

    return FormField<String>(
      validator: (value) => validationError,
      builder: (FormFieldState<String> field) {
        return InputDecorator(
          decoration: InputDecoration(
            labelText: 'Текущий КПП',
            prefixIcon: const Icon(Icons.pin_outlined, size: 20),
            errorText: field.errorText,
            border: const OutlineInputBorder(),
          ),
          child: GestureDetector(
            onTap: () => copyToClipboard(currentKpp, 'КПП'),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      currentKpp.isNotEmpty ? currentKpp : 'Не указан',
                      style: TextStyle(
                        color:
                            currentKpp.isNotEmpty
                                ? Theme.of(context).textTheme.bodyLarge?.color
                                : Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  if (currentKpp.isNotEmpty)
                    const Icon(Icons.copy, size: 16, color: Colors.grey),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOwnershipFormField(BuildContext context) {
    final theme = Theme.of(context);
    if (isEditing) {
      return DropdownButtonFormField<String>(
        value: ownershipForm,
        decoration: const InputDecoration(
          labelText: 'Форма собственности',
          prefixIcon: Icon(Icons.account_balance_outlined, size: 20),
        ),
        items:
            ClientConstants.ownershipForms
                .map((form) => DropdownMenuItem(value: form, child: Text(form)))
                .toList(),
        onChanged: onOwnershipFormChanged,
      );
    }

    return GestureDetector(
      onTap: () => copyToClipboard(ownershipForm ?? '', 'Форма собственности'),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Icon(
              Icons.account_balance_outlined,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                ownershipForm ?? 'Не указано',
                style: TextStyle(
                  color: ownershipForm == null ? Colors.grey : null,
                ),
              ),
            ),
            const Icon(Icons.copy, size: 16, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildDirectorTypeField(BuildContext context) {
    final theme = Theme.of(context);
    if (isEditing) {
      return DropdownButtonFormField<String>(
        value: directorType,
        decoration: const InputDecoration(
          labelText: 'Руководитель *',
          prefixIcon: Icon(Icons.person_outline, size: 20),
        ),
        items:
            ClientConstants.directorTypes
                .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                .toList(),
        onChanged: onDirectorTypeChanged,
        validator: (value) {
          if (_isDirectorTypeRequired() && (value == null || value.isEmpty)) {
            return 'Выберите тип руководителя';
          }
          return null;
        },
      );
    }

    return GestureDetector(
      onTap: () => copyToClipboard(directorType ?? '', 'Руководитель'),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Icon(
              Icons.person_outline,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                directorType ?? 'Не указано',
                style: TextStyle(
                  color: directorType == null ? Colors.grey : null,
                ),
              ),
            ),
            const Icon(Icons.copy, size: 16, color: Colors.grey),
          ],
        ),
      ),
    );
  }
}
