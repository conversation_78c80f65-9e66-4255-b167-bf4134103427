import requests
import json
from datetime import datetime

# --- НАСТРОЙКИ (установлены согласно вашим требованиям) ---
API_KEY = "d8f4386838cbe8f8ba1ff887fbd6faaba30abf06"
INN_TO_CHECK = "450212912749"
API_URL = "https://suggestions.dadata.ru/suggestions/api/4_1/rs/findById/party"
# --- КОНЕЦ НАСТРОЕК ---


# СПРАВОЧНИК ДЛЯ СОПОСТАВЛЕНИЯ ОКВЭД С ВАШИМИ КАТЕГОРИЯМИ
# Ключ - ваша категория, Значение - список кодов или их начальных цифр,
# которые относятся к этой категории.
# Этот справочник можно и нужно дополнять.
ACTIVITY_MAPPER = {
    "Сельское хозяйство": ["01"],
    "Ломбард": ["64.92.6"],
    "МКК / КПК": ["64.92", "64.99"],
    "Производство и оборот алкоголя": ["11.0", "46.34.2", "47.25"],
    # 'Экспорт в страны ЕАЭС' - невозможно определить по коду ОКВЭД,
    # так как это характеристика деятельности, а не ее вид.
}


def get_company_info(inn: str):
    """
    Отправляет запрос к DaData API для получения информации о компании по ИНН.
    """
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Token {API_KEY}"
    }
    # Мы запрашиваем список ОКВЭД, чтобы проанализировать их все
    data = {
        "query": inn,
        "branch_type": "MAIN",
        "count": 1
    }

    try:
        response = requests.post(API_URL, data=json.dumps(data), headers=headers)
        response.raise_for_status()

        result = response.json()

        # 1. Вывод сырого JSON-ответа
        print("--- СЫРОЙ JSON ОТВЕТ ОТ API ---")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        print("--------------------------------\n")

        # 2. Обработка и форматированный вывод
        print("--- ОБРАБОТАННАЯ ИНФОРМАЦИЯ ---")
        if not result.get('suggestions'):
            print(f"❌ Компания с ИНН {inn} не найдена.")
            return

        company_data = result['suggestions'][0]['data']
        process_and_print(company_data)

    except requests.exceptions.HTTPError as http_err:
        print(f"❌ HTTP ошибка: {http_err}")
        print(f"Тело ответа: {response.text}")
    except requests.exceptions.RequestException as err:
        print(f"❌ Сетевая ошибка: {err}")
    except Exception as e:
        print(f"❌ Произошла непредвиденная ошибка: {e}")


def get_fio(data: dict):
    """Определяет ФИО руководителя для ЮЛ или самого ИП."""
    if data.get('management') and data['management'].get('name'):
        return data['management']['name']
    if data.get('fio'):
        fio_parts = [
            data['fio'].get('surname', ''),
            data['fio'].get('name', ''),
            data['fio'].get('patronymic', '')
        ]
        return ' '.join(filter(None, fio_parts))
    return "❌"


def map_okved_to_activity_types(all_okveds: list or None) -> str:
    """
    Проверяет все ОКВЭД компании и сопоставляет их с категориями из справочника.
    """
    if not all_okveds:
        return "Нет данных для анализа ❌"

    found_activities = set()  # Используем set, чтобы избежать дубликатов категорий

    for okved_item in all_okveds:
        okved_code = okved_item.get("code", "")
        for activity_name, activity_codes in ACTIVITY_MAPPER.items():
            for code_prefix in activity_codes:
                if okved_code.startswith(code_prefix):
                    found_activities.add(activity_name)

    if not found_activities:
        return "Совпадений с категориями не найдено"

    return ", ".join(sorted(list(found_activities)))


def process_and_print(data: dict):
    """
    Форматирует и выводит данные в консоль согласно требуемому списку.
    """

    def get_nested(d, *keys, default="❌"):
        for key in keys:
            if isinstance(d, dict) and d.get(key) is not None:
                d = d[key]
            else:
                return default
        return d if d is not None else default

    reg_date_ts = get_nested(data, 'state', 'registration_date')
    reg_date = datetime.fromtimestamp(reg_date_ts / 1000).strftime('%d.%m.%Y') if isinstance(reg_date_ts, int) else "❌"

    main_okved = "❌"
    okveds_list = get_nested(data, 'okveds')
    if okveds_list != "❌" and isinstance(okveds_list, list):
        for o in okveds_list:
            if o.get('main'):
                main_okved = f"{o.get('code', '')} - {o.get('name', '')}"
                break
    else:
        main_okved = get_nested(data, 'okved')

    tax_system = get_nested(data, 'finance', 'tax_system', default="Нет данных ❌")
    vat_status = "Нет данных ❌"
    if get_nested(data, 'finance') != '❌':
        is_vat_payer = get_nested(data, 'finance', 'vat')
        vat_status = 'Плательщик' if is_vat_payer else 'Не является плательщиком'

    # Получаем все ОКВЭД и сопоставляем их с нашими категориями
    all_okved_codes = get_nested(data, 'okveds', default=[])
    # Если список пуст, добавим основной ОКВЭД для проверки
    if not all_okved_codes and data.get('okved'):
        all_okved_codes.append({'code': data['okved'], 'main': True})

    special_activities = map_okved_to_activity_types(all_okved_codes)

    print(f"- Название: {get_nested(data, 'name', 'full_with_opf')}")
    print(f"- Сокращённое название: {get_nested(data, 'name', 'short_with_opf')}")
    print(f"- Контакты (ФИО): {get_fio(data)}")
    print(f"- Контакты (телефон, имейл): {get_nested(data, 'phones')} / {get_nested(data, 'emails')}")
    print(f"- Дата создания (регистрации): {reg_date}")
    print(f"- ТИП фиксированных взносов ИП: ❌")
    print(f"- ТИП формы собственности: {get_nested(data, 'opf', 'full')}")
    print(f"- ТИП системы налогообложения: {tax_system}")
    print(f"- ТИП налога на прибыль: ❌")
    print(f"- ТИП НДС: {vat_status}")
    print(f"- ТИП имущества (транспорт / земля / недвижимость): ❌")
    print(f"- ТИП отчётности (электронная / бумажная): ❌")
    print(f"- ТИП оператора отчётности (СБИС, контур, 1с): ❌")
    print(f"- ТИП подакцизных товаров: ❌")
    print(f"- ТИП срок оплаты акциза: ❌")
    print(f"- ТИП ЕНП: ❌")
    print(f"- ТИП виды деятельности (основной, из реестра): {main_okved}")
    print(f"- ТИП виды деятельности (согласно вашим категориям): {special_activities}")
    print(f"- ТИП НДФЛ: ❌")
    print(f"- Список, доп ТЕГИ: ❌")
    print(f"- Выплата зарплаты, дата + дата переноса: ❌")
    print(f"- Выплата аванса, дата + дата переноса: ❌")
    print(f"- Уплата ндфл, дата + дата переноса: ❌")
    print(f"- СПИСОК, патенты (json структуры): ❌")
    print(f"- Комментарий: ❌")


if __name__ == "__main__":
    print(f"Выполняю запрос для ИНН: {INN_TO_CHECK}...")
    get_company_info(INN_TO_CHECK)