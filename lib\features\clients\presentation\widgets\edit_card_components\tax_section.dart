import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/form_widgets.dart';

class TaxSection extends StatelessWidget {
  final bool isEditing;
  final List<String> taxSystems;
  final List<String> profitTaxTypes;
  final List<String> vatTypes;
  final List<String> propertyTypes;
  final String? reportingType;
  final String? reportingOperator;
  final List<String> exciseGoods;
  final List<String> excisePaymentTerms;
  final List<String> edoOperators;
  final String? enpType;
  final String? ndflType;
  final Function(String) onTaxSystemAdd;
  final Function(String) onTaxSystemRemove;
  final Function(String) onProfitTaxTypeAdd;
  final Function(String) onProfitTaxTypeRemove;
  final Function(String) onVatTypeAdd;
  final Function(String) onVatTypeRemove;
  final Function(String) onPropertyTypeAdd;
  final Function(String) onPropertyTypeRemove;
  final Function(String?) onReportingTypeChanged;
  final Function(String?) onReportingOperatorChanged;
  final Function(String) onExciseGoodAdd;
  final Function(String) onExciseGoodRemove;
  final Function(String) onExcisePaymentTermAdd;
  final Function(String) onExcisePaymentTermRemove;
  final Function(String) onEdoOperatorAdd;
  final Function(String) onEdoOperatorRemove;
  final Function(String?) onEnpTypeChanged;
  final Function(String?) onNdflTypeChanged;
  final Function(String, String) copyToClipboard;
  final bool highlightEnp;
  final GlobalKey? enpKey;

  const TaxSection({
    super.key,
    required this.isEditing,
    required this.taxSystems,
    required this.profitTaxTypes,
    required this.vatTypes,
    required this.propertyTypes,
    required this.reportingType,
    required this.reportingOperator,
    required this.exciseGoods,
    required this.excisePaymentTerms,
    required this.edoOperators,
    required this.enpType,
    required this.ndflType,
    required this.onTaxSystemAdd,
    required this.onTaxSystemRemove,
    required this.onProfitTaxTypeAdd,
    required this.onProfitTaxTypeRemove,
    required this.onVatTypeAdd,
    required this.onVatTypeRemove,
    required this.onPropertyTypeAdd,
    required this.onPropertyTypeRemove,
    required this.onReportingTypeChanged,
    required this.onReportingOperatorChanged,
    required this.onExciseGoodAdd,
    required this.onExciseGoodRemove,
    required this.onExcisePaymentTermAdd,
    required this.onExcisePaymentTermRemove,
    required this.onEdoOperatorAdd,
    required this.onEdoOperatorRemove,
    required this.onEnpTypeChanged,
    required this.onNdflTypeChanged,
    required this.copyToClipboard,
    this.highlightEnp = false,
    this.enpKey,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isEditing) {
      // Для режима редактирования оставляем прежнюю логику, т.к. все поля видимы
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet_outlined,
                size: 20,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Text(
                'Налоги и отчётность',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          MultiSelectWidget(
            label: 'Система налогообложения',
            selected: taxSystems,
            options: ClientConstants.taxSystems,
            isEditing: isEditing,
            onAdd: onTaxSystemAdd,
            onRemove: onTaxSystemRemove,
            copyToClipboard: copyToClipboard,
            icon: Icons.gavel_outlined,
          ),
          const SizedBox(height: 12),
          MultiSelectWidget(
            label: 'Налог на прибыль',
            selected: profitTaxTypes,
            options: ClientConstants.profitTaxTypes,
            isEditing: isEditing,
            onAdd: onProfitTaxTypeAdd,
            onRemove: onProfitTaxTypeRemove,
            copyToClipboard: copyToClipboard,
            icon: Icons.trending_up_outlined,
          ),
          const SizedBox(height: 12),
          MultiSelectWidget(
            label: 'НДС',
            selected: vatTypes,
            options: ClientConstants.vatTypes,
            isEditing: isEditing,
            onAdd: onVatTypeAdd,
            onRemove: onVatTypeRemove,
            copyToClipboard: copyToClipboard,
            maxSelection: 2,
            icon: Icons.percent_outlined,
          ),
          const SizedBox(height: 12),
          MultiSelectWidget(
            label: 'Имущество',
            selected: propertyTypes,
            options: ClientConstants.propertyTypes,
            isEditing: isEditing,
            onAdd: onPropertyTypeAdd,
            onRemove: onPropertyTypeRemove,
            copyToClipboard: copyToClipboard,
            icon: Icons.home_work_outlined,
          ),
          const SizedBox(height: 12),
          DropdownFieldWidget(
            label: 'Отчётность',
            value: reportingType,
            options: ClientConstants.reportingTypes,
            isEditing: isEditing,
            onChange: onReportingTypeChanged,
            copyToClipboard: copyToClipboard,
            icon: Icons.description_outlined,
          ),
          const SizedBox(height: 12),
          DropdownFieldWidget(
            label: 'Оператор отчётности',
            value: reportingOperator,
            options: ClientConstants.reportingOperators,
            isEditing: isEditing,
            onChange: onReportingOperatorChanged,
            copyToClipboard: copyToClipboard,
            icon: Icons.engineering_outlined,
          ),
          const SizedBox(height: 12),
          Container(
            decoration:
                highlightEnp
                    ? BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).colorScheme.error,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    )
                    : null,
            child: DropdownFieldWidget(
              key: enpKey,
              label: 'ЕНП',
              value: enpType,
              options: ClientConstants.enpTypes,
              isEditing: isEditing,
              onChange: onEnpTypeChanged,
              copyToClipboard: copyToClipboard,
              icon: Icons.payment_outlined,
            ),
          ),
          const SizedBox(height: 12),
          MultiSelectWidget(
            label: 'Подакцизные товары',
            selected: exciseGoods,
            options: ClientConstants.exciseGoods,
            isEditing: isEditing,
            onAdd: onExciseGoodAdd,
            onRemove: onExciseGoodRemove,
            copyToClipboard: copyToClipboard,
            icon: Icons.shopping_cart_outlined,
          ),
          const SizedBox(height: 12),
          MultiSelectWidget(
            label: 'Сроки уплаты акцизов',
            selected: excisePaymentTerms,
            options: ClientConstants.excisePaymentTerms,
            isEditing: isEditing,
            onAdd: onExcisePaymentTermAdd,
            onRemove: onExcisePaymentTermRemove,
            copyToClipboard: copyToClipboard,
            icon: Icons.schedule_outlined,
          ),
          const SizedBox(height: 12),
          MultiSelectWidget(
            label: 'Оператор ЭДО',
            selected: edoOperators,
            options: ClientConstants.edoOperators,
            isEditing: isEditing,
            onAdd: onEdoOperatorAdd,
            onRemove: onEdoOperatorRemove,
            copyToClipboard: copyToClipboard,
            maxSelection: 2,
            icon: Icons.schedule_outlined,
          ),
          const SizedBox(height: 12),
          DropdownFieldWidget(
            label: 'НДФЛ',
            value: ndflType,
            options: ClientConstants.ndflTypes,
            isEditing: isEditing,
            onChange: onNdflTypeChanged,
            copyToClipboard: copyToClipboard,
            icon: Icons.person_outline,
          ),
        ],
      );
    }

    // Для режима просмотра собираем только видимые виджеты
    final List<Widget> visibleWidgets = [];
    if (taxSystems.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Система налогообложения',
          selected: taxSystems,
          options: ClientConstants.taxSystems,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.gavel_outlined,
        ),
      );
    }
    if (profitTaxTypes.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Налог на прибыль',
          selected: profitTaxTypes,
          options: ClientConstants.profitTaxTypes,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.trending_up_outlined,
        ),
      );
    }
    if (vatTypes.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'НДС',
          selected: vatTypes,
          options: ClientConstants.vatTypes,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          maxSelection: 2,
          icon: Icons.percent_outlined,
        ),
      );
    }
    if (propertyTypes.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Имущество',
          selected: propertyTypes,
          options: ClientConstants.propertyTypes,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.home_work_outlined,
        ),
      );
    }
    if (reportingType != null && reportingType!.isNotEmpty) {
      visibleWidgets.add(
        DropdownFieldWidget(
          label: 'Отчётность',
          value: reportingType,
          options: ClientConstants.reportingTypes,
          isEditing: isEditing,
          onChange: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.description_outlined,
        ),
      );
    }
    if (reportingOperator != null && reportingOperator!.isNotEmpty) {
      visibleWidgets.add(
        DropdownFieldWidget(
          label: 'Оператор отчётности',
          value: reportingOperator,
          options: ClientConstants.reportingOperators,
          isEditing: isEditing,
          onChange: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.engineering_outlined,
        ),
      );
    }
    if (enpType != null && enpType!.isNotEmpty) {
      visibleWidgets.add(
        DropdownFieldWidget(
          key: enpKey,
          label: 'ЕНП',
          value: enpType,
          options: ClientConstants.enpTypes,
          isEditing: isEditing,
          onChange: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.payment_outlined,
        ),
      );
    }
    if (exciseGoods.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Подакцизные товары',
          selected: exciseGoods,
          options: ClientConstants.exciseGoods,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.shopping_cart_outlined,
        ),
      );
    }
    if (excisePaymentTerms.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Сроки уплаты акцизов',
          selected: excisePaymentTerms,
          options: ClientConstants.excisePaymentTerms,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          maxSelection: 2,
          icon: Icons.schedule_outlined,
        ),
      );
    }
    if (edoOperators.isNotEmpty) {
      visibleWidgets.add(
        MultiSelectWidget(
          label: 'Оператор ЭДО',
          selected: edoOperators,
          options: ClientConstants.edoOperators,
          isEditing: isEditing,
          onAdd: (_) {},
          onRemove: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.cloud_outlined,
        ),
      );
    }
    if (ndflType != null && ndflType!.isNotEmpty) {
      visibleWidgets.add(
        DropdownFieldWidget(
          label: 'НДФЛ',
          value: ndflType,
          options: ClientConstants.ndflTypes,
          isEditing: isEditing,
          onChange: (_) {},
          copyToClipboard: copyToClipboard,
          icon: Icons.person_outline,
        ),
      );
    }

    if (visibleWidgets.isEmpty) {
      return const SizedBox.shrink();
    }

    // Собираем итоговый столбец с отступами
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            const Text(
              'Налоги и отчётность',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 8),
        for (int i = 0; i < visibleWidgets.length; i++) ...[
          visibleWidgets[i],
          if (i < visibleWidgets.length - 1)
            const SizedBox(height: ClientConstants.fieldSpacing),
        ],
      ],
    );
  }
}
