// lib/features/clients/presentation/widgets/edit_card_components/client_attachments_and_comments_section.dart
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/file_display/files_grid_section.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_create/sections/unified_attachments_section.dart';
import 'package:flutter/material.dart';

class ClientAttachmentsAndCommentsSection extends StatelessWidget {
  final bool isEditing;
  final List<FileAttachmentItem> cloudFiles;
  final List<Map<String, dynamic>> attachments;
  final String? attachmentComments;
  final TextEditingController attachmentCommentsCtrl;
  final ValueChanged<FileAttachmentItem> onAddCloudFile;
  final ValueChanged<FileAttachmentItem> onRemoveCloudFile;
  final ValueChanged<FileAttachmentItem> onUpdateCloudFile;
  final Future<void> Function() onTaskAutoSave;
  final ValueChanged<String> onCommentsChanged;

  const ClientAttachmentsAndCommentsSection({
    super.key,
    required this.isEditing,
    required this.cloudFiles,
    required this.attachments,
    required this.attachmentComments,
    required this.attachmentCommentsCtrl,
    required this.onAddCloudFile,
    required this.onRemoveCloudFile,
    required this.onUpdateCloudFile,
    required this.onTaskAutoSave,
    required this.onCommentsChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // File attachments
        if (isEditing)
          UnifiedAttachmentsSection(
            cloudFiles: cloudFiles,
            onAddCloudFile: onAddCloudFile,
            onRemoveCloudFile: onRemoveCloudFile,
            onUpdateCloudFile: onUpdateCloudFile,
            onTaskAutoSave: onTaskAutoSave,
          )
        else
          FilesGridSection(files: attachments, title: 'Файловые вложения'),
        const SizedBox(height: 16.0),

        // Attachment comments
        if (isEditing)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Комментарии к вложению',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: attachmentCommentsCtrl,
                maxLines: 3,
                decoration: const InputDecoration(
                  hintText: 'Введите комментарии к вложениям...',
                  border: OutlineInputBorder(),
                ),
                onChanged: onCommentsChanged,
              ),
            ],
          )
        else if (attachmentComments != null && attachmentComments!.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Комментарии к вложению',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  attachmentComments!,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
      ],
    );
  }
}
