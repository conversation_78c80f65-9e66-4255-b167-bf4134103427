import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/task_entity.dart';
import '../../domain/usecases/save_task_usecase.dart';
import '../widgets/task_create/models.dart';
import 'task_create_state.dart';
import '../../../profile/presentation/cubit/profile_cubit.dart';

class TaskCreateCubit extends Cubit<TaskCreateState> {
  final SaveTaskUseCase saveTaskUseCase;
  final ProfileCubit profileCubit;
  StreamSubscription? _profileSubscription;

  TaskCreateCubit({required this.saveTaskUseCase, required this.profileCubit})
    : super(TaskCreateInitial());

  /// Инициализация с существующей задачей или создание новой
  void initialize({TaskEntity? initialTask}) {
    if (initialTask != null) {
      _initializeWithTask(initialTask);
    } else {
      _initializeNewTask();
    }
  }

  /// Обновление дат исполнения (произвольное повторение)
  void updateRecurrenceExecutionDates(List<DateTime> dates) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(recurrenceExecutionDates: dates));
    }
  }

  /// Обновление признака ежегодного повторения
  void updateRecurrenceIsAnnual(bool isAnnual) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(recurrenceIsAnnual: isAnnual));
    }
  }

  /// Инициализация с существующей задачей
  void _initializeWithTask(TaskEntity task) {
    final checklist =
        task.checklist
            .map(
              (e) => ChecklistItem(
                text: e['text'] ?? '',
                completed: e['completed'] == true || e['is_done'] == true,
              ),
            )
            .toList();

    final cloudFiles =
        task.attachments
            .where((e) => e['fileKey'] != null)
            .map(
              (e) => FileAttachmentItem(
                name: e['name'] ?? '',
                fileKey: e['fileKey'] as String?,
                fileSize: e['fileSize'] as int?,
                status: FileAttachmentStatus.uploaded,
              ),
            )
            .toList();

    final reminders =
        task.reminders.map((e) {
          DateTime? dt;
          final dtRaw = e['datetime'];
          if (dtRaw is String && dtRaw.isNotEmpty) {
            dt = DateTime.tryParse(dtRaw)?.toLocal();
          }
          return ReminderItem(
            dateTime: dt ?? DateTime.now(),
            role: e['role'] ?? 'assignee',
          );
        }).toList();

    final selectedAssigneeIds =
        task.assigneeIds.isNotEmpty ? [task.assigneeIds.first] : <String>[];
    final selectedCoAssigneeIds =
        task.assigneeIds.length > 1 ? task.assigneeIds.sublist(1) : <String>[];

    String recurrenceType = 'none';
    String recurrenceInterval = 'monthly';
    List<DateTime> recurrenceExecutionDates = [];
    bool recurrenceIsAnnual = false;
    if (task.recurrence != null && task.recurrence!.isNotEmpty) {
      recurrenceType = task.recurrence!['type']?.toString() ?? 'none';
      if (recurrenceType == 'periodic') {
        recurrenceInterval =
            task.recurrence!['interval']?.toString() ?? 'monthly';
      } else if (recurrenceType == 'arbitrary') {
        final datesRaw = task.recurrence!['execution_dates_json'] as List? ?? [];
        recurrenceExecutionDates = datesRaw.map<DateTime>((e) {
          if (e is String && e.isNotEmpty) {
            return DateTime.tryParse(e)?.toLocal() ?? DateTime.now();
          }
          return DateTime.now();
        }).toList();
        recurrenceIsAnnual = task.recurrence!['is_annual'] == true;
      }
    }

    emit(
      TaskCreateLoaded(
        title: task.title,
        description: task.description ?? '',
        selectedClientIds: List<String>.from(task.clientIds),
        selectedAssigneeIds: selectedAssigneeIds,
        selectedCoAssigneeIds: selectedCoAssigneeIds,
        selectedObserverIds: List<String>.from(task.observerIds),
        selectedCreatorIds: List<String>.from(task.creatorIds),
        dueDate: task.dueDate,
        priority: task.priority,
        allowAssigneeToChangeDueDate:
            task.options['allow_assignee_to_change_due_date'] == true,
        checklist: checklist,
        reminders: reminders,
        cloudFiles: cloudFiles,
        recurrenceType: recurrenceType,
        recurrenceInterval: recurrenceInterval,
        recurrenceExecutionDates: recurrenceExecutionDates,
        recurrenceIsAnnual: recurrenceIsAnnual,
        holidayTransferRule: task.holidayTransferRule ?? 'next_workday',
        isFormValid: task.title.isNotEmpty,
      ),
    );
  }

  /// Инициализация новой задачи
  void _initializeNewTask() {
    emit(const TaskCreateLoaded());
    _setCurrentUserAsCreator();

    // Подписываемся на изменения профиля для случая, когда профиль еще не загружен
    _profileSubscription = profileCubit.stream.listen((profileState) {
      if (profileState is ProfileLoaded) {
        _setCurrentUserAsCreator();
      }
    });
  }

  /// Автоматически устанавливает текущего пользователя как постановщика
  void _setCurrentUserAsCreator() {
    final profileState = profileCubit.state;

    if (profileState is ProfileLoaded && state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final currentUserId = profileState.profile.id;

      if (!currentState.selectedCreatorIds.contains(currentUserId)) {
        final updatedCreatorIds = List<String>.from(
          currentState.selectedCreatorIds,
        )..add(currentUserId);
        emit(currentState.copyWith(selectedCreatorIds: updatedCreatorIds));
      }
    }
  }

  /// Обновление заголовка задачи
  void updateTitle(String title) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(
        currentState.copyWith(
          title: title,
          isFormValid: title.trim().isNotEmpty,
        ),
      );
    }
  }

  /// Обновление описания задачи
  void updateDescription(String description) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(description: description));
    }
  }

  /// Обновление выбранных клиентов
  void updateSelectedClients(String clientId, bool selected) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedClientIds = List<String>.from(
        currentState.selectedClientIds,
      );

      if (selected) {
        updatedClientIds.add(clientId);
      } else {
        updatedClientIds.remove(clientId);
      }

      emit(currentState.copyWith(selectedClientIds: updatedClientIds));
    }
  }

  /// Обновление исполнителей
  void updateAssignees(List<String> assigneeIds) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(
        currentState.copyWith(
          selectedAssigneeIds: assigneeIds.take(1).toList(),
        ),
      );
    }
  }

  /// Обновление соисполнителей
  void updateCoAssignees(List<String> coAssigneeIds) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(selectedCoAssigneeIds: coAssigneeIds));
    }
  }

  /// Обновление наблюдателей
  void updateObservers(List<String> observerIds) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(selectedObserverIds: observerIds));
    }
  }

  /// Обновление постановщиков
  void updateCreators(List<String> creatorIds) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(selectedCreatorIds: creatorIds));
    }
  }

  /// Обновление приоритета
  void updatePriority(String priority) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(priority: priority));
    }
  }

  /// Обновление срока выполнения
  void updateDueDate(DateTime? dueDate, {bool isNewTask = false}) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final oldDueDate = currentState.dueDate;

      emit(
        currentState.copyWith(dueDate: dueDate, clearDueDate: dueDate == null),
      );

      // Автоматически создаем/обновляем напоминания для новой задачи
      if (isNewTask) {
        _updateAutoReminders(oldDueDate, dueDate);
      }
    }
  }

  /// Обновление дополнительных параметров
  void updateAllowAssigneeToChangeDueDate(bool allow) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(allowAssigneeToChangeDueDate: allow));
    }
  }

  /// Добавление элемента чек-листа
  void addChecklistItem(ChecklistItem item) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedChecklist = List<ChecklistItem>.from(currentState.checklist)
        ..add(item);
      emit(currentState.copyWith(checklist: updatedChecklist));
    }
  }

  /// Добавление пустого элемента чек-листа
  void addEmptyChecklistItem() {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final newItem = ChecklistItem(text: '', completed: false);
      emit(
        currentState.copyWith(checklist: [...currentState.checklist, newItem]),
      );
    }
  }

  /// Удаление элемента чек-листа
  void removeChecklistItem(ChecklistItem item) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedChecklist = List<ChecklistItem>.from(currentState.checklist)
        ..remove(item);
      emit(currentState.copyWith(checklist: updatedChecklist));
    }
  }

  /// Переключение состояния элемента чек-листа
  void toggleChecklistItem(ChecklistItem item, bool completed) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedChecklist = List<ChecklistItem>.from(currentState.checklist);
      final index = updatedChecklist.indexOf(item);
      if (index != -1) {
        updatedChecklist[index].completed = completed;
        emit(currentState.copyWith(checklist: updatedChecklist));
      }
    }
  }

  /// Изменение порядка элементов чек-листа
  void reorderChecklist(int oldIndex, int newIndex) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedChecklist = List<ChecklistItem>.from(currentState.checklist);

      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final ChecklistItem item = updatedChecklist.removeAt(oldIndex);
      updatedChecklist.insert(newIndex, item);

      emit(currentState.copyWith(checklist: updatedChecklist));
    }
  }

  /// Добавление напоминания
  void addReminder(ReminderItem reminder) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedReminders = List<ReminderItem>.from(currentState.reminders)
        ..add(reminder);
      emit(
        currentState.copyWith(
          reminders: updatedReminders,
          remindersModifiedByUser: true,
        ),
      );
      _checkIfAutoRemindersModified();
    }
  }

  /// Добавление нового напоминания с дефолтными значениями
  void addNewReminder() {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final newReminder = ReminderItem(
        dateTime: DateTime.now().add(const Duration(hours: 1)),
        role: 'assignee',
      );
      emit(
        currentState.copyWith(
          reminders: [...currentState.reminders, newReminder],
          remindersModifiedByUser: true,
        ),
      );
      _checkIfAutoRemindersModified();
    }
  }

  /// Удаление напоминания
  void removeReminder(ReminderItem reminder) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedReminders = List<ReminderItem>.from(currentState.reminders)
        ..remove(reminder);
      emit(
        currentState.copyWith(
          reminders: updatedReminders,
          remindersModifiedByUser: true,
        ),
      );
      _checkIfAutoRemindersModified();
    }
  }

  /// Добавление облачного файла
  void addCloudFile(
    FileAttachmentItem file, {
    String? firmId,
    TaskEntity? initialTask,
    Function(String)? onAutoSaved,
  }) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedCloudFiles = List<FileAttachmentItem>.from(
        currentState.cloudFiles,
      )..add(file);
      emit(
        currentState.copyWith(
          cloudFiles: updatedCloudFiles,
          hasActiveFileOperations: _hasActiveFileOperations(updatedCloudFiles),
        ),
      );

      // Автосохранение при добавлении файла
      if (firmId != null && file.status == FileAttachmentStatus.uploaded) {
        _performAutoSaveWithCallback(
          firmId,
          initialTask: initialTask,
          onAutoSaved: onAutoSaved,
        );
      }
    }
  }

  /// Удаление облачного файла
  void removeCloudFile(FileAttachmentItem file) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedCloudFiles = List<FileAttachmentItem>.from(
        currentState.cloudFiles,
      )..remove(file);
      emit(
        currentState.copyWith(
          cloudFiles: updatedCloudFiles,
          hasActiveFileOperations: _hasActiveFileOperations(updatedCloudFiles),
        ),
      );
    }
  }

  /// Обновление облачного файла
  void updateCloudFile(FileAttachmentItem updatedFile) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      final updatedCloudFiles = List<FileAttachmentItem>.from(
        currentState.cloudFiles,
      );
      final index = updatedCloudFiles.indexWhere(
        (f) => f.name == updatedFile.name,
      );
      if (index != -1) {
        updatedCloudFiles[index] = updatedFile;
        emit(
          currentState.copyWith(
            cloudFiles: updatedCloudFiles,
            hasActiveFileOperations: _hasActiveFileOperations(
              updatedCloudFiles,
            ),
          ),
        );
      }
    }
  }

  /// Добавление облачного файла из описания
  void addCloudFileFromDescription(FileAttachmentItem file) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      // Проверяем, нет ли уже такого файла в списке
      final existingFile =
          currentState.cloudFiles.where((f) => f.name == file.name).firstOrNull;
      if (existingFile == null) {
        addCloudFile(file);
      }
    }
  }

  /// Обновление типа повторения
  void updateRecurrenceType(String recurrenceType) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(recurrenceType: recurrenceType));
    }
  }

  /// Обновление интервала повторения
  void updateRecurrenceInterval(String recurrenceInterval) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(recurrenceInterval: recurrenceInterval));
    }
  }

  /// Обновление правила переноса праздников
  void updateHolidayTransferRule(String holidayTransferRule) {
    if (state is TaskCreateLoaded) {
      final currentState = state as TaskCreateLoaded;
      emit(currentState.copyWith(holidayTransferRule: holidayTransferRule));
    }
  }

  /// Сохранение задачи
  Future<bool> saveTask(String firmId, {TaskEntity? initialTask}) async {
    if (state is! TaskCreateLoaded) return false;

    final currentState = state as TaskCreateLoaded;

    if (!currentState.isFormValid) return false;

    emit(TaskCreateSaving(currentState: currentState));

    final task = _buildTaskEntity(currentState, initialTask);

    final result = await saveTaskUseCase(firmId, task);

    return result.fold(
      (failure) {
        emit(
          TaskCreateError(message: failure.message, currentState: currentState),
        );
        return false;
      },
      (savedTaskId) {
        final savedTask = task.copyWith(id: savedTaskId);
        // Обновляем состояние, сохраняя флаг wasAutoCreated
        final newState = currentState.copyWith(
          id: savedTaskId,
          // Другие поля можно обновить, если нужно
        );
        emit(TaskCreateSaved(savedTask: savedTask, currentState: newState));
        return true;
      },
    );
  }

  /// Автосохранение задачи в фоне
  Future<void> autoSaveTask(String firmId, {TaskEntity? initialTask}) async {
    if (state is! TaskCreateLoaded) return;

    final currentState = state as TaskCreateLoaded;

    // Проверяем, что есть минимально необходимые данные
    if (currentState.title.trim().isEmpty) {
      return; // Не сохраняем задачу без названия
    }

    final task = _buildTaskEntity(currentState, initialTask);

    // Для существующих задач (редактирование) - просто обновляем
    if (initialTask?.id != null && initialTask!.id.isNotEmpty) {
      // Фоновое сохранение без уведомлений
      await saveTaskUseCase(firmId, task);
    }
    // Для новых задач - создаем только если есть файлы и еще не создавали автоматически
    else if (!currentState.wasAutoCreated &&
        currentState.cloudFiles.any(
          (f) => f.status == FileAttachmentStatus.uploaded,
        )) {
      // Автоматически создаем задачу при загрузке файлов
      final result = await saveTaskUseCase(firmId, task);

      result.fold((failure) => null, (savedTaskId) {
        // Помечаем, что задача была автоматически создана и сохраняем ее ID
        emit(currentState.copyWith(wasAutoCreated: true, id: savedTaskId));
      });
    }
    // Если задача уже была автоматически создана, продолжаем ее обновлять
    else if (currentState.wasAutoCreated) {
      await saveTaskUseCase(firmId, task);
    }
  }

  /// Автосохранение с callback для навигации
  Future<void> _performAutoSaveWithCallback(
    String firmId, {
    TaskEntity? initialTask,
    Function(String)? onAutoSaved,
  }) async {
    if (state is! TaskCreateLoaded) return;

    final currentState = state as TaskCreateLoaded;

    // Проверяем, что есть минимально необходимые данные
    if (currentState.title.trim().isEmpty) {
      return; // Не сохраняем задачу без названия
    }

    final task = _buildTaskEntity(currentState, initialTask);

    // Для новых задач - создаем только если есть файлы и еще не создавали автоматически
    if (!currentState.wasAutoCreated &&
        currentState.cloudFiles.any(
          (f) => f.status == FileAttachmentStatus.uploaded,
        )) {
      // Автоматически создаем задачу при загрузке файлов
      final result = await saveTaskUseCase(firmId, task);

      result.fold((failure) => null, (savedTaskId) {
        // Помечаем, что задача была автоматически создана и сохраняем ее ID
        emit(currentState.copyWith(wasAutoCreated: true, id: savedTaskId));
        // Вызываем callback для навигации
        if (onAutoSaved != null) {
          onAutoSaved(savedTaskId);
        }
      });
    }
  }

  /// Создает объект TaskEntity из текущего состояния
  TaskEntity _buildTaskEntity(TaskCreateLoaded state, TaskEntity? initialTask) {
    return TaskEntity(
      id: state.id ?? initialTask?.id ?? '',
      title: state.title,
      description: state.description,
      clientIds: state.selectedClientIds,
      assigneeIds: [
        ...state.selectedAssigneeIds,
        ...state.selectedCoAssigneeIds.where(
          (id) => !state.selectedAssigneeIds.contains(id),
        ),
      ],
      observerIds: state.selectedObserverIds,
      creatorIds: state.selectedCreatorIds,
      status: initialTask?.status ?? 'in_progress',
      priority: state.priority,
      dueDate: state.dueDate,
      attachments: [
        // Облачные файлы с ключами (только успешно загруженные)
        ...state.cloudFiles
            .where(
              (f) =>
                  f.status == FileAttachmentStatus.uploaded &&
                  f.fileKey != null,
            )
            .map(
              (f) => {
                'name': f.name,
                'fileKey': f.fileKey!,
                'fileSize': f.fileSize,
              },
            ),
      ],
      checklist:
          state.checklist
              .map((c) => {'text': c.text, 'completed': c.completed})
              .toList(),
      reminders:
          state.reminders
              .map(
                (r) => {
                  'datetime':
                      '${r.dateTime.toUtc().toIso8601String().split('.').first}Z',
                  'role': r.role,
                },
              )
              .toList(),
      recurrence:
          state.recurrenceType == 'none'
              ? null
              : state.recurrenceType == 'arbitrary'
                  ? {
                      'type': 'arbitrary',
                      'execution_dates_json': state.recurrenceExecutionDates
                          .map((d) => '${d.toUtc().toIso8601String().split('.').first}Z')
                          .toList(),
                      'is_annual': state.recurrenceIsAnnual,
                    }
                  : {
                      'type': state.recurrenceType,
                      'interval': state.recurrenceType == 'periodic'
                          ? state.recurrenceInterval
                          : null,
                    },
      options: {
        'allow_assignee_to_change_due_date': state.allowAssigneeToChangeDueDate,
        'holiday_transfer_rule': state.holidayTransferRule,
      },
      createdAt: initialTask?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Автоматически создает напоминания за сутки до срока задачи
  void _createAutoReminders(DateTime dueDate) {
    if (state is! TaskCreateLoaded) return;

    final currentState = state as TaskCreateLoaded;

    if (currentState.autoRemindersCreated) return;

    // Удаляем старые автоматические напоминания если они есть
    _removeAutoReminders();

    // Вычисляем дату напоминания (за сутки до срока)
    final reminderDate = dueDate.subtract(const Duration(days: 1));

    // Проверяем, что дата напоминания не в прошлом
    if (reminderDate.isBefore(DateTime.now())) {
      return;
    }

    // Создаем напоминание для исполнителя
    final assigneeReminder = ReminderItem(
      dateTime: reminderDate,
      role: 'assignee',
    );

    // Создаем напоминание для постановщика
    final creatorReminder = ReminderItem(
      dateTime: reminderDate,
      role: 'creator',
    );

    final updatedReminders =
        List<ReminderItem>.from(currentState.reminders)
          ..add(assigneeReminder)
          ..add(creatorReminder);

    emit(
      currentState.copyWith(
        reminders: updatedReminders,
        autoRemindersCreated: true,
      ),
    );
  }

  /// Удаляет автоматически созданные напоминания
  void _removeAutoReminders() {
    if (state is! TaskCreateLoaded) return;

    final currentState = state as TaskCreateLoaded;

    if (!currentState.autoRemindersCreated || currentState.dueDate == null) {
      return;
    }

    // Удаляем напоминания за сутки до срока для исполнителя и постановщика
    final reminderDate = currentState.dueDate!.subtract(
      const Duration(days: 1),
    );
    final updatedReminders =
        currentState.reminders
            .where(
              (reminder) =>
                  !(reminder.dateTime.isAtSameMomentAs(reminderDate) &&
                      (reminder.role == 'assignee' ||
                          reminder.role == 'creator')),
            )
            .toList();

    emit(
      currentState.copyWith(
        reminders: updatedReminders,
        autoRemindersCreated: false,
      ),
    );
  }

  /// Обновляет автоматические напоминания при изменении срока задачи
  void _updateAutoReminders(DateTime? oldDueDate, DateTime? newDueDate) {
    if (state is! TaskCreateLoaded) return;

    final currentState = state as TaskCreateLoaded;

    // Если пользователь изменял напоминания вручную, не трогаем их
    if (currentState.remindersModifiedByUser) {
      return;
    }

    if (newDueDate == null) {
      // Если срок убран, удаляем автоматические напоминания
      if (currentState.autoRemindersCreated) {
        _removeAutoReminders();
      }
      return;
    }

    // Если это первая установка срока, создаем напоминания
    if (oldDueDate == null) {
      _createAutoReminders(newDueDate);
      return;
    }

    // Если срок изменился и у нас есть автоматические напоминания, обновляем даты существующих напоминаний
    if (currentState.autoRemindersCreated &&
        !oldDueDate.isAtSameMomentAs(newDueDate)) {
      final oldReminderDate = oldDueDate.subtract(const Duration(days: 1));
      final newReminderDate = newDueDate.subtract(const Duration(days: 1));

      final updatedReminders =
          currentState.reminders.map((reminder) {
            if (reminder.dateTime.isAtSameMomentAs(oldReminderDate) &&
                (reminder.role == 'assignee' || reminder.role == 'creator')) {
              return ReminderItem(
                dateTime: newReminderDate,
                role: reminder.role,
              );
            }
            return reminder;
          }).toList();

      emit(currentState.copyWith(reminders: updatedReminders));
    }
  }

  /// Проверяет, были ли изменены автоматически созданные напоминания
  void _checkIfAutoRemindersModified() {
    if (state is! TaskCreateLoaded) return;

    final currentState = state as TaskCreateLoaded;

    if (!currentState.autoRemindersCreated || currentState.dueDate == null) {
      return;
    }

    final expectedReminderDate = currentState.dueDate!.subtract(
      const Duration(days: 1),
    );

    // Проверяем, есть ли ожидаемые автоматические напоминания
    final hasAssigneeReminder = currentState.reminders.any(
      (reminder) =>
          reminder.dateTime.isAtSameMomentAs(expectedReminderDate) &&
          reminder.role == 'assignee',
    );

    final hasCreatorReminder = currentState.reminders.any(
      (reminder) =>
          reminder.dateTime.isAtSameMomentAs(expectedReminderDate) &&
          reminder.role == 'creator',
    );

    // Если одно из ожидаемых напоминаний отсутствует, значит пользователь их изменил
    if (!hasAssigneeReminder || !hasCreatorReminder) {
      emit(
        currentState.copyWith(
          remindersModifiedByUser: true,
          autoRemindersCreated: false,
        ),
      );
    }
  }

  /// Проверяет наличие активных файловых операций
  bool _hasActiveFileOperations(List<FileAttachmentItem> cloudFiles) {
    return cloudFiles.any(
      (file) =>
          file.status == FileAttachmentStatus.uploading ||
          file.status == FileAttachmentStatus.deleting ||
          file.status == FileAttachmentStatus.pending,
    );
  }

  @override
  Future<void> close() {
    _profileSubscription?.cancel();
    return super.close();
  }
}
