import 'dart:html' as html;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dropzone/flutter_dropzone.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_state.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';
import 'dart:async';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/file_preview/inline_image_widget.dart';

class RichDescriptionField extends StatefulWidget {
  final String? initialText;
  final void Function(String description) onChanged;
  final List<FileAttachmentItem> attachments;
  final VoidCallback? onTaskAutoSave;
  final void Function(FileAttachmentItem)? onFileAdded;
  final void Function(FileAttachmentItem)? onFileUpdated;

  const RichDescriptionField({
    super.key,
    required this.initialText,
    required this.onChanged,
    required this.attachments,
    this.onTaskAutoSave,
    this.onFileAdded,
    this.onFileUpdated,
  });

  @override
  State<RichDescriptionField> createState() => _RichDescriptionFieldState();
}

class _RichDescriptionFieldState extends State<RichDescriptionField> {
  late final TextEditingController _controller;
  DropzoneViewController? _dzController;
  bool _hover = false;
  Timer? _autoSaveDebounce;
  final FocusNode _editorFocusNode = FocusNode();
  StreamSubscription? _storageSubscription;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText ?? '');
    _controller.addListener(_onTextChanged);

    _storageSubscription = context.read<TariffsAndStorageCubit>().stream.listen(
      _handleStorageState,
    );
  }

  void _onTextChanged() {
    widget.onChanged(_controller.text);

    if (widget.onTaskAutoSave != null) {
      _autoSaveDebounce?.cancel();
      _autoSaveDebounce = Timer(const Duration(seconds: 1), () {
        if (mounted) widget.onTaskAutoSave!();
      });
    }
  }

  void _handleStorageState(TariffsAndStorageState state) {
    if (state is FileUploadCompleted) {
      final fileName = state.fileName;
      final text = _controller.text;

      final patchedText = text.replaceAllMapped(
        RegExp(r'\[\[local/([^|]+)\|([^]]+)\]\]'),
        (match) {
          final matchFileName = match.group(1) ?? '';
          final dimensions = match.group(2) ?? '300x200';
          if (matchFileName == fileName) {
            return '[[${state.fileKey}|$dimensions]]';
          }
          return match.group(0) ?? '';
        },
      );

      if (text != patchedText) {
        final selection = _controller.selection;
        _controller.text = patchedText;
        if (selection.baseOffset <= patchedText.length) {
          _controller.selection = selection;
        }
      }

      if (widget.onFileUpdated != null) {
        final existingFile =
            widget.attachments.where((f) => f.name == fileName).firstOrNull;
        if (existingFile != null) {
          widget.onFileUpdated!(
            existingFile.copyWith(
              fileKey: state.fileKey,
              status: FileAttachmentStatus.uploaded,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Описание', style: Theme.of(context).textTheme.labelLarge),
        const SizedBox(height: 8),
        WrapToolbar(
          controller: _controller,
          attachments: widget.attachments,
          onTaskAutoSave: widget.onTaskAutoSave,
        ),
        Container(
          height: 300,
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.05),
            border: Border.all(color: Colors.red, width: 2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: ExtendedTextField(
                  controller: _controller,
                  focusNode: _editorFocusNode,
                  maxLines: null,
                  expands: true,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Введите описание...',
                  ),
                  style: Theme.of(context).textTheme.bodyMedium,
                  specialTextSpanBuilder: ImageTextSpanBuilder(
                    attachments: widget.attachments,
                  ),
                ),
              ),
              Positioned.fill(
                child: IgnorePointer(
                  ignoring: true,
                  child: DropzoneView(
                    onCreated: (c) => _dzController = c,
                    onHover: () => setState(() => _hover = true),
                    onLeave: () => setState(() => _hover = false),
                    onDropFile: _onDrop,
                  ),
                ),
              ),
              if (_hover)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.05),
                      border: Border.all(color: Colors.red, width: 2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Text(
                        'Отпустите для загрузки изображения',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        _buildImagePreview(),
      ],
    );
  }

  Widget _buildImagePreview() {
    final imageTokens = _extractImageTokens(_controller.text);
    if (imageTokens.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Изображения в тексте:',
          style: Theme.of(context).textTheme.labelMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              imageTokens.map((token) {
                final parts = token.split('|');
                final fileKey = parts[0].trim();
                final dimensions =
                    parts.length > 1 ? parts[1].trim() : '300x200';
                final dimensionParts = dimensions.split('x');
                final width = int.tryParse(dimensionParts[0]) ?? 300;
                final height =
                    dimensionParts.length > 1
                        ? (int.tryParse(dimensionParts[1]) ?? 200)
                        : 200;

                return InlineImageWidget(
                  fileKey: fileKey,
                  fileName: _getFileNameByKey(fileKey),
                  originalWidth: width,
                  originalHeight: height,
                );
              }).toList(),
        ),
      ],
    );
  }

  List<String> _extractImageTokens(String text) {
    final regex = RegExp(r'\[\[([^]]+)\]\]');
    final matches = regex.allMatches(text);
    return matches.map((match) => match.group(1) ?? '').toList();
  }

  String _getFileNameByKey(String fileKey) {
    final attachment =
        widget.attachments.where((a) => a.fileKey == fileKey).firstOrNull;
    if (attachment != null) {
      return attachment.name;
    }

    if (fileKey.startsWith('local/')) {
      return fileKey.substring(6);
    }

    return fileKey;
  }

  Future<void> _onDrop(dynamic ev) async {
    setState(() => _hover = false);

    if (ev is! html.File) return;
    final file = ev;

    final ext = file.name.split('.').last.toLowerCase();
    final allowedExtensions = [
      'jpg',
      'jpeg',
      'png',
      'webp',
      'jpe',
      'gif',
      'bmp',
    ];
    if (!allowedExtensions.contains(ext)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Неподдерживаемый формат файла: .$ext'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Файл слишком большой. Максимум 10MB'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final reader = html.FileReader();
    reader.readAsArrayBuffer(file);
    await reader.onLoadEnd.first;
    final bytes = reader.result as Uint8List;

    final imageDimensions = await _getImageDimensions(file);
    final width = imageDimensions['width'] ?? 300;
    final height = imageDimensions['height'] ?? 200;

    final localId = 'local/${file.name}';
    final imageText = '[[$localId|${width}x$height]]';
    final cursorPosition = _controller.selection.baseOffset;
    final currentText = _controller.text;
    final newText =
        currentText.substring(0, cursorPosition) +
        imageText +
        currentText.substring(cursorPosition);

    _controller.text = newText;
    _controller.selection = TextSelection.collapsed(
      offset: cursorPosition + imageText.length,
    );

    final firmState = context.read<ActiveFirmCubit>().state;
    final firmId = firmState.selectedFirm?.id;
    if (firmId == null) return;

    try {
      if (mounted) {
        context.read<TariffsAndStorageCubit>().uploadFile(
          firmId: firmId,
          fileName: file.name,
          fileBytes: bytes,
        );

        if (widget.onFileAdded != null) {
          widget.onFileAdded!(
            FileAttachmentItem(
              name: file.name,
              fileKey: null,
              status: FileAttachmentStatus.uploading,
            ),
          );
        }

        if (widget.onTaskAutoSave != null) {
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) widget.onTaskAutoSave!();
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка загрузки файла: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<Map<String, int>> _getImageDimensions(html.File file) async {
    final completer = Completer<Map<String, int>>();
    final reader = html.FileReader();

    reader.onLoad.listen((event) {
      final img = html.ImageElement();
      img.onLoad.listen((event) {
        completer.complete({
          'width': img.naturalWidth,
          'height': img.naturalHeight,
        });
      });
      img.onError.listen((event) {
        completer.complete({'width': 300, 'height': 200});
      });
      img.src = reader.result as String;
    });

    reader.readAsDataUrl(file);
    return completer.future;
  }

  @override
  void dispose() {
    _autoSaveDebounce?.cancel();
    _controller.dispose();
    _editorFocusNode.dispose();
    _storageSubscription?.cancel();
    super.dispose();
  }
}

class WrapToolbar extends StatelessWidget {
  final TextEditingController controller;
  final List<FileAttachmentItem> attachments;
  final VoidCallback? onTaskAutoSave;

  const WrapToolbar({
    super.key,
    required this.controller,
    required this.attachments,
    this.onTaskAutoSave,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          tooltip: 'Вставить картинку',
          icon: const Icon(Icons.attach_file),
          onPressed: () => _showImagePicker(context),
        ),
      ],
    );
  }

  void _showImagePicker(BuildContext context) {
    final imageAttachments =
        attachments.where((a) {
          final ext = a.name.split('.').last.toLowerCase();
          return a.fileKey != null &&
              ['jpg', 'jpeg', 'png', 'webp', 'jpe'].contains(ext);
        }).toList();

    showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text('Выберите изображение'),
            content: SizedBox(
              width: 400,
              child:
                  imageAttachments.isEmpty
                      ? const Text('Нет доступных изображений')
                      : Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children:
                            imageAttachments.map((a) {
                              return GestureDetector(
                                onTap: () {
                                  final cursorPosition =
                                      controller.selection.baseOffset;
                                  final imageText = '[[${a.fileKey!}|300x200]]';
                                  final currentText = controller.text;
                                  final newText =
                                      currentText.substring(0, cursorPosition) +
                                      imageText +
                                      currentText.substring(cursorPosition);

                                  controller.text = newText;
                                  controller
                                      .selection = TextSelection.collapsed(
                                    offset: cursorPosition + imageText.length,
                                  );
                                  Navigator.pop(ctx);

                                  if (onTaskAutoSave != null) {
                                    Future.delayed(
                                      const Duration(milliseconds: 100),
                                      () {
                                        onTaskAutoSave!();
                                      },
                                    );
                                  }
                                },
                                child: Container(
                                  width: 80,
                                  height: 100,
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: Colors.grey.shade300,
                                    ),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(Icons.image, size: 40),
                                      const SizedBox(height: 4),
                                      Text(
                                        a.name,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(fontSize: 10),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                      ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx),
                child: const Text('Закрыть'),
              ),
            ],
          ),
    );
  }
}

class ImageTextSpanBuilder extends SpecialTextSpanBuilder {
  final List<FileAttachmentItem> attachments;

  ImageTextSpanBuilder({required this.attachments});

  @override
  SpecialText? createSpecialText(
    String flag, {
    TextStyle? textStyle,
    SpecialTextGestureTapCallback? onTap,
    int? index,
  }) {
    if (flag == '[') {
      return ImageText(
        textStyle: textStyle,
        start: index!,
        attachments: attachments,
      );
    }
    return null;
  }
}

class ImageText extends SpecialText {
  final List<FileAttachmentItem> attachments;

  ImageText({
    TextStyle? textStyle,
    required int start,
    required this.attachments,
  }) : super('[[', ']]', textStyle);

  @override
  InlineSpan finishText() {
    final String content = getContent();

    if (content.isEmpty) {
      return TextSpan(text: toString(), style: textStyle);
    }

    try {
      final parts = content.split('|');
      final fileKey = parts[0].trim();
      final dimensions = parts.length > 1 ? parts[1].trim() : '300x200';

      final dimensionParts = dimensions.split('x');
      final width = int.tryParse(dimensionParts[0]) ?? 300;
      final height =
          dimensionParts.length > 1
              ? (int.tryParse(dimensionParts[1]) ?? 200)
              : 200;

      return ExtendedWidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 1),
          child: InlineImageWidget(
            fileKey: fileKey,
            fileName: _getFileNameByKey(fileKey),
            originalWidth: width,
            originalHeight: height,
          ),
        ),
      );
    } catch (e) {
      return TextSpan(text: toString(), style: textStyle);
    }
  }

  String _getFileNameByKey(String fileKey) {
    final attachment =
        attachments.where((a) => a.fileKey == fileKey).firstOrNull;
    if (attachment != null) {
      return attachment.name;
    }

    if (fileKey.startsWith('local/')) {
      return fileKey.substring(6);
    }

    return fileKey;
  }
}
