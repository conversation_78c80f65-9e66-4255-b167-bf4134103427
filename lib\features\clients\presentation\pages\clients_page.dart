import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';
import 'package:balansoved_enterprise/injection_container.dart';

import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_versions_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_create_card.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_edit_card.dart';
import 'package:balansoved_enterprise/features/clients/data/models/client_model.dart';
import 'package:balansoved_enterprise/presentation/widgets/resizable_data_table.dart';



@RoutePage()
class ClientsPage extends StatefulWidget {
  const ClientsPage({super.key});

  @override
  State<ClientsPage> createState() => _ClientsPageState();
}

class _ClientsPageState extends State<ClientsPage> {
  final TextEditingController _searchController = TextEditingController();
  String _filter = '';
  bool _creating = false;
  ClientEntity? _editingClient;

  int _sortColumnIndex = 0;
  bool _sortAsc = true;

  // Ключ для доступа к состоянию виджета создания клиента
  final GlobalKey<ClientCreateCardState> _createCardKey = GlobalKey<ClientCreateCardState>();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() => _filter = _searchController.text);
    });
  }

  /// Сбрасывает состояние страницы к начальному виду
  void _resetPageState() {
    setState(() {
      _creating = false;
      _editingClient = null;
      _filter = '';
      _searchController.clear();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Сбрасываем состояние страницы и принудительно обновляем данные
    _resetPageState();
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm != null) {
      context.read<ClientsCubit>().fetchClients(firmState.selectedFirm!.id);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.search),
                    hintText: 'Фильтр',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                icon: const Icon(Icons.add_business),
                label: const Text('Создать'),
                onPressed: () => setState(() => _creating = true),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child:
                _creating
                    ? ListView(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: ClientCreateCard(
                            key: _createCardKey,
                            onCancel: () => setState(() => _creating = false),
                            onCreated: (client) {
                              // Карточка создания сама сохраняет клиента.
                              // Cubit должен сам обновить состояние.
                              // Нам нужно просто закрыть карточку.
                              setState(() => _creating = false);
                            },
                          ),
                        ),
                      ],
                    )
                    : _editingClient != null
                    ? ListView(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: BlocProvider(
                            create: (context) => sl<ClientVersionsCubit>(),
                            child: ClientEditCard(
                              client: _editingClient!,
                              onCancel:
                                  () => setState(() => _editingClient = null),
                              onSaved: (updated) {
                                final debugJson =
                                    ClientModel.fromEntity(updated).toJson();
                                NetworkLogger.printJson(
                                  'SAVE CLIENT JSON =>',
                                  debugJson,
                                );

                                // Карточка редактирования теперь сама сохраняет клиента.
                                // Cubit должен сам обновить состояние.
                                // Нам нужно просто закрыть карточку.
                                setState(() => _editingClient = null);
                              },
                            ),
                          ),
                        ),
                      ],
                    )
                    : _ClientsTableSection(
                      filterProvider: () => _filter,
                      sortColumnIndex: _sortColumnIndex,
                      sortAsc: _sortAsc,
                      onSort:
                          (i, asc) => setState(() {
                            _sortColumnIndex = i;
                            _sortAsc = asc;
                          }),
                      onEdit:
                          (client) => setState(() {
                            _creating = false;
                            _editingClient = client;
                          }),
                    ),
          ),
        ],
      ),
    );
  }
}

// === Table Section ===
class _ClientsTableSection extends StatefulWidget {
  final String Function() filterProvider;
  final int sortColumnIndex;
  final bool sortAsc;
  final void Function(int, bool) onSort;
  final void Function(ClientEntity) onEdit;
  const _ClientsTableSection({
    required this.filterProvider,
    required this.sortColumnIndex,
    required this.sortAsc,
    required this.onSort,
    required this.onEdit,
  });

  @override
  State<_ClientsTableSection> createState() => _ClientsTableSectionState();
}

class _ClientsTableSectionState extends State<_ClientsTableSection> {
  @override
  void initState() {
    super.initState();
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm != null) {
      context.read<ClientsCubit>().fetchClients(firmState.selectedFirm!.id);
    }
  }

  @override
  void didUpdateWidget(covariant _ClientsTableSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    final firmState = context.read<ActiveFirmCubit>().state;
    final oldFirmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm != null &&
        firmState.selectedFirm!.id != oldFirmState.selectedFirm?.id) {
      context.read<ClientsCubit>().fetchClients(firmState.selectedFirm!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActiveFirmCubit, ActiveFirmState>(
      builder: (context, firmState) {
        if (firmState.isLoading || firmState.selectedFirm == null) {
          return const Align(
            alignment: Alignment.topCenter,
            child: LoadingTile(height: 120, width: double.infinity),
          );
        }

        return BlocListener<ClientsCubit, ClientsState>(
          listenWhen: (previous, current) {
            // Показываем ошибку только если она новая и операция завершена неуспешно
            return previous.error != current.error &&
                current.error != null &&
                !current.isLoading &&
                current.clients.isEmpty;
          },
          listener: (context, state) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.error!)));
          },
          child: BlocBuilder<ClientsCubit, ClientsState>(
            builder: (context, state) {
              if (state.isLoading) {
                return const Align(
                  alignment: Alignment.topCenter,
                  child: LoadingTile(height: 120, width: double.infinity),
                );
              }

              if (state.noAccess) {
                return const Center(
                  child: Text('Недостаточно прав для просмотра клиентов'),
                );
              }

              final search = widget.filterProvider().toLowerCase();
              List<ClientEntity> data =
                  state.clients.where((c) {
                    final concat =
                        '${c.name}${c.shortName ?? ''}${c.ownershipForm ?? ''}${c.comment ?? ''}${c.inn ?? ''}${c.taxSystems.join(',')}'
                            .toLowerCase();
                    return search.isEmpty || concat.contains(search);
                  }).toList();

              data.sort((a, b) {
                int res;
                switch (widget.sortColumnIndex) {
                  case 0:
                    res = (a.shortName ?? '').compareTo(b.shortName ?? '');
                    break;
                  case 1:
                    res = (a.ownershipForm ?? '').compareTo(b.ownershipForm ?? '');
                    break;
                  case 2:
                    res = a.name.compareTo(b.name);
                    break;
                  case 3:
                    res = (a.inn ?? '').compareTo(b.inn ?? '');
                    break;
                  case 4:
                    res = (a.taxSystems.isNotEmpty ? a.taxSystems.first : '')
                        .compareTo(
                          b.taxSystems.isNotEmpty ? b.taxSystems.first : '',
                        );
                    break;
                  default:
                    res = 0;
                }
                return widget.sortAsc ? res : -res;
              });

              if (data.isEmpty) {
                return const Center(child: Text('Нет клиентов'));
              }

              return Expanded(
                child: SingleChildScrollView(
                  child: ResizableDataTable(
                    prefsKey: 'clients_table_column_widths',
                    sortColumnIndex: widget.sortColumnIndex,
                    sortAscending: widget.sortAsc,
                    columns: [
                      DataColumn(
                        label: const Text('Сокращенное наименование'),
                        onSort: (i, asc) => widget.onSort(i, asc),
                      ),
                      DataColumn(
                        label: const Text('Форма собственности'),
                        onSort: (i, asc) => widget.onSort(i, asc),
                      ),
                      DataColumn(
                        label: const Text('Наименование'),
                        onSort: (i, asc) => widget.onSort(i, asc),
                      ),
                      DataColumn(
                        label: const Text('ИНН'),
                        onSort: (i, asc) => widget.onSort(i, asc),
                      ),
                      DataColumn(
                        label: const Text('Сист. налогообл.'),
                        onSort: (i, asc) => widget.onSort(i, asc),
                      ),
                      const DataColumn(label: Text('Действия')),
                    ],
                    initialColumnWidths: const [
                      180.0,
                      160.0,
                      250.0,
                      150.0,
                      200.0,
                      150.0,
                    ],
                    rows:
                        data
                            .map(
                              (c) => DataRow(
                                onSelectChanged: (_) => widget.onEdit(c),
                                cells: [
                                  DataCell(
                                    Text(
                                      c.shortName ?? '-',
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  DataCell(
                                    Text(
                                      c.ownershipForm ?? '-',
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  DataCell(
                                    Text(
                                      c.name,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  DataCell(Text(c.inn ?? '-')),
                                  DataCell(
                                    Text(
                                      c.taxSystems.isNotEmpty
                                          ? c.taxSystems.join(', ')
                                          : '-',
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  DataCell(
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        IconButton(
                                          icon: const Icon(
                                            Icons.remove_red_eye,
                                          ),
                                          tooltip: 'Подробнее',
                                          onPressed: () => widget.onEdit(c),
                                        ),
                                        IconButton(
                                          icon: const Icon(
                                            Icons.delete_outline,
                                          ),
                                          tooltip: 'Удалить все версии клиента',
                                          onPressed: () async {
                                            final firm =
                                                context
                                                    .read<ActiveFirmCubit>()
                                                    .state
                                                    .selectedFirm;
                                            if (firm == null) return;
                                            final clientsCubit =
                                                context.read<ClientsCubit>();
                                            final confirm = await showDialog<
                                              bool
                                            >(
                                              context: context,
                                              builder:
                                                  (ctx) => AlertDialog(
                                                    title: const Text(
                                                      'Удалить клиента',
                                                    ),
                                                    content: Text(
                                                      'Вы уверены, что хотите удалить ВСЕ ВЕРСИИ клиента "${c.name}"? Это действие нельзя отменить.',
                                                    ),
                                                    actions: [
                                                      TextButton(
                                                        onPressed:
                                                            () => Navigator.pop(
                                                              ctx,
                                                              false,
                                                            ),
                                                        child: const Text(
                                                          'Отмена',
                                                        ),
                                                      ),
                                                      TextButton(
                                                        onPressed:
                                                            () => Navigator.pop(
                                                              ctx,
                                                              true,
                                                            ),
                                                        child: const Text(
                                                          'Удалить',
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                            );
                                            if (confirm == true) {
                                              clientsCubit.deleteAllClientVersions(
                                                firm.id,
                                                c.name,
                                              );
                                            }
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            )
                            .toList(),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
