import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_input_field.dart';
import 'package:intl/intl.dart';

/// Примеры использования SmartDateInputField для различных сценариев
class SmartDateInputFieldExamples extends StatefulWidget {
  const SmartDateInputFieldExamples({super.key});

  @override
  State<SmartDateInputFieldExamples> createState() =>
      _SmartDateInputFieldExamplesState();
}

class _SmartDateInputFieldExamplesState
    extends State<SmartDateInputFieldExamples> {
  final _basicController = TextEditingController();
  final _customFormatController = TextEditingController();
  final _readOnlyController = TextEditingController(text: '01.01.2024');
  final _validationController = TextEditingController();
  final _customParserController = TextEditingController();

  DateTime? _selectedDate;

  @override
  void dispose() {
    _basicController.dispose();
    _customFormatController.dispose();
    _readOnlyController.dispose();
    _validationController.dispose();
    _customParserController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('SmartDateInputField Examples')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Базовый пример
            const Text(
              '1. Базовое использование:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SmartDateInputField(
              controller: _basicController,
              labelText: 'Дата рождения',
              prefixIcon: Icons.person,
              onDateChanged: (date) {
                print('Дата изменена: $date');
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Введите дату';
                }
                return null;
              },
            ),

            const SizedBox(height: 24),

            // Пример с кастомным форматом
            const Text(
              '2. Кастомный формат (yyyy-MM-dd):',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SmartDateInputField(
              controller: _customFormatController,
              labelText: 'Дата в ISO формате',
              dateFormat: 'yyyy-MM-dd',
              prefixIcon: Icons.calendar_today,
              hintText: 'гггг-мм-дд',
              onDateChanged: (date) {
                setState(() {
                  _selectedDate = date;
                });
              },
            ),
            if (_selectedDate != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Выбрана дата: ${DateFormat('dd.MM.yyyy').format(_selectedDate!)}',
                ),
              ),

            const SizedBox(height: 24),

            // Режим только для чтения
            const Text(
              '3. Режим только для чтения:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SmartDateInputField(
              controller: _readOnlyController,
              labelText: 'Дата создания записи',
              readOnly: true,
              prefixIcon: Icons.info,
              onCopyToClipboard: (text) {
                // Копирование в буфер обмена уже встроено
              },
              fieldName: 'Дата создания',
            ),

            const SizedBox(height: 24),

            // Пример с валидацией
            const Text(
              '4. С кастомной валидацией:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SmartDateInputField(
              controller: _validationController,
              labelText: 'Дата не ранее 2020 года',
              prefixIcon: Icons.warning,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Введите дату';
                }
                try {
                  final date = DateFormat('dd.MM.yyyy').parse(value);
                  if (date.year < 2020) {
                    return 'Дата должна быть не ранее 2020 года';
                  }
                  if (date.isAfter(DateTime.now())) {
                    return 'Дата не может быть в будущем';
                  }
                  return null;
                } catch (e) {
                  return 'Неверный формат даты';
                }
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),

            const SizedBox(height: 24),

            // Пример с кастомным парсером
            const Text(
              '5. С кастомным парсером (только числа):',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SmartDateInputField(
              controller: _customParserController,
              labelText: 'Введите 8 цифр (ддммгггг)',
              prefixIcon: Icons.numbers,
              hintText: 'например: 01012024',
              customDateParser: (input) {
                // Кастомный парсер, который принимает только 8 цифр подряд
                if (RegExp(r'^\d{8}$').hasMatch(input)) {
                  final day = int.parse(input.substring(0, 2));
                  final month = int.parse(input.substring(2, 4));
                  final year = int.parse(input.substring(4, 8));

                  if (day >= 1 && day <= 31 && month >= 1 && month <= 12) {
                    return DateTime(year, month, day);
                  }
                }
                return null;
              },
              customDateFormatter: (date) {
                // Кастомный форматер для отображения
                return '${date.day.toString().padLeft(2, '0')}${date.month.toString().padLeft(2, '0')}${date.year}';
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Введите 8 цифр';
                }
                if (!RegExp(r'^\d{8}$').hasMatch(value)) {
                  return 'Только 8 цифр (ддммгггг)';
                }
                return null;
              },
            ),

            const SizedBox(height: 32),

            // Кнопка для демонстрации программного установки значения
            ElevatedButton(
              onPressed: () {
                final today = DateTime.now();
                _basicController.text = DateFormat('dd.MM.yyyy').format(today);
                _customFormatController.text = DateFormat(
                  'yyyy-MM-dd',
                ).format(today);
                setState(() {
                  _selectedDate = today;
                });
              },
              child: const Text('Установить сегодняшнюю дату'),
            ),
          ],
        ),
      ),
    );
  }
}
