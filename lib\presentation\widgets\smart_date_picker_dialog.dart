import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

enum _SmartDatePickerMode { day, month, year }

/// Универсальный русскоязычный диалог выбора даты в стиле 1С
class SmartDatePickerDialog extends StatefulWidget {
  final DateTime? initialDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String? helpText;
  final bool allowClear;

  const SmartDatePickerDialog({
    super.key,
    this.initialDate,
    this.firstDate,
    this.lastDate,
    this.helpText,
    this.allowClear = true,
  });

  @override
  State<SmartDatePickerDialog> createState() => _SmartDatePickerDialogState();

  /// Показать диалог и вернуть выбранную дату или null
  static Future<DateTime?> show({
    required BuildContext context,
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
    String? helpText,
    bool allowClear = true,
  }) {
    return showDialog<DateTime>(
      context: context,
      builder:
          (context) => SmartDatePickerDialog(
            initialDate: initialDate,
            firstDate: firstDate,
            lastDate: lastDate,
            helpText: helpText,
            allowClear: allowClear,
          ),
    );
  }
}

class _SmartDatePickerDialogState extends State<SmartDatePickerDialog> {
  late final TextEditingController _controller;
  late DateTime _viewDate;
  DateTime? _selectedDate;
  bool _hasError = false;
  String? _errorText;
  _SmartDatePickerMode _mode = _SmartDatePickerMode.day;
  late final ScrollController _yearScrollController;

  static const List<String> _monthNames = [
    'Январь',
    'Февраль',
    'Март',
    'Апрель',
    'Май',
    'Июнь',
    'Июль',
    'Август',
    'Сентябрь',
    'Октябрь',
    'Ноябрь',
    'Декабрь',
  ];

  static const List<String> _weekdayNames = [
    'Пн',
    'Вт',
    'Ср',
    'Чт',
    'Пт',
    'Сб',
    'Вс',
  ];

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _viewDate = widget.initialDate ?? DateTime.now();
    _yearScrollController = ScrollController(
      initialScrollOffset:
          (_viewDate.year - (widget.firstDate?.year ?? 1900)) * 40.0,
    );

    _controller = TextEditingController(
      text:
          _selectedDate != null
              ? DateFormat('dd.MM.yyyy').format(_selectedDate!)
              : '',
    );

    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _yearScrollController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    if (text.isEmpty) {
      setState(() {
        _selectedDate = null;
        _hasError = false;
        _errorText = null;
      });
      return;
    }

    final date = _tryParseDate(text);
    if (date != null && _isDateInRange(date)) {
      setState(() {
        _selectedDate = date;
        _viewDate = date;
        _hasError = false;
        _errorText = null;
      });
    } else {
      setState(() {
        _hasError = true;
        _errorText = 'Неверный формат даты или дата вне диапазона';
      });
    }
  }

  DateTime? _tryParseDate(String text) {
    try {
      // Пробуем парсить dd.MM.yyyy
      if (text.length == 10) {
        return DateFormat('dd.MM.yyyy').parseStrict(text);
      }
      // Пробуем парсить dd.MM.yy
      if (text.length == 8) {
        final now = DateTime.now();
        final yearSuffix = int.parse(text.substring(6, 8));
        // YY to YYYY conversion logic (e.g., 25 -> 2025, 98 -> 1998)
        final year =
            yearSuffix + (yearSuffix + 2000 > now.year + 50 ? 1900 : 2000);
        return DateFormat('dd.MM.yy').parseStrict(text).copyWith(year: year);
      }
      // Пробуем парсить dd.MM (текущий год)
      if (text.length <= 5 && (text.contains('.') || text.contains(','))) {
        final parts = text.replaceAll(',', '.').split('.');
        if (parts.length == 2) {
          final day = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          if (day != null && month != null) {
            return DateTime(DateTime.now().year, month, day);
          }
        }
      }
    } catch (e) {
      // Игнорируем ошибки парсинга
    }
    return null;
  }

  bool _isDateInRange(DateTime date) {
    if (widget.firstDate != null && date.isBefore(widget.firstDate!)) {
      return false;
    }
    if (widget.lastDate != null && date.isAfter(widget.lastDate!)) {
      return false;
    }
    return true;
  }

  void _setToday() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    if (_isDateInRange(today)) {
      Navigator.of(context).pop(today);
    }
  }

  void _clear() {
    Navigator.of(context).pop(null);
  }

  void _selectDate(DateTime date) {
    if (_isDateInRange(date)) {
      Navigator.of(context).pop(date);
    }
  }

  void _previousMonth() {
    setState(() {
      _viewDate = DateTime(_viewDate.year, _viewDate.month - 1, 1);
    });
  }

  void _nextMonth() {
    setState(() {
      _viewDate = DateTime(_viewDate.year, _viewDate.month + 1, 1);
    });
  }

  void _previousYear() {
    setState(() {
      _viewDate = DateTime(_viewDate.year - 1, _viewDate.month, 1);
    });
  }

  void _nextYear() {
    setState(() {
      _viewDate = DateTime(_viewDate.year + 1, _viewDate.month, 1);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SizedBox(
        width: 340,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Заголовок
              Text(
                widget.helpText ?? 'Выберите дату',
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Поле ввода даты
              TextField(
                controller: _controller,
                onSubmitted: (_) {
                  if (!_hasError) {
                    Navigator.of(context).pop(_selectedDate);
                  }
                },
                decoration: InputDecoration(
                  labelText: 'Дата',
                  hintText: 'дд.мм.гггг',
                  border: const OutlineInputBorder(),
                  errorText: _errorText,
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.calendar_today, size: 18),
                    onPressed: () {
                      setState(() {
                        _mode = _SmartDatePickerMode.day;
                      });
                    },
                  ),
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                  LengthLimitingTextInputFormatter(10),
                ],
                onTap: () {
                  if (_mode != _SmartDatePickerMode.day) {
                    setState(() {
                      _mode = _SmartDatePickerMode.day;
                    });
                  }
                },
              ),
              const SizedBox(height: 12),

              // Календарь
              AnimatedSize(
                duration: const Duration(milliseconds: 200),
                child: _buildPicker(),
              ),
              const SizedBox(height: 16),

              // Кнопки действий
              Row(
                children: [
                  TextButton(
                    onPressed: _setToday,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                    child: const Text('Сегодня'),
                  ),
                  if (widget.allowClear)
                    TextButton(
                      onPressed: _clear,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                      ),
                      child: const Text('Очистить'),
                    ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                    child: const Text('Отмена'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPicker() {
    switch (_mode) {
      case _SmartDatePickerMode.day:
        return _buildDayPicker();
      case _SmartDatePickerMode.month:
        return _buildMonthPicker();
      case _SmartDatePickerMode.year:
        return _buildYearPicker();
    }
  }

  Widget _buildDayPicker() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Навигация по календарю
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              onPressed: _previousYear,
              icon: const Icon(Icons.keyboard_double_arrow_left),
              tooltip: 'Предыдущий год',
            ),
            IconButton(
              onPressed: _previousMonth,
              icon: const Icon(Icons.keyboard_arrow_left),
              tooltip: 'Предыдущий месяц',
            ),
            Expanded(
              child: TextButton(
                onPressed:
                    () => setState(() => _mode = _SmartDatePickerMode.month),
                child: Text(
                  '${_monthNames[_viewDate.month - 1]} ${_viewDate.year}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
            IconButton(
              onPressed: _nextMonth,
              icon: const Icon(Icons.keyboard_arrow_right),
              tooltip: 'Следующий месяц',
            ),
            IconButton(
              onPressed: _nextYear,
              icon: const Icon(Icons.keyboard_double_arrow_right),
              tooltip: 'Следующий год',
            ),
          ],
        ),
        const SizedBox(height: 8),
        _buildCalendarGrid(),
      ],
    );
  }

  Widget _buildMonthPicker() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              onPressed: _previousYear,
              icon: const Icon(Icons.keyboard_arrow_left),
              tooltip: 'Предыдущий год',
            ),
            Expanded(
              child: TextButton(
                onPressed:
                    () => setState(() => _mode = _SmartDatePickerMode.year),
                child: Text(
                  _viewDate.year.toString(),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
            IconButton(
              onPressed: _nextYear,
              icon: const Icon(Icons.keyboard_arrow_right),
              tooltip: 'Следующий год',
            ),
          ],
        ),
        const SizedBox(height: 8),
        GridView.count(
          shrinkWrap: true,
          crossAxisCount: 4,
          childAspectRatio: 2.5,
          children: List.generate(12, (index) {
            final month = index + 1;
            return TextButton(
              onPressed: () {
                setState(() {
                  _viewDate = DateTime(_viewDate.year, month, 1);
                  _mode = _SmartDatePickerMode.day;
                });
              },
              child: Text(_monthNames[index].substring(0, 3)),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildYearPicker() {
    final firstYear = widget.firstDate?.year ?? 1900;
    final lastYear = widget.lastDate?.year ?? 2100;
    final yearCount = lastYear - firstYear + 1;

    return SizedBox(
      height: 200,
      child: ListView.builder(
        controller: _yearScrollController,
        itemCount: yearCount,
        itemBuilder: (context, index) {
          final year = firstYear + index;
          final isSelected = year == _viewDate.year;
          return TextButton(
            style: TextButton.styleFrom(
              backgroundColor:
                  isSelected
                      ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                      : null,
            ),
            onPressed: () {
              setState(() {
                _viewDate = DateTime(year, _viewDate.month, 1);
                _mode = _SmartDatePickerMode.month;
              });
            },
            child: Text(year.toString()),
          );
        },
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(_viewDate.year, _viewDate.month, 1);
    final lastDayOfMonth = DateTime(_viewDate.year, _viewDate.month + 1, 0);

    // Понедельник = 1, воскресенье = 7
    final firstWeekday = firstDayOfMonth.weekday;
    final daysInMonth = lastDayOfMonth.day;

    // Дни предыдущего месяца для заполнения первой строки
    final prevMonthDays = firstWeekday - 1;

    return Column(
      children: [
        // Заголовки дней недели
        Row(
          children:
              _weekdayNames
                  .map(
                    (day) => Expanded(
                      child: Center(
                        child: Text(
                          day,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),
        const SizedBox(height: 4),

        // Сетка календаря
        ..._buildCalendarRows(prevMonthDays, daysInMonth),
      ],
    );
  }

  List<Widget> _buildCalendarRows(int prevMonthDays, int daysInMonth) {
    final rows = <Widget>[];
    var dayCounter = 1 - prevMonthDays;

    for (int week = 0; week < 6; week++) {
      final rowChildren = <Widget>[];

      for (int day = 0; day < 7; day++) {
        if (dayCounter <= 0 || dayCounter > daysInMonth) {
          // Пустая клетка или дни другого месяца
          rowChildren.add(const Expanded(child: SizedBox(height: 32)));
        } else {
          final date = DateTime(_viewDate.year, _viewDate.month, dayCounter);
          final isSelected =
              _selectedDate != null &&
              _selectedDate!.year == date.year &&
              _selectedDate!.month == date.month &&
              _selectedDate!.day == date.day;
          final isToday = DateTime.now().difference(date).inDays == 0;
          final isInRange = _isDateInRange(date);

          rowChildren.add(
            Expanded(
              child: GestureDetector(
                onTap: isInRange ? () => _selectDate(date) : null,
                behavior: HitTestBehavior.opaque,
                child: Container(
                  height: 32,
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Theme.of(context).colorScheme.primary
                            : null,
                    border:
                        isToday
                            ? Border.all(
                              color: Theme.of(context).colorScheme.primary,
                              width: 1,
                            )
                            : null,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      dayCounter.toString(),
                      style: TextStyle(
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : isInRange
                                ? null
                                : Theme.of(context).disabledColor,
                        fontWeight: isSelected ? FontWeight.bold : null,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        }
        dayCounter++;
      }

      rows.add(Row(children: rowChildren));

      // Прерываем, если все дни месяца уже показаны
      if (dayCounter > daysInMonth) break;
    }

    return rows;
  }
}
