import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../cubit/zero_reporting_cubit.dart';
import '../../cubit/price_calculator_state.dart';
import 'zero_reporting_settings_dialog.dart';

class ZeroReportingBody extends StatelessWidget {
  const ZeroReportingBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Основное содержимое
        Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: BlocBuilder<ZeroReportingCubit, ZeroReportingState>(
                  builder: (context, state) {
                    final cubit = context.read<ZeroReportingCubit>();

                    return Center(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 600),
                        child: Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Калькулятор нулевой отчетности',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.headlineSmall?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.settings_outlined),
                                      tooltip: 'Настроить расценки',
                                      onPressed: () {
                                        showDialog(
                                          context: context,
                                          builder:
                                              (_) => BlocProvider.value(
                                                value: cubit,
                                                child:
                                                    const ZeroReportingSettingsDialog(),
                                              ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Выберите тип организации и систему налогообложения:',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyMedium?.copyWith(
                                    color:
                                        Theme.of(
                                          context,
                                        ).colorScheme.onSurfaceVariant,
                                  ),
                                ),
                                const SizedBox(height: 24),

                                // Радио-кнопки для выбора типа отчетности
                                ...ZeroReportingType.values.map((type) {
                                  final customPrice = cubit.getCustomPrice(
                                    type,
                                  );
                                  return RadioListTile<ZeroReportingType>(
                                    title: Text(type.label),
                                    subtitle: Text(
                                      '${NumberFormat('#,##0', 'ru_RU').format(customPrice)} ₽/квартал',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium?.copyWith(
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    value: type,
                                    groupValue: state.selectedType,
                                    onChanged: (value) {
                                      if (value != null) {
                                        cubit.selectType(value);
                                      }
                                    },
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                    ),
                                  );
                                }),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            // отступ, чтобы контент не перекрывался футером
            const SizedBox(height: 100),
          ],
        ),
        // Плашка с итогом
        Positioned(
          bottom: 16,
          right: 16,
          child: const _ZeroReportingTotalFooter(),
        ),
      ],
    );
  }
}

class _ZeroReportingTotalFooter extends StatefulWidget {
  const _ZeroReportingTotalFooter();

  @override
  State<_ZeroReportingTotalFooter> createState() =>
      _ZeroReportingTotalFooterState();
}

class _ZeroReportingTotalFooterState extends State<_ZeroReportingTotalFooter>
    with SingleTickerProviderStateMixin {
  bool _expanded = false;
  static const double _width = 420;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ZeroReportingCubit, ZeroReportingState>(
      builder: (context, state) {
        final cubit = context.read<ZeroReportingCubit>();
        final numberFormat = NumberFormat.currency(
          locale: 'ru_RU',
          symbol: '₽',
        );
        final total = cubit.quarterlyPrice;

        return SizedBox(
          width: _expanded ? 600 : _width,
          child: AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: Card(
              elevation: 8,
              child: InkWell(
                onTap: () => setState(() => _expanded = !_expanded),
                customBorder: const RoundedRectangleBorder(),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  child:
                      _expanded
                          ? Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Итоговая стоимость',
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        numberFormat.format(total),
                                        style: Theme.of(
                                          context,
                                        ).textTheme.headlineMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      const Icon(Icons.expand_more),
                                    ],
                                  ),
                                ],
                              ),
                              const Divider(),
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color:
                                      Theme.of(
                                        context,
                                      ).colorScheme.primaryContainer,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Выбранный тариф:',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      state.selectedType.label,
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyLarge?.copyWith(
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Стоимость: ${numberFormat.format(total)} /квартал',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium?.copyWith(
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          )
                          : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  'Итоговая стоимость в квартал:',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                numberFormat.format(total),
                                style: Theme.of(
                                  context,
                                ).textTheme.headlineMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Icon(Icons.expand_less),
                            ],
                          ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
