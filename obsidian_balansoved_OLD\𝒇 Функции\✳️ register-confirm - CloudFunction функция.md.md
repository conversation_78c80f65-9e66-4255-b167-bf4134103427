Идентификатор - d4eu095gn2tga52no22p
Описание - Подтвердить регистрацию с помощью кода, активировать пользователя и связанные приглашения.

Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `email`: Email пользователя.
	-> `code`: 6-значный код из email.

Внутренняя работа:
	-> Парсинг тела запроса для извлечения `email` и `code`.
	-> Проверка наличия `email` и `code`; если отсутствуют, возврат 400 с сообщением.
	-> Получение драйверов YDB для `jwt-database` и `firms-database`.
	-> Транзакционная обработка в `jwt-database`:
		-> Поиск неактивного пользователя по `email`.
		-> Если пользователь не найден или уже активен, возврат 404.
		-> Проверка предоставленного `code` на совпадение и срока действия (`code_expires_at`), с учетом возможных форматов даты (datetime или микросекунды).
		-> Если код неверный или просрочен, возврат 400.
		-> Обновление пользователя: установка `is_active = true`, очистка `verification_code` и `code_expires_at`.
		-> Коммит транзакции.
	-> Обновление в `firms-database` (отдельная операция с retry):
		-> Обновление `user_id` для всех записей в таблице `Users`, где `email` совпадает и `is_active = false`.
		-> Если обновление fails, логирование ошибки, но продолжение (пользователь активирован).
	-> Генерация JWT-токена на основе `user_id` и `email`.
	-> Обработка ошибок: возврат 500 при внутренних сбоях.

На выходе:
	-> `200 OK`: {"token": "<jwt_token>"}
	-> `400 Bad Request`: {"message": "Invalid or expired code."} или `{"message": "Email and code are required."}`
	-> `404 Not Found`: `{"message": "User not found or already active."}`
	-> `500 Internal Server Error`: {"message": "Internal Server Error"}

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
    - `YDB_ENDPOINT`, `YDB_DATABASE` ([[💾 jwt-database - База данных YandexDatabase]])
    - `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
    - SA_KEY_FILE ([[ydb_sa_key.json]])
    - JWT_SECRET 

---

#### Код функции

```python
import json
import os
import datetime
import pytz
import logging
import ydb
from utils import ydb_utils, auth_utils, request_parser

logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    try:
        data = request_parser.parse_request_body(event)
    except ValueError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
        
    email = data.get('email', '').lower()
    code = data.get('code')

    if not all([email, code]):
        return {"statusCode": 400, "body": json.dumps({"message": "Email and code are required."})}

    # Драйвер для основной базы аутентификации
    auth_driver = ydb_utils.get_ydb_driver()
    auth_pool = ydb.SessionPool(auth_driver)

    # Драйвер для базы фирм
    firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
    firms_pool = ydb.SessionPool(firms_driver)

    def transaction(session):
        # Эта транзакция работает с jwt-database
        tx = session.transaction(ydb.SerializableReadWrite())

        # 1. Ищем неактивного пользователя в jwt-database
        select_query = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8;
            SELECT user_id, verification_code, code_expires_at FROM users WHERE email = $email AND is_active = false;
        """
        result_sets = tx.execute(session.prepare(select_query), {'$email': email})
        
        if not result_sets[0].rows:
            tx.rollback()
            return {"status": 404, "message": "User not found or already active."}

        user_data = result_sets[0].rows[0]
        
        # 2. Проверяем код и срок его жизни
        now_utc = datetime.datetime.now(pytz.utc)

        # YDB может возвращать Timestamp как datetime или как числовое количество микросекунд.
        expires_raw = user_data.code_expires_at
        if expires_raw is not None:
            if isinstance(expires_raw, datetime.datetime):
                is_expired = now_utc > expires_raw
            else:
                # Предполагаем, что хранится количество микросекунд с эпохи UNIX
                try:
                    expires_dt = datetime.datetime.fromtimestamp(expires_raw / 1_000_000, tz=pytz.utc)
                    is_expired = now_utc > expires_dt
                except Exception:
                    # Непредвиденный формат – считаем код просроченным
                    is_expired = True
        else:
            is_expired = False

        if user_data.verification_code != code or is_expired:
            tx.rollback()
            return {"status": 400, "message": "Invalid or expired code."}

        # 3. Активируем пользователя в jwt-database
        user_id_to_activate = user_data.user_id
        update_query = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $user_id AS Utf8;
            UPDATE users SET is_active = true, verification_code = NULL, code_expires_at = NULL WHERE user_id = $user_id;
        """
        tx.execute(session.prepare(update_query), {'$user_id': user_id_to_activate})
        tx.commit() # Коммитим изменения в jwt-database

        # 4. Обновляем user_id в ожидающих приглашениях в firms-database (отдельная операция)
        def update_invitations_in_firms(firm_session):
            logging.info(f"CONFIRM: Searching for pending invitations for {email} in firms-database.")
            update_tx = firm_session.transaction(ydb.SerializableReadWrite())
            update_query_text = """
                DECLARE $new_user_id AS Utf8; DECLARE $email AS Utf8;
                UPDATE Users SET user_id = $new_user_id
                WHERE email = $email AND is_active = false;
            """
            update_tx.execute(
                firm_session.prepare(update_query_text),
                {'$new_user_id': user_id_to_activate, '$email': email}
            )
            update_tx.commit()
            logging.info(f"CONFIRM: Updated pending invitations for {email} with new user_id {user_id_to_activate}.")

        try:
            firms_pool.retry_operation_sync(update_invitations_in_firms)
        except Exception as e:
            # Если здесь произойдет ошибка, пользователь уже активирован, но не привязан к фирме.
            # Это критическая ситуация, которую нужно залогировать.
            logging.critical(f"User {user_id_to_activate} was activated, but FAILED to link to pending invitations in firms-database. Error: {e}", exc_info=True)
            # Возвращаем токен, так как основная регистрация прошла успешно. Проблему с приглашением придется решать вручную.
        
        # 5. Генерируем токен
        token = auth_utils.generate_jwt(user_id_to_activate, email)
        return {"status": 200, "token": token}

    try:
        result = auth_pool.retry_operation_sync(transaction)
        if result.get("status") == 200:
            return {"statusCode": 200, "body": json.dumps({"token": result["token"]})}
        else:
            return {"statusCode": result.get("status", 400), "body": json.dumps({"message": result.get("message")})}
    except Exception as e:
        logging.error(f"Error during registration confirmation for {email}: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}

```