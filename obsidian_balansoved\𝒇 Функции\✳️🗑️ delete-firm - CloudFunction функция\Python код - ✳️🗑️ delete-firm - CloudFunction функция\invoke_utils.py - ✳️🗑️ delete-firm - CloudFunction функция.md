
```python
import os
import json
import logging
import time
import requests
import ydb.iam

# Переменные окружения для YDB
YDB_ENDPOINT_FIRMS = os.environ.get("YDB_ENDPOINT_FIRMS")
YDB_DATABASE_FIRMS = os.environ.get("YDB_DATABASE_FIRMS")
YDB_ENDPOINT_TASKS = os.environ.get("YDB_ENDPOINT_TASKS")
YDB_DATABASE_TASKS = os.environ.get("YDB_DATABASE_TASKS")
YDB_ENDPOINT_CLIENTS = os.environ.get("YDB_ENDPOINT_CLIENTS")
YDB_DATABASE_CLIENTS = os.environ.get("YDB_DATABASE_CLIENTS")
YDB_ENDPOINT_CLIENT_PAYMENTS = os.environ.get("YDB_ENDPOINT_CLIENT_PAYMENTS")
YDB_DATABASE_CLIENT_PAYMENTS = os.environ.get("YDB_DATABASE_CLIENT_PAYMENTS")
YDB_ENDPOINT_TARIFFS_AND_STORAGE = os.environ.get("YDB_ENDPOINT_TARIFFS_AND_STORAGE")
YDB_DATABASE_TARIFFS_AND_STORAGE = os.environ.get("YDB_DATABASE_TARIFFS_AND_STORAGE")

# ID целевых функций
FUNCTION_ID_EDIT_TASK = os.environ.get("FUNCTION_ID_EDIT_TASK")
FUNCTION_ID_EDIT_CLIENT = os.environ.get("FUNCTION_ID_EDIT_CLIENT")
FUNCTION_ID_EDIT_EMPLOYEE = os.environ.get("FUNCTION_ID_EDIT_EMPLOYEE")
FUNCTION_ID_DELETE_EMPLOYEE = os.environ.get("FUNCTION_ID_DELETE_EMPLOYEE")

# Кеш для IAM-токена
_IAM_TOKEN_CACHE = {"token": None, "expires_at": 0}

def _get_iam_token():
    """Получает и кеширует IAM-токен."""
    now = time.time()
    if _IAM_TOKEN_CACHE["token"] and now < _IAM_TOKEN_CACHE["expires_at"]:
        return _IAM_TOKEN_CACHE["token"]
    
    try:
        creds = ydb.iam.MetadataUrlCredentials()
        token, expires_at = creds.token
        _IAM_TOKEN_CACHE["token"] = token
        _IAM_TOKEN_CACHE["expires_at"] = expires_at - 60 # Запас в 60 секунд
        return token
    except Exception as e:
        logging.error(f"Failed to get IAM token: {e}", exc_info=True)
        return None

def invoke_function(function_id: str, payload: dict, user_jwt: str) -> dict | None:
    """Вызывает облачную функцию с авторизацией."""
    if not function_id:
        logging.error(f"Function ID is not specified for payload: {payload}")
        return None

    iam_token = _get_iam_token()
    if not iam_token:
        logging.error("Cannot invoke function, failed to get IAM token.")
        return None

    function_url = f"https://functions.yandexcloud.net/{function_id}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {iam_token}",
        "X-Forwarded-Authorization": f"Bearer {user_jwt}"
    }

    try:
        response = requests.post(function_url, headers=headers, json=payload, timeout=20)
        response.raise_for_status()
        return response.json() if response.content else {}
    except requests.exceptions.RequestException as e:
        logging.error(f"Error invoking function {function_id}: {e}", exc_info=True)
        return None
```