// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'router.dart';

/// generated route for
/// [AcceptInvitationPage]
class AcceptInvitationRoute extends PageRouteInfo<AcceptInvitationRouteArgs> {
  AcceptInvitationRoute({
    Key? key,
    String? invitationKey,
    List<PageRouteInfo>? children,
  }) : super(
         AcceptInvitationRoute.name,
         args: AcceptInvitationRouteArgs(
           key: key,
           invitationKey: invitationKey,
         ),
         rawPathParams: {'invitationKey': invitationKey},
         initialChildren: children,
       );

  static const String name = 'AcceptInvitationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<AcceptInvitationRouteArgs>(
        orElse:
            () => AcceptInvitationRouteArgs(
              invitationKey: pathParams.optString('invitationKey'),
            ),
      );
      return AcceptInvitationPage(
        key: args.key,
        invitationKey: args.invitationKey,
      );
    },
  );
}

class AcceptInvitationRouteArgs {
  const AcceptInvitationRouteArgs({this.key, this.invitationKey});

  final Key? key;

  final String? invitationKey;

  @override
  String toString() {
    return 'AcceptInvitationRouteArgs{key: $key, invitationKey: $invitationKey}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AcceptInvitationRouteArgs) return false;
    return key == other.key && invitationKey == other.invitationKey;
  }

  @override
  int get hashCode => key.hashCode ^ invitationKey.hashCode;
}

/// generated route for
/// [CalendarPage]
class CalendarRoute extends PageRouteInfo<CalendarRouteArgs> {
  CalendarRoute({
    Key? key,
    required int initialYear,
    required int initialMonth,
    double? initialScrollOffset,
    List<PageRouteInfo>? children,
  }) : super(
         CalendarRoute.name,
         args: CalendarRouteArgs(
           key: key,
           initialYear: initialYear,
           initialMonth: initialMonth,
           initialScrollOffset: initialScrollOffset,
         ),
         rawPathParams: {'year': initialYear, 'month': initialMonth},
         rawQueryParams: {'scrollOffset': initialScrollOffset},
         initialChildren: children,
       );

  static const String name = 'CalendarRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final queryParams = data.queryParams;
      final args = data.argsAs<CalendarRouteArgs>(
        orElse:
            () => CalendarRouteArgs(
              initialYear: pathParams.getInt('year'),
              initialMonth: pathParams.getInt('month'),
              initialScrollOffset: queryParams.optDouble('scrollOffset'),
            ),
      );
      return CalendarPage(
        key: args.key,
        initialYear: args.initialYear,
        initialMonth: args.initialMonth,
        initialScrollOffset: args.initialScrollOffset,
      );
    },
  );
}

class CalendarRouteArgs {
  const CalendarRouteArgs({
    this.key,
    required this.initialYear,
    required this.initialMonth,
    this.initialScrollOffset,
  });

  final Key? key;

  final int initialYear;

  final int initialMonth;

  final double? initialScrollOffset;

  @override
  String toString() {
    return 'CalendarRouteArgs{key: $key, initialYear: $initialYear, initialMonth: $initialMonth, initialScrollOffset: $initialScrollOffset}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CalendarRouteArgs) return false;
    return key == other.key &&
        initialYear == other.initialYear &&
        initialMonth == other.initialMonth &&
        initialScrollOffset == other.initialScrollOffset;
  }

  @override
  int get hashCode =>
      key.hashCode ^
      initialYear.hashCode ^
      initialMonth.hashCode ^
      initialScrollOffset.hashCode;
}

/// generated route for
/// [CalendarRedirectPage]
class CalendarRedirectRoute extends PageRouteInfo<void> {
  const CalendarRedirectRoute({List<PageRouteInfo>? children})
    : super(CalendarRedirectRoute.name, initialChildren: children);

  static const String name = 'CalendarRedirectRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CalendarRedirectPage();
    },
  );
}

/// generated route for
/// [ClientPaymentsPage]
class ClientPaymentsRoute extends PageRouteInfo<void> {
  const ClientPaymentsRoute({List<PageRouteInfo>? children})
    : super(ClientPaymentsRoute.name, initialChildren: children);

  static const String name = 'ClientPaymentsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ClientPaymentsPage();
    },
  );
}

/// generated route for
/// [ClientsPage]
class ClientsRoute extends PageRouteInfo<void> {
  const ClientsRoute({List<PageRouteInfo>? children})
    : super(ClientsRoute.name, initialChildren: children);

  static const String name = 'ClientsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ClientsPage();
    },
  );
}

/// generated route for
/// [ConfirmRegistrationPage]
class ConfirmRegistrationRoute
    extends PageRouteInfo<ConfirmRegistrationRouteArgs> {
  ConfirmRegistrationRoute({
    Key? key,
    required String email,
    List<PageRouteInfo>? children,
  }) : super(
         ConfirmRegistrationRoute.name,
         args: ConfirmRegistrationRouteArgs(key: key, email: email),
         rawPathParams: {'email': email},
         initialChildren: children,
       );

  static const String name = 'ConfirmRegistrationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<ConfirmRegistrationRouteArgs>(
        orElse:
            () => ConfirmRegistrationRouteArgs(
              email: pathParams.getString('email'),
            ),
      );
      return ConfirmRegistrationPage(key: args.key, email: args.email);
    },
  );
}

class ConfirmRegistrationRouteArgs {
  const ConfirmRegistrationRouteArgs({this.key, required this.email});

  final Key? key;

  final String email;

  @override
  String toString() {
    return 'ConfirmRegistrationRouteArgs{key: $key, email: $email}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ConfirmRegistrationRouteArgs) return false;
    return key == other.key && email == other.email;
  }

  @override
  int get hashCode => key.hashCode ^ email.hashCode;
}

/// generated route for
/// [EmployeesPage]
class EmployeesRoute extends PageRouteInfo<void> {
  const EmployeesRoute({List<PageRouteInfo>? children})
    : super(EmployeesRoute.name, initialChildren: children);

  static const String name = 'EmployeesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const EmployeesPage();
    },
  );
}

/// generated route for
/// [EmptyRouterPage]
class EmptyRouterRoute extends PageRouteInfo<void> {
  const EmptyRouterRoute({List<PageRouteInfo>? children})
    : super(EmptyRouterRoute.name, initialChildren: children);

  static const String name = 'EmptyRouterRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const EmptyRouterPage();
    },
  );
}

/// generated route for
/// [HomePage]
class HomeRoute extends PageRouteInfo<void> {
  const HomeRoute({List<PageRouteInfo>? children})
    : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HomePage();
    },
  );
}

/// generated route for
/// [LoginPage]
class LoginRoute extends PageRouteInfo<void> {
  const LoginRoute({List<PageRouteInfo>? children})
    : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const LoginPage();
    },
  );
}

/// generated route for
/// [PriceCalculatorPage]
class PriceCalculatorRoute extends PageRouteInfo<void> {
  const PriceCalculatorRoute({List<PageRouteInfo>? children})
    : super(PriceCalculatorRoute.name, initialChildren: children);

  static const String name = 'PriceCalculatorRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const PriceCalculatorPage();
    },
  );
}

/// generated route for
/// [ProfilePage]
class ProfileRoute extends PageRouteInfo<void> {
  const ProfileRoute({List<PageRouteInfo>? children})
    : super(ProfileRoute.name, initialChildren: children);

  static const String name = 'ProfileRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ProfilePage();
    },
  );
}

/// generated route for
/// [TasksCreatePage]
class TasksCreateRoute extends PageRouteInfo<void> {
  const TasksCreateRoute({List<PageRouteInfo>? children})
    : super(TasksCreateRoute.name, initialChildren: children);

  static const String name = 'TasksCreateRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const TasksCreatePage();
    },
  );
}

/// generated route for
/// [TasksEditPage]
class TasksEditRoute extends PageRouteInfo<TasksEditRouteArgs> {
  TasksEditRoute({
    Key? key,
    required String taskId,
    List<PageRouteInfo>? children,
  }) : super(
         TasksEditRoute.name,
         args: TasksEditRouteArgs(key: key, taskId: taskId),
         initialChildren: children,
       );

  static const String name = 'TasksEditRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TasksEditRouteArgs>();
      return TasksEditPage(key: args.key, taskId: args.taskId);
    },
  );
}

class TasksEditRouteArgs {
  const TasksEditRouteArgs({this.key, required this.taskId});

  final Key? key;

  final String taskId;

  @override
  String toString() {
    return 'TasksEditRouteArgs{key: $key, taskId: $taskId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TasksEditRouteArgs) return false;
    return key == other.key && taskId == other.taskId;
  }

  @override
  int get hashCode => key.hashCode ^ taskId.hashCode;
}

/// generated route for
/// [TasksPage]
class TasksRoute extends PageRouteInfo<TasksRouteArgs> {
  TasksRoute({
    Key? key,
    TaskRequestParams? initialParams,
    String? initialTaskId,
    bool resetFilters = false,
    List<PageRouteInfo>? children,
  }) : super(
         TasksRoute.name,
         args: TasksRouteArgs(
           key: key,
           initialParams: initialParams,
           initialTaskId: initialTaskId,
           resetFilters: resetFilters,
         ),
         initialChildren: children,
       );

  static const String name = 'TasksRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TasksRouteArgs>(
        orElse: () => const TasksRouteArgs(),
      );
      return TasksPage(
        key: args.key,
        initialParams: args.initialParams,
        initialTaskId: args.initialTaskId,
        resetFilters: args.resetFilters,
      );
    },
  );
}

class TasksRouteArgs {
  const TasksRouteArgs({
    this.key,
    this.initialParams,
    this.initialTaskId,
    this.resetFilters = false,
  });

  final Key? key;

  final TaskRequestParams? initialParams;

  final String? initialTaskId;

  final bool resetFilters;

  @override
  String toString() {
    return 'TasksRouteArgs{key: $key, initialParams: $initialParams, initialTaskId: $initialTaskId, resetFilters: $resetFilters}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TasksRouteArgs) return false;
    return key == other.key &&
        initialParams == other.initialParams &&
        initialTaskId == other.initialTaskId &&
        resetFilters == other.resetFilters;
  }

  @override
  int get hashCode =>
      key.hashCode ^
      initialParams.hashCode ^
      initialTaskId.hashCode ^
      resetFilters.hashCode;
}

/// generated route for
/// [TasksViewPage]
class TasksViewRoute extends PageRouteInfo<TasksViewRouteArgs> {
  TasksViewRoute({
    Key? key,
    required String taskId,
    List<PageRouteInfo>? children,
  }) : super(
         TasksViewRoute.name,
         args: TasksViewRouteArgs(key: key, taskId: taskId),
         rawPathParams: {'taskId': taskId},
         initialChildren: children,
       );

  static const String name = 'TasksViewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<TasksViewRouteArgs>(
        orElse:
            () => TasksViewRouteArgs(taskId: pathParams.getString('taskId')),
      );
      return TasksViewPage(key: args.key, taskId: args.taskId);
    },
  );
}

class TasksViewRouteArgs {
  const TasksViewRouteArgs({this.key, required this.taskId});

  final Key? key;

  final String taskId;

  @override
  String toString() {
    return 'TasksViewRouteArgs{key: $key, taskId: $taskId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TasksViewRouteArgs) return false;
    return key == other.key && taskId == other.taskId;
  }

  @override
  int get hashCode => key.hashCode ^ taskId.hashCode;
}

/// generated route for
/// [UnauthorizedPage]
class UnauthorizedRoute extends PageRouteInfo<void> {
  const UnauthorizedRoute({List<PageRouteInfo>? children})
    : super(UnauthorizedRoute.name, initialChildren: children);

  static const String name = 'UnauthorizedRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const UnauthorizedPage();
    },
  );
}

/// generated route for
/// [WelcomePage]
class WelcomeRoute extends PageRouteInfo<void> {
  const WelcomeRoute({List<PageRouteInfo>? children})
    : super(WelcomeRoute.name, initialChildren: children);

  static const String name = 'WelcomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const WelcomePage();
    },
  );
}

/// generated route for
/// [ZeroReportingCalculatorPage]
class ZeroReportingCalculatorRoute extends PageRouteInfo<void> {
  const ZeroReportingCalculatorRoute({List<PageRouteInfo>? children})
    : super(ZeroReportingCalculatorRoute.name, initialChildren: children);

  static const String name = 'ZeroReportingCalculatorRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ZeroReportingCalculatorPage();
    },
  );
}
