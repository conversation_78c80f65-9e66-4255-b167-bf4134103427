import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/main_info_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_sections.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/tax_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/payment_schedules_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/extended_info_section.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_create/sections/unified_attachments_section.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';

class ClientCreateCard extends StatefulWidget {
  final VoidCallback onCancel;
  final void Function(ClientEntity client) onCreated;
  const ClientCreateCard({
    super.key,
    required this.onCancel,
    required this.onCreated,
  });

  @override
  State<ClientCreateCard> createState() => ClientCreateCardState();
}

class ClientCreateCardState extends State<ClientCreateCard> {
  final _formKey = GlobalKey<FormState>();

  // Основные контроллеры
  final _nameCtrl = TextEditingController();
  final _shortNameCtrl = TextEditingController();
  final _innCtrl = TextEditingController();
  final _commentCtrl = TextEditingController();
  final _directorNameCtrl = TextEditingController();
  final _directorStartDateCtrl = TextEditingController();
  final _directorStartDateKey = GlobalKey();
  final _directorStartDateFN = FocusNode();

  // Флаг для отслеживания изменений
  bool _hasUnsavedChanges = false;
  final _creationDateCtrl = TextEditingController();
  final _creationDateKey = GlobalKey();
  final _creationDateFN = FocusNode();

  // Контроллеры для графиков платежей
  final _salaryDateCtrl = TextEditingController();
  final _salaryDateFN = FocusNode();
  final _advanceDateCtrl = TextEditingController();
  final _advanceDateFN = FocusNode();
  final _ndflDate1Ctrl = TextEditingController();
  final _ndflDate1FN = FocusNode();
  final _ndflDate2Ctrl = TextEditingController();
  final _ndflDate2FN = FocusNode();

  // Контроллеры для дополнительной секции
  final _fixedContributionsDateCtrl = TextEditingController();
  final _fixedContributionsDateKey = GlobalKey();
  final _fixedContributionsDateFN = FocusNode();
  final _contributionsIP1PercentDateCtrl = TextEditingController();
  final _contributionsIP1PercentDateKey = GlobalKey();
  final _contributionsIP1PercentDateFN = FocusNode();

  // Контроллер для комментариев к вложениям
  final _attachmentCommentsCtrl = TextEditingController();

  // Контроллеры для цифровой подписи
  final _digitalSignatureExpiryDateCtrl = TextEditingController();
  final _digitalSignatureExpiryDateKey = GlobalKey();
  final _digitalSignatureExpiryDateFN = FocusNode();

  // Основные поля и списки (изначально пустые)
  DateTime? _creationDate;
  String? _ownershipForm;
  String? _directorType;
  String? _directorName;
  DateTime? _directorStartDate;
  DateTime? _digitalSignatureExpiryDate;
  final List<String> _taxSystems = [];
  final List<String> _profitTaxTypes = [];
  final List<String> _vatTypes = [];
  final List<String> _propertyTypes = [];
  String? _reportingType;
  String? _reportingOperator;
  final List<String> _exciseGoods = [];
  final List<String> _excisePaymentTerms = [];
  final List<String> _edoOperators = [];
  String? _enpType;
  final List<String> _activityTypes = [];
  String? _ndflType;
  final List<String> _additionalTags = [];
  final List<String> _fixedContributionsIP = [];
  List<int> _fixedContributionsPaymentDate = [];
  final List<String> _contributionsIP1Percent = [];
  List<int> _contributionsIP1PercentPaymentDate = [];

  // Новые поля для расширенной информации
  final List<FnsInfo> _fnsInfo = [];
  final List<KppInfo> _kppInfo = [];
  SfrInfo? _sfrInfo;
  String? _okpo;
  bool? _cashOrBank;
  bool _cashPayment = false;
  bool _bankPayment = false;
  bool _hasEmployees = false;
  bool _isSoleFounderDirector = false;
  String? _ogrn;
  String? _legalAddress;
  String? _actualAddress;
  bool _actualAddressSameAsLegal = false;

  PaymentSchedule? _salaryPayment;
  bool _salaryPaymentEnabled = false;
  PaymentSchedule? _advancePayment;
  PaymentSchedule? _ndflPayment;

  final List<ContactEntity> _contacts = [];
  final List<PatentEntity> _patents = [];

  // Файловые вложения (облачные)
  final List<FileAttachmentItem> _cloudFiles = [];

  bool get _hasActiveFileOperations => _cloudFiles.any(
    (f) =>
        f.status == FileAttachmentStatus.uploading ||
        f.status == FileAttachmentStatus.deleting ||
        f.status == FileAttachmentStatus.pending,
  );

  @override
  void initState() {
    super.initState();
    // Добавляем слушатели для отслеживания изменений
    _nameCtrl.addListener(_markAsChanged);
    _shortNameCtrl.addListener(_markAsChanged);
    _innCtrl.addListener(_markAsChanged);
    _commentCtrl.addListener(_markAsChanged);
    _creationDateCtrl.addListener(_markAsChanged);
    _directorNameCtrl.addListener(_markAsChanged);
    _directorStartDateCtrl.addListener(_markAsChanged);
    _salaryDateCtrl.addListener(_markAsChanged);
    _advanceDateCtrl.addListener(_markAsChanged);
    _ndflDate1Ctrl.addListener(_markAsChanged);
    _ndflDate2Ctrl.addListener(_markAsChanged);
    _fixedContributionsDateCtrl.addListener(_markAsChanged);
    _contributionsIP1PercentDateCtrl.addListener(_markAsChanged);
    _digitalSignatureExpiryDateCtrl.addListener(_markAsChanged);
  }

  void _markAsChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
  }

  bool get hasUnsavedChanges => _hasUnsavedChanges;

  @override
  void dispose() {
    _nameCtrl.dispose();
    _shortNameCtrl.dispose();
    _innCtrl.dispose();
    _commentCtrl.dispose();
    _creationDateCtrl.dispose();
    _creationDateFN.dispose();
    _directorNameCtrl.dispose();
    _directorStartDateCtrl.dispose();
    _directorStartDateFN.dispose();
    _salaryDateCtrl.dispose();
    _salaryDateFN.dispose();
    _advanceDateCtrl.dispose();
    _advanceDateFN.dispose();
    _ndflDate1Ctrl.dispose();
    _ndflDate1FN.dispose();
    _ndflDate2Ctrl.dispose();
    _ndflDate2FN.dispose();
    _fixedContributionsDateCtrl.dispose();
    _fixedContributionsDateFN.dispose();
    _contributionsIP1PercentDateCtrl.dispose();
    _contributionsIP1PercentDateFN.dispose();
    _digitalSignatureExpiryDateCtrl.dispose();
    _digitalSignatureExpiryDateFN.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      color: ClientConstants.cardColor(context),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Новый клиент',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              MainInfoSection(
                isEditing: true,
                nameCtrl: _nameCtrl,
                shortNameCtrl: _shortNameCtrl,
                innCtrl: _innCtrl,
                commentCtrl: _commentCtrl,
                creationDateCtrl: _creationDateCtrl,
                creationDateKey: _creationDateKey,
                creationDateFN: _creationDateFN,
                creationDate: _creationDate,
                ownershipForm: _ownershipForm,
                directorType: _directorType,
                directorName: _directorName,
                kppInfo: _kppInfo, // Добавляем информацию о КПП
                onDateChanged: (date) {
                  setState(() => _creationDate = date);
                  if (date != null) {
                    _creationDateCtrl.text = DateFormat(
                      'dd.MM.yyyy',
                    ).format(date);
                  }
                },
                onOwnershipFormChanged:
                    (value) => setState(() => _ownershipForm = value),
                onDirectorTypeChanged:
                    (value) => setState(() => _directorType = value),
                directorNameCtrl: _directorNameCtrl,
                directorStartDateCtrl: _directorStartDateCtrl,
                directorStartDateKey: _directorStartDateKey,
                directorStartDateFN: _directorStartDateFN,
                directorStartDate: _directorStartDate,
                onDirectorNameChanged:
                    (value) => setState(() => _directorName = value),
                onDirectorStartDateChanged: (date) {
                  setState(() => _directorStartDate = date);
                  if (date != null) {
                    _directorStartDateCtrl.text = DateFormat(
                      'dd.MM.yyyy',
                    ).format(date);
                  }
                },
                digitalSignatureExpiryDate: _digitalSignatureExpiryDate,
                digitalSignatureExpiryDateCtrl: _digitalSignatureExpiryDateCtrl,
                digitalSignatureExpiryDateKey: _digitalSignatureExpiryDateKey,
                digitalSignatureExpiryDateFN: _digitalSignatureExpiryDateFN,
                onDigitalSignatureExpiryDateChanged: (date) {
                  setState(() => _digitalSignatureExpiryDate = date);
                  if (date != null) {
                    _digitalSignatureExpiryDateCtrl.text = DateFormat(
                      'dd.MM.yyyy',
                    ).format(date);
                  }
                },
                copyToClipboard: (text, field) {}, // не используется в создании
              ),
              const SizedBox(height: ClientConstants.sectionSpacing),

              ContactsSection(
                isEditing: true,
                contacts: _contacts,
                onRemoveContact:
                    (index) => setState(() => _contacts.removeAt(index)),
                onUpdateContact:
                    (index, contact) =>
                        setState(() => _contacts[index] = contact),
                onAddContact:
                    () => setState(
                      () => _contacts.add(
                        const ContactEntity(
                          fullName: '',
                          phone: '',
                          emails: [],
                          communicationMethods: {},
                        ),
                      ),
                    ),
                copyToClipboard: (text, field) {}, // не используется в создании
              ),
              const SizedBox(height: ClientConstants.sectionSpacing),

              TaxSection(
                isEditing: true,
                taxSystems: _taxSystems,
                profitTaxTypes: _profitTaxTypes,
                vatTypes: _vatTypes,
                propertyTypes: _propertyTypes,
                reportingType: _reportingType,
                reportingOperator: _reportingOperator,
                exciseGoods: _exciseGoods,
                excisePaymentTerms: _excisePaymentTerms,
                edoOperators: _edoOperators,
                enpType: _enpType,
                ndflType: _ndflType,
                onTaxSystemAdd:
                    (value) => setState(() => _taxSystems.add(value)),
                onTaxSystemRemove:
                    (value) => setState(() => _taxSystems.remove(value)),
                onProfitTaxTypeAdd:
                    (value) => setState(() => _profitTaxTypes.add(value)),
                onProfitTaxTypeRemove:
                    (value) => setState(() => _profitTaxTypes.remove(value)),
                onVatTypeAdd: (value) => setState(() => _vatTypes.add(value)),
                onVatTypeRemove:
                    (value) => setState(() => _vatTypes.remove(value)),
                onPropertyTypeAdd:
                    (value) => setState(() => _propertyTypes.add(value)),
                onPropertyTypeRemove:
                    (value) => setState(() => _propertyTypes.remove(value)),
                onReportingTypeChanged:
                    (value) => setState(() => _reportingType = value),
                onReportingOperatorChanged:
                    (value) => setState(() => _reportingOperator = value),
                onExciseGoodAdd:
                    (value) => setState(() => _exciseGoods.add(value)),
                onExciseGoodRemove:
                    (value) => setState(() => _exciseGoods.remove(value)),
                onExcisePaymentTermAdd:
                    (value) => setState(() => _excisePaymentTerms.add(value)),
                onExcisePaymentTermRemove:
                    (value) =>
                        setState(() => _excisePaymentTerms.remove(value)),
                onEdoOperatorAdd:
                    (value) => setState(() => _edoOperators.add(value)),
                onEdoOperatorRemove:
                    (value) => setState(() => _edoOperators.remove(value)),
                onEnpTypeChanged: (value) => setState(() => _enpType = value),
                onNdflTypeChanged: (value) => setState(() => _ndflType = value),
                copyToClipboard: (text, field) {}, // не используется в создании
              ),
              const SizedBox(height: ClientConstants.sectionSpacing),

              PaymentSchedulesSection(
                isEditing: true,
                salaryPayment: _salaryPayment,
                salaryPaymentEnabled: _salaryPaymentEnabled,
                onSalaryPaymentEnabledChanged:
                    (enabled) =>
                        setState(() => _salaryPaymentEnabled = enabled),
                cashOrBank: _cashOrBank,
                onCashOrBankChanged:
                    (value) => setState(() => _cashOrBank = value),
                cashPayment: _cashPayment,
                onCashPaymentChanged:
                    (value) => setState(() => _cashPayment = value),
                bankPayment: _bankPayment,
                onBankPaymentChanged:
                    (value) => setState(() => _bankPayment = value),
                hasEmployees: _hasEmployees,
                onHasEmployeesChanged:
                    (value) => setState(() => _hasEmployees = value),
                isSoleFounderDirector: _isSoleFounderDirector,
                onIsSoleFounderDirectorChanged:
                    (value) => setState(() => _isSoleFounderDirector = value),
                advancePayment: _advancePayment,
                ndflPayment: _ndflPayment,
                onSalaryChanged: (schedule) {
                  setState(() => _salaryPayment = schedule);
                  _salaryDateCtrl.text = schedule?.paymentDate.toString() ?? '';
                },
                onAdvanceChanged: (schedule) {
                  setState(() => _advancePayment = schedule);
                  _advanceDateCtrl.text =
                      schedule?.paymentDate.toString() ?? '';
                },
                onNdflChanged: (schedule) {
                  setState(() => _ndflPayment = schedule);
                  _ndflDate1Ctrl.text = schedule?.paymentDate.toString() ?? '';
                  _ndflDate2Ctrl.text = schedule?.transferDate.toString() ?? '';
                },
                copyToClipboard: (text, field) {}, // не используется в создании
                salaryDateCtrl: _salaryDateCtrl,
                salaryDateFN: _salaryDateFN,
                advanceDateCtrl: _advanceDateCtrl,
                advanceDateFN: _advanceDateFN,
                ndflDate1Ctrl: _ndflDate1Ctrl,
                ndflDate1FN: _ndflDate1FN,
                ndflDate2Ctrl: _ndflDate2Ctrl,
                ndflDate2FN: _ndflDate2FN,
              ),
              const SizedBox(height: ClientConstants.sectionSpacing),

              AdditionalSection(
                isEditing: true,
                additionalTags: _additionalTags,
                activityTypes: _activityTypes,
                fixedContributionsIP: _fixedContributionsIP,
                fixedContributionsPaymentDate: _fixedContributionsPaymentDate,
                contributionsIP1Percent: _contributionsIP1Percent,
                contributionsIP1PercentPaymentDate:
                    _contributionsIP1PercentPaymentDate,
                fixedContributionsDateCtrl: _fixedContributionsDateCtrl,
                fixedContributionsDateKey: _fixedContributionsDateKey,
                fixedContributionsDateFN: _fixedContributionsDateFN,
                contributionsIP1PercentDateCtrl:
                    _contributionsIP1PercentDateCtrl,
                contributionsIP1PercentDateKey: _contributionsIP1PercentDateKey,
                contributionsIP1PercentDateFN: _contributionsIP1PercentDateFN,
                onAddTag: (tag) => setState(() => _additionalTags.add(tag)),
                onRemoveTag:
                    (tag) => setState(() => _additionalTags.remove(tag)),
                onActivityTypeAdd:
                    (value) => setState(() => _activityTypes.add(value)),
                onActivityTypeRemove:
                    (value) => setState(() => _activityTypes.remove(value)),
                onFixedContributionAdd:
                    (value) => setState(() => _fixedContributionsIP.add(value)),
                onFixedContributionRemove:
                    (value) =>
                        setState(() => _fixedContributionsIP.remove(value)),
                onFixedContributionDateChanged: (dates) {
                  setState(() => _fixedContributionsPaymentDate = dates);
                  _fixedContributionsDateCtrl
                      .text = ClientConstants.formatPaymentDates(dates);
                },
                onContributionsIP1PercentAdd:
                    (value) =>
                        setState(() => _contributionsIP1Percent.add(value)),
                onContributionsIP1PercentRemove:
                    (value) =>
                        setState(() => _contributionsIP1Percent.remove(value)),
                onContributionsIP1PercentDateChanged: (dates) {
                  setState(() => _contributionsIP1PercentPaymentDate = dates);
                  _contributionsIP1PercentDateCtrl
                      .text = ClientConstants.formatPaymentDates(dates);
                },
                copyToClipboard: (text, field) {}, // не используется в создании
              ),
              const SizedBox(height: ClientConstants.sectionSpacing),

              ExtendedInfoSection(
                isEditing: true,
                ownershipForm: _ownershipForm,
                fnsInfo: _fnsInfo,
                kppInfo: _kppInfo,
                sfrInfo: _sfrInfo,
                okpo: _okpo,
                ogrn: _ogrn,
                legalAddress: _legalAddress,
                actualAddress: _actualAddress,
                actualAddressSameAsLegal: _actualAddressSameAsLegal,
                onFnsInfoChanged:
                    (fnsInfo) => setState(() {
                      _fnsInfo.clear();
                      _fnsInfo.addAll(fnsInfo);
                    }),
                onKppInfoChanged:
                    (kppInfo) => setState(() {
                      _kppInfo.clear();
                      _kppInfo.addAll(kppInfo);
                    }),
                onSfrInfoChanged:
                    (sfrInfo) => setState(() => _sfrInfo = sfrInfo),
                onOkpoChanged: (okpo) => setState(() => _okpo = okpo),
                onOgrnChanged: (ogrn) => setState(() => _ogrn = ogrn),
                onLegalAddressChanged:
                    (legalAddress) =>
                        setState(() => _legalAddress = legalAddress),
                onActualAddressChanged:
                    (actualAddress) =>
                        setState(() => _actualAddress = actualAddress),
                onActualAddressSameAsLegalChanged:
                    (same) => setState(() => _actualAddressSameAsLegal = same),
                copyToClipboard: (text, field) {}, // не используется в создании
              ),
              const SizedBox(height: ClientConstants.sectionSpacing),

              // Файловые вложения (облако)
              UnifiedAttachmentsSection(
                cloudFiles: _cloudFiles,
                onAddCloudFile: _addCloudFile,
                onRemoveCloudFile: _removeCloudFile,
                onUpdateCloudFile: _updateCloudFile,
              ),

              const SizedBox(height: ClientConstants.sectionSpacing),

              // Комментарии к вложениям
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).dividerColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.comment_outlined,
                          size: 20,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Комментарии к вложениям',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: _attachmentCommentsCtrl,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        hintText:
                            'Добавьте комментарии к файловым вложениям...',
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                      onChanged:
                          (value) => setState(() => _hasUnsavedChanges = true),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: ClientConstants.sectionSpacing),

              PatentsSection(
                isEditing: true,
                patents: _patents,
                onUpdatePatent:
                    (index, patent) => setState(() => _patents[index] = patent),
                onRemovePatent:
                    (index) => setState(() => _patents.removeAt(index)),
                onAddPatent: () {
                  setState(() {
                    final startDate = DateTime.now();
                    _patents.add(
                      PatentEntity(
                        startDate: startDate,
                        endDate: DateTime(
                          startDate.year + 1,
                          startDate.month,
                          startDate.day,
                        ),
                        issueDate: startDate,
                        patentAmount: 0,
                        patentNumber:
                            'Новый патент ${DateTime.now().millisecondsSinceEpoch}',
                        patentTitle: 'Новый патент',
                      ),
                    );
                  });
                },
              ),

              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: widget.onCancel,
                    child: const Text('Отмена'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _hasActiveFileOperations ? null : _saveClient,
                    child: const Text('Создать'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveClient() async {
    if (!_formKey.currentState!.validate()) return;

    final activeFirm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (activeFirm == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Сначала выберите фирму')));
      return;
    }

    if (_enpType == null || _enpType!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Пожалуйста, выберите значение ЕНП')),
      );
      return;
    }

    final cubit = context.read<ClientsCubit>();
    ClientEntity? savedClient;

    final client = _buildClientEntity();

    savedClient = await cubit.createClient(activeFirm.id, client);

    if (savedClient != null) {
      widget.onCreated(savedClient);
      widget.onCancel();
    } else {
      if (mounted) {
        final errorMessage =
            cubit.state.error ?? 'Неизвестная ошибка при создании клиента';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при создании клиента: $errorMessage'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // === Работа с файлами ===
  void _addCloudFile(FileAttachmentItem file) {
    setState(() => _cloudFiles.add(file));
  }

  void _removeCloudFile(FileAttachmentItem file) {
    setState(() => _cloudFiles.remove(file));
  }

  void _updateCloudFile(FileAttachmentItem updated) {
    setState(() {
      final idx = _cloudFiles.indexWhere((f) => f.name == updated.name);
      if (idx != -1) _cloudFiles[idx] = updated;
    });
  }

  ClientEntity _buildClientEntity() {
    return ClientEntity(
      id: '', // Всегда пустая строка - ID присваивается сервером
      name: _nameCtrl.text.trim(),
      inn: _innCtrl.text.trim().isEmpty ? null : _innCtrl.text.trim(),
      shortName:
          _shortNameCtrl.text.trim().isEmpty
              ? null
              : _shortNameCtrl.text.trim(),
      comment:
          _commentCtrl.text.trim().isEmpty ? null : _commentCtrl.text.trim(),
      creationDate: _creationDate,
      ownershipForm: _ownershipForm,
      directorType: _directorType,
      directorName: _directorName,
      directorStartDate: _directorStartDate,
      taxSystems: _taxSystems,
      profitTaxTypes: _profitTaxTypes,
      vatTypes: _vatTypes,
      propertyTypes: _propertyTypes,
      reportingType: _reportingType,
      reportingOperator: _reportingOperator,
      exciseGoods: _exciseGoods,
      excisePaymentTerms: _excisePaymentTerms,
      edoOperators: _edoOperators,
      enpType: _enpType,
      activityTypes: _activityTypes,
      ndflType: _ndflType,
      additionalTags: _additionalTags,
      fixedContributionsIP: _fixedContributionsIP,
      fixedContributionsPaymentDate: _fixedContributionsPaymentDate,
      contributionsIP1Percent: _contributionsIP1Percent,
      contributionsIP1PercentPaymentDate: _contributionsIP1PercentPaymentDate,
      salaryPayment: _salaryPayment,
      salaryPaymentEnabled: _salaryPaymentEnabled,
      advancePayment: _advancePayment,
      ndflPayment: _ndflPayment,
      contacts: _contacts,
      patents: _patents,
      attachments:
          _cloudFiles
              .where(
                (f) =>
                    f.status == FileAttachmentStatus.uploaded &&
                    f.fileKey != null,
              )
              .map((f) => f.toMap())
              .toList(),
      attachmentComments:
          _attachmentCommentsCtrl.text.trim().isEmpty
              ? null
              : _attachmentCommentsCtrl.text.trim(),
      // Новые поля для расширенной информации
      fnsInfo: _fnsInfo,
      kppInfo: _kppInfo,
      sfrInfo: _sfrInfo,
      okpo: _okpo,
      cashOrBank: _cashOrBank,
      cashPayment: _cashPayment,
      bankPayment: _bankPayment,
      hasEmployees: _hasEmployees,
      isSoleFounderDirector: _isSoleFounderDirector,
      ogrn: _ogrn,
      legalAddress: _legalAddress,
      actualAddress: _actualAddress,
      actualAddressSameAsLegal: _actualAddressSameAsLegal,
      digitalSignatureExpiryDate: _digitalSignatureExpiryDate,
    );
  }
}
