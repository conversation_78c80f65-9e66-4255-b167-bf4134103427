
```python
import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Retry
import os
import re

# --- Константы ---
TOKEN_FILE = 'yandex_token.txt'
TARGET_FOLDER = 'balansoved enterprise'
CHUNK_SIZE = 1024 * 1024  # 1 МБ


def save_token(token):
    """Сохраняет токен в файл."""
    with open(TOKEN_FILE, 'w') as f:
        f.write(token)


def load_token():
    """Загружает токен из файла."""
    if os.path.exists(TOKEN_FILE):
        with open(TOKEN_FILE, 'r') as f:
            return f.read().strip()
    return None


def get_disk_info(token):
    """Проверяет токен и возвращает информацию о диске или None."""
    if not token: return None
    try:
        response = requests.get("https://cloud-api.yandex.net/v1/disk/", headers={'Authorization': f'OAuth {token}'})
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException:
        return None


def get_new_token():
    """Инструкция для получения нового токена."""
    print("--- Получение нового токена Яндекс Диска ---")
    print("1. Перейдите по ссылке: https://yandex.ru/dev/disk/poligon/")
    print("2. Нажмите жёлтую кнопку 'Получить OAuth-токен'.")
    print("3. Скопируйте полученный токен и вставьте его ниже.")
    print("-" * 45)
    return input("Введите ваш OAuth-токен: ")


def manage_token():
    """Управляет токеном: загружает, проверяет, обновляет."""
    token = load_token()
    disk_info = get_disk_info(token)
    if disk_info:
        print("Сохраненный токен действителен.")
        return token, disk_info
    print("Сохраненный токен не найден или недействителен.")
    while True:
        new_token = get_new_token()
        disk_info = get_disk_info(new_token)
        if disk_info:
            save_token(new_token)
            print("Токен успешно проверен и сохранен.")
            return new_token, disk_info
        else:
            print("Введенный токен недействителен. Попробуйте снова.")


def ensure_folder_exists(token, folder_path):
    """Проверяет наличие папки и создает ее, если она отсутствует."""
    headers = {'Authorization': f'OAuth {token}'}
    try:
        # Проверяем, существует ли папка
        response = requests.get("https://cloud-api.yandex.net/v1/disk/resources", headers=headers,
                                params={'path': folder_path})
        if response.status_code == 200:
            # print(f"Папка '{folder_path}' уже существует.")
            return True
        # Если папки нет (ошибка 404), создаем ее
        if response.status_code == 404:
            # print(f"Папка '{folder_path}' не найдена. Создаю...")
            create_response = requests.put("https://cloud-api.yandex.net/v1/disk/resources", headers=headers,
                                           params={'path': folder_path})
            create_response.raise_for_status()
            # print("Папка успешно создана.")
            return True
        response.raise_for_status()
    except requests.exceptions.HTTPError as e:
        # print(f"Ошибка при работе с папкой: {e.response.json().get('message', e)}")
        return False
    return False


def sanitize_filename(name: str) -> str:
    """Возвращает безопасное имя файла/папки, удаляя недопустимые символы."""
    # Заменяем запрещённые символы файловой системы на подчёркивания
    sanitized = re.sub(r'[\\/:"*?<>|]+', '_', name)
    # Ограничиваем длину до 255 символов (макс. для большинства ФС)
    return sanitized.strip()[:255]


def upload_and_get_link(token, file_path, target_path_on_disk):
    """Загружает файл и возвращает публичную ссылку."""
    headers = {'Authorization': f'OAuth {token}'}
    base_url = "https://cloud-api.yandex.net/v1/disk/resources/"
    try:
        params = {'path': target_path_on_disk, 'overwrite': 'true'}
        upload_info_resp = _SESSION.get(base_url + "upload", headers=headers, params=params, timeout=30)
        upload_info_resp.raise_for_status()
        upload_url = upload_info_resp.json().get('href')

        # Настраиваем сессию с ретраями на сетевые ошибки
        # sess = requests.Session()
        # retries = Retry(total=5, backoff_factor=2, status_forcelist=[429, 500, 502, 503, 504], allowed_methods=["PUT"])
        # sess.mount('https://', HTTPAdapter(max_retries=retries))

        with open(file_path, 'rb') as f:
            def read_in_chunks(file_object, chunk_size):
                while True:
                    data = file_object.read(chunk_size)
                    if not data:
                        break
                    yield data

            upload_response = _SESSION.put(upload_url, data=read_in_chunks(f, CHUNK_SIZE), timeout=120)
            upload_response.raise_for_status()

        publish_params = {'path': target_path_on_disk}
        publish_response = _SESSION.put(base_url + "publish", headers=headers, params=publish_params, timeout=30)
        publish_response.raise_for_status()

        meta_info_resp = _SESSION.get(base_url, headers=headers, params=params, timeout=30)
        meta_info_resp.raise_for_status()
        return meta_info_resp.json().get('public_url')

    except requests.exceptions.HTTPError as e:
        error = e.response.json()
        # Вместо print создаем и выбрасываем исключение с деталями
        raise IOError(f"Ошибка API Я.Диска: {error.get('message', 'Нет деталей')} ({error.get('error', 'N/A')})") from e
    except requests.exceptions.RequestException as e:
        # Вместо print создаем и выбрасываем исключение
        raise IOError(f"Ошибка сети при работе с Я.Диском: {e}") from e


def upload_and_get_link_from_bytes(token: str, file_bytes: bytes, target_path_on_disk: str) -> str | None:
    """Загружает файл из байтов и возвращает публичную ссылку."""
    headers = {'Authorization': f'OAuth {token}'}
    base_url = "https://cloud-api.yandex.net/v1/disk/resources/"
    try:
        params = {'path': target_path_on_disk, 'overwrite': 'true'}
        upload_info_resp = _SESSION.get(base_url + "upload", headers=headers, params=params, timeout=30)
        upload_info_resp.raise_for_status()
        upload_url = upload_info_resp.json().get('href')

        # sess = requests.Session()
        # retries = Retry(total=5, backoff_factor=2, status_forcelist=[429, 500, 502, 503, 504], allowed_methods=["PUT"])
        # sess.mount('https://', HTTPAdapter(max_retries=retries))

        upload_response = _SESSION.put(upload_url, data=file_bytes, timeout=120)
        upload_response.raise_for_status()

        publish_params = {'path': target_path_on_disk}
        publish_response = _SESSION.put(base_url + "publish", headers=headers, params=publish_params, timeout=30)
        publish_response.raise_for_status()

        meta_info_resp = _SESSION.get(base_url, headers=headers, params=params, timeout=30)
        meta_info_resp.raise_for_status()
        return meta_info_resp.json().get('public_url')

    except requests.exceptions.HTTPError as e:
        error = e.response.json()
        raise IOError(f"Ошибка API Я.Диска: {error.get('message', 'Нет деталей')} ({error.get('error', 'N/A')})") from e
    except requests.exceptions.RequestException as e:
        raise IOError(f"Ошибка сети при работе с Я.Диском: {e}") from e


def display_folder_tree(token, folder_path, limit=100):
    """Выводит содержимое папки в виде дерева."""
    print(f"\n--- Содержимое папки '{folder_path}' (до {limit} файлов) ---")
    headers = {'Authorization': f'OAuth {token}'}
    params = {'path': folder_path, 'limit': limit}
    try:
        response = requests.get("https://cloud-api.yandex.net/v1/disk/resources", headers=headers, params=params)
        response.raise_for_status()
        items = response.json().get('_embedded', {}).get('items', [])
        if not items:
            print("  └── Папка пуста.")
        else:
            for item in items:
                icon = "📁" if item['type'] == 'dir' else "📄"
                print(f"  └── {icon} {item['name']}")
        print("-" * 50)
    except requests.exceptions.RequestException as e:
        print(f"Не удалось получить содержимое папки: {e}")


def get_resource_info(token: str, resource_path: str) -> dict | None:
    """Возвращает JSON-метаинформацию ресурса (файл/папка) на Яндекс.Диске или None, если не найдено."""
    headers = {'Authorization': f'OAuth {token}'}
    try:
        response = requests.get(
            "https://cloud-api.yandex.net/v1/disk/resources",
            headers=headers,
            params={'path': resource_path}
        )
        if response.status_code == 200:
            return response.json()
        if response.status_code == 404:
            return None
        response.raise_for_status()
    except requests.exceptions.RequestException:
        return None

# -- Предыдущая функция оставлена для обратной совместимости --

def resource_exists(token: str, resource_path: str) -> bool:
    """Проверяет, существует ли файл или папка на Яндекс.Диске без их создания."""
    return get_resource_info(token, resource_path) is not None


# Тестовый CLI-блок удалён как неиспользуемый в продакшене

def ensure_path_recursively(token: str, full_path: str) -> bool:
    """
    Создает все папки в указанном пути по очереди, если они не существуют.
    Например, для 'a/b/c' сначала создаст 'a', потом 'a/b', потом 'a/b/c'.
    """
    parts = full_path.split('/')
    path_to_check = ""
    for part in parts:
        if not part: continue
        path_to_check = f"{path_to_check}/{part}" if path_to_check else part

        # Используем уже существующую функцию для создания одной папки
        if not ensure_folder_exists(token, path_to_check):
            # Если даже одна папка не создалась, прерываем и возвращаем False
            # print(f"Не удалось создать или проверить папку: {path_to_check}")
            return False

    return True

def _get_retrying_session() -> requests.Session:
    """Создаёт requests.Session с ретраями для любых методов."""
    sess = requests.Session()
    retry_cfg = Retry(
        total=5,
        connect=5,
        read=5,
        backoff_factor=2,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["GET", "PUT"],
    )
    sess.mount('https://', HTTPAdapter(max_retries=retry_cfg))
    return sess

_SESSION = _get_retrying_session()
```

