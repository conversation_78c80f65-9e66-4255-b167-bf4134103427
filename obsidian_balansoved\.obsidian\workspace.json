{"main": {"id": "d3536f1e300273df", "type": "split", "children": [{"id": "c56f7033c6f313d9", "type": "tabs", "children": [{"id": "41da6023e14a570e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "𝒇 Функции/✳️💾 sync-tasks-to-disk - CloudFunction функция/Информация, Конвейер работы, Зависимости, Окружение - ✳️💾 sync-tasks-to-disk - CloudFunction функция.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Информация, Конвейер работы, Зависимости, Окружение - ✳️💾 sync-tasks-to-disk - CloudFunction функция"}}]}], "direction": "vertical"}, "left": {"id": "6d8c5fb9e49f0b67", "type": "split", "children": [{"id": "c198cdd655ff79f5", "type": "tabs", "children": [{"id": "48b2ad103adbf23b", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Файловый менеджер"}}, {"id": "71fc7d877d2d03c7", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Поиск"}}, {"id": "5c463bff54b2d9e2", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Закладки"}}]}], "direction": "horizontal", "width": 348.5}, "right": {"id": "d430a80be19e1aec", "type": "split", "children": [{"id": "f7f073c62502c985", "type": "tabs", "children": [{"id": "820c830b01c6ec5b", "type": "leaf", "state": {"type": "backlink", "state": {"file": "𝒇 Функции/✳️📝 edit-task - CloudFunction функция.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Обратные ссылки для ✳️📝 edit-task - CloudFunction функция"}}, {"id": "503783fd2188cffd", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "𝒇 Функции/✳️📝 edit-task - CloudFunction функция.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Исходящие ссылки из ✳️📝 edit-task - CloudFunction функция"}}, {"id": "0861aa2d16e98034", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Теги"}}, {"id": "70d3152b4dc539c2", "type": "leaf", "state": {"type": "outline", "state": {"file": "𝒇 Функции/✳️📝 edit-task - CloudFunction функция.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Структура ✳️📝 edit-task - CloudFunction функция"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Меню быстрого перехода": false, "graph:Граф": false, "canvas:Создать новый холст": false, "daily-notes:Сегодняшняя заметка": false, "templates:Вставить шаблон": false, "command-palette:Открыть палитру команд": false}}, "active": "41da6023e14a570e", "lastOpenFiles": ["𝒇 Функции/✳️📎 create-employee - CloudFunction функция/Информация, Конвейер работы, Зависимости, Окружение - ✳️📎 create-employee - CloudFunction функция.md", "𝒇 Функции/✳️💾 sync-tasks-to-disk - CloudFunction функция/Информация, Конвейер работы, Зависимости, Окружение - ✳️💾 sync-tasks-to-disk - CloudFunction функция.md", "𝒇 Функции/✳️💾 sync-tasks-to-disk - CloudFunction функция/Python код - ✳️💾 sync-tasks-to-disk - CloudFunction функция/custom_errors.py - ✳️💾 sync-tasks-to-disk - CloudFunction функция.md", "𝒇 Функции/✳️💾 sync-tasks-to-disk - CloudFunction функция/Python код - ✳️💾 sync-tasks-to-disk - CloudFunction функция/data_fetchers.py - ✳️💾 sync-tasks-to-disk - CloudFunction функция.md", "𝒇 Функции/✳️💾 sync-tasks-to-disk - CloudFunction функция/Python код - ✳️💾 sync-tasks-to-disk - CloudFunction функция/index.py - ✳️💾 sync-tasks-to-disk - CloudFunction функция.md", "𝒇 Функции/✳️💾 sync-tasks-to-disk - CloudFunction функция/Python код - ✳️💾 sync-tasks-to-disk - CloudFunction функция/runtime_utils.py - ✳️💾 sync-tasks-to-disk - CloudFunction функция.md", "𝒇 Функции/✳️💾 sync-tasks-to-disk - CloudFunction функция/Python код - ✳️💾 sync-tasks-to-disk - CloudFunction функция/task_processor.py - ✳️💾 sync-tasks-to-disk - CloudFunction функция.md", "𝒇 Функции/✳️📝 edit-task - CloudFunction функция/Python код - ✳️📝 edit-task - CloudFunction функция/upsert.py - ✳️📝 edit-task - CloudFunction функция.md", "𝒇 Функции/✳️💳 client-payments-manager - CloudFunction функция/Python код - ✳️💳 client-payments-manager - CloudFunction функция/get.py  - ✳️💳 client-payments-manager - CloudFunction функция.md", "𝒇 Функции/✳️💳 client-payments-manager - CloudFunction функция/Python код - ✳️💳 client-payments-manager - CloudFunction функция/custom_errors.py - ✳️💳 client-payments-manager - CloudFunction функция.md", "𝒇 Функции/✳️💳 client-payments-manager - CloudFunction функция/Python код - ✳️💳 client-payments-manager - CloudFunction функция/delete.py - ✳️💳 client-payments-manager - CloudFunction функция.md", "𝒇 Функции/✳️💳 client-payments-manager - CloudFunction функция/Python код - ✳️💳 client-payments-manager - CloudFunction функция/index.py - ✳️💳 client-payments-manager - CloudFunction функция.md", "𝒇 Функции/✳️💳 client-payments-manager - CloudFunction функция/Python код - ✳️💳 client-payments-manager - CloudFunction функция/upsert.py  - ✳️💳 client-payments-manager - CloudFunction функция.md", "🗄️ Базы данных/💾 clients-database - База данных YandexDatabase.md", "𝒇 Функции/✳️👤 edit-client - CloudFunction функция/Python код - ✳️👤 edit-client - CloudFunction функция/delete.py - ✳️👤 edit-client - CloudFunction функция.md", "𝒇 Функции/✳️👤 edit-client - CloudFunction функция/Python код - ✳️👤 edit-client - CloudFunction функция/upsert.py - ✳️👤 edit-client - CloudFunction функция.md", "𝒇 Функции/✳️👤 edit-client - CloudFunction функция/Python код - ✳️👤 edit-client - CloudFunction функция/get.py - ✳️👤 edit-client - CloudFunction функция.md", "𝒇 Функции/✳️👤 edit-client - CloudFunction функция/Python код - ✳️👤 edit-client - CloudFunction функция/custom_errors.py - ✳️👤 edit-client - CloudFunction функция.md", "𝒇 Функции/✳️👤 edit-client - CloudFunction функция/Python код - ✳️👤 edit-client - CloudFunction функция/index.py - ✳️👤 edit-client - CloudFunction функция.md", "𝒇 Функции/✳️🗑️ delete-firm - CloudFunction функция/Python код - ✳️🗑️ delete-firm - CloudFunction функция/custom_errors.py - ✳️🗑️ delete-firm - CloudFunction функция.md", "𝒇 Функции/✳️🗑️ delete-firm - CloudFunction функция.md", "𝒇 Функции/✳️🗑️ delete-firm - CloudFunction функция/Python код - ✳️🗑️ delete-firm - CloudFunction функция/invoke_utils.py - ✳️🗑️ delete-firm - CloudFunction функция.md", "𝒇 Функции/✳️🗑️ delete-firm - CloudFunction функция/Python код - ✳️🗑️ delete-firm - CloudFunction функция/deletion_logic.py - ✳️🗑️ delete-firm - CloudFunction функция.md", "𝒇 Функции/✳️🗑️ delete-firm - CloudFunction функция/Python код - ✳️🗑️ delete-firm - CloudFunction функция/precondition_checks.py - ✳️🗑️ delete-firm - CloudFunction функция.md", "𝒇 Функции/✳️🗑️ delete-firm - CloudFunction функция/Python код - ✳️🗑️ delete-firm - CloudFunction функция/index.py - ✳️🗑️ delete-firm - CloudFunction функция.md", "𝒇 Функции/✳️🗑️ delete-firm - CloudFunction функция/Информация, Конвейер работы, Зависимости, Окружение - ✳️🗑️ delete-firm - CloudFunction функция.md", "𝒇 Функции/Базы данных", "𝒇 Функции/✳️📎 delete-employee - CloudFunction функция/Python код -/desktop.ini", "𝒇 Функции/Переменные окружения", "utils", "𝒇 Функции/✳️🚪 auth-gate - CloudFunction функция/Python код -", "𝒇 Функции/✳️🗑️ delete-firm - CloudFunction функция/Python код - ✳️🗑️ delete-firm - CloudFunction функция", "𝒇 Функции/✳️🔗 edit-integrations - CloudFunction функция/Python код - ✳️🔗 edit-integrations - CloudFunction функция", "𝒇 Функции/✳️🔔 endpoints-manager - CloudFunction функция/Python код - ✳️🔔 endpoints-manager - CloudFunction функция", "𝒇 Функции/✳️📥 notices-manager - CloudFunction функция/Python код - ✳️📥 notices-manager - CloudFunction функция", "𝒇 Функции/✳️📝 edit-task - CloudFunction функция/Python код - ✳️📝 edit-task - CloudFunction функция", "𝒇 Функции/⚙️ utils/🐍 test/Bitmap image.bmp"]}