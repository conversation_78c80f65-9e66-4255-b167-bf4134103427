import '../../domain/entities/notification_entity.dart';

class NotificationModel extends NotificationEntity {
  const NotificationModel({
    required super.id,
    super.title,
    super.body,
    super.icon,
    required super.createdAt,
    required super.isDelivered,
    required super.isArchived,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['notice_id'] as String,
      title: json['title'] as String?,
      body: json['body'] as String?,
      icon: json['icon'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['created_at'] as int),
      isDelivered: json['is_delivered'] as bool? ?? false,
      isArchived: json['is_archived'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notice_id': id,
      'title': title,
      'body': body,
      'icon': icon,
      'created_at': createdAt.toIso8601String(),
      'is_delivered': isDelivered,
      'is_archived': isArchived,
    };
  }

  NotificationEntity toEntity() {
    return NotificationEntity(
      id: id,
      title: title,
      body: body,
      icon: icon,
      createdAt: createdAt,
      isDelivered: isDelivered,
      isArchived: isArchived,
    );
  }

  factory NotificationModel.fromEntity(NotificationEntity entity) {
    return NotificationModel(
      id: entity.id,
      title: entity.title,
      body: entity.body,
      icon: entity.icon,
      createdAt: entity.createdAt,
      isDelivered: entity.isDelivered,
      isArchived: entity.isArchived,
    );
  }
}