import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_utils.dart';
import 'custom_payments_editor.dart';

class PatentPaymentSection extends StatelessWidget {
  final PatentPayment payment;
  final bool isEditing;
  final ValueNotifier<String> paymentTypeNotifier;
  final Function(PatentPayment) onPaymentChanged;
  final VoidCallback onSyncMonthlyAmount;

  const PatentPaymentSection({
    super.key,
    required this.payment,
    required this.isEditing,
    required this.paymentTypeNotifier,
    required this.onPaymentChanged,
    required this.onSyncMonthlyAmount,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildPaymentTypeSelector(context),
        const SizedBox(height: 16),
        ValueListenableBuilder<String>(
          valueListenable: paymentTypeNotifier,
          builder: (context, type, child) {
            return type == 'ежемесячно'
                ? _buildMonthlyPaymentEditor(context)
                : CustomPaymentsEditor(
                  payments: payment.customPayments ?? [],
                  isEditing: isEditing,
                  title: 'Платеж',
                  onUpdate: (updatedList) {
                    onPaymentChanged(
                      payment.copyWith(customPayments: updatedList),
                    );
                  },
                );
          },
        ),
      ],
    );
  }

  Widget _buildPaymentTypeSelector(BuildContext context) {
    return ValueListenableBuilder<String>(
      valueListenable: paymentTypeNotifier,
      builder: (context, currentType, child) {
        if (!isEditing) {
          // Режим просмотра - показываем только текущий тип
          return GestureDetector(
            onTap:
                () => ClientUtils.copyToClipboard(
                  context,
                  currentType,
                  'Тип платежа',
                ),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Тип платежа: ${currentType == 'произвольно' ? 'Произвольно' : 'Ежемесячно'}',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const Icon(Icons.copy, size: 16),
                ],
              ),
            ),
          );
        }

        // Режим редактирования
        return Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: const Text('Произвольно'),
                value: 'произвольно',
                groupValue: currentType,
                onChanged: (val) {
                  if (val != null) {
                    paymentTypeNotifier.value = val;
                    onPaymentChanged(payment.copyWith(type: val));
                  }
                },
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: const Text('Ежемесячно'),
                value: 'ежемесячно',
                groupValue: currentType,
                onChanged: (val) {
                  if (val != null) {
                    paymentTypeNotifier.value = val;
                    final monthlyPayment =
                        payment.monthlyPayment ??
                        MonthlyPayment(
                          startDate: DateTime.now(),
                          endDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                          customAmount: 0.0,
                          paymentDays: [],
                        );
                    onPaymentChanged(
                      payment.copyWith(
                        type: val,
                        monthlyPayment: monthlyPayment,
                      ),
                    );
                    onSyncMonthlyAmount();
                  }
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMonthlyPaymentEditor(BuildContext context) {
    final monthlyPayment =
        payment.monthlyPayment ??
        MonthlyPayment(
          startDate: DateTime.now(),
          endDate: DateTime.now().add(const Duration(days: 365)),
          customAmount: 0.0,
          paymentDays: [],
        );

    // Синхронизация вычисленной суммы
    WidgetsBinding.instance.addPostFrameCallback((_) => onSyncMonthlyAmount());

    final amountText = monthlyPayment.customAmount.toStringAsFixed(2);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap:
              () => ClientUtils.copyToClipboard(
                context,
                amountText,
                'Сумма в месяц',
              ),
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Сумма в месяц',
              border: OutlineInputBorder(),
              suffixIcon: Icon(Icons.copy, size: 16),
            ),
            child: Text(amountText),
          ),
        ),
        const SizedBox(height: 16),
        const Text('Даты оплаты в месяце:'),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: List.generate(31, (index) {
            final day = index + 1;
            final isSelected = monthlyPayment.paymentDays.contains(day);
            return FilterChip(
              label: Text('$day'),
              selected: isSelected,
              onSelected:
                  !isEditing
                      ? null
                      : (selected) {
                        final days = List<int>.from(monthlyPayment.paymentDays);
                        if (selected) {
                          days.add(day);
                        } else {
                          days.remove(day);
                        }
                        days.sort();
                        onPaymentChanged(
                          payment.copyWith(
                            monthlyPayment: monthlyPayment.copyWith(
                              paymentDays: days,
                            ),
                          ),
                        );
                        onSyncMonthlyAmount();
                      },
            );
          }),
        ),
      ],
    );
  }
}
