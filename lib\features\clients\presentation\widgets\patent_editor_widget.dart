import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:intl/intl.dart';
import 'patent_components/patent_main_info.dart';
import 'patent_components/patent_payment_section.dart';
import 'patent_components/patent_reduction_section.dart';

class PatentEditorWidget extends StatefulWidget {
  final PatentEntity patent;
  final ValueChanged<PatentEntity> onChanged;
  final VoidCallback onDelete;
  final bool isEditing;

  const PatentEditorWidget({
    super.key,
    required this.patent,
    required this.onChanged,
    required this.onDelete,
    required this.isEditing,
  });

  @override
  State<PatentEditorWidget> createState() => _PatentEditorWidgetState();
}

class _PatentEditorWidgetState extends State<PatentEditorWidget> {
  // Неизменяемые поля
  late DateTime _startDate;
  late DateTime _issueDate;

  // Редактируемые поля
  late DateTime _endDate;
  late final TextEditingController _amountCtrl;
  late final TextEditingController _commentCtrl;
  late final TextEditingController _patentNumberCtrl;
  late final TextEditingController _patentTitleCtrl;
  late final TextEditingController _startDateCtrl;
  late final TextEditingController _issueDateCtrl;
  late final TextEditingController _endDateCtrl;
  late final TextEditingController _newPatentApplicationDateCtrl;
  late DateTime? _newPatentApplicationDate;
  late PatentPayment _payment;
  late PatentReduction _reduction;

  final _paymentTypeNotifier = ValueNotifier<String>('произвольно');

  final _startDateKey = GlobalKey();
  final _issueDateKey = GlobalKey();
  final _endDateKey = GlobalKey();
  final _newPatentApplicationDateKey = GlobalKey();

  // FocusNodes для умного ввода дат
  final FocusNode _startDateFN = FocusNode();
  final FocusNode _issueDateFN = FocusNode();
  final FocusNode _endDateFN = FocusNode();
  final FocusNode _newPatentAppFN = FocusNode();

  @override
  void initState() {
    super.initState();
    _startDate = widget.patent.startDate;
    _issueDate = widget.patent.issueDate;
    _endDate = widget.patent.endDate;
    _amountCtrl = TextEditingController(
      text: widget.patent.patentAmount.toStringAsFixed(2),
    );
    _commentCtrl = TextEditingController(text: widget.patent.comment ?? '');
    _patentNumberCtrl = TextEditingController(text: widget.patent.patentNumber);
    _patentTitleCtrl = TextEditingController(
      text: widget.patent.patentTitle ?? '',
    );

    // Date controllers
    _startDateCtrl = TextEditingController(
      text: DateFormat('dd.MM.yyyy').format(_startDate),
    );
    _issueDateCtrl = TextEditingController(
      text: DateFormat('dd.MM.yyyy').format(_issueDate),
    );
    _endDateCtrl = TextEditingController(
      text: DateFormat('dd.MM.yyyy').format(_endDate),
    );
    _newPatentApplicationDateCtrl = TextEditingController(
      text:
          widget.patent.newPatentApplicationDate != null
              ? DateFormat(
                'dd.MM.yyyy',
              ).format(widget.patent.newPatentApplicationDate!)
              : '',
    );

    _newPatentApplicationDate = widget.patent.newPatentApplicationDate;
    _payment =
        widget.patent.payment ??
        const PatentPayment(type: 'произвольно', customPayments: []);
    _reduction =
        widget.patent.reduction ?? const PatentReduction(customReductions: []);

    _paymentTypeNotifier.value = _payment.type;

    // --- Smart parsing listeners for date fields ---
    _startDateFN.addListener(() {
      _parseAndFormatDateField(_startDateFN, _startDateCtrl, (date) {
        setState(() {
          _startDate = date ?? _startDate;
          if (date != null) {
            _startDateCtrl.text = DateFormat('dd.MM.yyyy').format(date);
          }
        });
      });
    });

    _issueDateFN.addListener(() {
      _parseAndFormatDateField(_issueDateFN, _issueDateCtrl, (date) {
        setState(() {
          _issueDate = date ?? _issueDate;
          if (date != null) {
            _issueDateCtrl.text = DateFormat('dd.MM.yyyy').format(date);
          }
        });
      });
    });

    _endDateFN.addListener(() {
      _parseAndFormatDateField(_endDateFN, _endDateCtrl, (date) {
        setState(() {
          if (date != null) {
            _endDate = date;
            _endDateCtrl.text = DateFormat('dd.MM.yyyy').format(date);
          }
        });
      });
    });

    _newPatentAppFN.addListener(() {
      _parseAndFormatDateField(_newPatentAppFN, _newPatentApplicationDateCtrl, (
        date,
      ) {
        setState(() {
          _newPatentApplicationDate = date;
          _newPatentApplicationDateCtrl.text =
              date != null ? DateFormat('dd.MM.yyyy').format(date) : '';
        });
      });
    });
  }

  @override
  void dispose() {
    _amountCtrl.dispose();
    _commentCtrl.dispose();
    _patentNumberCtrl.dispose();
    _patentTitleCtrl.dispose();
    _startDateCtrl.dispose();
    _issueDateCtrl.dispose();
    _endDateCtrl.dispose();
    _newPatentApplicationDateCtrl.dispose();
    _paymentTypeNotifier.dispose();
    _startDateFN.dispose();
    _issueDateFN.dispose();
    _endDateFN.dispose();
    _newPatentAppFN.dispose();
    super.dispose();
  }

  void _onChanged() {
    _syncMonthlyAmount();
    final amount = double.tryParse(_amountCtrl.text) ?? 0.0;
    widget.onChanged(
      PatentEntity(
        startDate: _startDate,
        endDate: _endDate,
        issueDate: _issueDate,
        patentAmount: amount,
        patentNumber: _patentNumberCtrl.text,
        patentTitle: _patentTitleCtrl.text,
        comment: _commentCtrl.text,
        payment: _payment,
        reduction: _reduction,
        newPatentApplicationDate: _newPatentApplicationDate,
      ),
    );
  }

  double _calculateMonthlyAmount() {
    final totalAmount = double.tryParse(_amountCtrl.text) ?? 0.0;
    final totalReduction =
        _reduction.customReductions?.fold<double>(
          0.0,
          (sum, r) => sum + r.amount,
        ) ??
        0.0;

    // Кол-во месяцев между start и end (включительно)
    int months =
        (_endDate.year - _startDate.year) * 12 +
        _endDate.month -
        _startDate.month +
        1;

    // Учесть месяцы без подходящего дня оплаты
    final paymentDays = _payment.monthlyPayment?.paymentDays ?? [];
    if (paymentDays.isNotEmpty) {
      int validMonths = 0;
      DateTime iter = DateTime(_startDate.year, _startDate.month);
      for (int i = 0; i < months; i++) {
        final lastDay = DateTime(iter.year, iter.month + 1, 0).day;
        final hasValidDay = paymentDays.any((d) => d <= lastDay);
        if (hasValidDay) validMonths++;
        iter = DateTime(iter.year, iter.month + 1);
      }
      months = validMonths;
    }

    if (months <= 0) return 0.0;
    final remaining = totalAmount - totalReduction;
    if (remaining <= 0) return 0.0;
    return remaining / months;
  }

  void _syncMonthlyAmount() {
    if (_payment.type != 'ежемесячно' || _payment.monthlyPayment == null) {
      return;
    }
    final newAmount = _calculateMonthlyAmount();
    if ((_payment.monthlyPayment!.customAmount - newAmount).abs() > 0.0001) {
      setState(() {
        _payment = _payment.copyWith(
          monthlyPayment: _payment.monthlyPayment!.copyWith(
            customAmount: newAmount,
          ),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 1,
      clipBehavior: Clip.antiAlias,
      child: ExpansionTile(
        title: Text(
          widget.patent.patentTitle != null &&
                  widget.patent.patentTitle!.isNotEmpty
              ? widget.patent.patentTitle!
              : (widget.patent.patentNumber.startsWith('Новый патент')
                  ? 'Новый патент'
                  : 'Патент №${widget.patent.patentNumber}'),
        ),
        subtitle: Text(
          'Действует с ${_startDate.day}.${_startDate.month}.${_startDate.year} по ${_endDate.day}.${_endDate.month}.${_endDate.year}',
        ),
        trailing:
            widget.isEditing
                ? IconButton(
                  icon: const Icon(
                    Icons.delete_outline,
                    color: Colors.redAccent,
                  ),
                  tooltip: 'Удалить патент',
                  onPressed: widget.onDelete,
                )
                : null,
        onExpansionChanged: (isExpanded) {
          if (!isExpanded) _onChanged();
        },
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PatentMainInfo(
                  startDate: _startDate,
                  issueDate: _issueDate,
                  endDate: _endDate,
                  newPatentApplicationDate: _newPatentApplicationDate,
                  patentNumberCtrl: _patentNumberCtrl,
                  patentTitleCtrl: _patentTitleCtrl,
                  amountCtrl: _amountCtrl,
                  commentCtrl: _commentCtrl,
                  isEditing: widget.isEditing,
                  startDateCtrl: _startDateCtrl,
                  startDateKey: _startDateKey,
                  issueDateCtrl: _issueDateCtrl,
                  issueDateKey: _issueDateKey,
                  endDateCtrl: _endDateCtrl,
                  endDateKey: _endDateKey,
                  newPatentApplicationDateCtrl: _newPatentApplicationDateCtrl,
                  newPatentApplicationDateKey: _newPatentApplicationDateKey,
                  onStartDateChanged: (date) {
                    setState(() {
                      _startDate = date;
                      _startDateCtrl.text = DateFormat(
                        'dd.MM.yyyy',
                      ).format(date);
                      // Обновляем период ежемесячной оплаты, если есть
                      if (_payment.monthlyPayment != null) {
                        _payment = _payment.copyWith(
                          monthlyPayment: _payment.monthlyPayment!.copyWith(
                            startDate: date,
                          ),
                        );
                      }
                    });
                    _syncMonthlyAmount();
                    _onChanged();
                  },
                  onIssueDateChanged: (date) {
                    setState(() {
                      _issueDate = date;
                      _issueDateCtrl.text = DateFormat(
                        'dd.MM.yyyy',
                      ).format(date);
                    });
                    _onChanged();
                  },
                  onEndDateChanged: (date) {
                    setState(() {
                      _endDate = date;
                      _endDateCtrl.text = DateFormat('dd.MM.yyyy').format(date);
                    });
                    _onChanged();
                  },
                  onNewPatentApplicationDateChanged: (date) {
                    setState(() {
                      _newPatentApplicationDate = date;
                      _newPatentApplicationDateCtrl.text =
                          date != null
                              ? DateFormat('dd.MM.yyyy').format(date)
                              : '';
                    });
                    _onChanged();
                  },
                  onChanged: _onChanged,
                  startDateFN: _startDateFN,
                  issueDateFN: _issueDateFN,
                  endDateFN: _endDateFN,
                  newPatentApplicationDateFN: _newPatentAppFN,
                ),
                const SizedBox(height: 24),
                _buildSectionTitle('Оплата патента'),
                PatentPaymentSection(
                  payment: _payment,
                  isEditing: widget.isEditing,
                  paymentTypeNotifier: _paymentTypeNotifier,
                  onPaymentChanged: (payment) {
                    setState(() => _payment = payment);
                    _onChanged();
                  },
                  onSyncMonthlyAmount: _syncMonthlyAmount,
                ),
                const SizedBox(height: 24),
                _buildSectionTitle('Уменьшение патента'),
                PatentReductionSection(
                  reduction: _reduction,
                  isEditing: widget.isEditing,
                  onReductionChanged: (reduction) {
                    setState(() => _reduction = reduction);
                    _onChanged();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    );
  }

  // === Smart date parsing helpers ===
  void _parseAndFormatDateField(
    FocusNode fn,
    TextEditingController ctrl,
    Function(DateTime?) updateModel,
  ) {
    if (!fn.hasFocus) {
      final text = ctrl.text.trim();
      if (text.isEmpty) {
        updateModel(null);
        return;
      }

      final parsed = _tryParseDate(text);
      if (parsed != null) {
        ctrl.text = DateFormat('dd.MM.yyyy').format(parsed);
        updateModel(parsed);
      }
    }
  }

  DateTime? _tryParseDate(String input) {
    input = input.trim();

    // Без разделителей ddMMyyyy / ddMMyy
    if (RegExp(r'^\d{8}$').hasMatch(input)) {
      final d = int.parse(input.substring(0, 2));
      final m = int.parse(input.substring(2, 4));
      final y = int.parse(input.substring(4, 8));
      if (_isValidDayMonth(d, m)) return DateTime(y, m, d);
    }

    if (RegExp(r'^\d{6}$').hasMatch(input)) {
      final d = int.parse(input.substring(0, 2));
      final m = int.parse(input.substring(2, 4));
      final yy = int.parse(input.substring(4, 6));
      final y = yy < 50 ? 2000 + yy : 1900 + yy;
      if (_isValidDayMonth(d, m)) return DateTime(y, m, d);
    }

    // Стандартные форматы через DateFormat
    const fmts = [
      'dd.MM.yyyy',
      'dd/MM/yyyy',
      'dd-MM-yyyy',
      'dd.MM.yy',
      'dd/MM/yy',
      'dd-MM-yy',
      'dd.MM',
      'dd/MM',
      'dd-MM',
    ];
    for (final f in fmts) {
      try {
        DateTime dt = DateFormat(f).parseStrict(input);
        if (f.length <= 5) {
          // без года
          dt = DateTime(DateTime.now().year, dt.month, dt.day);
        } else if (f.contains('yy') && !f.contains('yyyy')) {
          if (dt.year < 50) {
            dt = DateTime(dt.year + 2000, dt.month, dt.day);
          } else {
            dt = DateTime(dt.year + 1900, dt.month, dt.day);
          }
        }
        return dt;
      } catch (_) {}
    }

    // Извлекаем числа
    final nums =
        RegExp(
          r'\d+',
        ).allMatches(input).map((m) => int.parse(m.group(0)!)).toList();
    if (nums.length >= 2) {
      final d = nums[0];
      final m = nums[1];
      final y = nums.length >= 3 ? nums[2] : DateTime.now().year;
      int year = y;
      if (year < 100) year = year < 50 ? 2000 + year : 1900 + year;
      if (_isValidDayMonth(d, m)) return DateTime(year, m, d);
    }
    return null;
  }

  bool _isValidDayMonth(int day, int month) =>
      day >= 1 && day <= 31 && month >= 1 && month <= 12;
}
