import 'package:equatable/equatable.dart';

/// Entity для операции загрузки файла
class FileUploadEntity extends Equatable {
  /// URL для загрузки файла
  final String uploadUrl;

  /// Ключ файла в хранилище
  final String fileKey;

  /// Имя файла
  final String fileName;

  /// Размер файла в байтах
  final int fileSize;

  const FileUploadEntity({
    required this.uploadUrl,
    required this.fileKey,
    required this.fileName,
    required this.fileSize,
  });

  @override
  List<Object?> get props => [uploadUrl, fileKey, fileName, fileSize];
}

/// Entity для операции скачивания файла
class FileDownloadEntity extends Equatable {
  /// URL для скачивания файла
  final String downloadUrl;

  /// Ключ файла в хранилище
  final String fileKey;

  const FileDownloadEntity({required this.downloadUrl, required this.fileKey});

  @override
  List<Object?> get props => [downloadUrl, fileKey];
}

/// Entity для результата загрузки файла с прогрессом
class FileUploadProgressEntity extends Equatable {
  /// Ключ файла
  final String fileKey;

  /// Прогресс загрузки (0.0 - 1.0)
  final double progress;

  /// Статус загрузки
  final FileUploadStatus status;

  /// Сообщение об ошибке (если есть)
  final String? errorMessage;

  const FileUploadProgressEntity({
    required this.fileKey,
    required this.progress,
    required this.status,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [fileKey, progress, status, errorMessage];
}

/// Статусы загрузки файла
enum FileUploadStatus {
  preparing, // Подготовка к загрузке
  uploading, // Процесс загрузки
  confirming, // Подтверждение загрузки
  completed, // Загрузка завершена
  failed, // Ошибка загрузки
}
