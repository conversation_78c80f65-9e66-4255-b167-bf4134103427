import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/clients_repository.dart';
import 'package:dartz/dartz.dart';

class DeleteClientUseCase {
  final IClientsRepository repository;
  const DeleteClientUseCase(this.repository);

  Future<Either<Failure, Unit>> call(String firmId, String clientId, DateTime creationDate) {
    return repository.deleteClient(firmId, clientId, creationDate);
  }
}
