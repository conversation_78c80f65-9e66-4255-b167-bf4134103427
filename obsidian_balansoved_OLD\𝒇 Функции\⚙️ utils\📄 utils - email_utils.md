**1. `send_verification_code(recipient_email: str, code: str) -> bool`**
- **Назначение**: Формирует и отправляет email с 6-значным кодом подтверждения для регистрации.
- **На входе**:
	-> `recipient_email`: Email пользователя, которому отправляется код.
	-> `code`: Строка с 6-значным кодом.
- **На выходе**:
	-> `True`, если письмо успешно отправлено, иначе `False`.

**2. `send_invitation(recipient_email: str, invitation_key: str, firm_name: str, admin_name: str) -> bool`**
- **Назначение**: Формирует и отправляет email с приглашением присоединиться к фирме.
- **На входе**:
	-> `recipient_email`: Email приглашаемого пользователя.
	-> `invitation_key`: Уникальный ключ-приглашение.
	-> `firm_name`: Название фирмы, в которую приглашают.
	-> `admin_name`: Имя или email администратора, который отправил приглашение.
- **На выходе**:
	-> `True`, если письмо успешно отправлено, иначе `False`.

---

```python
# utils/email_utils.py

import os
import requests
import logging

def _send_email(recipient_email: str, subject: str, html_body: str) -> bool:
    """
    Приватная функция для отправки email через API Unisender.
    Использует переменные окружения для конфигурации.
    """
    api_key = os.environ.get('UNISENDER_API_KEY')
    sender_email = os.environ.get('UNISENDER_SENDER_EMAIL')
    sender_name = os.environ.get('UNISENDER_SENDER_NAME')
    list_id = os.environ.get('UNISENDER_LIST_ID')

    if not all([api_key, sender_email, sender_name, list_id]):
        logging.error("Unisender configuration is incomplete. Please set UNISENDER_API_KEY, UNISENDER_SENDER_EMAIL, UNISENDER_SENDER_NAME, and UNISENDER_LIST_ID environment variables.")
        return False

    api_url = 'https://api.unisender.com/ru/api/sendEmail'
    
    params = {
        'api_key': api_key,
        'format': 'json',
        'email': recipient_email,
        'sender_name': sender_name,
        'sender_email': sender_email,
        'subject': subject,
        'body': html_body,
        'list_id': list_id,
    }

    try:
        logging.info(f"Sending email to {recipient_email} with subject '{subject}'")
        response = requests.post(api_url, data=params)
        response.raise_for_status()  # Проверка на HTTP ошибки (4xx, 5xx)
        
        response_data = response.json()
        if response_data.get('error'):
            logging.error(f"Unisender API returned an error: {response_data.get('error')}")
            return False

        logging.info(f"Email successfully sent. Unisender response: {response_data}")
        return True

    except requests.exceptions.RequestException as e:
        logging.error(f"Failed to send email via Unisender. Request error: {e}", exc_info=True)
        return False
    except Exception as e:
        logging.error(f"An unexpected error occurred during email sending: {e}", exc_info=True)
        return False


def send_verification_code(recipient_email: str, code: str) -> bool:
    """Формирует и отправляет email с кодом подтверждения."""
    
    email_subject = f'Код подтверждения: {code}'
    html_body = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f4f4f4; }}
            .container {{ background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); text-align: center; }}
            .code {{ font-size: 24px; font-weight: bold; color: #333; letter-spacing: 5px; margin: 20px 0; padding: 15px; background-color: #eef1f3; border-radius: 5px; }}
            p {{ color: #555; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h2>Подтверждение аккаунта</h2>
            <p>Здравствуйте!</p>
            <p>Для завершения регистрации, пожалуйста, используйте этот проверочный код:</p>
            <div class="code">{code}</div>
            <p>Если вы не запрашивали этот код, просто проигнорируйте это письмо.</p>
        </div>
    </body>
    </html>
    """
    return _send_email(recipient_email, email_subject, html_body)


def send_invitation(recipient_email: str, invitation_key: str, firm_name: str, admin_name: str) -> bool:
    """Формирует и отправляет email с приглашением в фирму."""

    email_subject = f'Приглашение в команду "{firm_name}"'
    # TODO: Заменить 'https://your-app-domain.com/accept-invite' на реальную ссылку вашего фронтенда
    accept_url = f'https://your-app-domain.com/accept-invite?key={invitation_key}'

    html_body = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f4f4f4; }}
            .container {{ background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); text-align: center; }}
            .key {{ font-size: 18px; font-weight: bold; color: #333; font-family: 'Courier New', Courier, monospace; margin: 20px 0; padding: 15px; background-color: #eef1f3; border-radius: 5px; }}
            .button {{ display: inline-block; background-color: #007bff; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; }}
            p {{ color: #555; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h2>Приглашение в команду</h2>
            <p>Здравствуйте!</p>
            <p>Пользователь <b>{admin_name}</b> пригласил вас присоединиться к фирме <b>"{firm_name}"</b>.</p>
            <p>Чтобы принять приглашение, перейдите по ссылке ниже. Если вы еще не зарегистрированы, вам будет предложено создать аккаунт.</p>
            <br>
            <a href="{accept_url}" class="button">Принять приглашение</a>
            <br><br>
            <p>Если кнопка не работает, вы можете использовать этот ключ приглашения на странице входа:</p>
            <div class="key">{invitation_key}</div>
            <p>Если вы не ожидали этого приглашения, просто проигнорируйте это письмо.</p>
        </div>
    </body>
    </html>
    """
    return _send_email(recipient_email, email_subject, html_body)
```