import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/employees_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';

class TasksCreateButton extends StatelessWidget {
  final VoidCallback onPressed;

  const TasksCreateButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    final firmState = context.watch<ActiveFirmCubit>().state;
    final employeesState = context.watch<EmployeesCubit>().state;
    final clientsState = context.watch<ClientsCubit>().state;
    final tasksState = context.watch<TasksCubit>().state;
    final isLoading =
        firmState.isLoading ||
        firmState.selectedFirm == null ||
        employeesState.isLoading ||
        clientsState.isLoading ||
        tasksState is TasksLoading;
    if (isLoading) {
      return const LoadingTile(height: 48, width: 160);
    }
    return ElevatedButton.icon(
      icon: const Icon(Icons.add_task),
      label: const Text('Создать задачу'),
      onPressed: onPressed,
    );
  }
}