import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/tasks/domain/repositories/tasks_repository.dart';

class DeleteTaskUseCase {
  final TasksRepository repository;

  DeleteTaskUseCase(this.repository);

  Future<Either<Failure, void>> call(String firmId, String taskId) async {
    return await repository.deleteTask(firmId, taskId);
  }
}
