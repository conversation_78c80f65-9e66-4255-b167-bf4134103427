**1. `get_s3_client() -> boto3.client`**
- **Назначение**: Создает и возвращает клиент `boto3` для работы с Object Storage. Использует переменные окружения для конфигурации.
- **На выходе**: Объект клиента `boto3`.

**2. `get_folder_size(client, bucket: str, prefix: str) -> int`**
- **Назначение**: Подсчитывает суммарный размер всех объектов в указанной "папке" (префиксе) бакета.
- **На входе**:
	-> `client`: `boto3` клиент.
	-> `bucket`: Имя бакета.
	-> `prefix`: Префикс (путь к папке), например, `my-firm-id/`.
- **На выходе**:
	-> Суммарный размер в байтах.

**3. `generate_presigned_upload_url(client, bucket: str, key: str, original_filename: str) -> str | None`**
- **Назначение**: Генерирует временную (на 15 минут) подписанную ссылку, которая позволяет выполнить `PUT` запрос для загрузки файла напрямую в хранилище.
- **На выходе**:
	-> Строка с pre-signed URL или `None` в случае ошибки.

**4. `delete_object(client, bucket: str, key: str) -> bool`**
- **Назначение**: Удаляет объект из хранилища по его ключу.
- **На выходе**:
	-> `True` в случае успеха, `False` в случае ошибки.

---

```python
# utils/storage_utils.py

import os
import re
import datetime
import logging
import boto3
from botocore.exceptions import ClientError

def get_s3_client():
    """
    Инициализирует и возвращает клиент boto3 для работы с Yandex Object Storage.
    """
    try:
        return boto3.client(
            's3',
            endpoint_url=os.environ['STORAGE_ENDPOINT'],
            region_name=os.environ['STORAGE_REGION'],
            aws_access_key_id=os.environ['STORAGE_ACCESS_KEY'],
            aws_secret_access_key=os.environ['STORAGE_SECRET_KEY']
        )
    except Exception as e:
        logging.error(f"Failed to initialize S3 client: {e}", exc_info=True)
        raise

def get_folder_size(client, bucket: str, prefix: str) -> int:
    """
    Подсчитывает суммарный размер всех объектов в указанной "папке" (префиксе) бакета.
    """
    total_size = 0
    paginator = client.get_paginator('list_objects_v2')
    try:
        logging.info(f"Calculating folder size for prefix '{prefix}' in bucket '{bucket}'...")
        pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
        for page in pages:
            if "Contents" in page:
                for obj in page['Contents']:
                    total_size += obj.get('Size', 0)
        logging.info(f"Calculated folder size is {total_size} bytes.")
        return total_size
    except Exception as e:
        logging.error(f"Could not calculate folder size for prefix '{prefix}': {e}", exc_info=True)
        # В случае ошибки важно пробросить исключение, чтобы прервать операцию
        raise

def generate_upload_artefacts(firm_id: str, filename: str):
    """
    Генерирует ключ файла и pre-signed URL для загрузки.
    """
    logging.info(f"Generating upload artefacts for firm {firm_id}, filename {filename}")
    s3_client = get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']
    
    now = datetime.datetime.utcnow()
    original_basename, extension = os.path.splitext(filename)
    safe_basename = re.sub(r'[^\w\d-]', '_', original_basename)
    final_filename = f"{safe_basename}_{firm_id}{extension}"
    file_key = f"{firm_id}/{now.year}/{now.month:02d}/{final_filename}"
    
    try:
        upload_url = s3_client.generate_presigned_url(
            'put_object',
            Params={'Bucket': bucket_name, 'Key': file_key},
            ExpiresIn=3600  # Ссылка действительна 1 час
        )
        logging.info(f"Successfully generated presigned URL for key: {file_key}")
        return file_key, upload_url
    except ClientError as e:
        logging.error(f"Failed to generate presigned URL: {e}", exc_info=True)
        return None, None

def delete_object(s3_client, bucket_name: str, file_key: str) -> bool:
    """
    Удаляет объект из бакета.
    """
    try:
        s3_client.delete_object(Bucket=bucket_name, Key=file_key)
        logging.info(f"Successfully initiated deletion for key: {file_key}")
        return True
    except ClientError as e:
        logging.error(f"Failed to delete object with key {file_key}: {e}", exc_info=True)
        return False

def generate_presigned_download_url(s3_client, bucket_name: str, file_key: str) -> str | None:
    """
    Генерирует долгоживущую (1 дней) pre-signed URL для СКАЧИВАНИЯ объекта.
    """
    try:
        url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': file_key},
            ExpiresIn=86400  # 1 дней в секундах
        )
        logging.info(f"Successfully generated long-lived download URL for key: {file_key}")
        return url
    except ClientError as e:
        logging.error(f"Failed to generate presigned download URL for {file_key}: {e}", exc_info=True)
        return None
```
