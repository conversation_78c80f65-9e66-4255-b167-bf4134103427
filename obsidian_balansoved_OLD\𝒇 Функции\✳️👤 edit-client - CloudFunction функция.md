
Идентификатор - d4e0nco8ka4c1me3qdrt
Описание - 📇 Создать, обновить, удалить или получить информацию о клиенте фирмы
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: То<PERSON><PERSON>н любого сотрудника фирмы.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы.
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
		- `client_id` (string, необязательно): ID клиента.
		- `creation_date` (string): Дата версии в формате `YYYY-MM-DD`. Обязательно для обновления в UPSERT и для DELETE, опционально для GET.
		- `payload` (object, необязательно): Данные клиента для `UPSERT`.

Внутренняя работа:
    -> Логирование входных данных
    -> Авторизация и парсинг запроса
        -> Извлечение auth header (предпочтительно x-forwarded-authorization)
        -> Верификация JWT токена для получения user_id
        -> Парсинг тела запроса для получения firm_id, action, client_id, creation_date, payload
    -> Проверка обязательных параметров (firm_id, action)
    -> Парсинг creation_date, если предоставлена
    -> Инициализация YDB подключений
        -> Для firms-database (проверка членства)
        -> Для clients-database (основная логика)
    -> Проверка членства пользователя в фирме и получение ролей (в firms-database)
    -> Роутинг по action в транзакции clients-database (таблица clients_{firm_id})
        -> Для GET:
            -> Если client_id и creation_date: Получить конкретную версию клиента
            -> Если только client_id: Получить все версии клиента, отсортированные по дате DESC
            -> Иначе: Получить все актуальные клиенты (is_actual = true)
            -> Сериализация результатов в JSON
        -> Для UPSERT (требует ролей OWNER или ADMIN):
            -> Добавление updated_at в payload
            -> Если client_id и creation_date: Обновление существующей версии (только предоставленные поля)
            -> Если client_id и manual_creation_date в payload: Создание новой версии
                -> Поиск и деактивация старой актуальной версии (is_actual = false)
                -> Вставка новой версии с is_actual = true и created_at
            -> Если нет client_id и manual_creation_date в payload: Создание нового клиента
                -> Генерация client_id
                -> Установка is_actual = true, is_active = true, created_at
            -> Валидация и подготовка параметров для YDB запросов
        -> Для DELETE (требует ролей OWNER или ADMIN):
            -> Проверка наличия client_id и creation_date
            -> Проверка существования версии
            -> Если версия актуальна: Проверка, не является ли она последней (запрет удаления)
            -> Удаление версии
            -> Если удалена актуальная: Поиск и установка самой поздней оставшейся версии как актуальной (is_actual = true)
    -> Обработка результатов и исключений (AuthError -> 403, LogicError -> 400, NotFoundError -> 404, прочие -> 500)
    -> Накопление и вывод логов в конце обработки

На выходе:
	-> `200 OK` (GET): `{"data": [{...}]}` или `{"data": {...}}` или `{"data": []}`
	-> `201 Created` (UPSERT/create): `{"message": "Client created", "client_id": "...", "creation_date": "..."}`
	-> `200 OK` (UPSERT/update): `{"message": "Client version updated", "client_id": "...", "creation_date": "..."}`
	-> `200 OK` (UPSERT/new-version): `{"message": "New client version created", "client_id": "...", "creation_date": "..."}`
	-> `200 OK` (DELETE): `{"message": "Client version deleted"}`
	-> `400 Bad Request`: Неверные или отсутствующие параметры.
	-> `403 Forbidden`: Недостаточно прав для `UPSERT` или `DELETE`.
	-> `404 Not Found`: Указанный `client_id` не найден.
	-> `500 Internal Server Error`: Ошибка на стороне сервера или БД.

---
#### Зависимости и окружение
- **Необходимые утилиты**: [[utils/auth_utils.py]], [[utils/ydb_utils.py]], [[utils/request_parser.py]]
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS` ([[💾 firms-database - База данных YandexDatabase]]), `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
	- `YDB_ENDPOINT_CLIENTS` ([[💾 clients-database - База данных YandexDatabase]]), `YDB_DATABASE_CLIENTS` ([[💾 clients-database - База данных YandexDatabase]])
	- `SA_KEY_FILE` ([[ydb_sa_key.json]])
	- `JWT_SECRET`

---

```python
import json, os, datetime, pytz, logging, uuid
import ydb
from utils import auth_utils, ydb_utils, request_parser
from get import get_client
from upsert import upsert_client
from delete import delete_client

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

# Глобальный список для накопления логов
log_accumulator = []

def check_membership_and_get_roles(session, user_id, firm_id):
    log_accumulator.append(f"[check_membership_and_get_roles] Checking membership for user_id={user_id}, firm_id={firm_id}")
    query_text = """
        DECLARE $user_id AS Utf8;
        DECLARE $firm_id AS Utf8;
        SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;
    """
    log_accumulator.append(f"[check_membership_and_get_roles] Executing query: {query_text}")
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(
        query,
        {"$user_id": user_id, "$firm_id": firm_id},
        commit_tx=True
    )
    log_accumulator.append(f"[check_membership_and_get_roles] Query result rows count: {len(result[0].rows) if result and result[0].rows else 0}")
    if not result[0].rows:
        log_accumulator.append(f"[check_membership_and_get_roles] User {user_id} is not a member of firm {firm_id}")
        raise AuthError("User is not a member of the specified firm.")
    roles = json.loads(result[0].rows[0].roles or '[]')
    log_accumulator.append(f"[check_membership_and_get_roles] User {user_id} is a member of firm {firm_id} with roles: {roles}")
    return roles

def handler(event, context):
    global log_accumulator
    log_accumulator = []  # Сброс накопителя логов для каждого запроса
    
    try:
        # Логирование входных данных в сыром виде
        log_accumulator.append(f"[handler] RAW INPUT EVENT: {json.dumps(event, default=str)}")
        log_accumulator.append(f"[handler] RAW INPUT CONTEXT: {json.dumps(context.__dict__ if hasattr(context, '__dict__') else str(context), default=str)}")
        
        log_accumulator.append("[handler] Starting request processing")
        headers = event.get('headers', {}) or {}
        log_accumulator.append(f"[handler] Headers: {headers}")
        
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        log_accumulator.append(f"[handler] Auth header found: {'Yes' if auth_header else 'No'}")
        
        if not auth_header.startswith('Bearer '):
            log_accumulator.append("[handler] Auth header does not start with 'Bearer '")
            raise AuthError("Unauthorized")
        parts = auth_header.split(' ')
        if len(parts) != 2:
            log_accumulator.append(f"[handler] Invalid Bearer token format, parts count: {len(parts)}")
            raise AuthError("Unauthorized: Invalid Bearer token format.")
        token = parts[1]
        log_accumulator.append(f"[handler] Token extracted, length: {len(token)}")
        
        user_payload = auth_utils.verify_jwt(token)
        log_accumulator.append(f"[handler] JWT verification result: {user_payload is not None}")
        if not user_payload or 'user_id' not in user_payload: 
            log_accumulator.append("[handler] Invalid token or missing user_id")
            raise AuthError("Invalid token")
        requesting_user_id = user_payload['user_id']
        log_accumulator.append(f"[handler] Requesting user ID: {requesting_user_id}")
        try:
            data = request_parser.parse_request_body(event)
            log_accumulator.append(f"[handler] Parsed request body: {data}")
        except ValueError as e:
            log_accumulator.append(f"[handler] Request body parsing failed: {str(e)}")
            raise LogicError(str(e))
        
        firm_id = data.get('firm_id')
        action = data.get('action')
        client_id = data.get('client_id')
        creation_date_str = data.get('creation_date')
        payload = data.get('payload', {})
        
        log_accumulator.append(f"[handler] Request parameters - firm_id: {firm_id}, action: {action}, client_id: {client_id}, creation_date: {creation_date_str}")
        log_accumulator.append(f"[handler] Payload size: {len(str(payload))} characters")
        
        if not all([firm_id, action]):
            log_accumulator.append("[handler] Missing required parameters: firm_id and/or action")
            raise LogicError("firm_id and action are required.")
        
        creation_date = None
        if creation_date_str:
            log_accumulator.append(f"[handler] Parsing creation_date: {creation_date_str} (type: {type(creation_date_str)})")
            try:
                creation_date = datetime.datetime.strptime(str(creation_date_str), '%Y-%m-%d').date()
                log_accumulator.append(f"[handler] Successfully parsed creation_date: {creation_date}")
            except (ValueError, TypeError) as e:
                log_accumulator.append(f"[handler] Failed to parse creation_date '{creation_date_str}' (type: {type(creation_date_str).__name__}): {str(e)}")
                raise LogicError(f"Invalid date format for 'creation_date' '{creation_date_str}' (type: {type(creation_date_str).__name__}). Use YYYY-MM-DD.")
        log_accumulator.append("[handler] Initializing YDB connections")
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        log_accumulator.append("[handler] Firms YDB connection established")
        
        log_accumulator.append("[handler] Checking user membership and roles")
        user_roles = firms_pool.retry_operation_sync(
            lambda s: check_membership_and_get_roles(s, requesting_user_id, firm_id)
        )
        log_accumulator.append(f"[handler] User roles retrieved: {user_roles}")
        
        clients_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_CLIENTS"], os.environ["YDB_DATABASE_CLIENTS"])
        clients_pool = ydb.SessionPool(clients_driver)
        log_accumulator.append("[handler] Clients YDB connection established")
        
        table_name = f"clients_{firm_id}"
        log_accumulator.append(f"[handler] Target table: {table_name}")
        def client_transaction_router(session):
            log_accumulator.append(f"[client_transaction_router] Routing action: {action}")
            if action == "GET":
                log_accumulator.append("[client_transaction_router] Executing GET operation")
                return get_client(session, table_name, client_id, creation_date, log_accumulator)
            elif action == "UPSERT":
                log_accumulator.append("[client_transaction_router] Executing UPSERT operation")
                return upsert_client(session, table_name, payload, client_id, creation_date, user_roles, log_accumulator)
            elif action == "DELETE":
                log_accumulator.append("[client_transaction_router] Executing DELETE operation")
                return delete_client(session, table_name, client_id, creation_date, user_roles, log_accumulator)
            else:
                log_accumulator.append(f"[client_transaction_router] Invalid action: {action}")
                raise LogicError(f"Invalid action: {action}")
        
        log_accumulator.append("[handler] Starting database transaction")
        result = clients_pool.retry_operation_sync(client_transaction_router)
        log_accumulator.append(f"[handler] Transaction completed successfully with status: {result.get('statusCode')}")
        return result
    except AuthError as e:
        log_accumulator.append(f"[handler] AuthError occurred: {str(e)}")
        result = {"statusCode": 403, "body": json.dumps({"error": str(e)})}
    except LogicError as e:
        log_accumulator.append(f"[handler] LogicError occurred: {str(e)}")
        result = {"statusCode": 400, "body": json.dumps({"error": str(e)})}
    except NotFoundError as e:
        log_accumulator.append(f"[handler] NotFoundError occurred: {str(e)}")
        result = {"statusCode": 404, "body": json.dumps({"error": str(e)})}
    except Exception as e:
        log_accumulator.append(f"[handler] Unhandled exception occurred: {str(e)}")
        result = {"statusCode": 500, "body": json.dumps({"error": "Internal Server Error"})}
    finally:
        # Вывод всех накопленных логов единым блоком
        accumulated_logs = "\n".join(log_accumulator)
        print(f"=== ACCUMULATED LOGS START ===\n{accumulated_logs}\n=== ACCUMULATED LOGS END ===")
        
    return result
```

#### get.py
```python
import json
import ydb
import decimal
import datetime
import uuid
import pytz

class NotFoundError(Exception): pass

def _json_serializer(obj):
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()
    elif isinstance(obj, datetime.date):
        return obj.isoformat()
    elif isinstance(obj, decimal.Decimal):
        return float(obj)
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def rows_to_json(rows):
    if rows is None:
        return None
    if not isinstance(rows, list):
        rows = [rows]
    result_list = []
    for row in rows:
        row_dict = {column_name: row[column_name] for column_name in row}
        result_list.append(row_dict)
    if len(result_list) == 1 and not isinstance(rows, list):
        return result_list[0]
    return result_list

def get_client(session, table_name, client_id, creation_date, log_accumulator):
    log_accumulator.append(f"[get_client] Starting GET operation with client_id={client_id}, creation_date={creation_date}, table={table_name}")
    
    tx = session.transaction(ydb.SerializableReadWrite())
    if client_id and creation_date:
        log_accumulator.append("[get_client] Mode: Get specific client by ID and creation date")
        query_text = f"""DECLARE $client_id AS Utf8; DECLARE $creation_date AS Date;
                         SELECT * FROM `{table_name}` WHERE client_id = $client_id AND manual_creation_date = $creation_date;"""
        log_accumulator.append(f"[get_client] Query: {query_text}")
        result = tx.execute(session.prepare(query_text), {'$client_id': client_id, '$creation_date': creation_date})
        log_accumulator.append(f"[get_client] Query executed, rows found: {len(result[0].rows) if result and result[0].rows else 0}")
        data = result[0].rows[0] if result and result[0].rows else None
        if data is None:
            log_accumulator.append(f"[get_client] Client not found: client_id={client_id}, creation_date={creation_date}")
        else:
            log_accumulator.append("[get_client] Successfully found specific client")
    elif client_id:
        log_accumulator.append("[get_client] Mode: Get all versions of client by ID")
        query_text = f"""DECLARE $client_id AS Utf8;
                         SELECT * FROM `{table_name}` WHERE client_id = $client_id ORDER BY manual_creation_date DESC;"""
        log_accumulator.append(f"[get_client] Query: {query_text}")
        result = tx.execute(session.prepare(query_text), {'$client_id': client_id})
        log_accumulator.append(f"[get_client] Query executed, rows found: {len(result[0].rows) if result and result[0].rows else 0}")
        data = result[0].rows if result else []
        log_accumulator.append(f"[get_client] Successfully found {len(data)} versions of client")
    else:
        log_accumulator.append("[get_client] Mode: Get all actual clients")
        query_text = f"""SELECT * FROM `{table_name}` WHERE is_actual = true;"""
        log_accumulator.append(f"[get_client] Query: {query_text}")
        result = tx.execute(session.prepare(query_text))
        log_accumulator.append(f"[get_client] Query executed, total rows found: {len(result[0].rows) if result and result[0].rows else 0}")
        data = result[0].rows if result else []
        log_accumulator.append("[get_client] Successfully retrieved all actual clients")
    
    tx.commit()
    log_accumulator.append("[get_client] Transaction committed successfully")
    return {"statusCode": 200, "body": json.dumps({"data": rows_to_json(data)}, default=_json_serializer)}
```

#### upsert.py
```python
import json, uuid, datetime, pytz
import ydb

class AuthError(Exception): pass
class LogicError(Exception): pass

def get_declare_clauses_and_params(payload, log_accumulator):
    log_accumulator.append(f"[get_declare_clauses_and_params] Building query parameters for payload with {len(payload)} fields")
    log_accumulator.append(f"[get_declare_clauses_and_params] Payload fields: {list(payload.keys())}")
    
    declare_clauses = ""
    params = {}
    type_map = {
        'client_id': ydb.PrimitiveType.Utf8,
        'client_name': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'short_name': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'contacts_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'tax_and_legal_info_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'payment_schedule_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'patents_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'tags_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'comment': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'manual_creation_date': ydb.PrimitiveType.Date,
        'is_actual': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'is_active': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'created_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
        'updated_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
    }
    
    processed_fields = []
    for key, value in payload.items():
        if key in type_map:
            is_date_type = False
            if isinstance(type_map[key], ydb.OptionalType):
                is_date_type = type_map[key].item == ydb.PrimitiveType.Date
            else:
                is_date_type = type_map[key] == ydb.PrimitiveType.Date
            if is_date_type and isinstance(value, str):
                try:
                    params[f"${key}"] = datetime.datetime.strptime(value, '%Y-%m-%d').date()
                    log_accumulator.append(f"[get_declare_clauses_and_params] Parsed date field '{key}': {value} -> {params[f'${key}']}")
                except ValueError:
                    log_accumulator.append(f"[get_declare_clauses_and_params] Error parsing date field '{key}': {value}")
                    raise LogicError(f"Invalid date format for '{key}'. Use YYYY-MM-DD.")
            else:
                params[f"${key}"] = value
                log_accumulator.append(f"[get_declare_clauses_and_params] Added field '{key}': {type(value).__name__}")
            type_name_str = str(type_map[key])
            if "Optional" in type_name_str:
                type_name = type_name_str.replace("Optional[", "").replace("]", "")
            else:
                type_name = type_name_str
            declare_clauses += f"DECLARE ${key} AS {type_name}; "
            processed_fields.append(key)
    
    log_accumulator.append(f"[get_declare_clauses_and_params] Processed fields: {processed_fields}")
    log_accumulator.append(f"[get_declare_clauses_and_params] Total parameters: {len(params)}")
    return declare_clauses, params

def upsert_client(session, table_name, payload, client_id, creation_date, roles, log_accumulator):
    log_accumulator.append(f"[upsert_client] Starting UPSERT operation with client_id={client_id}, creation_date={creation_date}, table={table_name}")
    log_accumulator.append(f"[upsert_client] User roles: {roles}")
    log_accumulator.append(f"[upsert_client] Payload: {payload}")
    
    if 'OWNER' not in roles and 'ADMIN' not in roles:
        log_accumulator.append(f"[upsert_client] Error: Insufficient permissions. User roles: {roles}")
        raise AuthError("Forbidden: Insufficient permissions for UPSERT action.")
    
    if not payload:
        log_accumulator.append("[upsert_client] Error: Empty payload")
        raise LogicError("Payload is required for UPSERT action.")
    
    log_accumulator.append("[upsert_client] Permission check passed")
    
    now = datetime.datetime.now(pytz.utc)
    payload['updated_at'] = now
    log_accumulator.append(f"[upsert_client] Added updated_at timestamp: {now}")
    
    tx = session.transaction(ydb.SerializableReadWrite())
    log_accumulator.append("[upsert_client] Started database transaction")
    
    if client_id and creation_date:
        log_accumulator.append("[upsert_client] Mode: Update existing client version")
        payload.pop('client_id', None)
        payload.pop('manual_creation_date', None)
        payload.pop('is_actual', None)
        if not payload:
            log_accumulator.append("[upsert_client] Error: No fields provided for update after cleanup")
            raise LogicError("No fields provided for update.")
        
        log_accumulator.append(f"[upsert_client] Fields to update: {list(payload.keys())}")
        set_clauses = ", ".join([f"{key} = ${key}" for key in payload.keys()])
        declare_clauses, params = get_declare_clauses_and_params(payload, log_accumulator)
        params['$client_id'] = client_id
        params['$creation_date'] = creation_date
        query_text = f""" {declare_clauses}
                        DECLARE $client_id AS Utf8; DECLARE $creation_date AS Date;
                        UPDATE `{table_name}` SET {set_clauses} WHERE client_id = $client_id AND manual_creation_date = $creation_date;"""
        log_accumulator.append(f"[upsert_client] Update query: {query_text}")
        tx.execute(session.prepare(query_text), params)
        tx.commit()
        log_accumulator.append("[upsert_client] Successfully updated existing client version")
        result = {"message": "Client version updated", "client_id": client_id, "creation_date": creation_date.isoformat() if creation_date else None}
        status_code = 200
    elif client_id and 'manual_creation_date' in payload:
        log_accumulator.append("[upsert_client] Mode: Create new version of existing client")
        new_creation_date_str = payload['manual_creation_date']
        log_accumulator.append(f"[upsert_client] New creation date: {new_creation_date_str}")
        try:
            new_creation_date = datetime.datetime.strptime(str(new_creation_date_str), '%Y-%m-%d').date()
            log_accumulator.append(f"[upsert_client] Parsed new creation date: {new_creation_date}")
        except (ValueError, TypeError) as e:
            log_accumulator.append(f"[upsert_client] Error parsing new creation date: {str(e)}")
            raise LogicError("Invalid date format for 'manual_creation_date' in payload.")
        
        find_old_query = f"""DECLARE $client_id AS Utf8;
                             SELECT manual_creation_date FROM `{table_name}` WHERE client_id = $client_id AND is_actual = true;"""
        log_accumulator.append(f"[upsert_client] Finding old actual version query: {find_old_query}")
        result_old = tx.execute(session.prepare(find_old_query), {'$client_id': client_id})
        if result_old and result_old[0].rows:
            old_creation_date = result_old[0].rows[0].manual_creation_date
            log_accumulator.append(f"[upsert_client] Found old actual version with date: {old_creation_date}")
            update_old_query = f"""DECLARE $client_id AS Utf8; DECLARE $old_date AS Date;
                                   UPDATE `{table_name}` SET is_actual = false WHERE client_id = $client_id AND manual_creation_date = $old_date;"""
            log_accumulator.append(f"[upsert_client] Setting old version to is_actual=false")
            tx.execute(session.prepare(update_old_query), {'$client_id': client_id, '$old_date': old_creation_date})
        else:
            log_accumulator.append("[upsert_client] No old actual version found")
        
        payload['client_id'] = client_id
        payload['is_actual'] = True
        payload['created_at'] = now
        log_accumulator.append("[upsert_client] Set new version as actual and added created_at")
        
        declare_clauses, params = get_declare_clauses_and_params(payload, log_accumulator)
        columns = ", ".join(payload.keys())
        placeholders = ", ".join([f"${key}" for key in payload.keys()])
        query_text = f"""{declare_clauses}
                        UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"""
        log_accumulator.append(f"[upsert_client] New version upsert query: {query_text}")
        tx.execute(session.prepare(query_text), params)
        tx.commit()
        log_accumulator.append("[upsert_client] Successfully created new client version")
        result = {"message": "New client version created", "client_id": client_id, "creation_date": new_creation_date_str}
        status_code = 200
    elif not client_id and 'manual_creation_date' in payload:
        log_accumulator.append("[upsert_client] Mode: Create new client")
        new_client_id = str(uuid.uuid4())
        log_accumulator.append(f"[upsert_client] Generated new client_id: {new_client_id}")
        payload['client_id'] = new_client_id
        payload['is_actual'] = True
        payload['is_active'] = True
        payload['created_at'] = now
        log_accumulator.append("[upsert_client] Set new client as actual and active")
        
        declare_clauses, params = get_declare_clauses_and_params(payload, log_accumulator)
        columns = ", ".join(payload.keys())
        placeholders = ", ".join([f"${key}" for key in payload.keys()])
        query_text = f"""{declare_clauses}
                        UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"""
        log_accumulator.append(f"[upsert_client] New client upsert query: {query_text}")
        tx.execute(session.prepare(query_text), params)
        tx.commit()
        log_accumulator.append(f"[upsert_client] Successfully created new client with id: {new_client_id}")
        result = {"message": "Client created", "client_id": new_client_id, "creation_date": payload['manual_creation_date']}
        status_code = 201
    else:
        log_accumulator.append("[upsert_client] Error: Invalid UPSERT combination")
        raise LogicError("Invalid UPSERT combination. Provide client_id and new manual_creation_date for a new version, or just manual_creation_date for a new client.")
    
    log_accumulator.append(f"[upsert_client] Operation completed with status: {status_code}")
    return {"statusCode": status_code, "body": json.dumps(result)}
```

#### delete.py
```python
import json
import ydb
import uuid
import pytz

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

def delete_client(session, table_name, client_id, creation_date, roles, log_accumulator):
    log_accumulator.append(f"[delete_client] Starting DELETE operation with client_id={client_id}, creation_date={creation_date}, table={table_name}")
    log_accumulator.append(f"[delete_client] User roles: {roles}")
    
    if 'OWNER' not in roles and 'ADMIN' not in roles:
        log_accumulator.append(f"[delete_client] Error: Insufficient permissions. User roles: {roles}")
        raise AuthError("Forbidden: Insufficient permissions for DELETE action.")
    if not client_id or not creation_date:
        log_accumulator.append(f"[delete_client] Error: Missing required parameters. client_id={client_id}, creation_date={creation_date}")
        raise LogicError("client_id and creation_date are required for DELETE action.")
    
    log_accumulator.append("[delete_client] Permission check passed")
    tx = session.transaction(ydb.SerializableReadWrite())
    log_accumulator.append("[delete_client] Started database transaction")
    check_query = f"""DECLARE $cid AS Utf8; DECLARE $cdate AS Date;
                      SELECT is_actual FROM `{table_name}` WHERE client_id = $cid AND manual_creation_date = $cdate;"""
    log_accumulator.append(f"[delete_client] Checking if version exists: {check_query}")
    result = tx.execute(session.prepare(check_query), {'$cid': client_id, '$cdate': creation_date})
    log_accumulator.append(f"[delete_client] Check query result: {len(result[0].rows) if result and result[0].rows else 0} rows found")
    if not result[0].rows:
        log_accumulator.append("[delete_client] Error: Version not found")
        raise NotFoundError("Client version not found.")
    is_version_actual = result[0].rows[0].is_actual
    log_accumulator.append(f"[delete_client] Version found. is_actual={is_version_actual}")
    if is_version_actual:
        log_accumulator.append("[delete_client] Version is actual, checking if it's the last version")
        count_query = f"""DECLARE $cid AS Utf8;
                          SELECT COUNT(client_id) AS total_versions FROM `{table_name}` WHERE client_id = $cid;"""
        log_accumulator.append(f"[delete_client] Count query: {count_query}")
        count_result = tx.execute(session.prepare(count_query), {'$cid': client_id})
        total_versions = count_result[0].rows[0].total_versions if count_result[0].rows else 0
        log_accumulator.append(f"[delete_client] Total versions for client: {total_versions}")
        if count_result[0].rows and count_result[0].rows[0].total_versions <= 1:
            log_accumulator.append("[delete_client] Error: Cannot delete the last actual version")
            raise LogicError("Cannot delete the last actual version of a client.")
    delete_query = f"""DECLARE $cid AS Utf8; DECLARE $cdate AS Date;
                       DELETE FROM `{table_name}` WHERE client_id = $cid AND manual_creation_date = $cdate;"""
    log_accumulator.append(f"[delete_client] Executing delete query: {delete_query}")
    tx.execute(session.prepare(delete_query), {'$cid': client_id, '$cdate': creation_date})
    log_accumulator.append("[delete_client] Version deleted successfully")
    if is_version_actual:
        log_accumulator.append("[delete_client] Deleted version was actual, finding latest remaining version")
        find_latest_query = f"""DECLARE $cid AS Utf8;
                                SELECT manual_creation_date FROM `{table_name}` WHERE client_id = $cid ORDER BY manual_creation_date DESC LIMIT 1;"""
        log_accumulator.append(f"[delete_client] Finding latest version query: {find_latest_query}")
        latest_result = tx.execute(session.prepare(find_latest_query), {'$cid': client_id})
        log_accumulator.append(f"[delete_client] Latest version search result: {len(latest_result[0].rows) if latest_result and latest_result[0].rows else 0} rows found")
        if latest_result and latest_result[0].rows:
            latest_date = latest_result[0].rows[0].manual_creation_date
            log_accumulator.append(f"[delete_client] Setting new actual version with creation_date: {latest_date}")
            update_latest_query = f"""DECLARE $cid AS Utf8; DECLARE $ldate AS Date;
                                      UPDATE `{table_name}` SET is_actual = true WHERE client_id = $cid AND manual_creation_date = $ldate;"""
            log_accumulator.append(f"[delete_client] Update actual query: {update_latest_query}")
            tx.execute(session.prepare(update_latest_query), {'$cid': client_id, '$ldate': latest_date})
            log_accumulator.append(f"[delete_client] Successfully set new actual version: {latest_date}")
        else:
            log_accumulator.append("[delete_client] No remaining versions found for this client")
    else:
        log_accumulator.append("[delete_client] Deleted version was not actual, no need to update actual flag")
    tx.commit()
    log_accumulator.append("[delete_client] Transaction committed successfully")
    result = {"message": "Client version deleted"}
    return {"statusCode": 200, "body": json.dumps(result)}
```
