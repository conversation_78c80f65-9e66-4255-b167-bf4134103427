Идентификатор - d4el1emddp7tcl893f1n
Описание - 🚪 Проверка JWT токена для аутентификации запросов к API Gateway

Точка входа - index.handler
Таймаут - 3 сек

---

На входе:
	-> Событие от API Gateway, содержащее заголовки запроса. Ожидается заголовок `Authorization: Bearer <token>`.

Внутренняя работа:
	-> Извлекает токен из заголовка `Authorization`.
	-> Использует `utils.auth_utils.verify_jwt` для проверки подписи и валидности токена.
	-> Формирует специальный ответ, который понимает API Gateway.

На выходе:
	-> Если токен валиден:
		`{"isAuthorized": true, "context": { "user_id": "...", "email": "..." }}`
		(API Gateway пропустит запрос к основной функции и добавит `context` к событию)
	-> Если токен невалиден или отсутствует:
		`{"isAuthorized": false}`
		(API Gateway немедленно вернет клиенту ошибку `403 Forbidden`)

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`
- **Переменные окружения**:
    - `JWT_SECRET` - Секретный ключ для валидации подписи JWT. Должен быть тем же, что и при генерации токена.

---

```python
import json
from utils import auth_utils

def handler(event, context):
    # API Gateway передает заголовки в нижнем регистре
    auth_header = event.get('headers', {}).get('authorization', '')
    
    if not auth_header.startswith('Bearer '):
        return {"isAuthorized": False}

    token = auth_header.split(' ')[1]
    payload = auth_utils.verify_jwt(token)
    
    if payload and 'user_id' in payload:
        # Успешная авторизация.
        # Передаем данные пользователя в контекст,
        # чтобы они были доступны в основной функции.
        return {
            "isAuthorized": True,
            "context": {
                "user_id": payload['user_id'],
                "email": payload.get('email', '')
            }
        }
    else:
        # Неуспешная авторизация
        return {"isAuthorized": False}
```