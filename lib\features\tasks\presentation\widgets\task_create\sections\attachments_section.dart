import 'package:flutter/material.dart';
import '../models.dart';

// ------------------ Прикреплённые файлы ------------------
class AttachmentsSection extends StatelessWidget {
  final List<AttachmentItem> attachments;
  final VoidCallback onAddAttachment;
  final Function(AttachmentItem) onRemoveAttachment;

  const AttachmentsSection({
    super.key,
    required this.attachments,
    required this.onAddAttachment,
    required this.onRemoveAttachment,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Прикрепленные файлы',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: onAddAttachment,
              icon: const Icon(Icons.add),
              label: const Text('Добавить'),
            ),
          ],
        ),
        ...attachments.map(
          (attachment) => Card(
            child: ListTile(
              title: Text(attachment.name),
              subtitle: Text(attachment.url),
              trailing: IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => onRemoveAttachment(attachment),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
