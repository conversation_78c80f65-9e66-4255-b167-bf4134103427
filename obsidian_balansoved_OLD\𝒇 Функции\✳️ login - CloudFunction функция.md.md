Идентификатор - d4e11h046o2u1eihlgf0
Описание - Войти в систему. Возвращает существующий токен или создает новый.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `email`: Email пользователя.
	-> `password`: Пароль пользователя.
Внутренняя работа:
	-> Находит в YDB пользователя по `email`.
	-> Проверяет, активен ли аккаунт (is_active).
	-> Сверяет хеш пароля.
	-> Обновляет `last_login_at`.
	-> **Проверяет, существует ли для пользователя `jwt_token` в базе данных.**
	-> **Если токен существует:** возвращает **существующий** токен.
	-> **Если токена нет:** генерирует новый JWT, сохраняет его в базу и возвращает **новый** токен.
На выходе:
	-> `200 OK`: {"token": "<jwt_token>"}
	-> `401 Unauthorized`: {"message": "Invalid credentials."}
	-> `423 Locked`: {"message": "Account not confirmed. Please verify your email."}

---
#### Зависимости и окружение
- **Необходимые утилиты**: utils/auth_utils.py, utils/ydb_utils.py
- **Переменные окружения**:
    - YDB_ENDPOINT - Эндпоинт базы данных ([[🗃️ Структура YDB]])
    - YDB_DATABASE - Путь к базе данных ([[🗃️ Структура YDB]])
    - SA_KEY_FILE - ydb_sa_key.json ([[ydb_sa_key.json]])
    - JWT_SECRET - Надежная секретная строка (Генерируется пользователем)

---

#### Версия кода, с сырым, логированием

```python
import json
import os
import datetime
import pytz
import logging
import ydb
from utils import ydb_utils, auth_utils, request_parser

logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    logging.info("--- NEW INVOCATION ---")
    logging.info(f"RAW EVENT: {event}")

    try:
        data = request_parser.parse_request_body(event)
    except ValueError as e:
        logging.error(f"Request body processing error: {e}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}

    email = data.get('email')
    password = data.get('password')

    if not all([email, password]):
        logging.error("Email or password not provided in the parsed data.")
        return {"statusCode": 400, "body": json.dumps({"message": "Email and password are required."})}

    driver = ydb_utils.get_ydb_driver()
    pool = ydb.SessionPool(driver)

    def transaction(session):
        tx = session.transaction(ydb.SerializableReadWrite())

        select_query_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8;
            SELECT user_id, password_hash, jwt_token, is_active FROM users WHERE email = $email;
        """
        prepared_select = session.prepare(select_query_text)
        result_sets = tx.execute(prepared_select, {'$email': email})

        if not result_sets[0].rows:
            tx.rollback()
            return {"status": 401}

        user_data = result_sets[0].rows[0]

        # Проверяем подтверждение аккаунта
        if not user_data.is_active:
            tx.rollback()
            return {"status": 423}

        if not auth_utils.verify_password(password, user_data.password_hash):
            tx.rollback()
            return {"status": 401}

        now = datetime.datetime.now(pytz.utc)
        user_id = user_data.user_id

        update_login_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $user_id AS Utf8; DECLARE $now AS Timestamp;
            UPDATE users SET last_login_at = $now WHERE user_id = $user_id;
        """
        prepared_update_login = session.prepare(update_login_text)
        tx.execute(prepared_update_login, {'$user_id': user_id, '$now': now})

        if user_data.jwt_token:
            logging.info(f"Returning existing token for user {email}")
            tx.commit()
            return {"status": 200, "token": user_data.jwt_token}
        else:
            logging.info(f"Generating new token for user {email}")
            new_token = auth_utils.generate_jwt(user_id, email)
            
            update_token_text = f"""
                PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $user_id AS Utf8; DECLARE $token AS Utf8;
                UPDATE users SET jwt_token = $token WHERE user_id = $user_id;
            """
            prepared_update_token = session.prepare(update_token_text)
            tx.execute(prepared_update_token, {'$user_id': user_id, '$token': new_token})
            
            tx.commit()
            return {"status": 200, "token": new_token}

    try:
        result = pool.retry_operation_sync(transaction)
        if isinstance(result, dict):
            status = result.get("status")
            if status == 200:
                logging.info(f"Login successful for user {email}.")
                return {"statusCode": 200, "body": json.dumps({"token": result["token"]})}
            elif status == 423:
                logging.warning(f"Attempt to login before confirmation for {email}.")
                return {"statusCode": 423, "body": json.dumps({"message": "Account not confirmed. Please verify your email."})}
            else: # 401
                logging.warning(f"Invalid credentials attempt for user {email}.")
                return {"statusCode": 401, "body": json.dumps({"message": "Invalid credentials."})}
        else:
            return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
    except Exception as e:
        logging.error(f"Critical error during login transaction for user {email}: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```

```python
import json, os, datetime, pytz, logging
import ydb
from utils import ydb_utils, auth_utils

logging.basicConfig(level=logging.INFO)

def handler(event, context):
    body = json.loads(event.get('body', '{}'))
    email = body.get('email')
    password = body.get('password')

    if not all([email, password]):
        return {"statusCode": 400, "body": json.dumps({"message": "Email and password are required."})}

    driver = ydb_utils.get_ydb_driver()
    pool = ydb.SessionPool(driver)

    def transaction(session):
        # Создаем явный объект транзакции
        tx = session.transaction(ydb.SerializableReadWrite())

        # Шаг 1: Ищем пользователя
        select_query_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8;
            SELECT user_id, password_hash, jwt_token, is_active FROM users WHERE email = $email;
        """
        prepared_select = session.prepare(select_query_text)
        result_sets = tx.execute(prepared_select, {'$email': email})

        if not result_sets[0].rows:
            tx.rollback()
            return {"status": 401}

        user_data = result_sets[0].rows[0]

        # Проверяем подтверждение аккаунта
        if not user_data.is_active:
            tx.rollback()
            return {"status": 423}

        # Проверяем пароль
        if not auth_utils.verify_password(password, user_data.password_hash):
            tx.rollback()
            return {"status": 401}

        now = datetime.datetime.now(pytz.utc)
        user_id = user_data.user_id

        # Шаг 2: Обновляем дату входа
        update_login_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $user_id AS Utf8; DECLARE $now AS Timestamp;
            UPDATE users SET last_login_at = $now WHERE user_id = $user_id;
        """
        prepared_update_login = session.prepare(update_login_text)
        tx.execute(prepared_update_login, {'$user_id': user_id, '$now': now})

        # Шаг 3: Логика с токеном
        if user_data.jwt_token:
            logging.info(f"Returning existing token for user {email}")
            tx.commit()
            return {"status": 200, "token": user_data.jwt_token}
        else:
            logging.info(f"Generating new token for user {email}")
            new_token = auth_utils.generate_jwt(user_id, email)
            
            update_token_text = f"""
                PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $user_id AS Utf8; DECLARE $token AS Utf8;
                UPDATE users SET jwt_token = $token WHERE user_id = $user_id;
            """
            prepared_update_token = session.prepare(update_token_text)
            tx.execute(prepared_update_token, {'$user_id': user_id, '$token': new_token})
            
            tx.commit()
            return {"status": 200, "token": new_token}

    try:
        result = pool.retry_operation_sync(transaction)
        if isinstance(result, dict):
            status = result.get("status")
            if status == 200:
                logging.info(f"Login successful for user {email}.")
                return {"statusCode": 200, "body": json.dumps({"token": result["token"]})}
            elif status == 423:
                logging.warning(f"Attempt to login before confirmation for {email}.")
                return {"statusCode": 423, "body": json.dumps({"message": "Account not confirmed. Please verify your email."})}
            else: # 401
                logging.warning(f"Invalid credentials attempt for user {email}.")
                return {"statusCode": 401, "body": json.dumps({"message": "Invalid credentials."})}
        else:
            return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
    except Exception as e:
        logging.error(f"Error during login: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```