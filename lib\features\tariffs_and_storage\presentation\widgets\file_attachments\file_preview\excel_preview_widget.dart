import 'dart:async';

import 'package:excel/excel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;

import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class ExcelPreviewWidget extends StatefulWidget {
  final String fileKey;
  final String fileName;

  const ExcelPreviewWidget({
    super.key,
    required this.fileKey,
    required this.fileName,
  });

  @override
  State<ExcelPreviewWidget> createState() => _ExcelPreviewWidgetState();
}

class _ExcelPreviewWidgetState extends State<ExcelPreviewWidget> {
  bool _isLoading = true;
  String? _errorMessage;
  Excel? _workbook;

  @override
  void initState() {
    super.initState();
    _loadExcel();
  }

  Future<void> _loadExcel() async {
    try {
      final downloadUrl = await _getDownloadUrl();
      if (downloadUrl == null) {
        if (mounted) {
          setState(() {
            _errorMessage = 'Не удалось получить ссылку для предпросмотра';
            _isLoading = false;
          });
        }
        return;
      }

      final response = await http.get(Uri.parse(downloadUrl));
      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}');
      }

      final bytes = response.bodyBytes;
      final excel = Excel.decodeBytes(bytes);

      if (mounted) {
        setState(() {
          _workbook = excel;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Ошибка загрузки файла: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<String?> _getDownloadUrl() async {
    final storageCubit = context.read<TariffsAndStorageCubit>();
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) return null;

    final firmId = firmState.selectedFirm!.id;
    final completer = Completer<String?>();
    late StreamSubscription sub;

    sub = storageCubit.stream.listen((state) {
      if (state is FileDownloadUrlReady && state.fileKey == widget.fileKey) {
        if (!completer.isCompleted) completer.complete(state.downloadUrl);
        sub.cancel();
      } else if (state is TariffsAndStorageError) {
        if (!completer.isCompleted) completer.complete(null);
        sub.cancel();
      }
    });

    storageCubit.getDownloadUrl(firmId: firmId, fileKey: widget.fileKey);

    Future.delayed(const Duration(seconds: 15), () {
      if (!completer.isCompleted) {
        completer.complete(null);
        sub.cancel();
      }
    });

    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_errorMessage != null) {
      return Center(
        child: Text(
          _errorMessage!,
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      );
    }
    if (_workbook == null) {
      return const Center(child: Text('Не удалось загрузить таблицу.'));
    }

    final sheetNames = _workbook!.sheets.keys.toList();

    return DefaultTabController(
      length: sheetNames.length,
      child: Column(
        children: [
          Material(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: TabBar(
              isScrollable: true,
              labelColor: Theme.of(context).colorScheme.onSurfaceVariant,
              tabs: [for (final name in sheetNames) Tab(text: name)],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                for (final name in sheetNames)
                  _SheetView(sheet: _workbook![name]),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SheetView extends StatelessWidget {
  final Sheet sheet;
  const _SheetView({required this.sheet});

  @override
  Widget build(BuildContext context) {
    // Найти максимальное количество колонок и строк с данными
    final maxRow = sheet.maxRows;
    final maxCol = sheet.maxCols;

    List<DataRow> rows = [];
    for (int r = 0; r < maxRow; r++) {
      List<DataCell> cells = [];
      for (int c = 0; c < maxCol; c++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: c, rowIndex: r),
        );
        String text = cell.value?.toString() ?? '';
        cells.add(DataCell(Text(text)));
      }
      rows.add(DataRow(cells: cells));
    }

    return InteractiveViewer(
      maxScale: 5,
      minScale: 0.5,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: [
            for (int c = 0; c < maxCol; c++)
              DataColumn(label: Text(String.fromCharCode(65 + c))),
          ],
          rows: rows,
        ),
      ),
    );
  }
}
