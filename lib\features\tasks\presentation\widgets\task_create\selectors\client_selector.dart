import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../clients/presentation/cubit/clients_cubit.dart';
import '../../../../../clients/domain/entities/client_entity.dart';

// Виджет выбора клиентов
class ClientSelector extends StatelessWidget {
  final List<String> selectedClientIds;
  final void Function(String, bool) onClientChanged;
  const ClientSelector({
    super.key,
    required this.selectedClientIds,
    required this.onClientChanged,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ClientsCubit, ClientsState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const _Loading(title: 'Клиенты');
        }

        final selectedClients =
            state.clients
                .where((c) => selectedClientIds.contains(c.id))
                .toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Клиенты',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () => _openDialog(context, state.clients),
                  icon: const Icon(Icons.add),
                  label: const Text('Выбрать'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (selectedClients.isEmpty)
              const Text(
                'Клиенты не выбраны',
                style: TextStyle(color: Colors.grey),
              )
            else
              _SelectedList<ClientEntity>(
                items: selectedClients,
                titleBuilder: (c) => c.name,
                subtitleBuilder: (c) => c.inn ?? '',
                onRemove: (c) => onClientChanged(c.id, false),
              ),
          ],
        );
      },
    );
  }

  void _openDialog(BuildContext context, List<ClientEntity> all) {
    showDialog(
      context: context,
      builder:
          (_) => _ClientSelectionDialog(
            allClients: all,
            selectedIds: selectedClientIds,
            onSelectionChanged: onClientChanged,
          ),
    );
  }
}

// ---------- Dialog ----------
class _ClientSelectionDialog extends StatefulWidget {
  final List<ClientEntity> allClients;
  final List<String> selectedIds;
  final void Function(String, bool) onSelectionChanged;
  const _ClientSelectionDialog({
    required this.allClients,
    required this.selectedIds,
    required this.onSelectionChanged,
  });

  @override
  State<_ClientSelectionDialog> createState() => _ClientSelectionDialogState();
}

class _ClientSelectionDialogState extends State<_ClientSelectionDialog> {
  String _search = '';
  int _sortIndex = 0;
  bool _asc = true;
  late List<String> _currentSelectedIds;

  @override
  void initState() {
    super.initState();
    _currentSelectedIds = List.from(widget.selectedIds);
  }

  @override
  Widget build(BuildContext context) {
    List<ClientEntity> data =
        widget.allClients.where((c) {
          final s = _search.toLowerCase();
          return c.name.toLowerCase().contains(s) ||
              (c.inn?.toLowerCase().contains(s) ?? false);
        }).toList();

    data.sort((a, b) {
      int res;
      switch (_sortIndex) {
        case 0:
          res = a.name.compareTo(b.name);
          break;
        case 1:
          res = (a.inn ?? '').compareTo(b.inn ?? '');
          break;
        default:
          res = 0;
      }
      return _asc ? res : -res;
    });

    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Выбор клиентов',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Поиск',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (v) => setState(() => _search = v),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: DataTable(
                  sortColumnIndex: _sortIndex,
                  sortAscending: _asc,
                  columns: [
                    DataColumn(
                      label: const Text('Название'),
                      onSort:
                          (i, asc) => setState(() {
                            _sortIndex = i;
                            _asc = asc;
                          }),
                    ),
                    DataColumn(
                      label: const Text('ИНН'),
                      onSort:
                          (i, asc) => setState(() {
                            _sortIndex = i;
                            _asc = asc;
                          }),
                    ),
                    const DataColumn(label: Text('')),
                  ],
                  rows:
                      data.map((c) {
                        final sel = _currentSelectedIds.contains(c.id);
                        return DataRow(
                          cells: [
                            DataCell(
                              SizedBox(
                                width: 200,
                                child: Text(
                                  c.name,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ),
                            DataCell(Text(c.inn ?? '')),
                            DataCell(
                              IconButton(
                                icon: Icon(
                                  sel ? Icons.remove_circle : Icons.add_circle,
                                  color: sel ? Colors.red : Colors.green,
                                ),
                                onPressed: () {
                                  setState(() {
                                    if (sel) {
                                      _currentSelectedIds.remove(c.id);
                                    } else {
                                      _currentSelectedIds.add(c.id);
                                    }
                                  });
                                  widget.onSelectionChanged(c.id, !sel);
                                },
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomRight,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Закрыть'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ---------- Reusable small widgets ----------
class _Loading extends StatelessWidget {
  final String title;
  const _Loading({required this.title});
  @override
  Widget build(BuildContext context) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      const SizedBox(height: 8),
      const Text('Загрузка...'),
    ],
  );
}

class _SelectedList<T> extends StatelessWidget {
  final List<T> items;
  final String Function(T) titleBuilder;
  final String Function(T) subtitleBuilder;
  final void Function(T) onRemove;
  const _SelectedList({
    required this.items,
    required this.titleBuilder,
    required this.subtitleBuilder,
    required this.onRemove,
  });
  @override
  Widget build(BuildContext context) => Container(
    decoration: BoxDecoration(
      border: Border.all(color: Colors.grey.shade300),
      borderRadius: BorderRadius.circular(8),
    ),
    child: Column(
      children:
          items
              .map(
                (e) => ListTile(
                  title: Text(titleBuilder(e)),
                  subtitle: Text(subtitleBuilder(e)),
                  trailing: IconButton(
                    icon: const Icon(Icons.remove_circle_outline),
                    onPressed: () => onRemove(e),
                  ),
                ),
              )
              .toList(),
    ),
  );
}
