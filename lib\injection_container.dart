import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_local_data_source.dart';
import 'package:balansoved_enterprise/features/auth/data/repositories/auth_local_data_source_impl.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_remote_data_source.dart';
import 'package:balansoved_enterprise/features/auth/data/repositories/auth_remote_data_source_impl.dart';
import 'package:balansoved_enterprise/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:balansoved_enterprise/features/auth/domain/repositories/auth_repository.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/login_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/register_request_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/refresh_token_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/sign_out_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/check_auth_status_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/accept_invitation_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/confirm_registration_usecase.dart';
import 'package:balansoved_enterprise/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/refresh_token_cubit.dart';
import 'package:balansoved_enterprise/features/profile/domain/usecases/get_profile_usecase.dart';
import 'package:balansoved_enterprise/features/profile/domain/repositories/profile_repository.dart';
import 'package:balansoved_enterprise/features/profile/data/repositories/profile_repository_impl.dart';
import 'package:balansoved_enterprise/features/profile/data/data_source/user_data_remote_data_source.dart';
import 'package:balansoved_enterprise/features/profile/data/repositories/user_data_remote_data_source_impl.dart';
import 'router.dart'; // Оставляем, хотя он пока с ошибками
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/employees_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/get_employees_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/employees_repository.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/repositories/employees_repository_impl.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/data_source/employees_remote_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/repositories/employees_remote_data_source_impl.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/create_employee_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/invite_employee_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/add_role_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/remove_role_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/delete_employee_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/create_firm_usecase.dart';

// Для веба нужен специальный импорт
import 'package:dio_web_adapter/dio_web_adapter.dart' if (dart.library.io) '';

// === Clients feature ===
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_versions_cubit.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_clients_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_client_versions_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/upsert_client_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/delete_client_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/delete_all_client_versions_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/clients_repository.dart';
import 'package:balansoved_enterprise/features/clients/data/repositories/clients_repository_impl.dart';
import 'package:balansoved_enterprise/features/clients/data/data_source/clients_remote_data_source.dart';
import 'package:balansoved_enterprise/features/clients/data/repositories/clients_remote_data_source_impl.dart';

// === Client Payments feature ===
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_payments_cubit.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_client_payments_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/upsert_client_payment_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/delete_client_payment_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/client_payments_repository.dart';
import 'package:balansoved_enterprise/features/clients/data/repositories/client_payments_repository_impl.dart';
import 'package:balansoved_enterprise/features/clients/data/data_source/client_payments_remote_data_source.dart';
import 'package:balansoved_enterprise/features/clients/data/repositories/client_payments_remote_data_source_impl.dart';

// === Tasks feature ===
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/task_detail_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/get_tasks_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/get_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/save_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/delete_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/repositories/tasks_repository.dart';
import 'package:balansoved_enterprise/features/tasks/data/repositories/tasks_repository_impl.dart';
import 'package:balansoved_enterprise/features/tasks/data/data_source/tasks_remote_data_source.dart';
import 'package:balansoved_enterprise/features/tasks/data/repositories/tasks_remote_data_source_impl.dart';

// === Calendar feature ===
import 'package:balansoved_enterprise/features/calendar/presentation/cubit/calendar_cubit.dart';

// === Tariffs and Storage feature ===
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/domain/usecases/get_storage_info_usecase.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/domain/usecases/check_space_available_usecase.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/domain/usecases/upload_file_usecase.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/domain/usecases/download_file_usecase.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/domain/usecases/delete_file_usecase.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/domain/repositories/tariffs_and_storage_repository.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/data/repositories/tariffs_and_storage_repository_impl.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/data/data_source/tariffs_and_storage_remote_data_source.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/data/repositories/tariffs_and_storage_remote_data_source_impl.dart';
// === Integrations (Yandex Disk backup) feature ===
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/backup_integration_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/create_firm_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/data_source/integrations_remote_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/repositories/integrations_remote_data_source_impl.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/integrations_repository.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/repositories/integrations_repository_impl.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/get_backup_status_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/set_backup_enabled_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/save_yadisk_token_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/data_source/scheduler_remote_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/repositories/scheduler_remote_data_source_impl.dart';

// === Notifications feature ===
import 'package:balansoved_enterprise/features/notifications/presentation/cubit/notifications_cubit.dart';
import 'package:balansoved_enterprise/features/notifications/domain/usecases/get_notifications.dart';
import 'package:balansoved_enterprise/features/notifications/domain/usecases/mark_as_delivered.dart';
import 'package:balansoved_enterprise/features/notifications/domain/repositories/notifications_repository.dart';
import 'package:balansoved_enterprise/features/notifications/data/repositories/notifications_repository_impl.dart';
import 'package:balansoved_enterprise/features/notifications/data/data_source/notifications_remote_data_source.dart';

import 'dart:convert';

// Переименовываем locator в sl для краткости, как часто принято
final sl = GetIt.instance;

/// Функция для декодирования Unicode escape-последовательностей в нормальные русские символы
String decodeUnicodeString(String input) {
  try {
    // Заменяем \uXXXX на соответствующие символы
    return input.replaceAllMapped(RegExp(r'\\u([0-9a-fA-F]{4})'), (
      Match match,
    ) {
      final hexCode = match.group(1)!;
      final codeUnit = int.parse(hexCode, radix: 16);
      return String.fromCharCode(codeUnit);
    });
  } catch (e) {
    // Если декодирование не удалось, возвращаем исходную строку
    return input;
  }
}

/// Функция для красивого форматирования JSON с русскими символами
String formatJsonWithCyrillic(dynamic json) {
  try {
    String jsonString;
    if (json is String) {
      // Если уже строка, пытаемся распарсить как JSON
      try {
        final parsed = jsonDecode(json);
        jsonString = const JsonEncoder.withIndent('  ').convert(parsed);
      } catch (_) {
        // Если не JSON, возвращаем как есть
        jsonString = json;
      }
    } else {
      // Конвертируем объект в красивый JSON
      jsonString = const JsonEncoder.withIndent('  ').convert(json);
    }

    // Декодируем Unicode escape-последовательности
    return decodeUnicodeString(jsonString);
  } catch (e) {
    return json.toString();
  }
}

// Делаем функцию асинхронной для SharedPreferences
Future<void> setupLocator() async {
  // === External ===
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerSingleton<SharedPreferences>(sharedPreferences);

  // Настраиваем Dio для Web с CORS поддержкой и детальным логированием
  final dio = Dio();
  dio.options.connectTimeout = const Duration(seconds: 15);
  dio.options.receiveTimeout = const Duration(
    minutes: 5,
  ); // Увеличиваем для загрузки файлов
  dio.options.sendTimeout = const Duration(
    minutes: 5,
  ); // Добавляем sendTimeout для загрузки
  dio.options.headers['Content-Type'] = 'application/json; charset=utf-8';

  // Добавляем детальное логирование для отладки с правильной кодировкой
  if (kDebugMode) {
    dio.interceptors.add(
      LogInterceptor(
        request: true,
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        error: true,
        logPrint: (obj) {
          // --- Фильтрация «мусора»: не выводим огромные числовые массивы (например, байты файла) ---
          // Если передан List<int> – это наверняка бинарные данные, выводим лишь короткую справку
          if (obj is List) {
            print('🐛 [Binary/List data omitted | length: ${obj.length}]');
            return;
          }

          // Приводим к строке для дальнейшего анализа
          final rawMessage = obj.toString();

          // Если строка выглядит как большой массив чисел – пропускаем подробный вывод
          final bool looksLikeNumericArray =
              rawMessage.length > 300 && // достаточно длинная
              rawMessage.startsWith('[') &&
              RegExp(r'^[\d,\s\[\]]+$').hasMatch(rawMessage);

          if (looksLikeNumericArray) {
            print(
              '🐛 [Large numeric array omitted | ${rawMessage.length} chars]',
            );
            return;
          }

          // Декодируем Unicode escape-последовательности для русских символов
          final decodedMessage = decodeUnicodeString(obj.toString());

          // Если это JSON, форматируем его красиво
          String formattedMessage = decodedMessage;
          if (decodedMessage.contains('{') || decodedMessage.contains('[')) {
            try {
              // Пытаемся извлечь JSON из сообщения
              final jsonStart = decodedMessage.indexOf('{');
              final jsonStartArray = decodedMessage.indexOf('[');
              int actualStart = -1;

              if (jsonStart != -1 && jsonStartArray != -1) {
                actualStart =
                    jsonStart < jsonStartArray ? jsonStart : jsonStartArray;
              } else if (jsonStart != -1) {
                actualStart = jsonStart;
              } else if (jsonStartArray != -1) {
                actualStart = jsonStartArray;
              }

              if (actualStart != -1) {
                final prefix = decodedMessage.substring(0, actualStart);
                final jsonPart = decodedMessage.substring(actualStart);

                // Пытаемся распарсить и красиво отформатировать JSON
                final formatted = formatJsonWithCyrillic(jsonPart);
                formattedMessage = prefix + formatted;
              }
            } catch (e) {
              // Если форматирование не удалось, используем декодированную версию
              formattedMessage = decodedMessage;
            }
          }

          print('🐛 $formattedMessage');
        },
      ),
    );
  }

  // Для Flutter Web используем специальный адаптер
  if (kIsWeb) {
    dio.httpClientAdapter = BrowserHttpClientAdapter(withCredentials: false);
    debugPrint('🌐 [SETUP] Настроен браузерный адаптер для Dio');
  } else {
    debugPrint('🌐 [SETUP] Используется стандартный адаптер Dio');
  }

  sl.registerSingleton<Dio>(dio);

  // Также регистрируем http.Client как fallback
  sl.registerLazySingleton(() => http.Client());

  // === Core ===
  sl.registerSingleton<AppRouter>(AppRouter());
  // Возможно, сюда добавить NetworkInfo, если будем проверять сеть

  // === Features ===
  _registerAuthFeature();
  _registerProfileFeature();
  _registerEmployeesFeature();
  _registerClientsFeature();
  _registerCalendarFeature(); // Calendar должен быть зарегистрирован перед Tasks
  _registerTasksFeature();
  _registerTariffsAndStorageFeature();
  _registerIntegrationsFeature(); // Добавляем регистрацию для Integrations
  _registerNotificationsFeature(); // Добавляем регистрацию для Notifications

  debugPrint('✅ [SETUP] Все зависимости зарегистрированы');
}

void _registerAuthFeature() {
  debugPrint('🔧 [SETUP] Регистрируем зависимости Auth feature');

  // --- Presentation ---
  // Cubit (Singleton)
  sl.registerLazySingleton(
    () => AuthCubit(
      loginUseCase: sl(),
      registerRequestUseCase: sl(),
      refreshTokenUseCase: sl(),
      signOutUseCase: sl(),
      checkAuthStatusUseCase: sl(),
      confirmRegistrationUseCase: sl(),
      acceptInvitationUseCase: sl(),
    ),
  );

  // --- Domain ---
  // Use Cases (LazySingleton)
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => RegisterRequestUseCase(sl()));
  sl.registerLazySingleton(() => RefreshTokenUseCase(sl()));
  sl.registerLazySingleton(() => AcceptInvitationUseCase(sl()));
  sl.registerLazySingleton(() => SignOutUseCase(sl()));
  sl.registerLazySingleton(() => CheckAuthStatusUseCase(sl()));
  sl.registerLazySingleton(() => ConfirmRegistrationUseCase(sl()));

  // Repository (LazySingleton)
  sl.registerLazySingleton<IAuthRepository>(
    () => AuthRepositoryImpl(
      remote: sl(),
      local: sl(),
      // connectionChecker: sl(), // Если добавим проверку сети
    ),
  );

  // --- Data ---
  // Data Sources (LazySingleton)
  sl.registerLazySingleton<IAuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      // Для web используем http.Client, для мобильных Dio
      httpClient: kIsWeb ? sl<http.Client>() : null,
      dio: kIsWeb ? null : sl<Dio>(),
    ),
  );
  sl.registerLazySingleton<IAuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(prefs: sl()),
  );

  debugPrint('✅ [SETUP] Auth feature зарегистрирована');
}

void _registerProfileFeature() {
  debugPrint('🔧 [SETUP] Регистрируем зависимости Profile feature');

  // --- Presentation ---
  sl.registerLazySingleton(() => ActiveFirmCubit());
  sl.registerLazySingleton(() => ProfileCubit(sl(), sl()));
  sl.registerLazySingleton(() => RefreshTokenCubit(refreshTokenUseCase: sl()));

  // --- Domain ---
  sl.registerLazySingleton(() => GetProfileUseCase(sl()));

  // --- Data ---
  sl.registerLazySingleton<IUserDataRemoteDataSource>(
    () => UserDataRemoteDataSourceImpl(
      httpClient: kIsWeb ? sl<http.Client>() : null,
      dio: kIsWeb ? null : sl<Dio>(),
    ),
  );

  sl.registerLazySingleton<IProfileRepository>(
    () => ProfileRepositoryImpl(remote: sl(), localAuth: sl()),
  );

  debugPrint('✅ [SETUP] Profile feature зарегистрирована');
}

void _registerEmployeesFeature() {
  debugPrint('🔧 [SETUP] Регистрируем зависимости Employees feature');

  // --- Presentation ---
  sl.registerLazySingleton(
    () => EmployeesCubit(
      getEmployeesUseCase: sl(),
      createEmployeeUseCase: sl(),
      inviteEmployeeUseCase: sl(),
      addRoleUseCase: sl(),
      removeRoleUseCase: sl(),
      deleteEmployeeUseCase: sl(),
    ),
  );

  // --- Domain ---
  sl.registerLazySingleton(() => GetEmployeesUseCase(sl()));
  sl.registerLazySingleton(() => CreateEmployeeUseCase(sl()));
  sl.registerLazySingleton(() => InviteEmployeeUseCase(sl()));
  sl.registerLazySingleton(() => AddRoleUseCase(sl()));
  sl.registerLazySingleton(() => RemoveRoleUseCase(sl()));
  sl.registerLazySingleton(() => DeleteEmployeeUseCase(sl()));
  sl.registerLazySingleton(() => CreateFirmUseCase(sl()));

  // --- Presentation Cubit for creating firm ---
  sl.registerLazySingleton(() => CreateFirmCubit(useCase: sl()));

  // --- Data ---
  sl.registerLazySingleton<IEmployeesRemoteDataSource>(
    () => EmployeesRemoteDataSourceImpl(
      httpClient: kIsWeb ? sl<http.Client>() : null,
      dio: kIsWeb ? null : sl<Dio>(),
    ),
  );

  sl.registerLazySingleton<IEmployeesRepository>(
    () => EmployeesRepositoryImpl(remote: sl(), localAuth: sl()),
  );

  debugPrint('✅ [SETUP] Employees feature зарегистрирована');
}

void _registerClientsFeature() {
  debugPrint('🔧 [SETUP] Регистрируем зависимости Clients feature');

  // --- Presentation ---
  sl.registerLazySingleton(
    () => ClientsCubit(
      getClientsUseCase: sl(),
      getClientVersionsUseCase: sl(),
      upsertClientUseCase: sl(),
      deleteClientUseCase: sl(),
      deleteAllClientVersionsUseCase: sl(),
    ),
  );

  sl.registerFactory(
    () => ClientVersionsCubit(
      getClientVersionsUseCase: sl(),
      deleteClientUseCase: sl(),
      upsertClientUseCase: sl(),
    ),
  );

  // --- Domain ---
  sl.registerLazySingleton(() => GetClientsUseCase(sl()));
  sl.registerLazySingleton(() => GetClientVersionsUseCase(sl()));
  sl.registerLazySingleton(() => UpsertClientUseCase(sl()));
  sl.registerLazySingleton(() => DeleteClientUseCase(sl()));
  sl.registerLazySingleton(() => DeleteAllClientVersionsUseCase(sl()));

  // === Client Payments ===
  // --- Presentation ---
  sl.registerLazySingleton(
    () => ClientPaymentsCubit(
      getClientsUseCase: sl(),
      getClientPaymentsUseCase: sl(),
      upsertClientPaymentUseCase: sl(),
      deleteClientPaymentUseCase: sl(),
    ),
  );

  // --- Domain ---
  sl.registerLazySingleton(() => GetClientPaymentsUseCase(repository: sl()));
  sl.registerLazySingleton(() => UpsertClientPaymentUseCase(repository: sl()));
  sl.registerLazySingleton(() => DeleteClientPaymentUseCase(repository: sl()));

  // --- Data ---
  sl.registerLazySingleton<IClientsRemoteDataSource>(
    () => ClientsRemoteDataSourceImpl(
      httpClient: kIsWeb ? sl<http.Client>() : null,
      dio: kIsWeb ? null : sl<Dio>(),
    ),
  );

  sl.registerLazySingleton<IClientsRepository>(
    () => ClientsRepositoryImpl(remote: sl(), localAuth: sl()),
  );

  // --- Data ---
  sl.registerLazySingleton<IClientPaymentsRemoteDataSource>(
    () => ClientPaymentsRemoteDataSourceImpl(
      httpClient: kIsWeb ? sl<http.Client>() : null,
      dio: kIsWeb ? null : sl<Dio>(),
    ),
  );

  sl.registerLazySingleton<IClientPaymentsRepository>(
    () => ClientPaymentsRepositoryImpl(remoteDataSource: sl(), localAuth: sl()),
  );

  debugPrint('✅ [SETUP] Clients feature зарегистрирована');
}

void _registerTasksFeature() {
  debugPrint('🔧 [SETUP] Регистрируем зависимости Tasks feature');

  // --- Presentation ---
  sl.registerLazySingleton(
    () => TasksCubit(
      getTasksUseCase: sl(),
      getTaskUseCase: sl(),
      saveTaskUseCase: sl(),
      deleteTaskUseCase: sl(),
      calendarCubit: sl(),
      clientsCubit: sl(),
    ),
  );

  sl.registerFactory(
    () => TaskDetailCubit(getEmployeesUseCase: sl(), getClientsUseCase: sl()),
  );

  // --- Domain ---
  sl.registerLazySingleton(() => GetTasksUseCase(sl()));
  sl.registerLazySingleton(() => GetTaskUseCase(sl()));
  sl.registerLazySingleton(() => SaveTaskUseCase(sl()));
  sl.registerLazySingleton(() => DeleteTaskUseCase(sl()));

  // --- Data ---
  sl.registerLazySingleton<TasksRepository>(
    () => TasksRepositoryImpl(remoteDataSource: sl()),
  );

  sl.registerLazySingleton<TasksRemoteDataSource>(
    () => TasksRemoteDataSourceImpl(
      client: sl(),
      getToken: () {
        String? token;
        // Этот вызов должен быть синхронным, если возможно.
        // Если getAccessToken асинхронный, это создаст проблемы.
        // Предполагая, что у нас есть способ получить токен синхронно,
        // или нам нужно переработать то, как мы передаем токен.
        // В данном случае, так как Cubit'ы создаются лениво,
        // токен будет запрашиваться только при первом вызове.
        // Но сам вызов getAccessToken - асинхронный.
        // Это плохая практика.
        // Правильным решением было бы передавать токен в методы DataSource.
        // Но для быстрого исправления, мы можем сделать так,
        // но это может привести к гонкам состояний.
        // Давайте пока оставим как есть, но это нужно будет исправить.
        // Поскольку у нас нет синхронного метода, оставляем как было.
        // В идеале, метод getToken должен быть синхронным,
        // например, хранить токен в переменной в AuthCubit.
        // Но мы не будем сейчас рефакторить AuthCubit.
        // Мы просто сделаем вызов синхронным, хотя это и неверно.
        // Это "заглушка" для компиляции.

        // Вернемся к исходной реализации, так как она была рабочей
        return sl<SharedPreferences>().getString('access_token');
      },
    ),
  );

  debugPrint('✅ [SETUP] Tasks feature зарегистрирована');
}

void _registerCalendarFeature() {
  debugPrint('🔧 [SETUP] Регистрируем зависимости Calendar feature');

  // --- Presentation ---
  sl.registerLazySingleton(() => CalendarCubit(getTasksUseCase: sl()));

  debugPrint('✅ [SETUP] Calendar feature зарегистрирована');
}

void _registerTariffsAndStorageFeature() {
  debugPrint('🔧 [SETUP] Регистрируем зависимости Tariffs and Storage feature');

  // --- Presentation ---
  sl.registerLazySingleton(
    () => TariffsAndStorageCubit(
      getStorageInfoUseCase: sl(),
      checkSpaceAvailableUseCase: sl(),
      uploadFileUseCase: sl(),
      downloadFileUseCase: sl(),
      deleteFileUseCase: sl(),
    ),
  );

  // --- Domain ---
  sl.registerLazySingleton(() => GetStorageInfoUseCase(repository: sl()));
  sl.registerLazySingleton(() => CheckSpaceAvailableUseCase(repository: sl()));
  sl.registerLazySingleton(() => UploadFileUseCase(repository: sl()));
  sl.registerLazySingleton(() => DownloadFileUseCase(repository: sl()));
  sl.registerLazySingleton(() => DeleteFileUseCase(repository: sl()));

  // --- Data ---
  sl.registerLazySingleton<ITariffsAndStorageRepository>(
    () => TariffsAndStorageRepositoryImpl(
      remoteDataSource: sl(),
      localAuth: sl(),
    ),
  );

  sl.registerLazySingleton<ITariffsAndStorageRemoteDataSource>(
    () => TariffsAndStorageRemoteDataSourceImpl(dio: sl()),
  );

  debugPrint('✅ [SETUP] Tariffs and Storage feature зарегистрирована');
}

// === Integrations (Yandex Disk backup) feature ===
void _registerIntegrationsFeature() {
  debugPrint(
    '🔧 [SETUP] Регистрируем зависимости Integrations (Yandex Disk) feature',
  );

  // --- Data ---
  sl.registerLazySingleton<IIntegrationsRemoteDataSource>(
    () => IntegrationsRemoteDataSourceImpl(dio: sl()),
  );

  sl.registerLazySingleton<ISchedulerRemoteDataSource>(
    () => SchedulerRemoteDataSourceImpl(dio: sl()),
  );

  sl.registerLazySingleton<IntegrationsRepository>(
    () => IntegrationsRepositoryImpl(
      remoteDataSource: sl(),
      authLocalDataSource: sl(),
      prefs: sl(),
      schedulerRemoteDataSource: sl(),
    ),
  );

  // --- Domain ---
  sl.registerLazySingleton(() => GetBackupStatusUseCase(sl()));
  sl.registerLazySingleton(() => SetBackupEnabledUseCase(sl()));
  sl.registerLazySingleton(() => SaveYadiskTokenUseCase(sl()));

  // --- Presentation ---
  sl.registerFactory(
    () => BackupIntegrationCubit(
      getBackupStatusUseCase: sl(),
      setBackupEnabledUseCase: sl(),
      saveYadiskTokenUseCase: sl(),
    ),
  );

  debugPrint('✅ [SETUP] Integrations feature зарегистрирована');
}

void _registerNotificationsFeature() {
  debugPrint('🔧 [SETUP] Регистрируем зависимости Notifications feature');

  // --- Presentation ---
  sl.registerLazySingleton(
    () => NotificationsCubit(
      getNotifications: sl(),
      markAsDelivered: sl(),
      profileCubit: sl(),
    ),
  );

  // --- Domain ---
  sl.registerLazySingleton(() => GetNotifications(sl()));
  sl.registerLazySingleton(() => MarkAsDelivered(sl()));

  // --- Data ---
  sl.registerLazySingleton<NotificationsRepository>(
    () => NotificationsRepositoryImpl(
      remoteDataSource: sl(),
      authLocalDataSource: sl(),
    ),
  );

  sl.registerLazySingleton<NotificationsRemoteDataSource>(
    () => NotificationsRemoteDataSourceImpl(client: sl()),
  );

  debugPrint('✅ [SETUP] Notifications feature зарегистрирована');
}
