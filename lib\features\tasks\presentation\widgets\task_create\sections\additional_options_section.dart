import 'package:flutter/material.dart';

// ------------------ Дополнительные параметры ------------------
class AdditionalOptionsSection extends StatelessWidget {
  final bool allowAssigneeToChangeDueDate;
  final Function(bool) onAllowAssigneeChanged;

  const AdditionalOptionsSection({
    super.key,
    required this.allowAssigneeToChangeDueDate,
    required this.onAllowAssigneeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Дополнительные параметры',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        CheckboxListTile(
          title: const Text('Разрешить исполнителю менять сроки'),
          value: allowAssigneeToChangeDueDate,
          onChanged: (value) => onAllowAssigneeChanged(value ?? false),
        ),
      ],
    );
  }
}
