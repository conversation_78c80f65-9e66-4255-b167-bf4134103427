import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/file_upload_entity.dart';
import '../repositories/tariffs_and_storage_repository.dart';

/// Use case для загрузки файлов
class UploadFileUseCase {
  final ITariffsAndStorageRepository repository;

  const UploadFileUseCase({required this.repository});

  /// Получить URL для загрузки файла
  Future<Either<Failure, FileUploadEntity>> getUploadUrl({
    required String firmId,
    required String fileName,
    required int fileSize,
  }) async {
    return await repository.getUploadUrl(
      firmId: firmId,
      fileName: fileName,
      fileSize: fileSize,
    );
  }

  /// Загрузить файл с прогрессом
  Stream<FileUploadProgressEntity> uploadFile({
    required String uploadUrl,
    required String fileKey,
    required String firmId,
    required List<int> fileBytes,
    required String fileName,
  }) {
    return repository.uploadFile(
      uploadUrl: uploadUrl,
      fileKey: fileKey,
      firmId: firmId,
      fileBytes: fileBytes,
      fileName: fileName,
    );
  }

  /// Подтвердить загрузку файла
  Future<Either<Failure, void>> confirmUpload({
    required String firmId,
    required String fileKey,
  }) async {
    return await repository.confirmUpload(firmId: firmId, fileKey: fileKey);
  }
}
