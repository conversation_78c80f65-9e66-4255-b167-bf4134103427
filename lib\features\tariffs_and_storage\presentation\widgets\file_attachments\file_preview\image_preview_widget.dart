import 'dart:async';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class ImagePreviewWidget extends StatefulWidget {
  final String fileKey;
  final String fileName;

  const ImagePreviewWidget({
    super.key,
    required this.fileKey,
    required this.fileName,
  });

  @override
  State<ImagePreviewWidget> createState() => _ImagePreviewWidgetState();
}

class _ImagePreviewWidgetState extends State<ImagePreviewWidget> {
  final TransformationController _transformationController =
      TransformationController();

  bool _isLoading = true;
  String? _errorMessage;
  String? _imageUrl;
  double _rotationAngle = 0.0; // Угол поворота в радианах
  Timer? _rotationTimer;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      final downloadUrl = await _getDownloadUrl();
      if (downloadUrl == null) {
        setState(() {
          _errorMessage = 'Не удалось получить ссылку для предпросмотра';
          _isLoading = false;
        });
        return;
      }
      setState(() {
        _imageUrl = downloadUrl;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Ошибка загрузки изображения: $e';
        _isLoading = false;
      });
    }
  }

  Future<String?> _getDownloadUrl() async {
    final storageCubit = context.read<TariffsAndStorageCubit>();
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) return null;

    final completer = Completer<String?>();
    late StreamSubscription sub;

    sub = storageCubit.stream.listen((state) {
      if (state is FileDownloadUrlReady && state.fileKey == widget.fileKey) {
        if (!completer.isCompleted) completer.complete(state.downloadUrl);
        sub.cancel();
      } else if (state is TariffsAndStorageError) {
        if (!completer.isCompleted) completer.complete(null);
        sub.cancel();
      }
    });

    storageCubit.getDownloadUrl(
      firmId: firmState.selectedFirm!.id,
      fileKey: widget.fileKey,
    );

    Future.delayed(const Duration(seconds: 15), () {
      if (!completer.isCompleted) {
        completer.complete(null);
        sub.cancel();
      }
    });

    return completer.future;
  }

  void _zoom(double factor) {
    final Matrix4 current = _transformationController.value.clone();
    final double currentScale = current.getMaxScaleOnAxis();
    final double newScale = (currentScale * factor).clamp(0.25, 5.0);
    final double scaleFactor = newScale / currentScale;

    current.scale(scaleFactor);
    _transformationController.value = current;
  }

  void _recenter() {
    _transformationController.value = Matrix4.identity();
    setState(() {
      _rotationAngle = 0.0;
    });
  }

  void _rotateClockwise() {
    setState(() {
      _rotationAngle += math.pi / 2; // 90 градусов
    });
    _snapToNearestQuarter(); // Округляем при отдельном нажатии
  }

  void _rotateCounterClockwise() {
    setState(() {
      _rotationAngle -= math.pi / 2; // -90 градусов
    });
    _snapToNearestQuarter(); // Округляем при отдельном нажатии
  }

  void _startContinuousRotation(bool clockwise) {
    _rotationTimer?.cancel();
    _rotationTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      setState(() {
        _rotationAngle += clockwise ? 0.05 : -0.05; // Медленный поворот
      });
    });
  }

  void _stopContinuousRotation() {
    _rotationTimer?.cancel();
    _rotationTimer = null;
  }

  void _snapToNearestQuarter() {
    // Округляем до ближайшего угла кратного 90 градусам
    final quarterTurns = (_rotationAngle / (math.pi / 2)).round();
    setState(() {
      _rotationAngle = quarterTurns * (math.pi / 2);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_imageUrl == null) {
      return const Center(child: Text('Не удалось загрузить изображение'));
    }

    return Stack(
      children: [
        Positioned.fill(
          child: Center(
            child: GestureDetector(
              onDoubleTap: _recenter,
              child: InteractiveViewer(
                transformationController: _transformationController,
                minScale: 0.25,
                maxScale: 5.0,
                child: Transform.rotate(
                  angle: _rotationAngle,
                  child: Image.network(_imageUrl!),
                ),
              ),
            ),
          ),
        ),
        Positioned(
          right: 16,
          bottom: 32,
          child: Column(
            children: [
              // Кнопки поворота
              GestureDetector(
                onTap: _rotateCounterClockwise,
                onLongPressStart: (_) => _startContinuousRotation(false),
                onLongPressEnd: (_) => _stopContinuousRotation(),
                child: FloatingActionButton(
                  mini: true,
                  heroTag: 'img_rotate_ccw_${widget.fileKey}',
                  onPressed: null, // Обработка через GestureDetector
                  child: const Icon(Icons.rotate_left),
                ),
              ),
              const SizedBox(height: 8),
              GestureDetector(
                onTap: _rotateClockwise,
                onLongPressStart: (_) => _startContinuousRotation(true),
                onLongPressEnd: (_) => _stopContinuousRotation(),
                child: FloatingActionButton(
                  mini: true,
                  heroTag: 'img_rotate_cw_${widget.fileKey}',
                  onPressed: null, // Обработка через GestureDetector
                  child: const Icon(Icons.rotate_right),
                ),
              ),
              const SizedBox(height: 16),
              // Кнопки масштабирования
              FloatingActionButton(
                mini: true,
                heroTag: 'img_zoom_in_${widget.fileKey}',
                onPressed: () => _zoom(1.25),
                child: const Icon(Icons.add),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                mini: true,
                heroTag: 'img_zoom_out_${widget.fileKey}',
                onPressed: () => _zoom(0.8),
                child: const Icon(Icons.remove),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _rotationTimer?.cancel();
    _transformationController.dispose();
    super.dispose();
  }
}
