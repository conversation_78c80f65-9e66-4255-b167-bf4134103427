import 'package:equatable/equatable.dart';

/// Entity для информации о хранилище фирмы
class StorageInfoEntity extends Equatable {
  /// Использовано байт хранилища
  final int usedBytes;

  /// Последний пересчёт использования
  final DateTime lastRecalculatedAt;

  const StorageInfoEntity({
    required this.usedBytes,
    required this.lastRecalculatedAt,
  });

  /// Проверка, хватит ли места для загрузки файла
  bool hasSpaceForFile(int fileSize, int quotaBytes) {
    return (usedBytes + fileSize) <= quotaBytes;
  }

  /// Прогресс заполнения хранилища (0.0 - 1.0)
  double getUsageProgress(int quotaBytes) {
    if (quotaBytes <= 0) return 0.0;
    return (usedBytes / quotaBytes).clamp(0.0, 1.0);
  }

  /// Форматированный текст использования хранилища
  String getFormattedUsage(int quotaBytes) {
    final usedMB = (usedBytes / (1024 * 1024)).toStringAsFixed(1);
    final quotaMB = (quotaBytes / (1024 * 1024)).toStringAsFixed(1);
    return '$usedMB / $quotaMB МБ';
  }

  @override
  List<Object?> get props => [usedBytes, lastRecalculatedAt];
}
