import '../../domain/entities/employee_entity.dart';

abstract class IEmployeesRemoteDataSource {
  Future<List<EmployeeEntity>> fetchEmployees(String token, String firmId);
  Future<void> addEmployee(String token, String firmId, String email);
  Future<void> inviteEmployee(String token, String firmId, String email);
  Future<void> addRole(String token, String firmId, String userId, String role);
  Future<void> removeRole(
    String token,
    String firmId,
    String userId,
    String role,
  );
  Future<void> deleteEmployee(String token, String firmId, String userId);

  /// Создать новую фирму.
  Future<void> createFirm(String token, String firmName);
}
