import requests
import json
import sys
from datetime import date, timedelta
from colorama import init, Fore, Style

# Инициализируем colorama для работы в любой консоли (autoreset=True сбрасывает цвет после каждого print)
init(autoreset=True)

# --- Константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
API_CLIENTS_URL = "https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net/manage"

FIRM_ID = "9a33483b-dfad-44a3-a36d-102b498ec0ef"
LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL
INFO = Fore.CYAN + "→" + Style.RESET_ALL

# --- Глобальные переменные для хранения состояния теста ---
jwt_token = None
client_id_under_test = None
auth_headers = {}

# --- Даты для версионирования ---
DATE_V1 = (date.today() - timedelta(days=10)).isoformat()
DATE_V2 = (date.today() - timedelta(days=5)).isoformat()


def run_test_step(title: str, url: str, payload: dict, headers: dict, expected_status: int):
    """Выполняет один шаг теста, выводит результат и возвращает ответ в случае успеха."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ")

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=15)

        if response.status_code == expected_status:
            print(f"{TICK} (Статус: {response.status_code})")
            return response
        else:
            print(f"{CROSS} (Ожидался статус {expected_status}, получен {response.status_code})")
            print(Fore.RED + "  Текст ошибки:")
            try:
                error_json = response.json()
                print(Fore.RED + json.dumps(error_json, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        return None


def main_test_flow():
    """Основной сценарий тестирования с версионированием."""
    global client_id_under_test

    # --- Шаг 1: Создание клиента (первая версия) ---
    create_v1_payload = {
        "firm_id": FIRM_ID,
        "action": "UPSERT",
        "payload": {
            "client_name": "Тестовый Клиент (Версия 1)",
            "is_active": True,
            "comment": "Это самая первая версия клиента.",
            "manual_creation_date": DATE_V1
            # ИЗМЕНЕНО: Поле is_actual удалено, сервер установит его автоматически
        }
    }
    create_response = run_test_step("Шаг 1: Создание клиента (версия 1)", API_CLIENTS_URL, create_v1_payload,
                                    auth_headers, 201)
    if not create_response: return False
    client_id_under_test = create_response.json().get("client_id")
    print(f"{INFO} ID созданного клиента: {client_id_under_test}")
    print(f"{INFO} Дата версии 1: {DATE_V1}")

    # --- Шаг 2: Обновление существующей версии ---
    update_v1_payload = {
        "firm_id": FIRM_ID,
        "action": "UPSERT",
        "client_id": client_id_under_test,
        "creation_date": DATE_V1,
        "payload": {"comment": "Комментарий для версии 1 успешно обновлен."}
    }
    if not run_test_step("Шаг 2: Обновление существующей версии (V1)", API_CLIENTS_URL, update_v1_payload, auth_headers,
                         200): return False

    # --- Шаг 3: Создание новой, актуальной версии ---
    create_v2_payload = {
        "firm_id": FIRM_ID,
        "action": "UPSERT",
        "client_id": client_id_under_test,
        "payload": {
            "client_name": "Тестовый Клиент (Версия 2 - Актуальная)",
            "comment": "Это вторая, более новая версия.",
            "manual_creation_date": DATE_V2
            # ИЗМЕНЕНО: Поле is_actual удалено, сервер установит его автоматически, т.к. дата новее
        }
    }
    if not run_test_step("Шаг 3: Создание новой версии (V2)", API_CLIENTS_URL, create_v2_payload, auth_headers,
                         200): return False
    print(f"{INFO} Дата версии 2: {DATE_V2}")

    # --- Шаг 4: Проверка версий ---
    print(f"{Style.BRIGHT}► Шаг 4: Проверка версий и флага is_actual ... ")
    get_all_versions_payload = {"firm_id": FIRM_ID, "action": "GET", "client_id": client_id_under_test}
    versions_response = run_test_step("  - Запрос всех версий клиента", API_CLIENTS_URL, get_all_versions_payload,
                                      auth_headers, 200)
    if versions_response:
        versions = sorted(versions_response.json().get('data', []), key=lambda x: x['manual_creation_date'],
                          reverse=True)
        # Проверяем, что сервер корректно расставил флаги: последняя по дате версия - актуальна, остальные - нет.
        if len(versions) == 2 and versions[0]['manual_creation_date'] == DATE_V2 and versions[0][
            'is_actual'] is True and versions[1]['manual_creation_date'] == DATE_V1 and versions[1][
            'is_actual'] is False:
            print(f"  {TICK} Проверка успешна: Найдено 2 версии, V2 является актуальной.")
        else:
            print(f"  {CROSS} Проверка провалена: неверная структура версий или флаги 'is_actual'.")
            print(json.dumps(versions, indent=2, ensure_ascii=False))
            return False
    else:
        return False

    # --- Шаг 5: Получение списка только актуальных клиентов ---
    get_actual_list_payload = {"firm_id": FIRM_ID, "action": "GET"}
    actual_list_response = run_test_step("Шаг 5: Получение списка актуальных клиентов", API_CLIENTS_URL,
                                         get_actual_list_payload, auth_headers, 200)
    if actual_list_response:
        clients = actual_list_response.json().get('data', [])
        found = any(c['client_id'] == client_id_under_test and c['manual_creation_date'] == DATE_V2 for c in clients)
        if found:
            print(f"  {TICK} Проверка успешна: Актуальная версия клиента (V2) найдена в общем списке.")
        else:
            print(f"  {CROSS} Проверка провалена: Актуальная версия клиента не найдена в общем списке.")
            return False
    else:
        return False

    # --- Шаг 6: Удаление старой версии ---
    delete_v1_payload = {"firm_id": FIRM_ID, "action": "DELETE", "client_id": client_id_under_test,
                         "creation_date": DATE_V1}
    if not run_test_step("Шаг 6: Удаление старой версии (V1)", API_CLIENTS_URL, delete_v1_payload, auth_headers,
                         200): return False

    # --- ИЗМЕНЕНИЕ ЛОГИКИ ТЕСТА ---
    # --- Шаг 7: Удаление последней версии (теперь это должно работать) ---
    delete_v2_payload = {"firm_id": FIRM_ID, "action": "DELETE", "client_id": client_id_under_test,
                         "creation_date": DATE_V2}
    if not run_test_step("Шаг 7: Удаление последней версии (V2) - теперь должно быть успешно", API_CLIENTS_URL,
                         delete_v2_payload, auth_headers, 200): return False

    # --- Шаг 8: Проверка, что клиент действительно удален ---
    print(f"{Style.BRIGHT}► Шаг 8: Проверка полного удаления клиента ... ")
    final_check_response = run_test_step("  - Запрос удаленного клиента", API_CLIENTS_URL, get_all_versions_payload,
                                         auth_headers, 200)
    if final_check_response:
        remaining_versions = final_check_response.json().get('data', [])
        if not remaining_versions:
            print(f"  {TICK} Проверка успешна: Клиент полностью удален из базы данных.")
        else:
            print(f"  {CROSS} Проверка провалена: После удаления всех версий клиент все еще существует.")
            return False
    else:
        return False

    # Если все шаги пройдены, сбрасываем ID, чтобы очистка не запускалась
    client_id_under_test = None
    return True


def cleanup():
    """Очистка на случай, если тест упал на середине."""
    if not client_id_under_test:
        return
    print("\n--- Финальная очистка (на случай сбоя теста) ---")
    get_versions_payload = {"firm_id": FIRM_ID, "action": "GET", "client_id": client_id_under_test}
    response = requests.post(API_CLIENTS_URL, json=get_versions_payload, headers=auth_headers)
    if response.status_code != 200: return
    versions_to_delete = sorted(response.json().get('data', []), key=lambda x: x['manual_creation_date'], reverse=True)
    if not versions_to_delete: return
    print(f"{INFO} Найдено {len(versions_to_delete)} версий для принудительной очистки...")
    for version in versions_to_delete:
        delete_payload = {"firm_id": FIRM_ID, "action": "DELETE", "client_id": client_id_under_test,
                          "creation_date": version['manual_creation_date']}
        run_test_step(f"Очистка версии ({version['manual_creation_date']})", API_CLIENTS_URL, delete_payload,
                      auth_headers, 200)


if __name__ == "__main__":
    print("\n--- Начало комплексного тестирования Clients API (с версионированием) ---\n")
    login_response = run_test_step("Шаг 0: Получение JWT токена", API_AUTH_URL, LOGIN_PAYLOAD, DEFAULT_HEADERS, 200)
    if not login_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось войти в систему. Тестирование прервано.")
    jwt_token = login_response.json().get("token")
    auth_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {jwt_token}"}

    success = False
    try:
        success = main_test_flow()
    except Exception as e:
        print(f"\n{CROSS} Во время выполнения основного сценария возникло исключение: {e}")
    finally:
        cleanup()

    if success:
        print(f"\n{TICK}{TICK}{TICK} Тестирование успешно завершено {TICK}{TICK}{TICK}")
    else:
        print(f"\n{CROSS}{CROSS}{CROSS} Тестирование завершилось с ошибками {CROSS}{CROSS}{CROSS}")