import 'package:dartz/dartz.dart';

import '../../../../core/error/failure.dart';
import '../repositories/client_payments_repository.dart';

class DeleteClientPaymentUseCase {
  final IClientPaymentsRepository repository;

  DeleteClientPaymentUseCase({required this.repository});

  Future<Either<Failure, Unit>> call(DeleteClientPaymentParams params) {
    return repository.deletePayment(
      firmId: params.firmId,
      clientId: params.clientId,
      period: params.period,
    );
  }
}

class DeleteClientPaymentParams {
  final String firmId;
  final String clientId;
  final DateTime period;

  DeleteClientPaymentParams({
    required this.firmId,
    required this.clientId,
    required this.period,
  });
}
