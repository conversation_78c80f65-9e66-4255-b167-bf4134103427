import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/employee_entity.dart';
import '../repositories/employees_repository.dart';

class GetEmployeesUseCase {
  final IEmployeesRepository repository;
  const GetEmployeesUseCase(this.repository);

  Future<Either<Failure, List<EmployeeEntity>>> call(String firmId) {
    return repository.getEmployees(firmId);
  }
}
