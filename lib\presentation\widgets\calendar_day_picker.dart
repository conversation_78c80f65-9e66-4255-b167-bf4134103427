import 'package:flutter/material.dart';

class CalendarDayPicker extends StatefulWidget {
  final List<int> selectedDays;
  final ValueChanged<List<int>> onDaysSelected;
  final String paymentType;

  const CalendarDayPicker({
    super.key,
    required this.selectedDays,
    required this.onDaysSelected,
    required this.paymentType,
  });

  @override
  State<CalendarDayPicker> createState() => _CalendarDayPickerState();
}

class _CalendarDayPickerState extends State<CalendarDayPicker> {
  late List<int> _selectedDays;
  final Map<int, int> _dayToCalendarIndex = {}; // {encodedDay: calendarIndex}

  @override
  void initState() {
    super.initState();
    _selectedDays = List.from(widget.selectedDays);
    for (final dayValue in _selectedDays) {
      if (dayValue > 31) {
        final month = dayValue ~/ 100;
        final calendarIndex = month - 1;
        _dayToCalendarIndex[dayValue] = calendarIndex;
      } else {
        _dayToCalendarIndex[dayValue] = 0; // Для простых дней
      }
    }
  }

  int get _maxSelection {
    switch (widget.paymentType) {
      case 'Полугодовые платежи':
        return 2;
      default:
        return 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black54,
      body: Center(
        child: Container(
          constraints: _getConstraints(),
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _getTitle(),
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Flexible(child: _buildCalendars()),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Отмена'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed:
                        _selectedDays.isNotEmpty
                            ? () {
                              widget.onDaysSelected(_selectedDays);
                              Navigator.of(context).pop();
                            }
                            : null,
                    child: const Text('Выбрать'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  BoxConstraints _getConstraints() {
    switch (widget.paymentType) {
      case 'Квартальные платежи':
        return const BoxConstraints(maxWidth: 800, maxHeight: 400);
      case 'Полугодовые платежи':
      case 'Разовый платеж':
        return const BoxConstraints(maxWidth: 1000, maxHeight: 800);
      default:
        return const BoxConstraints(maxWidth: 400, maxHeight: 500);
    }
  }

  String _getTitle() {
    switch (widget.paymentType) {
      case 'Ежемесячные платежи':
        return 'Выберите день месяца';
      case 'Квартальные платежи':
        return 'Выберите день для кварталов';
      case 'Полугодовые платежи':
        return 'Выберите две даты для оплаты';
      case 'Разовый платеж':
        return 'Выберите месяц и день для разового платежа';
      default:
        return 'Выберите день';
    }
  }

  Widget _buildCalendars() {
    switch (widget.paymentType) {
      case 'Квартальные платежи':
        return _buildQuarterlyCalendars();
      case 'Полугодовые платежи':
      case 'Разовый платеж':
        return SingleChildScrollView(child: _buildYearlyCalendars());
      default:
        return _buildSingleCalendar();
    }
  }

  void _handleDaySelection(int day, int calendarIndex) {
    setState(() {
      int result;
      // Формируем значение в зависимости от типа календаря
      if (widget.paymentType == 'Ежемесячные платежи' ||
          widget.paymentType == 'default') {
        result = day; // Только день
      } else {
        final month = calendarIndex + 1;
        result = month * 100 + day;
      }

      if (_selectedDays.contains(result)) {
        // Если день уже выбран - снимаем выбор
        _selectedDays.remove(result);
        _dayToCalendarIndex.remove(result);
      } else {
        if (_selectedDays.length < _maxSelection) {
          // Если есть место - добавляем
          _selectedDays.add(result);
          _dayToCalendarIndex[result] = calendarIndex;
        } else if (_maxSelection == 1) {
          // Если макс. 1, то заменяем старое значение
          _selectedDays.clear();
          _dayToCalendarIndex.clear();
          _selectedDays.add(result);
          _dayToCalendarIndex[result] = calendarIndex;
        }
        // Если макс. > 1 и уже заполнено, ничего не делаем
      }
    });
  }

  Widget _buildQuarterlyCalendars() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMonthCalendar('1 месяц', 0),
            _buildMonthCalendar('2 месяц', 1),
            _buildMonthCalendar('3 месяц', 2),
          ],
        ),
      ],
    );
  }

  Widget _buildHalfYearlyCalendars() {
    return Column(
      children: [
        const Text(
          'Первое полугодие',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMonthCalendar('1 месяц', 0),
            _buildMonthCalendar('2 месяц', 1),
            _buildMonthCalendar('3 месяц', 2),
          ],
        ),
        const SizedBox(height: 16),
        const Text(
          'Второе полугодие',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMonthCalendar('4 месяц', 3),
            _buildMonthCalendar('5 месяц', 4),
            _buildMonthCalendar('6 месяц', 5),
          ],
        ),
      ],
    );
  }

  Widget _buildYearlyCalendars() {
    const List<String> monthNames = [
      'Январь',
      'Февраль',
      'Март',
      'Апрель',
      'Май',
      'Июнь',
      'Июль',
      'Август',
      'Сентябрь',
      'Октябрь',
      'Ноябрь',
      'Декабрь',
    ];

    return Column(
      children: [
        // Первый ряд (4 месяца)
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMonthCalendar(monthNames[0], 0),
            _buildMonthCalendar(monthNames[1], 1),
            _buildMonthCalendar(monthNames[2], 2),
            _buildMonthCalendar(monthNames[3], 3),
          ],
        ),
        const SizedBox(height: 16),
        // Второй ряд (4 месяца)
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMonthCalendar(monthNames[4], 4),
            _buildMonthCalendar(monthNames[5], 5),
            _buildMonthCalendar(monthNames[6], 6),
            _buildMonthCalendar(monthNames[7], 7),
          ],
        ),
        const SizedBox(height: 16),
        // Третий ряд (4 месяца)
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMonthCalendar(monthNames[8], 8),
            _buildMonthCalendar(monthNames[9], 9),
            _buildMonthCalendar(monthNames[10], 10),
            _buildMonthCalendar(monthNames[11], 11),
          ],
        ),
      ],
    );
  }

  Widget _buildMonthCalendar(String periodName, int calendarIndex) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Text(
                periodName,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  childAspectRatio: 1,
                  crossAxisSpacing: 2,
                  mainAxisSpacing: 2,
                ),
                itemCount: 31,
                itemBuilder: (context, index) {
                  final day = index + 1;
                  final month = calendarIndex + 1;
                  final dayValue = month * 100 + day;
                  final isSelected = _selectedDays.contains(dayValue);

                  return InkWell(
                    onTap: () => _handleDaySelection(day, calendarIndex),
                    borderRadius: BorderRadius.circular(4),
                    child: Container(
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.primaryContainer
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color:
                              isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(
                                    context,
                                  ).dividerColor.withOpacity(0.4),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          day.toString(),
                          style: TextStyle(
                            color:
                                isSelected
                                    ? Theme.of(
                                      context,
                                    ).colorScheme.onPrimaryContainer
                                    : Theme.of(
                                      context,
                                    ).textTheme.bodyMedium?.color,
                            fontSize: 10,
                            fontWeight:
                                isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSingleCalendar() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(12),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        itemCount: 31,
        itemBuilder: (context, index) {
          final day = index + 1;
          final isSelected = _selectedDays.contains(day);

          return InkWell(
            onTap: () => _handleDaySelection(day, 0),
            borderRadius: BorderRadius.circular(6),
            child: Container(
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.primaryContainer
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color:
                      isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).dividerColor,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Center(
                child: Text(
                  day.toString(),
                  style: TextStyle(
                    color:
                        isSelected
                            ? Theme.of(context).colorScheme.onPrimaryContainer
                            : Theme.of(context).textTheme.bodyMedium?.color,
                    fontSize: 14,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
