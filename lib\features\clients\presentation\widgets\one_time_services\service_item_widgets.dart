import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../cubit/one_time_services_cubit.dart';
import '../../cubit/one_time_services_state.dart';
import 'one_time_services_settings_dialog.dart';

/// Виджет для услуги с чекбоксом
class ServiceCheckboxItem extends StatelessWidget {
  const ServiceCheckboxItem({super.key, required this.service});

  final OneTimeServiceType service;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OneTimeServicesCubit, OneTimeServicesState>(
      builder: (context, state) {
        final cubit = context.read<OneTimeServicesCubit>();
        final isSelected = cubit.isServiceSelected(service.id);
        final isActive = service.isActive;
        final customPrice = cubit.getCustomPrice(service);

        return CheckboxListTile(
          title: Text(
            service.title,
            style: TextStyle(
              color:
                  isActive
                      ? Theme.of(context).colorScheme.onSurface
                      : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          subtitle: Text(
            isActive && customPrice != service.basePrice
                ? NumberFormat.currency(
                  locale: 'ru_RU',
                  symbol: '₽',
                ).format(customPrice)
                : service.priceLabel,
            style: TextStyle(
              color:
                  isActive
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          value: isSelected,
          onChanged:
              isActive ? (value) => cubit.toggleService(service.id) : null,
          enabled: isActive,
        );
      },
    );
  }
}

/// Виджет для услуги со счетчиком
class ServiceCounterItem extends StatelessWidget {
  const ServiceCounterItem({
    super.key,
    required this.service,
    this.maxValue = 999,
  });

  final OneTimeServiceType service;
  final int maxValue;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OneTimeServicesCubit, OneTimeServicesState>(
      builder: (context, state) {
        final cubit = context.read<OneTimeServicesCubit>();
        final quantity = cubit.getServiceQuantity(service.id);
        final isActive = service.isActive;
        final customPrice = cubit.getCustomPrice(service);

        return ListTile(
          title: Text(
            service.title,
            style: TextStyle(
              color:
                  isActive
                      ? Theme.of(context).colorScheme.onSurface
                      : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                service.priceLabel,
                style: TextStyle(
                  color:
                      isActive
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              if (quantity > 0 && isActive) ...[
                const SizedBox(height: 4),
                Text(
                  'Итого: ${NumberFormat.currency(locale: 'ru_RU', symbol: '₽').format(customPrice * quantity)}',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.secondary,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
          trailing:
              isActive
                  ? SizedBox(
                    width: 120,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed:
                              quantity > 0
                                  ? () => cubit.updateServiceQuantity(
                                    service.id,
                                    quantity - 1,
                                  )
                                  : null,
                          icon: const Icon(Icons.remove),
                        ),
                        SizedBox(
                          width: 40,
                          child: Center(
                            child: Text(
                              quantity.toString(),
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed:
                              quantity < maxValue
                                  ? () => cubit.updateServiceQuantity(
                                    service.id,
                                    quantity + 1,
                                  )
                                  : null,
                          icon: const Icon(Icons.add),
                        ),
                      ],
                    ),
                  )
                  : Text(
                    'Неактивно',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
          enabled: isActive,
        );
      },
    );
  }
}

/// Виджет для неактивной услуги (только для информации)
class ServiceInfoItem extends StatelessWidget {
  const ServiceInfoItem({super.key, required this.service});

  final OneTimeServiceType service;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(
        service.title,
        style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
      ),
      subtitle: Text(
        service.priceLabel,
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
          fontStyle: FontStyle.italic,
        ),
      ),
      trailing: Icon(
        Icons.info_outline,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      enabled: false,
    );
  }
}

/// Виджет для группы услуг (категории)
class ServiceCategoryCard extends StatefulWidget {
  const ServiceCategoryCard({
    super.key,
    required this.category,
    required this.services,
    this.initiallyExpanded = false,
  });

  final OneTimeServiceCategory category;
  final List<OneTimeServiceType> services;
  final bool initiallyExpanded;

  @override
  State<ServiceCategoryCard> createState() => _ServiceCategoryCardState();
}

class _ServiceCategoryCardState extends State<ServiceCategoryCard> {
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          ListTile(
            title: Text(
              widget.category.title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.settings_outlined),
                  tooltip: 'Настроить расценки',
                  onPressed: () {
                    final cubit = context.read<OneTimeServicesCubit>();
                    showDialog(
                      context: context,
                      builder:
                          (_) => BlocProvider.value(
                            value: cubit,
                            child: OneTimeServicesSettingsDialog(
                              category: widget.category,
                            ),
                          ),
                    );
                  },
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                  ),
                ),
              ],
            ),
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),
          if (_isExpanded) ...[
            const Divider(height: 1),
            ...widget.services.map((service) => _buildServiceItem(service)),
          ],
        ],
      ),
    );
  }

  Widget _buildServiceItem(OneTimeServiceType service) {
    // Определяем тип виджета в зависимости от услуги
    if (!service.isActive) {
      return ServiceInfoItem(service: service);
    }

    // Услуги, которые используют счетчик
    final counterServices = {
      OneTimeServiceType.additionalReportForms,
      OneTimeServiceType.szvReports,
      OneTimeServiceType.ipReceipts,
      OneTimeServiceType.taxReconciliation,
      OneTimeServiceType.fullAccountingRestoration,
      OneTimeServiceType.specialistVisit,
      OneTimeServiceType.hrDocuments,
      OneTimeServiceType.laborContract,
      OneTimeServiceType.fssCompensation,
      OneTimeServiceType.consultation,
      OneTimeServiceType.training1C,
      OneTimeServiceType.courierDelivery,
      OneTimeServiceType.courierSubmission,
      OneTimeServiceType.counterpartyCheck,
      OneTimeServiceType.bankAccountAssistance,
    };

    if (counterServices.contains(service)) {
      return ServiceCounterItem(service: service);
    }

    // Остальные услуги используют чекбокс
    return ServiceCheckboxItem(service: service);
  }
}
