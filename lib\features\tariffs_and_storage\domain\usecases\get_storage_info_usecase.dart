import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/tariffs_and_storage_entity.dart';
import '../repositories/tariffs_and_storage_repository.dart';

/// Use case для получения информации о тарифах и хранилище
class GetStorageInfoUseCase {
  final ITariffsAndStorageRepository repository;

  const GetStorageInfoUseCase({required this.repository});

  /// Получить информацию о тарифах и хранилище фирмы
  Future<Either<Failure, TariffsAndStorageEntity>> call(String firmId) async {
    return await repository.getTariffsAndStorage(firmId);
  }
}
