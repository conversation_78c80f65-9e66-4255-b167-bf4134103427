import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/core/constants/constants.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

import '../data_source/integrations_remote_data_source.dart';

class IntegrationsRemoteDataSourceImpl
    implements IIntegrationsRemoteDataSource {
  final Dio? dio;
  final http.Client? httpClient;

  IntegrationsRemoteDataSourceImpl({this.dio, this.httpClient})
    : assert(
        dio != null || httpClient != null,
        'Either dio or httpClient must be provided',
      );

  String _logPrefix(String method) => '[IntegrationsRemoteDataSource][$method]';

  @override
  Future<Map<String, dynamic>> getIntegrations(
    String token,
    String firmId,
  ) async {
    final body = {"firm_id": firmId, "action": "GET"};
    if (dio != null) {
      return _postDio(token, body);
    } else {
      return _postHttp(token, body);
    }
  }

  @override
  Future<void> upsertIntegration(
    String token,
    String firmId,
    Map<String, dynamic> payload,
  ) async {
    final body = {"firm_id": firmId, "action": "UPSERT", "payload": payload};
    if (dio != null) {
      await _postDio(token, body);
    } else {
      await _postHttp(token, body);
    }
  }

  @override
  Future<void> deleteIntegrations(
    String token,
    String firmId,
    List<String> keys,
  ) async {
    final body = {
      "firm_id": firmId,
      "action": "DELETE",
      "integration_keys": keys,
    };
    if (dio != null) {
      await _postDio(token, body);
    } else {
      await _postHttp(token, body);
    }
  }

  Future<Map<String, dynamic>> _postDio(
    String token,
    Map<String, dynamic> body,
  ) async {
    final url = TariffsAndStorageApiUrls.integrations();
    try {
      NetworkLogger.printInfo('${_logPrefix('DIO')} --> POST $url');
      final response = await dio!.post(
        url,
        data: body,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );
      NetworkLogger.printInfo(
        '${_logPrefix('DIO')} <-- ${response.statusCode}',
      );
      if (response.statusCode == 200) {
        dynamic data = response.data;
        // Если dio не распарсил JSON (например, из-за неверного content-type),
        // а вернул строку, то пытаемся декодировать её вручную.
        if (data is String) {
          try {
            data = jsonDecode(data);
          } catch (e, s) {
            NetworkLogger.printError(
              '${_logPrefix('DIO')} JSON decode failed',
              e,
              s,
            );
            return {}; // Возвращаем пустую карту при ошибке парсинга
          }
        }

        if (data is Map && data['integrations'] is Map) {
          return Map<String, dynamic>.from(data['integrations']);
        }
        return {};
      }
      throw ServerException(
        message: 'Ошибка Integrations API',
        statusCode: response.statusCode,
        requestUrl: url,
        responseBody: response.data.toString(),
      );
    } on DioException catch (e) {
      throw NetworkException(
        message: e.message ?? 'Dio error',
        originalError: e.toString(),
        requestUrl: TariffsAndStorageApiUrls.integrations(),
      );
    }
  }

  Future<Map<String, dynamic>> _postHttp(
    String token,
    Map<String, dynamic> body,
  ) async {
    final uri = Uri.parse(TariffsAndStorageApiUrls.integrations());
    try {
      final response = await httpClient!.post(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      );
      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        if (decoded is Map && decoded['integrations'] is Map) {
          return Map<String, dynamic>.from(decoded['integrations']);
        }
        return {};
      }
      throw ServerException(
        message: 'Ошибка Integrations API',
        statusCode: response.statusCode,
        requestUrl: uri.toString(),
        responseBody: response.body,
      );
    } catch (e) {
      throw NetworkException(message: e.toString(), requestUrl: uri.toString());
    }
  }
}
