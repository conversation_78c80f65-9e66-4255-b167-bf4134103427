import 'package:intl/intl.dart';

class TaskDetailUtils {
  static String translateStatus(String status) {
    switch (status) {
      case 'pending':
        return 'Новая';
      case 'in_progress':
      case 'ongoing':
        return 'Активно';
      case 'testing':
        return 'Тестирование';
      case 'completed':
      case 'done':
        return 'Завершено';
      case 'cancelled':
        return 'Отменено';
      case 'blocked':
        return 'Заблокирована';
      default:
        return status;
    }
  }

  static String translatePriority(String priority) {
    switch (priority) {
      case 'low':
        return 'Низкий';
      case 'medium':
        return 'Средний';
      case 'high':
        return 'Высокий';
      case 'critical':
        return 'Критический';
      default:
        return priority;
    }
  }

  static String translateHolidayRule(String? rule) {
    switch (rule) {
      case 'next_workday':
        return 'Следующий рабочий день';
      case 'previous_workday':
        return 'Предыдущий рабочий день';
      case 'no_transfer':
        return 'Не переносить';
      default:
        return rule ?? '-';
    }
  }

  static String translateRole(String? role) {
    switch (role) {
      case 'assignee':
        return 'Исполнитель';
      case 'observer':
        return 'Наблюдатель';
      case 'creator':
        return 'Постановщик';
      default:
        return role ?? '-';
    }
  }

  static String weekdayName(String code) {
    switch (code) {
      case 'MO':
        return 'Пн';
      case 'TU':
        return 'Вт';
      case 'WE':
        return 'Ср';
      case 'TH':
        return 'Чт';
      case 'FR':
        return 'Пт';
      case 'SA':
        return 'Сб';
      case 'SU':
        return 'Вс';
      default:
        return code;
    }
  }

  static String formatDate(DateTime? dt) =>
      dt != null ? DateFormat('dd.MM.yyyy').format(dt) : '-';

  static String formatDateTime(DateTime? dt) =>
      dt != null ? DateFormat('dd.MM.yyyy HH:mm').format(dt) : '-';

  static String formatRecurrence(Map<String, dynamic>? recurrence) {
    if (recurrence == null || recurrence.isEmpty) return '';

    final type = recurrence['type']?.toString() ?? '';
    if (type.isEmpty || type == 'none') return '';

    if (type == 'еженедельно' || type == 'weekly') {
      final interval = recurrence['interval'] ?? 1;
      final days = (recurrence['days'] as List?)?.cast<String>() ?? [];
      final dayNames = days.map(weekdayName).join(', ');
      return 'каждые $interval нед. ($dayNames)';
    }
    if (type == 'periodic') {
      final interval = recurrence['interval'] ?? 1;
      return 'каждые $interval периодически';
    }
    return type;
  }

  static String formatReminder(Map<String, dynamic> reminder) {
    final dtRaw = reminder['datetime'];
    final roleRaw = reminder['role'];
    String dateText = '-';
    if (dtRaw is String && dtRaw.isNotEmpty) {
      try {
        final dt = DateTime.parse(dtRaw).toLocal();
        dateText = DateFormat('dd.MM.yyyy HH:mm').format(dt);
      } catch (_) {
        dateText = dtRaw;
      }
    }
    final roleText = translateRole(roleRaw?.toString());
    return '$dateText ($roleText)';
  }

  static String formatOption(String key, dynamic value) {
    final labels = {
      'allow_assignee_to_change_due_date': 'Исполнитель может менять сроки',
      'auto_close_on_due_date': 'Автоматически закрыть по сроку',
      'allow_partial_completion': 'Разрешить частичное выполнение',
      'holiday_transfer_rule': 'Перенос при празднике/выходном',
    };
    
    final label = labels[key] ?? key;
    String formattedValue;
    if (value is bool) {
      formattedValue = value ? 'да' : 'нет';
    } else if (key == 'holiday_transfer_rule') {
      formattedValue = translateHolidayRule(value?.toString());
    } else {
      formattedValue = value?.toString() ?? '';
    }
    return '$label: $formattedValue';
  }
}