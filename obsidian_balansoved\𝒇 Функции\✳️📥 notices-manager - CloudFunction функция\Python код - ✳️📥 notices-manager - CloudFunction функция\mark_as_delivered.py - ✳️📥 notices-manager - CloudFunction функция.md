
```python
import json
import datetime
import pytz
import ydb
from custom_errors import <PERSON>Error

def mark_notices_as_delivered(session, table_name, notice_ids):
    if not isinstance(notice_ids, list) or not notice_ids:
        raise LogicError("notice_ids must be a non-empty list.")
    
    tx = session.transaction(ydb.SerializableReadWrite())

    update_query = session.prepare(f"DECLARE $ids AS List<Utf8>; DECLARE $now AS Timestamp; UPDATE `{table_name}` SET is_delivered = true, delivered_at = $now WHERE notice_id IN $ids;")
    print(f"Executing YQL update to mark {len(notice_ids)} notices as delivered")
    tx.execute(update_query, {
        "$ids": notice_ids,
        "$now": datetime.datetime.now(pytz.utc)
    })
    
    tx.commit()
    print(f"Marked {len(notice_ids)} notices as delivered in table {table_name}.")
    
    return {
        "statusCode": 200,
        "headers": {"Content-Type": "application/json; charset=utf-8"},
        "body": json.dumps({"message": f"{len(notice_ids)} notices marked as delivered."}, ensure_ascii=False)
    }
```