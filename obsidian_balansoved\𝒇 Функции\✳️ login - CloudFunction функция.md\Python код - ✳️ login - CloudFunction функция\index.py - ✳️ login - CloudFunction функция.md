

```python
import json
import os
import datetime
import pytz
import logging
import ydb
from utils import ydb_utils, auth_utils, request_parser

logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    logging.info("--- NEW INVOCATION ---")
    logging.info(f"RAW EVENT: {event}")

    try:
        data = request_parser.parse_request_body(event)
    except ValueError as e:
        logging.error(f"Request body processing error: {e}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}

    email = data.get('email')
    password = data.get('password')

    if not all([email, password]):
        logging.error("Email or password not provided in the parsed data.")
        return {"statusCode": 400, "body": json.dumps({"message": "Email and password are required."})}

    driver = ydb_utils.get_ydb_driver()
    pool = ydb.SessionPool(driver)

    def transaction(session):
        tx = session.transaction(ydb.SerializableReadWrite())

        select_query_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8;
            SELECT user_id, password_hash, jwt_token, is_active FROM users WHERE email = $email;
        """
        prepared_select = session.prepare(select_query_text)
        result_sets = tx.execute(prepared_select, {'$email': email})

        if not result_sets[0].rows:
            tx.rollback()
            return {"status": 401}

        user_data = result_sets[0].rows[0]

        # Проверяем подтверждение аккаунта
        if not user_data.is_active:
            tx.rollback()
            return {"status": 423}

        if not auth_utils.verify_password(password, user_data.password_hash):
            tx.rollback()
            return {"status": 401}

        now = datetime.datetime.now(pytz.utc)
        user_id = user_data.user_id

        update_login_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $user_id AS Utf8; DECLARE $now AS Timestamp;
            UPDATE users SET last_login_at = $now WHERE user_id = $user_id;
        """
        prepared_update_login = session.prepare(update_login_text)
        tx.execute(prepared_update_login, {'$user_id': user_id, '$now': now})

        if user_data.jwt_token:
            logging.info(f"Returning existing token for user {email}")
            tx.commit()
            return {"status": 200, "token": user_data.jwt_token}
        else:
            logging.info(f"Generating new token for user {email}")
            new_token = auth_utils.generate_jwt(user_id, email)
            
            update_token_text = f"""
                PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $user_id AS Utf8; DECLARE $token AS Utf8;
                UPDATE users SET jwt_token = $token WHERE user_id = $user_id;
            """
            prepared_update_token = session.prepare(update_token_text)
            tx.execute(prepared_update_token, {'$user_id': user_id, '$token': new_token})
            
            tx.commit()
            return {"status": 200, "token": new_token}

    try:
        result = pool.retry_operation_sync(transaction)
        if isinstance(result, dict):
            status = result.get("status")
            if status == 200:
                logging.info(f"Login successful for user {email}.")
                return {"statusCode": 200, "body": json.dumps({"token": result["token"]})}
            elif status == 423:
                logging.warning(f"Attempt to login before confirmation for {email}.")
                return {"statusCode": 423, "body": json.dumps({"message": "Account not confirmed. Please verify your email."})}
            else: # 401
                logging.warning(f"Invalid credentials attempt for user {email}.")
                return {"statusCode": 401, "body": json.dumps({"message": "Invalid credentials."})}
        else:
            return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
    except Exception as e:
        logging.error(f"Critical error during login transaction for user {email}: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```

