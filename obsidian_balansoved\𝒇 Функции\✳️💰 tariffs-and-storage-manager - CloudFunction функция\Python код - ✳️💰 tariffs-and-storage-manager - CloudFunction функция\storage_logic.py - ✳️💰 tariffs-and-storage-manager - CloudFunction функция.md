
```python
# storage_logic.py

import os
import re
import datetime
import json
import logging
import pytz
import ydb
from utils import storage_utils
from custom_errors import LogicError, QuotaExceededError, NotFoundError, AuthError
import get_logic

def handle_get_upload_url(pool, firm_id, filename, filesize):
    logging.info(f"Handling GET_UPLOAD_URL for firm {firm_id}, filename: {filename}, size: {filesize}")
    if not all([filename, filesize]):
        raise LogicError("filename and filesize are required for GET_UPLOAD_URL.")
    if not isinstance(filesize, int) or filesize <= 0:
        raise LogicError("filesize must be a positive integer.")

    # Шаг 1: Получаем актуальный размер использованного пространства из Object Storage
    logging.info("Calculating actual storage usage from S3.")
    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']
    try:
        prefix = f"{firm_id}/"
        actual_used_bytes = storage_utils.get_folder_size(s3_client, bucket_name, prefix)
    except Exception:
        logging.error(f"Could not calculate storage size for firm {firm_id}. Aborting.")
        raise Exception("Could not verify storage quota due to an internal error.")

    # Шаг 2: Получаем запись из БД, чтобы узнать квоту и сравнить с актуальным использованием
    logging.info("Fetching current tariff and storage info from DB.")
    record_response = pool.retry_operation_sync(lambda s: get_logic.get_or_create_record(s, firm_id))
    record_data = json.loads(record_response['body'])['data']
    
    quota_bytes = record_data.get('subscription_info_json', {}).get('quota_bytes', 0)
    used_bytes_from_db = record_data.get('storage_info_json', {}).get('used_bytes', 0)
    
    logging.info(f"Real-time Quota Check for firm {firm_id}: ActualUsed={actual_used_bytes}, FileSize={filesize}, Quota={quota_bytes}")

    # Шаг 3: Проверяем квоту, используя актуальные данные
    if (actual_used_bytes + filesize) > quota_bytes:
        raise QuotaExceededError(f"Upload failed: storage quota will be exceeded. Used: {actual_used_bytes}, File: {filesize}, Quota: {quota_bytes}")

    # Шаг 4: (Опционально, но рекомендуется) Если данные в БД устарели, синхронизируем их
    if actual_used_bytes != used_bytes_from_db:
        logging.warning(f"DB usage is out of sync for firm {firm_id}. DB: {used_bytes_from_db}, Real: {actual_used_bytes}. Syncing...")
        def sync_transaction(session):
            storage_info = record_data.get('storage_info_json', {})
            storage_info['used_bytes'] = actual_used_bytes
            storage_info['last_recalculated_at'] = datetime.datetime.now(pytz.utc).isoformat()
            
            update_q = session.prepare("DECLARE $fid AS Utf8; DECLARE $sij AS Json; UPDATE `tariffs_and_storage` SET storage_info_json = $sij WHERE firm_id = $fid;")
            tx = session.transaction(ydb.SerializableReadWrite())
            tx.execute(update_q, {"$fid": firm_id, "$sij": json.dumps(storage_info)})
            tx.commit()
            logging.info(f"Successfully synced used_bytes for firm {firm_id} to {actual_used_bytes}.")
        
        pool.retry_operation_sync(sync_transaction)

    # Шаг 5: Если проверка квоты пройдена, генерируем ссылку для загрузки
    logging.info("Quota check passed. Generating file key and presigned URL.")
    file_key, upload_url = storage_utils.generate_upload_artefacts(firm_id, filename)

    if not upload_url or not file_key:
        logging.error("storage_utils.generate_upload_artefacts failed to return artefacts.")
        raise Exception("Could not generate an upload URL from the storage service.")

    logging.info(f"Successfully generated upload URL for file_key: {file_key}")
    return {"statusCode": 200, "body": json.dumps({"upload_url": upload_url, "file_key": file_key})}


def handle_delete_file(pool, firm_id, file_key):
    logging.info(f"Handling DELETE_FILE for firm {firm_id}, file_key: {file_key}")
    if not file_key:
        raise LogicError("file_key is required for DELETE action.")
    if not file_key.startswith(f"{firm_id}/"):
        raise AuthError("Permission denied: you are not allowed to access this file key.")

    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']
    
    try:
        logging.info(f"Checking existence and getting metadata for S3 object: {file_key}")
        head_response = s3_client.head_object(Bucket=bucket_name, Key=file_key)
        file_size = head_response['ContentLength']
        logging.info(f"Object found. Size is {file_size} bytes.")
    except s3_client.exceptions.ClientError as e:
        if e.response['Error']['Code'] == '404':
            logging.warning(f"File with key {file_key} not found in storage. Cannot delete.")
            raise NotFoundError(f"File with key {file_key} not found.")
        else:
            logging.error(f"S3 ClientError when checking file {file_key}: {e}", exc_info=True)
            raise

    logging.info(f"Attempting to delete S3 object: {file_key}")
    storage_utils.delete_object(s3_client, bucket_name, file_key)
    logging.info(f"S3 object {file_key} deleted successfully.")

    if file_size > 0:
        logging.info(f"Updating used_bytes in DB by decrementing {file_size} bytes.")
        def decrement_storage_size_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())
            read_q = session.prepare("DECLARE $fid AS Utf8; SELECT storage_info_json FROM `tariffs_and_storage` WHERE firm_id = $fid;")
            res = tx.execute(read_q, {"$fid": firm_id})
            if not res[0].rows:
                logging.warning(f"No record found for firm {firm_id} in tariffs DB to decrement storage size.")
                return

            storage_info = json.loads(res[0].rows[0].storage_info_json or '{}')
            current_used = storage_info.get('used_bytes', 0)
            logging.info(f"Current used_bytes: {current_used}. New used_bytes will be {max(0, current_used - file_size)}.")
            storage_info['used_bytes'] = max(0, current_used - file_size)
            
            update_q = session.prepare("DECLARE $fid AS Utf8; DECLARE $sij AS Json; UPDATE `tariffs_and_storage` SET storage_info_json = $sij WHERE firm_id = $fid;")
            tx.execute(update_q, {"$fid": firm_id, "$sij": json.dumps(storage_info)})
            tx.commit()
            logging.info(f"Successfully updated used_bytes for firm {firm_id}.")
        
        pool.retry_operation_sync(decrement_storage_size_transaction)

    return {"statusCode": 200, "body": json.dumps({"message": "File deleted successfully"})}

def handle_get_download_url(firm_id, file_key):
    """
    Обрабатывает запрос на получение ссылки для скачивания.
    """
    logging.info(f"Handling GET_DOWNLOAD_URL for firm {firm_id}, file_key: {file_key}")
    if not file_key:
        raise LogicError("file_key is required for GET_DOWNLOAD_URL action.")
    
    if not file_key.startswith(f"{firm_id}/"):
        raise AuthError("Permission denied: you are not allowed to access this file key.")

    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']

    try:
        s3_client.head_object(Bucket=bucket_name, Key=file_key)
    except s3_client.exceptions.ClientError as e:
        if e.response['Error']['Code'] == '404':
            raise NotFoundError(f"Cannot get download URL. File with key {file_key} not found.")
        else:
            raise

    download_url = storage_utils.generate_presigned_download_url(s3_client, bucket_name, file_key)

    if not download_url:
        raise Exception("Could not generate a download URL from the storage service.")

    logging.info(f"Successfully generated download URL for file_key: {file_key}")
    return {"statusCode": 200, "body": json.dumps({"download_url": download_url, "file_key": file_key})}

def handle_confirm_upload(pool, firm_id, file_key):
    """
    Подтверждает успешную загрузку файла, получает его размер из S3
    и атомарно увеличивает счетчик used_bytes в базе данных.
    """
    logging.info(f"Handling CONFIRM_UPLOAD for firm {firm_id}, file_key: {file_key}")
    if not file_key:
        raise LogicError("file_key is required for CONFIRM_UPLOAD action.")
    if not file_key.startswith(f"{firm_id}/"):
        raise AuthError("Permission denied: you are not allowed to access this file key.")

    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']
    
    file_size = 0
    try:
        logging.info(f"Getting metadata for S3 object: {file_key}")
        file_size = s3_client.head_object(Bucket=bucket_name, Key=file_key)['ContentLength']
        logging.info(f"Object size is {file_size} bytes.")
    except s3_client.exceptions.ClientError as e:
        if e.response['Error']['Code'] == '404':
            raise NotFoundError(f"Cannot confirm upload. File with key {file_key} not found in storage.")
        else:
            raise

    if file_size > 0:
        logging.info(f"Updating used_bytes in DB by incrementing {file_size} bytes.")
        
        def increment_storage_size_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())
            # Сначала читаем текущее значение
            read_q = session.prepare("DECLARE $fid AS Utf8; SELECT storage_info_json FROM `tariffs_and_storage` WHERE firm_id = $fid;")
            res = tx.execute(read_q, {"$fid": firm_id})
            if not res[0].rows:
                logging.warning(f"No record found for firm {firm_id} in tariffs DB to increment storage size.")
                return

            storage_info = json.loads(res[0].rows[0].storage_info_json or '{}')
            current_used = storage_info.get('used_bytes', 0)
            logging.info(f"Current used_bytes: {current_used}. New used_bytes will be {current_used + file_size}.")
            storage_info['used_bytes'] = current_used + file_size
            
            # Затем записываем новое
            update_q = session.prepare("DECLARE $fid AS Utf8; DECLARE $sij AS Json; UPDATE `tariffs_and_storage` SET storage_info_json = $sij WHERE firm_id = $fid;")
            tx.execute(update_q, {"$fid": firm_id, "$sij": json.dumps(storage_info)})
            tx.commit()
            logging.info(f"Successfully updated used_bytes for firm {firm_id}.")
        
        pool.retry_operation_sync(increment_storage_size_transaction)

    return {"statusCode": 200, "body": json.dumps({"message": "Upload confirmed and storage usage updated."})}
```