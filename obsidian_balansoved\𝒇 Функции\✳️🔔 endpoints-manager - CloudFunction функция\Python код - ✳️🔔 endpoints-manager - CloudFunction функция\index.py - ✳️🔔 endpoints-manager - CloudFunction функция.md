
```python
# index.py

import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils, request_parser
import pprint

import add_endpoint
import delete_endpoint
import get_endpoint
import send_notification
from custom_errors import AuthError, LogicError, NotFoundError

logging.getLogger().setLevel(logging.DEBUG)

def _log_event_context(event, context):
    try:
        logging.debug("RAW EVENT: %s", json.dumps(event, default=str)[:10000])
    except Exception as e:
        logging.debug("RAW EVENT (non-json serialisable): %s", event)
    if context is not None:
        logging.debug(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s",
            getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None),
        )

def handler(event, context):
    _log_event_context(event, context)
    try:
        # 1) Пытаемся получить action из контекста API Gateway (работает для внешних вызовов)
        action = (
            event.get('requestContext', {})
                 .get('apiGateway', {})
                 .get('operationContext', {})
                 .get('action')
        )

        # 2) Разбираем тело запроса (данные понадобятся в любом случае)
        data = request_parser.parse_request_body(event)

        # 3) Если из контекста ничего не получили (внутренний вызов триггера), берём action из тела
        if not action:
            action = data.get('action')

        logging.debug("Action: %s, Parsed body: %s", action, data)
        if not action:
            raise LogicError("Action is a required parameter.")

        # --------- АВТОРИЗАЦИЯ ---------
        user_id = None
        headers = event.get('headers', {}) or {}
        
        # Для внутренних вызовов (от scheduler) JWT пользователя будет в X-Forwarded-Authorization.
        # Для прямых вызовов (от API-GW) - в стандартном Authorization.
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization', None))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))

        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            # Проверяем, является ли токен пользовательским JWT
            user_payload = auth_utils.verify_jwt(token)
            if user_payload and 'user_id' in user_payload:
                user_id = user_payload['user_id']
                logging.debug("Authenticated user_id=%s from user JWT", user_id)
            else:
                logging.debug("Auth header found, but it's not a valid user JWT. Treating as system call.")
        else:
            # Вызов без заголовка Authorization тоже считаем внутренним
            logging.debug("No user Authorization header found. Treating as internal system call.")

        # --- Маршрутизация ---
        endpoints_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_ENDPOINTS"], os.environ["YDB_DATABASE_ENDPOINTS"])
        endpoints_pool = ydb.SessionPool(endpoints_driver)
        logging.debug("Endpoints DB driver initialised")

        if action == 'ADD':
            if not user_id: raise AuthError("Authorization is required for ADD action.")
            push_token = data.get('push_token')
            device_info = data.get('device_info', {})
            return add_endpoint.handle_add_endpoint(endpoints_pool, user_id, push_token, device_info)
        
        elif action == 'DELETE':
            if not user_id: raise AuthError("Authorization is required for DELETE action.")
            push_token = data.get('push_token')
            return delete_endpoint.handle_delete_endpoint(endpoints_pool, push_token, user_id)

        elif action == 'GET_LIST':
            if not user_id: raise AuthError("Authorization is required for GET_LIST action.")
            return get_endpoint.handle_get_endpoints(endpoints_pool, user_id)

        elif action == 'SEND':
            # Для SEND авторизация по JWT не нужна, т.к. это внутренний вызов,
            # и user_id_to_notify передается в теле.
            notices_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_NOTICES"], os.environ["YDB_DATABASE_NOTICES"])
            notices_pool = ydb.SessionPool(notices_driver)
            user_id_to_notify = data.get('user_id_to_notify')
            payload = data.get('payload')
            return send_notification.handle_send_notification(endpoints_pool, notices_pool, notices_driver, user_id_to_notify, payload)

        else:
            raise LogicError(f"Invalid action specified: '{action}'.")

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing endpoint request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```