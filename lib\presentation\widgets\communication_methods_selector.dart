import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';

/// Виджет для выбора средств связи с текстовыми полями
class CommunicationMethodsSelector extends StatelessWidget {
  final Map<String, String> selectedMethods;
  final Function(Map<String, String>) onChanged;
  final bool isEditing;

  const CommunicationMethodsSelector({
    super.key,
    required this.selectedMethods,
    required this.onChanged,
    required this.isEditing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.connect_without_contact_outlined,
              size: 18,
              color: theme.textTheme.bodySmall?.color,
            ),
            const SizedBox(width: 8),
            Text(
              'Дополнительно',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: theme.textTheme.bodyMedium?.color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (selectedMethods.isEmpty && !isEditing)
          Text(
            'Не указаны',
            style: TextStyle(
              color: theme.textTheme.bodySmall?.color,
              fontStyle: FontStyle.italic,
            ),
          )
        else
          Column(
            children: [
              // Отображаем выбранные средства связи с текстовыми полями
              ...selectedMethods.entries.map((entry) {
                final method = entry.key;
                final value = entry.value;
                final methodColor = _getMethodColor(method, theme);

                final methodChip = Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: methodColor.backgroundColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _getMethodIcon(method, methodColor.textColor),
                      const SizedBox(width: 4),
                      Text(
                        method,
                        style: TextStyle(
                          color: methodColor.textColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );

                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child:
                      (value.isNotEmpty || isEditing)
                          ? Row(
                            children: [
                              methodChip,
                              const SizedBox(width: 8),
                              // Текстовое поле для значения
                              Expanded(
                                child: TextFormField(
                                  initialValue: value,
                                  decoration: InputDecoration(
                                    hintText: 'Введите $method',
                                    isDense: true,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                    border: const OutlineInputBorder(),
                                  ),
                                  readOnly: !isEditing,
                                  onChanged:
                                      isEditing
                                          ? (newValue) => _updateMethodValue(
                                            method,
                                            newValue,
                                          )
                                          : null,
                                ),
                              ),
                              // Кнопка удаления
                              if (isEditing)
                                IconButton(
                                  icon: const Icon(Icons.close, size: 16),
                                  onPressed: () => _removeMethod(method),
                                  padding: const EdgeInsets.all(4),
                                  constraints: const BoxConstraints(
                                    minWidth: 24,
                                    minHeight: 24,
                                  ),
                                ),
                            ],
                          )
                          : Row(children: [Expanded(child: methodChip)]),
                );
              }),

              // Кнопка добавления нового средства связи
              if (isEditing &&
                  selectedMethods.length <
                      (ClientConstants.communicationMethods.length +
                          ClientConstants.additionalInfo.length))
                Align(
                  alignment: Alignment.centerLeft,
                  child: ActionChip(
                    label: const Text(
                      '+ Добавить',
                      style: TextStyle(fontSize: 12),
                    ),
                    avatar: const Icon(Icons.add, size: 16),
                    onPressed: () => _showMethodSelector(context),
                  ),
                ),
            ],
          ),
      ],
    );
  }

  void _removeMethod(String method) {
    final newMethods = Map<String, String>.from(selectedMethods);
    newMethods.remove(method);
    onChanged(newMethods);
  }

  void _updateMethodValue(String method, String value) {
    final newMethods = Map<String, String>.from(selectedMethods);
    newMethods[method] = value;
    onChanged(newMethods);
  }

  void _showMethodSelector(BuildContext context) {
    final availableCommunicationMethods =
        ClientConstants.communicationMethods
            .where((method) => !selectedMethods.containsKey(method))
            .toList();

    final availableAdditionalInfo =
        ClientConstants.additionalInfo
            .where((method) => !selectedMethods.containsKey(method))
            .toList();

    if (availableCommunicationMethods.isEmpty &&
        availableAdditionalInfo.isEmpty) {
      return;
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Выберите поле'),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (availableAdditionalInfo.isNotEmpty)
                      _buildMethodGroup(
                        context,
                        'Дополнительно',
                        availableAdditionalInfo,
                      ),
                    if (availableCommunicationMethods.isNotEmpty)
                      _buildMethodGroup(
                        context,
                        'Средства связи',
                        availableCommunicationMethods,
                      ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Отмена'),
              ),
            ],
          ),
    );
  }

  Widget _buildMethodGroup(
    BuildContext context,
    String title,
    List<String> methods,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(title, style: Theme.of(context).textTheme.titleMedium),
        ),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              methods.map((method) {
                final methodColor = _getMethodColor(method, Theme.of(context));
                return ActionChip(
                  label: Text(method),
                  avatar: _getMethodIcon(method, methodColor.textColor),
                  backgroundColor: methodColor.backgroundColor,
                  labelStyle: TextStyle(color: methodColor.textColor),
                  onPressed: () {
                    final newMethods = Map<String, String>.from(
                      selectedMethods,
                    );
                    newMethods[method] = ''; // Добавляем с пустым значением
                    onChanged(newMethods);
                    Navigator.of(context).pop();
                  },
                );
              }).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Icon _getMethodIcon(String method, [Color? color]) {
    switch (method.toLowerCase()) {
      case 'должность':
        return Icon(Icons.work, size: 14, color: color);
      case 'телефон':
        return Icon(Icons.phone, size: 14, color: color);
      case 'whatsapp':
        return Icon(Icons.message, size: 14, color: color);
      case 'telegram':
        return Icon(Icons.send, size: 14, color: color);
      case 'viber':
        return Icon(Icons.vibration, size: 14, color: color);
      case 'email':
        return Icon(Icons.email, size: 14, color: color);
      case 'skype':
        return Icon(Icons.video_call, size: 14, color: color);
      case 'vk':
      case 'instagram':
      case 'facebook':
        return Icon(Icons.public, size: 14, color: color);
      case 'sms':
        return Icon(Icons.sms, size: 14, color: color);
      case 'discord':
        return Icon(Icons.chat, size: 14, color: color);
      case 'zoom':
        return Icon(Icons.videocam, size: 14, color: color);
      case 'почта россии':
        return Icon(Icons.local_post_office, size: 14, color: color);
      default:
        return Icon(Icons.contact_phone, size: 14, color: color);
    }
  }

  _MethodColors _getMethodColor(String method, ThemeData theme) {
    switch (method.toLowerCase()) {
      case 'должность':
        return _MethodColors(
          backgroundColor: Colors.amber.shade100,
          textColor: const Color.fromARGB(255, 173, 86, 19),
        );
      case 'телефон':
        return _MethodColors(
          backgroundColor: Colors.blue.shade100,
          textColor: Colors.blue.shade800,
        );
      case 'whatsapp':
        return _MethodColors(
          backgroundColor: Colors.green.shade100,
          textColor: Colors.green.shade800,
        );
      case 'telegram':
        return _MethodColors(
          backgroundColor: Colors.lightBlue.shade100,
          textColor: Colors.lightBlue.shade800,
        );
      case 'viber':
        return _MethodColors(
          backgroundColor: Colors.purple.shade100,
          textColor: Colors.purple.shade800,
        );
      case 'email':
        return _MethodColors(
          backgroundColor: Colors.red.shade100,
          textColor: Colors.red.shade800,
        );
      case 'skype':
        return _MethodColors(
          backgroundColor: Colors.cyan.shade100,
          textColor: Colors.cyan.shade800,
        );
      case 'vk':
        return _MethodColors(
          backgroundColor: Colors.indigo.shade100,
          textColor: Colors.indigo.shade800,
        );
      case 'instagram':
        return _MethodColors(
          backgroundColor: Colors.pink.shade100,
          textColor: Colors.pink.shade800,
        );
      case 'facebook':
        return _MethodColors(
          backgroundColor: Colors.blue.shade100,
          textColor: Colors.blue.shade800,
        );
      case 'sms':
        return _MethodColors(
          backgroundColor: Colors.orange.shade100,
          textColor: Colors.orange.shade800,
        );
      case 'discord':
        return _MethodColors(
          backgroundColor: Colors.deepPurple.shade100,
          textColor: Colors.deepPurple.shade800,
        );
      case 'zoom':
        return _MethodColors(
          backgroundColor: Colors.blue.shade100,
          textColor: Colors.blue.shade800,
        );
      case 'почта россии':
        return _MethodColors(
          backgroundColor: Colors.brown.shade100,
          textColor: Colors.brown.shade800,
        );
      default:
        return _MethodColors(
          backgroundColor: theme.colorScheme.primaryContainer,
          textColor: theme.colorScheme.onPrimaryContainer,
        );
    }
  }
}

class _MethodColors {
  final Color backgroundColor;
  final Color textColor;

  const _MethodColors({required this.backgroundColor, required this.textColor});
}
