
```python
import json, os, uuid, datetime, pytz, logging
from datetime import timedelta
import ydb
from custom_errors import LogicError, AuthError, NotFoundError
import reminders_processor

def json_datetime_serializer(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime.datetime, datetime.date)):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")

def _get_declare_for_task(payload):
    declare_clauses, params = "", {}
    type_map = {
        'task_id': ydb.PrimitiveType.Utf8, 
        'title': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'description': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'client_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'assignee_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'observer_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'creator_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'status': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'priority': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'due_date': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 
        'completed_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 
        'attachments_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'checklist_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'reminders_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'recurrence_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'options_json': ydb.OptionalType(ydb.PrimitiveType.Json), 
        'holiday_transfer_rule': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'origin_task_id': ydb.OptionalType(ydb.PrimitiveType.Utf8), 
        'is_system_task': ydb.OptionalType(ydb.PrimitiveType.Bool), 
        'created_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 
        'updated_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
        'periodic_parent_id': ydb.OptionalType(ydb.PrimitiveType.Utf8) # Добавлено: периодическая родительская задача
    }
    for key, value in payload.items():
        if key in type_map:
            if isinstance(type_map[key], ydb.OptionalType) and type_map[key].item == ydb.PrimitiveType.Timestamp and isinstance(value, str):
                try: params[f"${key}"] = datetime.datetime.strptime(value.replace('Z', ''), "%Y-%m-%dT%H:%M:%S").replace(tzinfo=pytz.utc)
                except (ValueError, TypeError): raise LogicError(f"Invalid timestamp format for '{key}'. Use YYYY-MM-DDTHH:MM:SSZ.")
            else: params[f"${key}"] = value
            type_name_str = str(type_map[key]); type_name = type_name_str.replace("Optional[", "").replace("]", "") if "Optional" in type_name_str else type_name_str; declare_clauses += f"DECLARE ${key} AS {type_name}; "
    return declare_clauses, params

def _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
    print(f"PERMISSIONS: Checking modify permissions for task {task_id}, user {requesting_user_id}, admin/owner: {is_admin_or_owner}")
    if is_admin_or_owner: 
        print(f"PERMISSIONS: User {requesting_user_id} has admin/owner access to task {task_id}")
        return True
    
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    res = session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(f"DECLARE $task_id AS Utf8; SELECT creator_ids_json, assignee_ids_json, options_json FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    if not res[0].rows: 
        print(f"PERMISSIONS: Task {task_id} not found when checking permissions")
        raise NotFoundError(f"Task with id {task_id} not found.")
    
    task_data = res[0].rows[0]
    creator_ids = json.loads(task_data.get('creator_ids_json', '[]') or '[]')
    assignee_ids = json.loads(task_data.get('assignee_ids_json', '[]') or '[]')
    options = json.loads(task_data.get('options_json', '{}') or '{}')
    
    print(f"PERMISSIONS: Task {task_id} - creators: {len(creator_ids)}, assignees: {len(assignee_ids)}")
    
    if requesting_user_id in creator_ids: 
        print(f"PERMISSIONS: User {requesting_user_id} is creator of task {task_id}")
        return True
    
    if requesting_user_id in assignee_ids:
        allow_assignee_edit = options.get('allow_assignee_to_edit', False)
        print(f"PERMISSIONS: User {requesting_user_id} is assignee of task {task_id}, allow_assignee_to_edit: {allow_assignee_edit}")
        if allow_assignee_edit: 
            return True
    
    print(f"PERMISSIONS: User {requesting_user_id} has no modify permissions for task {task_id}")
    return False

# ИСПРАВЛЕНО: Добавлен 'session' в аргументы
def _update_main_task_subtasks(session, tx, table_name, main_task_id, subtask_id, action='add'):
    print(f"UPDATE_SUBTASKS: Starting {action} operation for main_task {main_task_id}, subtask {subtask_id}")
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    query = session.prepare(f"DECLARE $task_id AS Utf8; SELECT subtasks_json FROM `{table_name}` WHERE task_id = $task_id;")
    res = tx.execute(query, {"$task_id": main_task_id})

    if not res[0].rows:
        print(f"UPDATE_SUBTASKS: Main task {main_task_id} not found when trying to update subtasks")
        logging.warning(f"Main task {main_task_id} not found when trying to update subtasks.")
        return
    
    subtasks = json.loads(res[0].rows[0].subtasks_json or '[]')
    print(f"UPDATE_SUBTASKS: Current subtasks for main_task {main_task_id}: {len(subtasks)} items")
    
    if action == 'add' and subtask_id not in subtasks:
        subtasks.append(subtask_id)
        print(f"UPDATE_SUBTASKS: Added subtask {subtask_id} to main_task {main_task_id}")
    elif action == 'remove' and subtask_id in subtasks:
        subtasks.remove(subtask_id)
        print(f"UPDATE_SUBTASKS: Removed subtask {subtask_id} from main_task {main_task_id}")
    else: 
        print(f"UPDATE_SUBTASKS: No changes needed for main_task {main_task_id}, subtask {subtask_id}")
        return
    
    new_subtasks_json = json.dumps(subtasks)
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    update_query = session.prepare(f"DECLARE $task_id AS Utf8; DECLARE $subtasks_json AS Json; UPDATE `{table_name}` SET subtasks_json = $subtasks_json WHERE task_id = $task_id;")
    tx.execute(update_query, {"$task_id": main_task_id, "$subtasks_json": new_subtasks_json})
    print(f"UPDATE_SUBTASKS: Successfully updated subtasks for main_task {main_task_id}, new count: {len(subtasks)}")

# ИСПРАВЛЕНО: Добавлен 'session' в аргументы
def _update_periodic_parent_children(session, tx, table_name, parent_id, child_id, action='add'):
    print(f"UPDATE_PERIODIC_CHILDREN: Starting {action} operation for parent {parent_id}, child {child_id}")
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    query = session.prepare(f"DECLARE $task_id AS Utf8; SELECT periodic_children_json FROM `{table_name}` WHERE task_id = $task_id;")
    res = tx.execute(query, {"$task_id": parent_id})

    if not res[0].rows:
        print(f"UPDATE_PERIODIC_CHILDREN: Periodic parent task {parent_id} not found when trying to update children")
        logging.warning(f"Periodic parent task {parent_id} not found when trying to update children.")
        return
    
    children = json.loads(res[0].rows[0].periodic_children_json or '[]')
    print(f"UPDATE_PERIODIC_CHILDREN: Current children for parent {parent_id}: {len(children)} items")
    
    if action == 'add' and child_id not in children:
        children.append(child_id)
        print(f"UPDATE_PERIODIC_CHILDREN: Added child {child_id} to parent {parent_id}")
    elif action == 'remove' and child_id in children:
        children.remove(child_id)
        print(f"UPDATE_PERIODIC_CHILDREN: Removed child {child_id} from parent {parent_id}")
    else: 
        print(f"UPDATE_PERIODIC_CHILDREN: No changes needed for parent {parent_id}, child {child_id}")
        return
    
    new_children_json = json.dumps(children)
    # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
    update_query = session.prepare(f"DECLARE $task_id AS Utf8; DECLARE $periodic_children_json AS Json; UPDATE `{table_name}` SET periodic_children_json = $periodic_children_json WHERE task_id = $task_id;")
    tx.execute(update_query, {"$task_id": parent_id, "$periodic_children_json": new_children_json})
    print(f"UPDATE_PERIODIC_CHILDREN: Successfully updated children for parent {parent_id}, new count: {len(children)}")


def upsert_task(session, table_name, payload, task_id, requesting_user_id, is_admin_or_owner, user_jwt, firm_id):
    print(f"UPSERT_TASK: Starting upsert operation for task_id: {task_id}, user: {requesting_user_id}")
    print(f"UPSERT_TASK: Payload keys: {list(payload.keys()) if payload else 'None'}")
    
    # ИСПРАВЛЕНО: Добавлен firm_id в сигнатуру вложенной функции
    def manage_scheduler_event(current_task_id, new_payload, old_recurrence_json_str, user_jwt_token, db_session, db_table_name, firm_id_for_child):
        print(f"UPSERT_TASK: Managing scheduler event for task {current_task_id}")
        new_recurrence_json_str = new_payload.get('recurrence_json')
        print(f"UPSERT_TASK: Old recurrence: {bool(old_recurrence_json_str)}, New recurrence: {bool(new_recurrence_json_str)}")
        
        if new_recurrence_json_str and new_recurrence_json_str != 'null':
            print(f"UPSERT_TASK: Creating/updating scheduler event for task {current_task_id}")
            logging.info(f"[{current_task_id}] Upserting scheduler event due to new/modified recurrence rule.")
            
            try:
                recurrence_data = json.loads(new_recurrence_json_str)
                is_annual = recurrence_data.get('is_annual', False)
                execution_dates_json = recurrence_data.get('execution_dates_json', [])
                print(f"UPSERT_TASK: Parsed recurrence data for task {current_task_id}: is_annual={is_annual}, dates_count={len(execution_dates_json)}")
                
                if not execution_dates_json and not is_annual:
                     logging.warning(f"[{current_task_id}] No execution dates provided. Aborting scheduler event creation.")
                     print(f"UPSERT_TASK: Warning - No execution dates for task {current_task_id}")
                     return

            except (json.JSONDecodeError, TypeError) as e:
                logging.error(f"[{current_task_id}] Invalid recurrence_json format: {e}")
                print(f"UPSERT_TASK: Error parsing recurrence data for task {current_task_id}: {e}")
                return

            # ------------------ NEW CODE: fetch existing event_id ------------------
            event_id = None
            try:
                get_payload = {"action": "GET", "custom_identifier": current_task_id}
                get_resp = reminders_processor.invoke_utils.invoke_function(
                    function_id=os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER"),
                    payload=get_payload,
                    user_jwt=user_jwt_token
                )
                if get_resp and get_resp.get("statusCode") == 200 and get_resp.get("body"):
                    try:
                        body_json = json.loads(get_resp["body"])
                        data_list = body_json.get("data") or []
                        if isinstance(data_list, list) and data_list:
                            event_id = data_list[0].get("event_id")
                            print(f"UPSERT_TASK: Found existing scheduler event_id {event_id} for task {current_task_id}")
                    except (json.JSONDecodeError, TypeError):
                        pass
            except Exception as e:
                logging.warning(f"[{current_task_id}] Could not fetch existing scheduler event: {e}")
            # -----------------------------------------------------------------------

            child_task_payload = new_payload.copy()
            child_task_payload.pop('task_id', None); child_task_payload.pop('recurrence_json', None); child_task_payload.pop('periodic_children_json', None); child_task_payload.pop('created_at', None); child_task_payload.pop('updated_at', None)

            # Наследование полей от родительской задачи
            fields_to_inherit = [
                'creator_ids_json', 'assignee_ids_json', 'observer_ids_json',
                'description', 'attachments_json', 'title',
                'client_ids_json', 'priority', 'checklist_json',
                'options_json', 'holiday_transfer_rule', 'is_system_task'
            ]
            for field in fields_to_inherit:
                if field in new_payload:
                    child_task_payload[field] = new_payload[field]

            child_task_payload['periodic_parent_id'] = current_task_id
            print(f"UPSERT_TASK: Prepared child task payload for task {current_task_id}")

            scheduler_payload = {
                "action": "UPSERT",
                "payload": {
                    "function_id": os.environ.get("FUNCTION_ID_EDIT_TASK"),
                    "custom_identifier": current_task_id,
                    "is_annual": is_annual,
                    "execution_dates_json": json.dumps(execution_dates_json),
                    "request_body_json": json.dumps({
                        "action": "UPSERT",
                        "firm_id": firm_id_for_child,
                        "payload": child_task_payload
                    }, default=json_datetime_serializer),
                    "request_headers_json": json.dumps({"Authorization": f"Bearer {user_jwt_token}"}),
                    "is_active": True
                }
            }
            if event_id:
                scheduler_payload["event_id"] = event_id
            
            try:
                print(f"UPSERT_TASK: Invoking scheduler-manager for task {current_task_id}")
                logging.info(f"[{current_task_id}] Invoking scheduler-manager to upsert event.")
                scheduler_response = reminders_processor.invoke_utils.invoke_function(
                    function_id=os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER"),
                    payload=scheduler_payload,
                    user_jwt=user_jwt_token
                )
                print(f"UPSERT_TASK: Successfully created/updated scheduler event for task {current_task_id}")
                logging.info(f"[{current_task_id}] Successfully upserted scheduler event.")
                
                # ИСПРАВЛЕНО: Корректно парсим ответ и извлекаем event_id
                if scheduler_response and scheduler_response.get('statusCode') == 201 and scheduler_response.get('body'):
                    try:
                        response_body = json.loads(scheduler_response['body'])
                        event_id = response_body.get('event_id')
                    except (json.JSONDecodeError, TypeError):
                        event_id = None
                        logging.warning(f"[{current_task_id}] Could not parse event_id from scheduler response body.")
                    
                    if event_id:
                        print(f"UPSERT_TASK: Saving event_id {event_id} to recurrence_json for task {current_task_id}")
                        logging.info(f"[{current_task_id}] Saving event_id {event_id} to recurrence_json.")
                        
                        updated_recurrence_data = recurrence_data.copy()
                        updated_recurrence_data['event_id'] = event_id
                        updated_recurrence_json = json.dumps(updated_recurrence_data)
                        
                        update_tx = db_session.transaction(ydb.SerializableReadWrite())
                        update_tx.execute(
                            db_session.prepare(f"DECLARE $task_id AS Utf8; DECLARE $recurrence_json AS Json; UPDATE `{db_table_name}` SET recurrence_json = $recurrence_json WHERE task_id = $task_id;"),
                            {"$task_id": current_task_id, "$recurrence_json": updated_recurrence_json}
                        )
                        update_tx.commit()
                        print(f"UPSERT_TASK: Successfully saved event_id {event_id} to recurrence_json for task {current_task_id}")
                        logging.info(f"[{current_task_id}] Successfully saved event_id {event_id} to recurrence_json.")

            except Exception as e:
                error_details = str(e);_ = getattr(e, 'details', None); error_details = _ if _ else error_details
                print(f"UPSERT_TASK: Warning - Failed to manage scheduler event for task {current_task_id}: {error_details}")
                logging.error(f"[{current_task_id}] Failed to upsert scheduler event. Details: {error_details}", exc_info=True)

        elif (old_recurrence_json_str and old_recurrence_json_str != 'null') and (not new_recurrence_json_str or new_recurrence_json_str == 'null'):
            print(f"UPSERT_TASK: Recurrence removed for task {current_task_id}, attempting to delete scheduler event")
            logging.info(f"[{current_task_id}] Recurrence removed, attempting to delete scheduler event.")
            
            try:
                old_recurrence_data = json.loads(old_recurrence_json_str)
                event_id = old_recurrence_data.get('event_id')
                
                if event_id:
                    print(f"UPSERT_TASK: Found event_id {event_id} in old recurrence_json for task {current_task_id}")
                    logging.info(f"[{current_task_id}] Found event_id {event_id} in old recurrence_json, deleting event.")
                    
                    delete_payload = {"action": "DELETE", "event_id": event_id}
                    reminders_processor.invoke_utils.invoke_function(
                        function_id=os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER"),
                        payload=delete_payload,
                        user_jwt=user_jwt_token
                    )
                    print(f"UPSERT_TASK: Successfully deleted scheduler event {event_id} for task {current_task_id}")
                    logging.info(f"[{current_task_id}] Successfully deleted scheduler event {event_id}.")
                else:
                    print(f"UPSERT_TASK: No event_id found in old recurrence_json for task {current_task_id}")
                    logging.warning(f"[{current_task_id}] No event_id found in old recurrence_json, cannot delete scheduler event.")
                    
            except (json.JSONDecodeError, TypeError) as e:
                print(f"UPSERT_TASK: Error parsing old recurrence_json for task {current_task_id}: {e}")
                logging.error(f"[{current_task_id}] Error parsing old recurrence_json: {e}")
            except Exception as e:
                print(f"UPSERT_TASK: Error deleting scheduler event for task {current_task_id}: {e}")
                logging.error(f"[{current_task_id}] Error deleting scheduler event: {e}", exc_info=True)

    if not payload: raise LogicError("payload is required for UPSERT action.")
    tx = session.transaction(ydb.SerializableReadWrite())
    payload['updated_at'] = datetime.datetime.now(pytz.utc)

    main_task_id = payload.get('main_task_id')
    periodic_parent_id = payload.get('periodic_parent_id')
    original_main_task_id = None
    original_periodic_parent_id = None
    original_recurrence_json = None

    if task_id:
        # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
        res = tx.execute(session.prepare(f"DECLARE $task_id AS Utf8; SELECT main_task_id, periodic_parent_id, recurrence_json FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
        if res[0].rows:
            row = res[0].rows[0]
            original_main_task_id = row.main_task_id
            original_periodic_parent_id = row.periodic_parent_id
            original_recurrence_json = row.recurrence_json
        else:
            raise NotFoundError(f"Task with id {task_id} not found.")

        if not _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
             raise AuthError("You do not have permission to modify this task.")

        payload['task_id'] = task_id
        declare_clauses, params = _get_declare_for_task(payload)
        set_clauses = ", ".join([f"`{k}` = ${k}" for k in payload if k != 'task_id'])
        # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
        tx.execute(session.prepare(f"{declare_clauses} UPDATE `{table_name}` SET {set_clauses} WHERE task_id = $task_id;"), params)

        # ИСПРАВЛЕНО: Передача 'session' в вызовы вспомогательных функций
        if main_task_id != original_main_task_id:
            if original_main_task_id: _update_main_task_subtasks(session, tx, table_name, original_main_task_id, task_id, action='remove')
            if main_task_id: _update_main_task_subtasks(session, tx, table_name, main_task_id, task_id, action='add')

        # ИСПРАВЛЕНО: Передача 'session' в вызовы вспомогательных функций
        if periodic_parent_id != original_periodic_parent_id:
            if original_periodic_parent_id: _update_periodic_parent_children(session, tx, table_name, original_periodic_parent_id, task_id, action='remove')
            if periodic_parent_id: _update_periodic_parent_children(session, tx, table_name, periodic_parent_id, task_id, action='add')

        tx.commit()
        print(f"UPSERT_TASK: Task {task_id} updated successfully in database")

        try:
            print(f"UPSERT_TASK: Managing scheduler events for updated task {task_id}")
            manage_scheduler_event(task_id, payload, original_recurrence_json, user_jwt, session, table_name, firm_id)
            print(f"UPSERT_TASK: Scheduler events managed successfully for task {task_id}")
        except Exception as e:
            print(f"UPSERT_TASK: Error managing scheduler event for task {task_id}: {e}")
            logging.error(f"[{task_id}] Error managing scheduler event after update: {e}", exc_info=True)

        try:
            if 'reminders_json' in payload:
                print(f"UPSERT_TASK: Syncing reminders for updated task {task_id}")
                reminders_processor.sync_reminders(task_id, payload, user_jwt)
                print(f"UPSERT_TASK: Reminders synced successfully for task {task_id}")
        except Exception as e:
            print(f"UPSERT_TASK: Error syncing reminders for task {task_id}: {e}")
            logging.error(f"[{task_id}] Error syncing reminders after update: {e}", exc_info=True)
        
        print(f"UPSERT_TASK: Task update operation completed successfully for task {task_id}")
        return {"statusCode": 200, "body": json.dumps({"message": "Task updated", "task_id": task_id})}
    else: # Create new task
        new_task_id = str(uuid.uuid4())
        payload['task_id'] = new_task_id
        payload['created_at'] = payload['updated_at']
        if 'creator_ids_json' not in payload: payload['creator_ids_json'] = json.dumps([requesting_user_id])
        if 'is_system_task' not in payload: payload['is_system_task'] = False
        
        declare_clauses, params = _get_declare_for_task(payload)
        columns = ", ".join([f"`{k}`" for k in payload.keys()]); placeholders = ", ".join([f"${k}" for k in payload.keys()])
        # ИСПРАВЛЕНО: Использование session.prepare() для подготовки запроса
        tx.execute(session.prepare(f"{declare_clauses} UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"), params)

        # ИСПРАВЛЕНО: Передача 'session' в вызовы вспомогательных функций
        if main_task_id: _update_main_task_subtasks(session, tx, table_name, main_task_id, new_task_id, action='add')
        if periodic_parent_id: _update_periodic_parent_children(session, tx, table_name, periodic_parent_id, new_task_id, action='add')

        tx.commit()
        print(f"UPSERT_TASK: New task {new_task_id} created successfully in database")

        try:
            print(f"UPSERT_TASK: Managing scheduler events for new task {new_task_id}")
            manage_scheduler_event(new_task_id, payload, None, user_jwt, session, table_name, firm_id)
            print(f"UPSERT_TASK: Scheduler events managed successfully for task {new_task_id}")
        except Exception as e:
            print(f"UPSERT_TASK: Error managing scheduler event for task {new_task_id}: {e}")
            logging.error(f"[{new_task_id}] Error managing scheduler event after create: {e}", exc_info=True)

        try:
            if 'reminders_json' in payload:
                print(f"UPSERT_TASK: Syncing reminders for new task {new_task_id}")
                reminders_processor.sync_reminders(new_task_id, payload, user_jwt)
                print(f"UPSERT_TASK: Reminders synced successfully for task {new_task_id}")
        except Exception as e:
            print(f"UPSERT_TASK: Error syncing reminders for task {new_task_id}: {e}")
            logging.error(f"[{new_task_id}] Error syncing reminders after create: {e}", exc_info=True)
        
        print(f"UPSERT_TASK: Task creation operation completed successfully for task {new_task_id}")
        return {"statusCode": 201, "body": json.dumps({"message": "Task created", "task_id": new_task_id})}
```