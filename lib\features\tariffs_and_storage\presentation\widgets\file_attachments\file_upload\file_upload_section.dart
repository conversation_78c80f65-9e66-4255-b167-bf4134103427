import '../utils/file_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'file_upload_zone.dart';
import '../models/file_attachment_models.dart';
import '../file_display/file_card_widget.dart';
import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class FileUploadSection extends StatefulWidget {
  final List<FileAttachmentItem> files;
  final Function(FileAttachmentItem) onAddFile;
  final Function(FileAttachmentItem) onRemoveFile;
  final Function(FileAttachmentItem) onUpdateFile;
  final VoidCallback? onAutoSave;
  final String? title;
  final List<String>? allowedExtensions;
  final int? maxFileSize;

  const FileUploadSection({
    super.key,
    required this.files,
    required this.onAddFile,
    required this.onRemoveFile,
    required this.onUpdateFile,
    this.onAutoSave,
    this.title,
    this.allowedExtensions,
    this.maxFileSize,
  });

  @override
  State<FileUploadSection> createState() => _FileUploadSectionState();
}

class _FileUploadSectionState extends State<FileUploadSection> {
  final GlobalKey<FileUploadZoneState> _fileUploadZoneKey = GlobalKey<FileUploadZoneState>();
  @override
  void initState() {
    super.initState();
    // Слушаем изменения состояния хранилища
    context.read<TariffsAndStorageCubit>().stream.listen((state) {
      if (mounted) {
        _handleStorageStateChange(state);
      }
    });
  }

  void _handleStorageStateChange(TariffsAndStorageState state) {
    if (state is FileUploadCompleted) {
      // Обновляем карточку (ищем по имени, без привязки к статусу)
      final fileIndex = widget.files.indexWhere(
        (file) => file.name == state.fileName,
      );

      if (fileIndex >= 0) {
        final updatedFile = widget.files[fileIndex].copyWith(
          fileKey: state.fileKey,
          status: FileAttachmentStatus.uploaded,
          uploadProgress: 1.0,
        );
        widget.onUpdateFile(updatedFile);
        widget.onAutoSave?.call();
      }
    } else if (state is FileUploadFailed) {
      // Обновляем карточку, помечая как failed (ищем только по имени)
      final fileIndex = widget.files.indexWhere(
        (file) => file.name == state.fileName,
      );

      if (fileIndex >= 0) {
        final updatedFile = widget.files[fileIndex].copyWith(
          status: FileAttachmentStatus.failed,
          errorMessage: state.message,
        );
        widget.onUpdateFile(updatedFile);
      }
    } else if (state is FileUploadInProgress) {
      // Обновляем прогресс поиска карточки по имени
      final fileIndex = widget.files.indexWhere(
        (file) => file.name == state.fileName,
      );

      if (fileIndex >= 0) {
        final updatedFile = widget.files[fileIndex].copyWith(
          uploadProgress: state.progress,
        );
        widget.onUpdateFile(updatedFile);
      }
    } else if (state is FileDeleted) {
      // Полное удаление из списка по fileKey
      final fileIndex = widget.files.indexWhere(
        (file) => file.fileKey == state.fileKey,
      );

      if (fileIndex >= 0) {
        widget.onRemoveFile(widget.files[fileIndex]);
        widget.onAutoSave?.call();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Заголовок и кнопка
        _buildSectionHeader(),
        const SizedBox(height: 16),

        // Область для файлов (сетка или плейсхолдер с drag&drop)
        widget.files.isEmpty ? _buildUploadZone() : _buildFilesSection(),

        const SizedBox(height: 16),

        // Информация о хранилище
        BlocBuilder<TariffsAndStorageCubit, TariffsAndStorageState>(
          builder: (context, state) {
            if (state is TariffsAndStorageLoaded) {
              return _buildStorageInfo(
                state.data.storageInfo.usedBytes,
                state.data.subscriptionInfo.quotaBytes,
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildSectionHeader() {
    return Row(
      children: [
        Text(
          widget.title ?? 'Файловые вложения',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const Spacer(),
        ElevatedButton.icon(
          icon: const Icon(Icons.cloud_upload_outlined),
          label: const Text('Загрузить файлы'),
          onPressed: () {
            _fileUploadZoneKey.currentState?.pickFiles();
          },
        ),
      ],
    );
  }

  Widget _buildUploadZone() {
    return FileUploadZone(
      key: _fileUploadZoneKey,
      onFilesSelected: _onFilesSelected,
      allowedExtensions: widget.allowedExtensions,
      maxFileSize: widget.maxFileSize,
    );
  }

  Widget _buildFilesSection() {
    // Сортируем файлы: сначала рабочие, потом с ошибками
    final sortedFiles = List<FileAttachmentItem>.from(widget.files);

    if (sortedFiles.isEmpty) return const SizedBox.shrink();

    return FileUploadZone(
      key: _fileUploadZoneKey,
      onFilesSelected: _onFilesSelected,
      allowedExtensions: widget.allowedExtensions,
      maxFileSize: widget.maxFileSize,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: _buildAdaptiveGrid(sortedFiles),
      ),
    );
  }

  Widget _buildAdaptiveGrid(List<FileAttachmentItem> files) {
    return LayoutBuilder(
      builder: (context, constraints) {
        const double minItemWidth = 145;
        const double itemHeight = 170;
        const double spacing = 8;

        // Вычисляем количество колонок, которое влезает
        int crossAxisCount =
            ((constraints.maxWidth + spacing) / (minItemWidth + spacing))
                .floor();
        crossAxisCount = crossAxisCount.clamp(1, 10);

        final double calculatedItemWidth =
            (constraints.maxWidth - (crossAxisCount - 1) * spacing) /
            crossAxisCount;

        final childAspectRatio = calculatedItemWidth / itemHeight;

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: spacing,
            crossAxisSpacing: spacing,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: files.length,
          itemBuilder: (context, index) {
            final file = files[index];

            // Карточка удаления
            if (file.status == FileAttachmentStatus.deleting) {
              return _buildDeletingFileCard(file);
            }

            // Карточка ошибки
            if (file.status == FileAttachmentStatus.failed) {
              return _buildFailedFileCard(file);
            }

            // Карточка процесса загрузки/ожидания
            if (file.status == FileAttachmentStatus.uploading ||
                file.status == FileAttachmentStatus.pending) {
              return _buildUploadingFileCard(file);
            }

            // Загруженный файл (только если fileKey не null)
            if (file.fileKey != null) {
              return FileCardWidget(
                fileData: file.toMap(),
                showRemoveButton: true,
                onRemove: () => _onDeleteFile(file),
              );
            }

            // Если fileKey null и статус загружен – пропускаем элемент
            return const SizedBox.shrink();
          },
        );
      },
    );
  }

  Widget _buildFailedFileCard(FileAttachmentItem file) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border.all(color: theme.colorScheme.error.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Stack(
        children: [
          // File Icon and Name
          Padding(
            padding: const EdgeInsets.all(6.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  flex: 3,
                  child: Center(child: FileUtils.getFileIcon(file.name)),
                ),
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        file.name,
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                          fontSize: 11,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                      if (file.fileSize != null) ...[
                        const SizedBox(height: 1),
                        Text(
                          FileUtils.formatFileSize(file.fileSize!),
                          style: theme.textTheme.labelSmall?.copyWith(
                            fontSize: 9,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Error icon bottom left
          Positioned(
            left: 4,
            bottom: 4,
            child: Tooltip(
              message: file.errorMessage ?? 'Неизвестная ошибка',
              child: Icon(
                Icons.error_outline,
                color: theme.colorScheme.error,
                size: 16,
              ),
            ),
          ),

          // Retry and Delete buttons top right
          Positioned(
            top: 2,
            right: 2,
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => _retryUpload(file),
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(9),
                    ),
                    child: Icon(
                      Icons.refresh,
                      size: 10,
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap:
                      () =>
                          file.fileKey != null
                              ? _onDeleteFile(file)
                              : widget.onRemoveFile(file),
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(9),
                    ),
                    child: Icon(
                      Icons.close,
                      size: 10,
                      color: theme.colorScheme.onError,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadingFileCard(FileAttachmentItem file) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border.all(color: theme.dividerColor.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(6),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.cloud_upload, size: 48),
                const SizedBox(height: 8),
                LinearProgressIndicator(value: file.uploadProgress),
                const SizedBox(height: 4),
                Text(
                  file.name,
                  style: theme.textTheme.bodySmall?.copyWith(fontSize: 11),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // Кнопка отмены загрузки/удаления
          Positioned(
            top: 2,
            right: 2,
            child: GestureDetector(
              onTap: () => widget.onRemoveFile(file),
              child: Container(
                width: 18,
                height: 18,
                decoration: BoxDecoration(
                  color: theme.colorScheme.error.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(9),
                ),
                child: Icon(
                  Icons.close,
                  size: 10,
                  color: theme.colorScheme.onError,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeletingFileCard(FileAttachmentItem file) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border.all(color: theme.dividerColor.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation(theme.colorScheme.primary),
          ),
        ),
      ),
    );
  }

  Widget _buildStorageInfo(int usedBytes, int quotaBytes) {
    final theme = Theme.of(context);
    final usedPercentage = quotaBytes > 0 ? (usedBytes / quotaBytes) : 0.0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.storage,
            size: 16,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Использовано хранилища: ${_formatFileSize(usedBytes)} из ${_formatFileSize(quotaBytes)}',
                  style: theme.textTheme.bodySmall,
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: usedPercentage.clamp(0.0, 1.0),
                  backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onFilesSelected(List<FileUploadItem> uploadItems) {
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Не выбрана фирма')));
      return;
    }

    final storageCubit = context.read<TariffsAndStorageCubit>();

    for (final uploadItem in uploadItems) {
      // Проверяем, не был ли такой файл уже добавлен (по имени)
      final alreadyExists = widget.files.any((f) => f.name == uploadItem.name);
      if (!alreadyExists) {
        final fileAttachment = FileAttachmentItem(
          name: uploadItem.name,
          fileSize: uploadItem.size,
          status: FileAttachmentStatus.uploading,
        );

        widget.onAddFile(fileAttachment);

        // Запускаем загрузку только если файл действительно добавлен
        if (uploadItem.bytes != null) {
          storageCubit.uploadFile(
            firmId: firmState.selectedFirm!.id,
            fileName: uploadItem.name,
            fileBytes: uploadItem.bytes!,
          );
        }
      }
    }

    widget.onAutoSave?.call();
  }

  void _retryUpload(FileAttachmentItem file) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Для повторной загрузки выберите файл заново'),
      ),
    );
    widget.onRemoveFile(file);
  }

  void _onDeleteFile(FileAttachmentItem file) {
    // Если файл ещё не загружен (нет fileKey) – просто убираем из списка
    if (file.fileKey == null) {
      // Если файл в процессе загрузки — отменяем стрим в Cubit
      if (file.status == FileAttachmentStatus.uploading ||
          file.status == FileAttachmentStatus.pending) {
        context.read<TariffsAndStorageCubit>().cancelUpload();
      }

      widget.onRemoveFile(file);
      return;
    }

    // Отмечаем как "удаляется"
    final deletingFile = file.copyWith(status: FileAttachmentStatus.deleting);
    widget.onUpdateFile(deletingFile);

    final firmState = context.read<ActiveFirmCubit>().state;
    final firmId = firmState.selectedFirm?.id;
    if (firmId == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Не выбрана фирма')));
      return;
    }

    context.read<TariffsAndStorageCubit>().deleteFile(
      firmId: firmId,
      fileKey: file.fileKey!,
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes == 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    int unitIndex = 0;
    double size = bytes.toDouble();

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(unitIndex == 0 ? 0 : 1)} ${units[unitIndex]}';
  }
}
