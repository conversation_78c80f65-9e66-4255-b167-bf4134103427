Идентификатор - etnv11r0ie2974s4c3pd
Путь к базе - /ru-central1/b1g2bgg0i9r8beucbthc/etnv11r0ie2974s4c3pd
Эндпоинт - grpcs://ydb.serverless.yandexcloud.net:2135/?database=/ru-central1/b1g2bgg0i9r8beucbthc/etnv11r0ie2974s4c3pd

---
# Таблицы
В данной базе хранятся данные о компаниях и их пользователях.

#### Таблица: `Firms`

| #   | Имя             | Ключ | Тип         | Описание                                             |
| --- | --------------- | ---- | ----------- | ---------------------------------------------------- |
| 0   | `firm_id`       | PK   | `Utf8`      | Уникальный идентификатор компании (UUID).            |
| 1   | `firm_name`     |      | `Utf8`      | Название компании.                                   |
| 2   | `owner_user_id` |      | `Utf8`      | Идентификатор пользователя-владельца компании.       |
| 3   | `plan_type`     |      | `Utf8`      | Тип тарифного плана (например, "free", "pro").       |
| 4   | `created_at`    |      | `Timestamp` | Время создания записи о компании.                    |
| 5   | `is_active`     |      | `Bool`      | Флаг, указывающий, активна ли компания.              |

#### Таблица: `Users` (ИЗМЕНЕНО)

| #   | Имя              | Ключ | Тип         | Описание                                                      |
| --- | ---------------- | ---- | ----------- | ------------------------------------------------------------- |
| 0   | `user_id`        | PK   | `Utf8`      | Уникальный идентификатор пользователя (UUID).                 |
| 1   | `firm_id`        |      | `Utf8`      | Идентификатор компании, к которой привязан пользователь.      |
| 2   | `email`          |      | `Utf8`      | Email пользователя (уникален в рамках `firm_id`).             |
| 3   | `password_hash`  |      | `Utf8`      | Хеш пароля пользователя.                                      |
| 4   | `full_name`      |      | `Utf8`      | Полное имя пользователя.                                      |
| 5   | `roles`          |      | `Json`      | Роли пользователя в системе в формате JSON-массива.           |
| 6   | `is_active`      |      | `Bool`      | Флаг, указывающий, активен ли пользователь.                   |
| 7   | `created_at`     |      | `Timestamp` | Время создания записи о пользователе.                         |
| 8   | `invitation_key` |      | `Utf8`      | **(НОВОЕ)** Уникальный ключ-приглашение для активации аккаунта. |
| 9   | `invitation_sent_at` |      | `Timestamp` | **(НОВОЕ)** Время последней отправки приглашения. |