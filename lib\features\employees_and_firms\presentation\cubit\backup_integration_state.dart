part of 'backup_integration_cubit.dart';

abstract class BackupIntegrationState extends Equatable {
  const BackupIntegrationState();

  @override
  List<Object?> get props => [];
}

class BackupIntegrationInitial extends BackupIntegrationState {
  const BackupIntegrationInitial();
}

class BackupIntegrationLoading extends BackupIntegrationState {
  const BackupIntegrationLoading();
}

class BackupIntegrationLoaded extends BackupIntegrationState {
  final BackupIntegrationEntity entity;
  const BackupIntegrationLoaded({required this.entity});

  @override
  List<Object?> get props => [entity];
}

class BackupIntegrationError extends BackupIntegrationState {
  final String message;
  const BackupIntegrationError({required this.message});

  @override
  List<Object?> get props => [message];
}
