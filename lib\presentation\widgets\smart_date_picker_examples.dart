import 'package:flutter/material.dart';
import 'smart_date_picker_dialog.dart';
import 'package:intl/intl.dart';

/// Примеры использования SmartDatePickerDialog
class SmartDatePickerExamplesPage extends StatefulWidget {
  const SmartDatePickerExamplesPage({super.key});

  @override
  State<SmartDatePickerExamplesPage> createState() =>
      _SmartDatePickerExamplesPageState();
}

class _SmartDatePickerExamplesPageState
    extends State<SmartDatePickerExamplesPage> {
  DateTime? _selectedDate1;
  DateTime? _selectedDate2;
  DateTime? _selectedDate3;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Примеры русского DatePicker')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Русскоязычный диалог выбора даты в стиле 1С',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 32),

            // Пример 1: Обычный выбор даты
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Пример 1: Выбор любой даты',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Выбранная дата: ${_formatDate(_selectedDate1)}'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () async {
                        final date = await SmartDatePickerDialog.show(
                          context: context,
                          initialDate: _selectedDate1,
                          helpText: 'Выберите дату',
                          allowClear: true,
                        );
                        setState(() => _selectedDate1 = date);
                      },
                      child: const Text('Выбрать дату'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Пример 2: Дата в ограниченном диапазоне
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Пример 2: Дата создания (только прошедшие даты)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Дата создания: ${_formatDate(_selectedDate2)}'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () async {
                        final date = await SmartDatePickerDialog.show(
                          context: context,
                          initialDate: _selectedDate2,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                          helpText: 'Выберите дату создания',
                          allowClear: true,
                        );
                        setState(() => _selectedDate2 = date);
                      },
                      child: const Text('Выбрать дату создания'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Пример 3: Крайний срок (только будущие даты)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Пример 3: Крайний срок (только будущие даты)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Крайний срок: ${_formatDate(_selectedDate3)}'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () async {
                        final date = await SmartDatePickerDialog.show(
                          context: context,
                          initialDate: _selectedDate3,
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                          helpText: 'Выберите крайний срок',
                          allowClear: false, // Нельзя очистить крайний срок
                        );
                        setState(() => _selectedDate3 = date);
                      },
                      child: const Text('Выбрать крайний срок'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),

            const Text(
              'Особенности:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Можно вводить дату напрямую в поле (дд.мм.гггг)\n'
              '• Поддерживаются сокращенные форматы: дд.мм.гг, дд.мм\n'
              '• Русские названия месяцев и дней недели\n'
              '• Кнопки "Сегодня" и "Очистить"\n'
              '• Валидация диапазона дат\n'
              '• Компактный интерфейс в стиле 1С',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'не выбрана';
    return DateFormat('dd.MM.yyyy').format(date);
  }
}
