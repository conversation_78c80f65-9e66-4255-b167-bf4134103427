import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/employees_repository.dart';

class DeleteEmployeeUseCase {
  final IEmployeesRepository repository;
  DeleteEmployeeUseCase(this.repository);

  Future<Either<Failure, Unit>> call(String firmId, String userId) {
    return repository.deleteEmployee(firmId, userId);
  }
}
