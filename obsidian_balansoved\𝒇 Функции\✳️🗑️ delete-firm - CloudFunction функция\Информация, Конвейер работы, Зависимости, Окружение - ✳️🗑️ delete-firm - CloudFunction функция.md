
Идентификатор - d4e7poo87ijl346vpetg
Описание - ⛔️ (Высоко рисковая операция) Выполняет полное и необратимое удаление фирмы и всех связанных с ней данных на основе строгих предусловий
Точка входа - index.handler
Таймаут - 10 минут

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: **(Обязательно)** JWT токен пользователя, инициирующего удаление.
	-> Тело запроса:
	    - `firm_id` (string, **обязательно**): ID фирмы, подлежащей удалению.
Внутренняя работа:
	-> Логирует начало процесса удаления для фирмы {firm_id} владельцем {user_id}.
	-> Авторизация и парсинг запроса:
	    -> Извлекает заголовок Authorization, проверяет наличие Bearer токена, иначе AuthError.
	    -> Верифицирует JWT, извлекает user_id, иначе AuthError.
	    -> Парсит тело запроса, извлекает firm_id (обязательно), иначе LogicError.
	-> Подключение к YDB (firms-database), создание пула сессий.
	-> Фаза 1: Проверка предусловий (run_all_checks):
	    -> Проверяет права владельца (_check_owner_permissions): Запрос к Users, проверка наличия записи и roles == ["OWNER"], иначе AuthError.
	    -> Проверяет отсутствие активных интеграций (_check_integrations): Запрос к Firms, парсинг integrations_json, для каждого проверка enabled != true, иначе PreconditionFailedError.
	    -> Проверяет отсутствие вложений в задачах (_check_task_attachments): Подключение к tasks DB, запрос COUNT в tasks_{firm_id} где attachments_json содержит '[{', если >0 - PreconditionFailedError; если таблицы нет - пропуск.
	    -> Проверяет отсутствие вложений у клиентов (_check_client_attachments): Вызов edit-client с action=GET, для каждого клиента парсинг tax_and_legal_info_json, проверка attached_files пустой, иначе PreconditionFailedError.
	    -> Логирует успешное прохождение проверок.
	-> Фаза 2: Поэтапное удаление (run_all_deletions):
	    -> Удаление всех задач (_delete_all_tasks): Подключение к tasks DB, получение всех task_id из tasks_{firm_id}, цикл invoke edit-task с action=DELETE; логирует прогресс.
	    -> Удаление всех версий клиентов (_delete_all_clients): Подключение к clients DB, получение всех (client_id, manual_creation_date) из clients_{firm_id}, цикл invoke edit-client с action=DELETE и creation_date; логирует прогресс, пропускает ошибки.
	    -> Удаление всех сотрудников кроме владельца (_delete_all_employees): Вызов edit-employee с action=GET_INFO, фильтр user_id != owner_id, цикл invoke delete-employee; логирует прогресс.
	    -> Удаление персональных таблиц (_drop_personal_tables): Для tasks_{firm_id}, clients_{firm_id}, client_payments_{firm_id} - подключение к соответствующей DB, drop_table, если не существует - предупреждение; логирует.
	    -> Удаление записи тарифов (_delete_tariffs_and_storage_record): Подключение к tariffs DB, DELETE FROM tariffs_and_storage WHERE firm_id; логирует.
	    -> Финальное удаление записей фирмы (_delete_firm_records): Подключение к firms DB, в транзакции DELETE FROM Firms WHERE firm_id, DELETE FROM Users WHERE user_id=owner_id AND firm_id; логирует.
	-> Логирует успешное завершение удаления.
	-> Обработка исключений: Логирует ошибки, возвращает соответствующий статус (403 для AuthError, 400 для LogicError, 412 для PreconditionFailedError, 500 для других).
На выходе:
	-> `200 OK`: `{"message": "Firm and all associated data successfully deleted."}`
	-> `400 Bad Request`: Отсутствует или некорректен `firm_id` в теле запроса.
	-> `403 Forbidden`: Токен недействителен или у пользователя нет прав `OWNER`.
	-> `412 Precondition Failed`: Одно из предусловий (по интеграциям или вложениям) не выполнено.
	-> `500 Internal Server Error`: Внутренняя ошибка сервера в процессе выполнения.

---

Зависимости и окружение

Необходимые утилиты: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `invoke_utils.py`
Переменные окружения:
    *   `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
    *   `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS`
    *   `YDB_ENDPOINT_CLIENTS`, `YDB_DATABASE_CLIENTS`
    *   `YDB_ENDPOINT_CLIENT_PAYMENTS`, `YDB_DATABASE_CLIENT_PAYMENTS`
    *   `YDB_ENDPOINT_TARIFFS_AND_STORAGE`, `YDB_DATABASE_TARIFFS_AND_STORAGE`
    *   `SA_KEY_FILE`
    *   `JWT_SECRET`
    *   `FUNCTION_ID_EDIT_TASK`
    *   `FUNCTION_ID_EDIT_CLIENT`
    *   `FUNCTION_ID_EDIT_EMPLOYEE`
    *   `FUNCTION_ID_DELETE_EMPLOYEE`