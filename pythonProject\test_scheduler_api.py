import requests
import json
import sys
import datetime
import pytz
from colorama import init, Fore, Style

# Инициализируем colorama
init(autoreset=True)

# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
API_SCHEDULER_MANAGE_URL = "https://d5dln4usaaktunc0kvia.8wihnuyr.apigw.yandexcloud.net/manage"
API_SCHEDULER_TRIGGER_URL = "https://d5dln4usaaktunc0kvia.8wihnuyr.apigw.yandexcloud.net/trigger"

LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
USER_ID_TO_NOTIFY = "7f77d806-c340-4217-af0e-9f01ff37d677"
NOTIFY_FUNCTION_ID = "d4euafv5ijaums363e84"

DEFAULT_HEADERS = {"Content-Type": "application/json"}

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL


def run_test_step(title: str, url: str, payload: dict, headers: dict, expected_status: int):
    """Выполняет один шаг теста, выводит результат и возвращает ответ в случае успеха."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ")
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=20)
        if response.status_code == expected_status:
            print(f"{TICK} (Статус: {response.status_code})")
            return response
        else:
            print(f"{CROSS} (Ожидался статус {expected_status}, получен {response.status_code})")
            print(Fore.RED + "  Текст ошибки:")
            try:
                print(Fore.RED + json.dumps(response.json(), indent=4, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        return None


def wait_for_enter(phase_name: str):
    """
    Выводит сообщение, приостанавливает выполнение скрипта и ждет,
    пока пользователь нажмет клавишу Enter.
    """
    prompt = f"\n--- Этап '{phase_name}' завершен. Нажмите Enter для перехода к следующему этапу... ---"
    input(Style.BRIGHT + Fore.CYAN + prompt + Style.RESET_ALL)
    print()  # Добавляем пустую строку для лучшей читаемости


if __name__ == "__main__":
    print("\n--- Начало комплексного тестирования Scheduler API ---\n")

    # --- Фаза 1: Создание события ---
    print(f"{Style.BRIGHT}--- Фаза 1: Создание тестового события ---{Style.RESET_ALL}")
    login_response = run_test_step("Шаг 1.1: Получение JWT токена", API_AUTH_URL, LOGIN_PAYLOAD,
                                   DEFAULT_HEADERS, 200)
    if not login_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось войти в систему. Тестирование прервано.")

    try:
        jwt_token = login_response.json().get("token")
        if not jwt_token:
            print(f"\n{CROSS} Критическая ошибка: JWT токен не найден в ответе на логин.")
            sys.exit()
    except (json.JSONDecodeError, AttributeError):
        print(f"\n{CROSS} Критическая ошибка: не удалось извлечь JWT токен из ответа.")
        sys.exit()

    authorized_headers = DEFAULT_HEADERS.copy()
    authorized_headers["Authorization"] = f"Bearer {jwt_token}"
    print(f"   - Успешно получен и установлен JWT токен для следующих запросов.")

    # --- НАЧАЛО МОДИФИКАЦИИ ---
    # Создаем 5 временных меток: первая - сейчас, остальные - с интервалом в 1 минуту.
    now = datetime.datetime.now(pytz.utc)
    execution_dates = [
        (now + datetime.timedelta(minutes=i)).isoformat() for i in range(5)
    ]
    print(f"   - Сгенерированы даты для 5 уведомлений:")
    for d in execution_dates:
        print(f"     - {d}")

    request_body_for_notification = {
        "user_id_to_notify": USER_ID_TO_NOTIFY,
        "payload": {"title": "Серийное тестовое уведомление", "body": "Это одно из 5 запланированных уведомлений."}
    }

    create_event_payload = {
        "action": "UPSERT",
        "payload": {
            "function_id": NOTIFY_FUNCTION_ID,
            # Используем новый идентификатор для ясности
            "custom_identifier": "auto-test-notification-series",
            "is_annual": False,
            # Передаем JSON-массив с 5 датами
            "execution_dates_json": json.dumps(execution_dates),
            "request_body_json": json.dumps(request_body_for_notification),
            "is_active": True
        }
    }
    # --- КОНЕЦ МОДИФИКАЦИИ ---

    create_response = run_test_step("Шаг 1.2: Создание нового запланированного события (с авторизацией)", API_SCHEDULER_MANAGE_URL,
                                    create_event_payload, authorized_headers, 201)
    if not create_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось создать событие. Тестирование прервано.")
    event_id = create_response.json().get("event_id")
    print(f"   - Создано событие с ID: {event_id}, которое вызовет 5 уведомлений.")

    wait_for_enter("Создание события")

    # --- Фаза 2: Запуск триггера ---
    print(f"{Style.BRIGHT}--- Фаза 2: Ручной запуск триггера ---{Style.RESET_ALL}")
    print("   - Этот шаг вызовет обработку событий, срок которых уже наступил (т.е. первого уведомления).")
    run_test_step("Шаг 2.1: Вызов scheduler-trigger", API_SCHEDULER_TRIGGER_URL, {}, DEFAULT_HEADERS, 200)

    wait_for_enter("Запуск триггера")

    # --- Фаза 3: Очистка ---
    print(f"{Style.BRIGHT}--- Фаза 3: Очистка тестовых данных ---{Style.RESET_ALL}")
    print("   - Удаляем родительское событие. Все запланированные, но еще не выполненные уведомления будут отменены.")
    delete_payload = {"action": "DELETE", "event_id": event_id}
    run_test_step("Шаг 3.1: Удаление тестового события (с авторизацией)", API_SCHEDULER_MANAGE_URL, delete_payload, authorized_headers, 200)

    # --- Фаза 4: Проверка ---
    print(f"{Style.BRIGHT}--- Фаза 4: Финальная проверка ---{Style.RESET_ALL}")
    get_payload = {"action": "GET", "event_id": event_id}
    run_test_step("Шаг 4.1: Проверка, что событие удалено (с авторизацией, ожидаем 404)", API_SCHEDULER_MANAGE_URL, get_payload,
                  authorized_headers, 404)

    print("\n--- Тестирование Scheduler API успешно завершено ---")