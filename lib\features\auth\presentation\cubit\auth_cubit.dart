import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/domain/entities/user_entity.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/login_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/register_request_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/refresh_token_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/sign_out_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/check_auth_status_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/confirm_registration_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/accept_invitation_usecase.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final LoginUseCase _loginUseCase;
  final RegisterRequestUseCase _registerUseCase;
  final RefreshTokenUseCase _refreshTokenUseCase;
  final SignOutUseCase _signOutUseCase;
  final CheckAuthStatusUseCase _checkAuthStatusUseCase;
  final ConfirmRegistrationUseCase _confirmRegistrationUseCase;
  final AcceptInvitationUseCase _acceptInvitationUseCase;
  bool _isLoggingIn = false;
  // --- Переменные для повторной отправки кода ---
  String? _pendingEmail;
  String? _pendingPassword;
  String? _pendingUserName;

  AuthCubit({
    required LoginUseCase loginUseCase,
    required RegisterRequestUseCase registerRequestUseCase,
    required RefreshTokenUseCase refreshTokenUseCase,
    required SignOutUseCase signOutUseCase,
    required CheckAuthStatusUseCase checkAuthStatusUseCase,
    required ConfirmRegistrationUseCase confirmRegistrationUseCase,
    required AcceptInvitationUseCase acceptInvitationUseCase,
  }) : _loginUseCase = loginUseCase,
       _registerUseCase = registerRequestUseCase,
       _refreshTokenUseCase = refreshTokenUseCase,
       _signOutUseCase = signOutUseCase,
       _checkAuthStatusUseCase = checkAuthStatusUseCase,
       _confirmRegistrationUseCase = confirmRegistrationUseCase,
       _acceptInvitationUseCase = acceptInvitationUseCase,
       super(AuthInitial());

  Future<void> checkAuth() async {
    NetworkLogger.printInfo('CUBIT: Проверка статуса авторизации');
    final result = await _checkAuthStatusUseCase();
    _emitAuthResult(result);
  }

  Future<void> login({required String email, required String password}) async {
    if (_isLoggingIn || state is AuthAuthenticated) return;
    _isLoggingIn = true;
    NetworkLogger.printInfo('CUBIT: Начинаем авторизацию для: $email');
    emit(AuthLoading());
    // Сохраняем email/password для возможной повторной отправки кода
    _pendingEmail = email;
    _pendingPassword = password;
    _pendingUserName = _pendingUserName ?? '';
    final result = await _loginUseCase(
      LoginParams(email: email, password: password),
    );
    result.fold(
      (failure) {
        NetworkLogger.printError('CUBIT: Ошибка авторизации:', failure);
        // Проверяем, не связано ли с неподтвержденной регистрацией
        final details = failure.details ?? failure.message;
        final isNotConfirmed =
            (failure.details?.contains('Status Code: 423') ?? false) ||
            details.toLowerCase().contains('not confirmed') ||
            details.toLowerCase().contains('не подтвержден') ||
            details.toLowerCase().contains('not active');
        if (isNotConfirmed) {
          emit(AuthAwaitingEmailConfirmation(email));
        } else {
          emit(AuthError(_mapFailure(failure)));
        }
        _isLoggingIn = false;
      },
      (_) async {
        NetworkLogger.printSuccess(
          'CUBIT: Авторизация успешна, проверяем статус',
        );
        await checkAuth();
        _isLoggingIn = false;
      },
    );
  }

  Future<void> registerRequest({
    required String email,
    required String password,
    required String userName,
  }) async {
    // Сохраняем для возможности повторной отправки кода
    _pendingEmail = email;
    _pendingPassword = password;
    _pendingUserName = userName;
    NetworkLogger.printInfo('CUBIT: Начинаем регистрацию для: $email');
    emit(AuthLoading());
    final result = await _registerUseCase(
      RegisterRequestParams(
        email: email,
        password: password,
        userName: userName,
      ),
    );
    result.fold(
      (failure) {
        NetworkLogger.printError('CUBIT: Ошибка регистрации:', failure);
        emit(AuthError(_mapFailure(failure)));
      },
      (_) {
        NetworkLogger.printSuccess(
          'CUBИТ: Регистрация успешна, требуется подтверждение',
        );
        emit(AuthAwaitingEmailConfirmation(email));
      },
    );
  }

  /// Повторно отправить код подтверждения
  Future<void> resendVerificationCode() async {
    if (_pendingEmail == null || _pendingPassword == null) {
      NetworkLogger.printError(
        'CUBIT: Нет данных для повторной отправки кода',
        '',
      );
      return;
    }
    final userName = _pendingUserName ?? '';
    NetworkLogger.printInfo(
      'CUBIT: Повторная отправка кода для ${_pendingEmail!}',
    );
    emit(AuthLoading());
    final result = await _registerUseCase(
      RegisterRequestParams(
        email: _pendingEmail!,
        password: _pendingPassword!,
        userName: userName,
      ),
    );
    result.fold(
      (failure) {
        NetworkLogger.printError(
          'CUBIT: Ошибка повторной отправки кода',
          failure,
        );
        emit(AuthError(_mapFailure(failure)));
      },
      (_) {
        NetworkLogger.printSuccess('CUBIT: Код отправлен повторно');
        emit(AuthAwaitingEmailConfirmation(_pendingEmail!));
      },
    );
  }

  Future<void> signOut() async {
    NetworkLogger.printInfo('CUBIT: Выход из системы');
    await _signOutUseCase();
    NetworkLogger.printSuccess('CUBIT: Выход завершен');
    emit(AuthUnauthenticated());
  }

  Future<void> confirmRegistration({
    required String email,
    required String code,
  }) async {
    emit(AuthLoading());
    final result = await _confirmRegistrationUseCase(
      ConfirmRegistrationParams(email: email, code: code),
    );
    result.fold((failure) => emit(AuthError(_mapFailure(failure))), (_) async {
      await checkAuth();
    });
  }

  Future<bool> acceptInvitation(String invitationKey) async {
    NetworkLogger.printInfo('CUBIT: Начинаем подтверждение приглашения');
    // Не меняем глобальное состояние на Loading, чтобы не блокировать UI
    // Возвращаем bool для локальной обработки в виджете
    final result = await _acceptInvitationUseCase(invitationKey);
    return result.fold(
      (failure) {
        NetworkLogger.printError(
          'CUBIT: Ошибка подтверждения приглашения:',
          failure,
        );
        // НЕ меняем глобальное состояние при ошибке приглашения
        // так как это локальная операция, ошибка обрабатывается в UI
        // Возвращаем false при ошибке
        return false;
      },
      (_) {
        NetworkLogger.printSuccess('CUBIT: Приглашение успешно подтверждено');
        // При успехе возвращаем true
        // Примечание: обновление профиля должно происходить в вызывающем коде
        // чтобы избежать циклических зависимостей между кубитами
        return true;
      },
    );
  }

  String _mapFailure(Failure failure) {
    // Показываем детальную информацию об ошибке
    if (failure is ServerFailure &&
        failure.details != null &&
        failure.details!.isNotEmpty) {
      NetworkLogger.printInfo('CUBIT: Детали ошибки:');
      NetworkLogger.printJson('CUBIT: Details:', failure.details);
      // В продакшене можно вернуть только failure.message для пользователя
      // В режиме отладки показываем полную информацию
      return '${failure.message}\n\nДетали: ${failure.details}';
    }
    return failure.message;
  }

  void _emitAuthResult(Either<Failure, Option<UserEntity>> res) {
    res.fold(
      (failure) {
        NetworkLogger.printError('CUBIT: Ошибка проверки статуса:', failure);
        emit(AuthError(_mapFailure(failure)));
      },
      (option) => option.fold(
        () {
          NetworkLogger.printInfo('CUBIT: Пользователь не авторизован');
          emit(AuthUnauthenticated());
        },
        (user) {
          NetworkLogger.printSuccess('CUBIT: Пользователь авторизован');
          emit(AuthAuthenticated(user));
        },
      ),
    );
  }
}
