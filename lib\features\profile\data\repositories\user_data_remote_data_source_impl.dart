import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:balansoved_enterprise/core/constants/constants.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import '../../domain/entities/profile_entity.dart';
import '../data_source/user_data_remote_data_source.dart';
import 'package:balansoved_enterprise/features/profile/data/models/profile_model.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

class UserDataRemoteDataSourceImpl implements IUserDataRemoteDataSource {
  final Dio? dio;
  final http.Client? httpClient;

  UserDataRemoteDataSourceImpl({this.dio, this.httpClient})
    : assert(
        dio != null || httpClient != null,
        'Either dio or httpClient must be provided',
      );

  @override
  Future<ProfileEntity> fetchProfile(String token) async {
    final url = ManagementApiUrls.getUserData();

    try {
      if (kIsWeb && httpClient != null) {
        NetworkLogger.printInfo('DIO: Получение данных пользователя');
        NetworkLogger.printInfo('URL: $url');

        final response = await httpClient!.get(
          Uri.parse(url),
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        );

        NetworkLogger.printInfo('HTTP status: ${response.statusCode}');
        NetworkLogger.printJson('Response Body:', response.body);

        if (response.statusCode == 200) {
          final jsonData = jsonDecode(response.body);
          return ProfileModel.fromJson(jsonData).toEntity();
        } else {
          throw ServerException(
            message: 'HTTP ${response.statusCode}: ${response.body}',
          );
        }
      } else if (dio != null) {
        NetworkLogger.printInfo('DIO: Получение данных пользователя');
        NetworkLogger.printInfo('URL: $url');

        final response = await dio!.get(
          url,
          options: Options(
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
            },
          ),
        );

        NetworkLogger.printSuccess(
          'DIO: Ответ получен: ${response.statusCode}',
        );
        NetworkLogger.printJson('Response Body:', response.data);

        if (response.statusCode == 200) {
          return ProfileModel.fromJson(response.data).toEntity();
        } else {
          throw ServerException(
            message: 'DIO ${response.statusCode}: ${response.data}',
          );
        }
      } else {
        throw NetworkException(message: 'Нет доступных HTTP клиентов');
      }
    } catch (e, stackTrace) {
      NetworkLogger.printError('Profile data source error:', e, stackTrace);
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw NetworkException(message: 'Ошибка сети при получении профиля: $e');
    }
  }
}
