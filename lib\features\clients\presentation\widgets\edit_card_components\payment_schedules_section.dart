import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/presentation/widgets/help_tooltip.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';

class PaymentSchedulesSection extends StatelessWidget {
  final bool isEditing;
  final PaymentSchedule? salaryPayment;
  final bool salaryPaymentEnabled;
  final Function(bool) onSalaryPaymentEnabledChanged;
  final bool? cashOrBank;
  final Function(bool?) onCashOrBankChanged;
  final bool cashPayment;
  final Function(bool) onCashPaymentChanged;
  final bool bankPayment;
  final Function(bool) onBankPaymentChanged;
  final bool hasEmployees;
  final Function(bool) onHasEmployeesChanged;
  final bool isSoleFounderDirector;
  final Function(bool) onIsSoleFounderDirectorChanged;
  final PaymentSchedule? advancePayment;
  final PaymentSchedule? ndflPayment;
  final Function(PaymentSchedule?) onSalaryChanged;
  final Function(PaymentSchedule?) onAdvanceChanged;
  final Function(PaymentSchedule?) onNdflChanged;
  final Function(String, String) copyToClipboard;

  final TextEditingController salaryDateCtrl;
  final FocusNode salaryDateFN;
  final TextEditingController advanceDateCtrl;
  final FocusNode advanceDateFN;
  final TextEditingController ndflDate1Ctrl;
  final FocusNode ndflDate1FN;
  final TextEditingController ndflDate2Ctrl;
  final FocusNode ndflDate2FN;

  const PaymentSchedulesSection({
    super.key,
    required this.isEditing,
    required this.salaryPayment,
    required this.salaryPaymentEnabled,
    required this.onSalaryPaymentEnabledChanged,
    required this.cashOrBank,
    required this.onCashOrBankChanged,
    required this.cashPayment,
    required this.onCashPaymentChanged,
    required this.bankPayment,
    required this.onBankPaymentChanged,
    required this.hasEmployees,
    required this.onHasEmployeesChanged,
    required this.isSoleFounderDirector,
    required this.onIsSoleFounderDirectorChanged,
    required this.advancePayment,
    required this.ndflPayment,
    required this.onSalaryChanged,
    required this.onAdvanceChanged,
    required this.onNdflChanged,
    required this.copyToClipboard,
    required this.salaryDateCtrl,
    required this.salaryDateFN,
    required this.advanceDateCtrl,
    required this.advanceDateFN,
    required this.ndflDate1Ctrl,
    required this.ndflDate1FN,
    required this.ndflDate2Ctrl,
    required this.ndflDate2FN,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.schedule_outlined, size: 20, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              'Графики платежей',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Настройки выплат
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.settings_outlined,
                    size: 16,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Настройки выплат',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: theme.textTheme.bodyMedium?.color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Есть сотрудники
              Row(
                children: [
                  Text(
                    'Есть сотрудники',
                    style: TextStyle(color: theme.textTheme.bodyMedium?.color),
                  ),
                  SizedBox(width: 10),
                  SegmentedButton<bool>(
                    segments: const [
                      ButtonSegment<bool>(value: true, label: Text('Да')),
                      ButtonSegment<bool>(value: false, label: Text('Нет')),
                    ],
                    selected: {hasEmployees},
                    onSelectionChanged:
                        isEditing
                            ? (Set<bool> newSelection) {
                              onHasEmployeesChanged(
                                newSelection.isNotEmpty
                                    ? newSelection.first
                                    : false,
                              );
                            }
                            : null,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Единственный учредитель-директор
              Row(
                children: [
                  Text(
                    'Единственный учредитель-директор',
                    style: TextStyle(color: theme.textTheme.bodyMedium?.color),
                  ),
                  SizedBox(width: 10),
                  SegmentedButton<bool>(
                    segments: const [
                      ButtonSegment<bool>(value: true, label: Text('Да')),
                      ButtonSegment<bool>(value: false, label: Text('Нет')),
                    ],
                    selected: {isSoleFounderDirector},
                    onSelectionChanged:
                        isEditing
                            ? (Set<bool> newSelection) {
                              onIsSoleFounderDirectorChanged(
                                newSelection.isNotEmpty
                                    ? newSelection.first
                                    : false,
                              );
                            }
                            : null,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Выплата зарплаты
              Row(
                children: [
                  Text(
                    'Выплата зарплаты',
                    style: TextStyle(color: theme.textTheme.bodyMedium?.color),
                  ),
                  SizedBox(width: 10),
                  SegmentedButton<bool>(
                    segments: const [
                      ButtonSegment<bool>(value: true, label: Text('Да')),
                      ButtonSegment<bool>(value: false, label: Text('Нет')),
                    ],
                    selected: {salaryPaymentEnabled},
                    onSelectionChanged:
                        isEditing
                            ? (Set<bool> newSelection) {
                              onSalaryPaymentEnabledChanged(
                                newSelection.isNotEmpty
                                    ? newSelection.first
                                    : false,
                              );
                            }
                            : null,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Банк
              Row(
                children: [
                  Text(
                    'Банк',
                    style: TextStyle(color: theme.textTheme.bodyMedium?.color),
                  ),
                  SizedBox(width: 10),
                  SegmentedButton<bool>(
                    segments: const [
                      ButtonSegment<bool>(value: true, label: Text('Да')),
                      ButtonSegment<bool>(value: false, label: Text('Нет')),
                    ],
                    selected: {bankPayment},
                    onSelectionChanged:
                        isEditing
                            ? (Set<bool> newSelection) {
                              onBankPaymentChanged(
                                newSelection.isNotEmpty
                                    ? newSelection.first
                                    : false,
                              );
                            }
                            : null,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Касса
              Row(
                children: [
                  Text(
                    'Касса',
                    style: TextStyle(color: theme.textTheme.bodyMedium?.color),
                  ),
                  SizedBox(width: 10),
                  SegmentedButton<bool>(
                    segments: const [
                      ButtonSegment<bool>(value: true, label: Text('Да')),
                      ButtonSegment<bool>(value: false, label: Text('Нет')),
                    ],
                    selected: {cashPayment},
                    onSelectionChanged:
                        isEditing
                            ? (Set<bool> newSelection) {
                              onCashPaymentChanged(
                                newSelection.isNotEmpty
                                    ? newSelection.first
                                    : false,
                              );
                            }
                            : null,
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Графики платежей
        _buildPaymentSchedule(
          context: context,
          label: 'Зарплата',
          schedule: salaryPayment,
          controller: salaryDateCtrl,
          focusNode: salaryDateFN,
          onChanged: onSalaryChanged,
          secondDateLabel: null,
          icon: Icons.account_balance_wallet_outlined,
          helpMessage: 'Задача будет перенесена на ближайший рабочий день, до',
        ),
        const SizedBox(height: 12),
        _buildPaymentSchedule(
          context: context,
          label: 'Аванс',
          schedule: advancePayment,
          controller: advanceDateCtrl,
          focusNode: advanceDateFN,
          onChanged: onAdvanceChanged,
          secondDateLabel: null,
          icon: Icons.payments_outlined,
        ),
        const SizedBox(height: 12),
        _buildPaymentSchedule(
          context: context,
          label: 'НДФЛ',
          schedule: ndflPayment,
          controller: ndflDate1Ctrl,
          focusNode: ndflDate1FN,
          secondController: ndflDate2Ctrl,
          secondFocusNode: ndflDate2FN,
          onChanged: onNdflChanged,
          secondDateLabel: 'Дата 2 (1-31)',
          icon: Icons.receipt_long_outlined,
          helpMessage:
              'Задача будет перенесена на ближайший рабочий день, после',
        ),
      ],
    );
  }

  Widget _buildPaymentSchedule({
    required BuildContext context,
    required String label,
    required PaymentSchedule? schedule,
    required Function(PaymentSchedule?) onChanged,
    required TextEditingController controller,
    required FocusNode focusNode,
    TextEditingController? secondController,
    FocusNode? secondFocusNode,
    String? secondDateLabel,
    IconData? icon,
    String? helpMessage,
  }) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (icon != null) ...[
              Icon(icon, size: 18, color: theme.textTheme.bodySmall?.color),
              const SizedBox(width: 8),
            ],
            Text(label),
            if (helpMessage != null) ...[
              const SizedBox(width: 8),
              HelpTooltip(message: helpMessage),
            ],
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: UniversalNumberField(
                controller: controller,
                focusNode: focusNode,
                labelText: 'Дата (1-31)',
                prefixIcon: Icons.today_outlined,
                fieldType: NumberFieldType.dayOfMonth,
                isEditing: isEditing,
                onTap:
                    () => copyToClipboard(
                      controller.text,
                      'Дата платежа ($label)',
                    ),
              ),
            ),
            if (secondDateLabel != null) ...[
              const SizedBox(width: 16),
              Expanded(
                child: UniversalNumberField(
                  controller: secondController!,
                  focusNode: secondFocusNode,
                  labelText: secondDateLabel,
                  prefixIcon: Icons.event_outlined,
                  fieldType: NumberFieldType.dayOfMonth,
                  isEditing: isEditing,
                  onTap:
                      () => copyToClipboard(
                        secondController.text,
                        '$secondDateLabel ($label)',
                      ),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }
}
