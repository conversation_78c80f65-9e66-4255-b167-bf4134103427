import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/firm_entity.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'active_firm_state.dart';

const String _selectedFirmIdKey = 'selected_firm_id';

class ActiveFirmCubit extends Cubit<ActiveFirmState> {
  ActiveFirmCubit() : super(const ActiveFirmState.initial()) {
    _loadSelectedFirm();
  }

  Future<void> _loadSelectedFirm() async {
    final prefs = await SharedPreferences.getInstance();
    final firmId = prefs.getString(_selectedFirmIdKey);
    if (firmId != null && state.firms.isNotEmpty) {
      final firm = state.firms.firstWhere(
        (f) => f.id == firmId,
        orElse: () => state.firms.first,
      );
      emit(state.copyWith(selectedFirm: firm));
    }
  }

  void setFirms(List<FirmEntity> firms) {
    print(
      '🏢 [ACTIVE_FIRM] Обновление списка фирм. Получено фирм: ${firms.length}',
    );
    for (final firm in firms) {
      print('   - Фирма: ${firm.name} (ID: ${firm.id})');
    }

    if (firms.isEmpty) {
      print('⚠️ [ACTIVE_FIRM] Список фирм пуст, очищаем состояние');
      // Если список фирм пуст, обновляем состояние и выходим
      emit(
        state.copyWith(
          firms: [],
          selectedFirm: null, // Убираем любую выбранную фирму
          isLoading: false,
        ),
      );
      return;
    }

    final current = state.selectedFirm;
    print(
      '🏢 [ACTIVE_FIRM] Текущая выбранная фирма: ${current?.name ?? 'нет'}',
    );

    // Если текущая фирма не в новом списке, выбираем первую
    final selected =
        current != null && firms.any((f) => f.id == current.id)
            ? current
            : firms.first;

    print(
      '🏢 [ACTIVE_FIRM] Выбранная фирма после обновления: ${selected.name}',
    );

    emit(
      state.copyWith(firms: firms, selectedFirm: selected, isLoading: false),
    );
    print('✅ [ACTIVE_FIRM] Состояние обновлено');
    _loadSelectedFirm(); // Попытаться восстановить выбор после обновления списка
  }

  void selectFirm(FirmEntity firm) async {
    if (state.selectedFirm?.id == firm.id) return; // Already selected
    emit(state.copyWith(selectedFirm: firm));
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedFirmIdKey, firm.id);
  }
}
