import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_local_data_source.dart';
import 'package:balansoved_enterprise/features/clients/data/data_source/clients_remote_data_source.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/clients_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';

class ClientsRepositoryImpl implements IClientsRepository {
  final IClientsRemoteDataSource remote;
  final IAuthLocalDataSource localAuth;

  ClientsRepositoryImpl({required this.remote, required this.localAuth});

  @override
  Future<Either<Failure, List<ClientEntity>>> getClients(
    String firmId, {
    bool onlyActual = false,
  }) async {
    try {
      NetworkLogger.printInfo(
        'ClientsRepositoryImpl: Starting getClients for firmId: $firmId, onlyActual: $onlyActual',
      );

      NetworkLogger.printInfo(
        'CLIENT REPO: Получаем JWT из локального хранилища',
      );
      final token = await localAuth.getAccessToken();
      if (token == null) {
        NetworkLogger.printError(
          'ClientsRepositoryImpl: No access token available',
          '',
        );
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      NetworkLogger.printInfo(
        'ClientsRepositoryImpl: Got token, calling remote.fetchClients',
      );

      // Всегда получаем все версии клиентов
      final allClients = await remote.fetchClients(
        token,
        firmId,
        onlyActual: false,
        clientId: null,
      );

      List<ClientEntity> resultClients;
      if (onlyActual) {
        // Фильтруем по manual_creation_date, оставляя только самые актуальные версии
        final clientGroups = <String, List<ClientEntity>>{};

        // Группируем клиентов по имени
        for (final client in allClients) {
          if (clientGroups[client.name] == null) {
            clientGroups[client.name] = [];
          }
          clientGroups[client.name]!.add(client);
        }

        // Для каждой группы выбираем версию с наиболее актуальным manual_creation_date
        resultClients = [];
        for (final group in clientGroups.values) {
          if (group.isNotEmpty) {
            group.sort((a, b) {
              final aDate =
                  a.manualCreationDate ?? a.creationDate ?? DateTime(1970);
              final bDate =
                  b.manualCreationDate ?? b.creationDate ?? DateTime(1970);
              return bDate.compareTo(
                aDate,
              ); // Сортируем по убыванию (новые первые)
            });
            resultClients.add(group.first); // Берем самую актуальную версию
          }
        }
      } else {
        resultClients = allClients;
      }

      NetworkLogger.printSuccess(
        'CLIENT REPO: Данные получены (${resultClients.length})',
      );
      for (final client in resultClients) {
        NetworkLogger.printInfo(
          'CLIENT REPO: Client ${client.name} - profitTaxTypes: ${client.profitTaxTypes}',
        );
      }
      return Right(resultClients);
    } on ServerException catch (e) {
      if (e.statusCode == 403) {
        return Left(
          AccessDeniedFailure(message: e.message, details: e.responseBody),
        );
      }
      return Left(ServerFailure(message: e.message, details: e.responseBody));
    } catch (e) {
      NetworkLogger.printError(
        'ClientsRepositoryImpl: Exception in getClients:',
        e,
      );
      return Left(NetworkFailure(message: 'Ошибка при получении клиентов: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ClientEntity>>> getClientVersions(
    String firmId,
    String clientName,
  ) async {
    try {
      NetworkLogger.printInfo(
        'ClientsRepositoryImpl: Getting all versions for client: $clientName',
      );

      final token = await localAuth.getAccessToken();
      if (token == null) {
        NetworkLogger.printError(
          'ClientsRepositoryImpl: No access token available',
          '',
        );
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }

      // Сначала получаем все клиенты для поиска client_id по имени
      final allClients = await remote.fetchClients(
        token,
        firmId,
        onlyActual: false,
        clientId: null,
      );

      // Находим client_id по имени клиента
      final clientWithName = allClients.firstWhere(
        (client) => client.name == clientName,
        orElse: () => throw Exception('Client with name "$clientName" not found'),
      );

      // Теперь получаем все версии конкретного клиента по client_id
      final clientVersions = await remote.fetchClients(
        token,
        firmId,
        onlyActual: false,
        clientId: clientWithName.id,
      );

      NetworkLogger.printSuccess(
        'CLIENT REPO: Found ${clientVersions.length} versions for client $clientName (client_id: ${clientWithName.id})',
      );

      return Right(clientVersions);
    } on ServerException catch (e) {
      if (e.statusCode == 403) {
        return Left(
          AccessDeniedFailure(message: e.message, details: e.responseBody),
        );
      }
      return Left(ServerFailure(message: e.message, details: e.responseBody));
    } catch (e) {
      NetworkLogger.printError(
        'ClientsRepositoryImpl: Exception in getClientVersions:',
        e,
      );
      return Left(
        NetworkFailure(message: 'Ошибка при получении версий клиента: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, ClientEntity>> upsertClient(
    String firmId,
    ClientEntity client,
  ) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      final newClient = await remote.upsertClient(token, firmId, client);
      return Right(newClient);
    } on ServerException catch (e) {
      if (e.statusCode == 403) {
        return Left(
          AccessDeniedFailure(message: e.message, details: e.responseBody),
        );
      }
      return Left(ServerFailure(message: e.message, details: e.responseBody));
    } catch (e) {
      NetworkLogger.printError(
        'ClientsRepositoryImpl: Exception in upsertClient:',
        e,
      );
      return Left(NetworkFailure(message: 'Ошибка при сохранении клиента: $e'));
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteClient(
    String firmId,
    String clientId,
    DateTime creationDate,
  ) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      await remote.deleteClient(token, firmId, clientId, creationDate);
      return const Right(unit);
    } on ServerException catch (e) {
      if (e.statusCode == 403) {
        return Left(
          AccessDeniedFailure(message: e.message, details: e.responseBody),
        );
      }
      return Left(ServerFailure(message: e.message, details: e.responseBody));
    } catch (e) {
      NetworkLogger.printError(
        'ClientsRepositoryImpl: Exception in deleteClient:',
        e,
      );
      return Left(NetworkFailure(message: 'Ошибка при удалении клиента: $e'));
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteAllClientVersions(
    String firmId,
    String clientName,
  ) async {
    try {
      NetworkLogger.printInfo(
        'ClientsRepositoryImpl: Starting deleteAllClientVersions for client: $clientName',
      );

      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }

      // Получаем все версии клиента
      final versionsResult = await getClientVersions(firmId, clientName);

      return versionsResult.fold((failure) => Left(failure), (versions) async {
        NetworkLogger.printInfo(
          'ClientsRepositoryImpl: Found ${versions.length} versions to delete for client $clientName',
        );

        // Удаляем каждую версию поэтапно
        for (int i = 0; i < versions.length; i++) {
          final version = versions[i];
          NetworkLogger.printInfo(
            'ClientsRepositoryImpl: Deleting version ${i + 1}/${versions.length} (ID: ${version.id}) for client $clientName',
          );

          try {
            // Используем manualCreationDate, если доступно, иначе fallback на creationDate
            final dateToUse =
                version.manualCreationDate ?? version.creationDate;
            if (dateToUse == null) {
              NetworkLogger.printError(
                'ClientsRepositoryImpl: Cannot delete version ${version.id} - both manualCreationDate and creationDate are null',
                '',
              );
              continue; // Пропускаем эту версию и переходим к следующей
            }
            await remote.deleteClient(token, firmId, version.id, dateToUse);
            NetworkLogger.printSuccess(
              'ClientsRepositoryImpl: Successfully deleted version ${i + 1}/${versions.length} for client $clientName',
            );
          } catch (e) {
            NetworkLogger.printError(
              'ClientsRepositoryImpl: Failed to delete version ${version.id} for client $clientName:',
              e,
            );
            // Продолжаем удаление остальных версий даже если одна не удалилась
          }
        }

        NetworkLogger.printSuccess(
          'ClientsRepositoryImpl: Completed deleteAllClientVersions for client $clientName',
        );
        return const Right(unit);
      });
    } on ServerException catch (e) {
      if (e.statusCode == 403) {
        return Left(
          AccessDeniedFailure(message: e.message, details: e.responseBody),
        );
      }
      return Left(ServerFailure(message: e.message, details: e.responseBody));
    } catch (e) {
      NetworkLogger.printError(
        'ClientsRepositoryImpl: Exception in deleteAllClientVersions:',
        e,
      );
      return Left(
        NetworkFailure(message: 'Ошибка при удалении всех версий клиента: $e'),
      );
    }
  }
}
