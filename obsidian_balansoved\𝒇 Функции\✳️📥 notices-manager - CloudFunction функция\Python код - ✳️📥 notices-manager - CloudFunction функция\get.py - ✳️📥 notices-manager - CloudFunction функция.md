
```python
import json
import math
import ydb
from custom_errors import NotFoundError

PAGE_SIZE = 100

def _format_notice(row, columns):
    data = {}
    for c in columns:
        value = row[c.name]
        if 'json' in c.name and value:
            data[c.name] = json.loads(value)
        else:
            data[c.name] = value
    return data

def get_notices(session, table_name, notice_id=None, page=0, get_archived=False):
    try:
        tx = session.transaction(ydb.SerializableReadWrite())

        if notice_id:
            query = session.prepare(f"DECLARE $id AS Utf8; SELECT * FROM `{table_name}` WHERE notice_id = $id;")
            print(f"Executing YQL for single notice fetch: NOTICE_ID={notice_id}")
            res = tx.execute(query, {"$id": notice_id})
            if not res[0].rows:
                raise NotFoundError(f"Notice with id {notice_id} not found.")
            
            data = _format_notice(res[0].rows[0], res[0].columns)
            tx.commit()
            print(f"Successfully fetched single notice {notice_id} from table {table_name}.")
            return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"data": data}, default=str, ensure_ascii=False)}

        page = int(page or 0)
        if page < 0: page = 0
        offset = page * PAGE_SIZE

        where_clause = "WHERE (is_archived IS NULL OR is_archived = false)"

        count_query = session.prepare(f"SELECT COUNT(notice_id) AS total FROM `{table_name}` {where_clause};")
        print(f"Executing YQL count query with clause: {where_clause}")
        count_res = tx.execute(count_query)
        total_items = count_res[0].rows[0].total if count_res[0].rows else 0
        total_pages = math.ceil(total_items / PAGE_SIZE) if total_items > 0 else 0
        
        print(f"Found {total_items} notices in table {table_name} matching criteria.")

        if total_items == 0:
            tx.commit()
            return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"metadata": {"total": 0, "page": 0, "pages": 0}, "data": []}, ensure_ascii=False)}

        if page >= total_pages and total_pages > 0:
            raise NotFoundError(f"Page {page} does not exist. Total pages: {total_pages}.")

        select_data_query = session.prepare(f"SELECT * FROM `{table_name}` {where_clause} ORDER BY created_at DESC LIMIT {PAGE_SIZE} OFFSET {offset};")
        print("Executing YQL select data query with pagination")
        data_res = tx.execute(select_data_query)
        
        data = [_format_notice(row, data_res[0].columns) for row in data_res[0].rows]
        metadata = {"total": total_items, "page": page, "pages": total_pages}
        
        print(f"Successfully fetched {len(data)} notices from table {table_name} on page {page}. Total items: {total_items}.")
        tx.commit()
        return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"metadata": metadata, "data": data}, default=str, ensure_ascii=False)}

    except ydb.SchemeError:
        print(f"Table '{table_name}' not found. Returning empty list as it's a valid case for a new user.")
        return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"metadata": {"total": 0, "page": 0, "pages": 0}, "data": []}, ensure_ascii=False)}
```