import 'package:flutter/material.dart';
import '../../../../tariffs_and_storage/presentation/widgets/file_attachments/file_attachments.dart';

class TaskDetailAttachmentsSection extends StatelessWidget {
  final List<Map<String, dynamic>> attachments;

  const TaskDetailAttachmentsSection({super.key, required this.attachments});

  @override
  Widget build(BuildContext context) {
    return FilesGridSection(files: attachments, title: 'Файловые вложения');
  }
}
