import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/employees_repository.dart';

class AddRoleUseCase {
  final IEmployeesRepository repository;
  AddRoleUseCase(this.repository);

  Future<Either<Failure, Unit>> call(
    String firmId,
    String userId,
    String role,
  ) {
    return repository.addRole(firmId, userId, role);
  }
}
