
Идентификатор: d4e3f6917icsff9hikrh
Описание: 💾 Синхронизирует задачи и связанные с ними данные (вложения, отчеты) в Yandex Disk. Вызывается по запросу владельца фирмы.
Точка входа: index.handler
Таймаут: 10 мин

---

На входе:
	-> `Authorization: Bearer <jwt_token>` или `X-Forwarded-Authorization: Bearer <jwt_token>`: **(Обязательно)** JWT токен пользователя, инициирующего синхронизацию.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы для синхронизации.
Конвейер работы:
    -> Авторизация и парсинг:
        -> Извлечение firm_id из тела запроса.
        -> Извлечение и верификация JWT-токена из заголовков.
        -> Проверка, что пользователь - OWNER в firms-database.
    -> Получение токена Я.Диска:
        -> Вызов edit-integrations (GET) для получения интеграций.
        -> Проверка квоты по last_sync_utc (пропуск в тестовом режиме).
    -> Обновление времени синхронизации:
        -> Вызов edit-integrations (UPSERT) с новым last_sync_utc.
    -> Кеширование данных:
        -> Вызов get-user-data для базового списка сотрудников.
        -> Вызов edit-employee (GET_INFO) для полного списка, объединение в кеш.
    -> Получение списка задач:
        -> Множественные вызовы edit-task (GET) для бессрочных и датированных задач.
    -> Обработка каждой задачи:
        -> Умный пропуск:
            -> Проверка наличия и возраста task_info_raw.json.
            -> Полная синхронизация, если отсутствует или старше 30 дней; обновление метаданных для 7-30 дней; пропуск для <7 дней.
        -> Получение деталей: Вызов edit-task (GET).
        -> Получение имен: Вызов edit-client (GET, is_actual: true); из кеша для сотрудников, с fallback-запросом.
        -> Обработка вложений:
            -> Для каждого: GET_DOWNLOAD_URL из tariffs-and-storage-manager.
            -> Скачивание и загрузка на Я.Диск.
        -> Генерация артефактов: PDF с pdf_generator, JSON с данными.
        -> Загрузка PDF и JSON на Я.Диск.
    -> Обработка исключений и возврат статусов.
На выходе:
    -> 200 OK: {"message": "Successfully processed X tasks."} или {"message": "No tasks to process."}
    -> 400 Bad Request: Неверные параметры (например, отсутствие firm_id).
    -> 403 Forbidden: Ошибка авторизации или недостаточно прав.
    -> 500 Internal Server Error: Внутренняя ошибка.

---
### Зависимости и окружение
-   **Необходимые утилиты**:
    -   `utils/auth_utils.py`
    -   `utils/ydb_utils.py`
    -   `utils/request_parser.py`
    -   `yandex_disk_uploader.py`
    -   `pdf_generator.py`
-   **Переменные окружения**:
    -   `SYNC_FREQUENCY_DAYS`: Квота частоты запуска в днях.
    -   `YADISK_BASE_FOLDER`: Корневая папка на Яндекс.Диске.
    -   `YC_REGION`: Регион для вызова функций (`ru-central1`).
    -   `JWT_SECRET`
    -   `SYNC_TASKS_TEST_MODE`: Если 'true', функция всегда работает в тестовом режиме без ограничения по частоте синхронизации.
    -   `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
    -   **ID целевых функций**:
        -   `FUNCTION_ID_EDIT_INTEGRATIONS` ([[✳️🔗 edit-integrations - CloudFunction функция]])
        -   `FUNCTION_ID_EDIT_EMPLOYEE` ([[✳️📎 edit-employee - CloudFunction функция]])
        -   `FUNCTION_ID_EDIT_TASK` ([[✳️📝 edit-task - CloudFunction функция]])
        -   `FUNCTION_ID_EDIT_CLIENT` ([[✳️👤 edit-client - CloudFunction функция]])
        -   `FUNCTION_ID_TARIFFS_AND_STORAGE_MANAGER` ([[✳️💰 tariffs-and-storage-manager - CloudFunction функция]])
        -   `FUNCTION_ID_GET_USER_DATA` ([[✳️ get-user-data - CloudFunction функция]])