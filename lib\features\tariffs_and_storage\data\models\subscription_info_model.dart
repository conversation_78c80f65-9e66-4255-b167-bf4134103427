import '../../domain/entities/subscription_info_entity.dart';

/// Модель для информации о подписке (data layer)
class SubscriptionInfoModel extends SubscriptionInfoEntity {
  const SubscriptionInfoModel({
    required super.planId,
    required super.startedAt,
    super.expiresAt,
    required super.autoRenew,
    required super.status,
    required super.quotaBytes,
  });

  /// Создать модель из JSON
  factory SubscriptionInfoModel.fromJson(Map<String, dynamic> json) {
    DateTime parseDate(dynamic value) {
      try {
        if (value == null) return DateTime.now();
        if (value is int) {
          return DateTime.fromMicrosecondsSinceEpoch(
            value,
            isUtc: true,
          ).toLocal();
        }
        if (value is String && value.isNotEmpty) {
          return DateTime.parse(value).toLocal();
        }
      } catch (_) {}
      return DateTime.now();
    }

    return SubscriptionInfoModel(
      planId: json['plan_id'] ?? 'free',
      startedAt: parseDate(json['started_at']),
      expiresAt:
          json['expires_at'] != null ? parseDate(json['expires_at']) : null,
      autoRenew: json['auto_renew'] ?? false,
      status: json['status'] ?? 'active',
      quotaBytes: json['quota_bytes'] ?? 104857600, // 100MB по умолчанию
    );
  }

  /// Преобразовать в JSON
  Map<String, dynamic> toJson() {
    return {
      'plan_id': planId,
      'started_at': startedAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'auto_renew': autoRenew,
      'status': status,
      'quota_bytes': quotaBytes,
    };
  }

  /// Преобразовать в Entity
  SubscriptionInfoEntity toEntity() {
    return SubscriptionInfoEntity(
      planId: planId,
      startedAt: startedAt,
      expiresAt: expiresAt,
      autoRenew: autoRenew,
      status: status,
      quotaBytes: quotaBytes,
    );
  }

  /// Создать модель из Entity
  factory SubscriptionInfoModel.fromEntity(SubscriptionInfoEntity entity) {
    return SubscriptionInfoModel(
      planId: entity.planId,
      startedAt: entity.startedAt,
      expiresAt: entity.expiresAt,
      autoRenew: entity.autoRenew,
      status: entity.status,
      quotaBytes: entity.quotaBytes,
    );
  }
}
