
```python
import os, json, logging
import invoke_utils

FUNCTION_ID_SCHEDULER_MANAGER = os.environ.get("FUNCTION_ID_SCHEDULER_MANAGER")
FUNCTION_ID_ENDPOINTS_MANAGER = os.environ.get("FUNCTION_ID_ENDPOINTS_MANAGER")

def _get_existing_reminders(task_id: str, user_jwt: str) -> dict: 
     print(f"REMINDERS: Fetching existing reminders from scheduler for task {task_id}") 
     payload = {"action": "GET", "custom_identifier": task_id} 
     try: 
         response = invoke_utils.invoke_function(FUNCTION_ID_SCHEDULER_MANAGER, payload, user_jwt) 
         print(f"REMINDERS: Received response from scheduler for task {task_id}") 
     except invoke_utils.FunctionInvokeError as e: 
         if e.status_code == 404: 
             print(f"REMINDERS: No existing events found for task {task_id} (404)") 
             return {} 
         print(f"REMINDERS: Error fetching existing reminders for task {task_id}: {e}") 
         raise e 
 
     existing = {} 
     
     # --- НАЧАЛО ИСПРАВЛЕНИЙ --- 
     # Корректно парсим тело ответа, которое является JSON-строкой 
     events = [] 
     if response and response.get("statusCode") == 200 and response.get("body"): 
         try: 
             response_data = json.loads(response["body"]) 
             events = response_data.get("data", []) 
         except (json.JSONDecodeError, TypeError): 
             print(f"REMINDERS: Could not parse JSON from scheduler response body for task {task_id}") 
             events = [] 
     # --- КОНЕЦ ИСПРАВЛЕНИЙ --- 
 
     if not isinstance(events, list): 
         events = [events] 
     
     print(f"REMINDERS: Processing {len(events)} events for task {task_id}") 
     for event in events: 
         try: 
             # Проверяем, что это событие - напоминание, а не периодическая задача 
             raw_body = event.get("request_body_json", "{}") 
             body = json.loads(raw_body or "{}") if isinstance(raw_body, str) else raw_body 
             if body.get("action") != "SEND": 
                 continue # Это не напоминание 
 
             raw_dates = event.get("execution_dates_json", "[]") 
             dates = json.loads(raw_dates or "[]") if isinstance(raw_dates, str) else raw_dates 
 
             user_id = body.get("user_id_to_notify") 
             if user_id and dates: 
                 existing[(dates[0], user_id)] = event["event_id"] 
                 print(f"REMINDERS: Found existing reminder for task {task_id}, user {user_id}, datetime {dates[0]}") 
         except (json.JSONDecodeError, KeyError, IndexError) as e: 
             print(f"REMINDERS: Error parsing event for task {task_id}: {e}") 
             continue 
     
     print(f"REMINDERS: Found {len(existing)} valid existing reminders for task {task_id}") 
     return existing

def _get_desired_reminders(task_payload: dict) -> set:
    print(f"REMINDERS: Calculating desired reminders from task payload")
    desired = set()
    try:
        reminders_str = task_payload.get("reminders_json", "[]")
        reminders = json.loads(reminders_str) if isinstance(reminders_str, str) else reminders_str
        print(f"REMINDERS: Found {len(reminders)} reminder definitions in payload")
        
        for i, rem in enumerate(reminders):
            role = rem.get("role")
            dt = rem.get("datetime")
            if not role or not dt:
                print(f"REMINDERS: Skipping reminder {i} - missing role or datetime")
                continue
            
            print(f"REMINDERS: Processing reminder {i} for role {role}, datetime {dt}")
            user_ids_str = task_payload.get(f"{role}_ids_json", "[]")
            user_ids = json.loads(user_ids_str) if isinstance(user_ids_str, str) else user_ids_str
            print(f"REMINDERS: Found {len(user_ids)} users for role {role}")
            
            for uid in user_ids:
                desired.add((dt, uid))
                print(f"REMINDERS: Added desired reminder for user {uid}, datetime {dt}")
    except (json.JSONDecodeError, TypeError) as e:
        print(f"REMINDERS: Error parsing reminders_json or user IDs: {e}")
        logging.error(f"Could not parse reminders_json or associated user IDs: {e}")
    
    print(f"REMINDERS: Calculated {len(desired)} total desired reminders")
    return desired

def sync_reminders(task_id: str, task_payload: dict, user_jwt: str):
    print(f"REMINDERS: Starting sync for task {task_id}")
    logging.info(f"[{task_id}] Синхронизация напоминаний…")
    try:
        print(f"REMINDERS: Getting existing reminders for task {task_id}")
        existing = _get_existing_reminders(task_id, user_jwt)
        print(f"REMINDERS: Found {len(existing)} existing reminders for task {task_id}")
        
        print(f"REMINDERS: Calculating desired reminders for task {task_id}")
        desired = _get_desired_reminders(task_payload)
        print(f"REMINDERS: Calculated {len(desired)} desired reminders for task {task_id}")
        
        to_delete = [eid for key, eid in existing.items() if key not in desired]
        to_create = [key for key in desired if key not in existing]

        print(f"REMINDERS: Task {task_id} - to create: {len(to_create)}, to delete: {len(to_delete)}")
        logging.info(f"[{task_id}] Reminders to create: {len(to_create)}, to delete: {len(to_delete)}")

        print(f"REMINDERS: Deleting {len(to_delete)} obsolete reminders for task {task_id}")
        for eid in to_delete:
            print(f"REMINDERS: Deleting reminder event {eid} for task {task_id}")
            invoke_utils.invoke_function(FUNCTION_ID_SCHEDULER_MANAGER, {"action": "DELETE", "event_id": eid}, user_jwt)
        
        print(f"REMINDERS: Creating {len(to_create)} new reminders for task {task_id}")
        title = task_payload.get("title", "Без названия")
        for dt, uid in to_create:
            print(f"REMINDERS: Creating reminder for task {task_id}, user {uid}, datetime {dt}")
            scheduler_payload = {
                "action": "UPSERT",
                "payload": {
                    "function_id": FUNCTION_ID_ENDPOINTS_MANAGER,
                    "custom_identifier": task_id,
                    "is_annual": False,
                    "execution_dates_json": json.dumps([dt]),
                    "request_body_json": json.dumps({
                        "action": "SEND", # Важно для фильтрации в _get_existing_reminders
                        "user_id_to_notify": uid,
                        "payload": {
                            "title": "Напоминание по задаче",
                            "body": f"Не забудьте про задачу: «{title}»"
                        }
                    }),
                    "request_headers_json": {"Authorization": f"Bearer {user_jwt}"},
                    "is_active": True
                }
            }
            invoke_utils.invoke_function(FUNCTION_ID_SCHEDULER_MANAGER, scheduler_payload, user_jwt)
            print(f"REMINDERS: Successfully created reminder for task {task_id}, user {uid}")
        
        print(f"REMINDERS: Sync completed successfully for task {task_id}")
        logging.info(f"[{task_id}] Синхронизация напоминаний завершена")
    except Exception as e:
        print(f"REMINDERS: Error syncing reminders for task {task_id}: {e}")
        logging.error(f"[{task_id}] Ошибка синхронизации напоминаний: {e}", exc_info=True)
```