import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/create_firm_cubit.dart';

class CreateFirmDialog extends StatefulWidget {
  const CreateFirmDialog({super.key});

  @override
  State<CreateFirmDialog> createState() => _CreateFirmDialogState();
}

class _CreateFirmDialogState extends State<CreateFirmDialog> {
  final _controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _submit(BuildContext ctx) {
    if (!_formKey.currentState!.validate()) return;
    final name = _controller.text.trim();
    ctx.read<CreateFirmCubit>().createFirm(name);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CreateFirmCubit, CreateFirmState>(
      listener: (ctx, state) {
        if (state.success) {
          Navigator.of(ctx).pop();
          ScaffoldMessenger.of(ctx).showSnackBar(
            const SnackBar(
              content: Text('Фирма успешно создана!'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (state.errorMessage != null) {
          ScaffoldMessenger.of(ctx).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      builder: (ctx, state) {
        return AlertDialog(
          title: const Text('Создать новую фирму'),
          content: Form(
            key: _formKey,
            child: TextFormField(
              controller: _controller,
              autofocus: true,
              decoration: const InputDecoration(
                labelText: 'Название фирмы',
                hintText: 'Введите название',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Название не может быть пустым';
                }
                return null;
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Отмена'),
            ),
            state.isLoading
                ? const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
                : FilledButton(
                  onPressed: () => _submit(context),
                  child: const Text('Создать'),
                ),
          ],
        );
      },
    );
  }
}
