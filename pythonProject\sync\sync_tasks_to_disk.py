import requests
import json
import sys
import os
import re
import datetime as dt
import time
import tempfile
from functools import wraps
from colorama import init, Fore, Style
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from io import BytesIO
from datetime import timezone, timedelta

# Импортируем наши утилиты
import yandex_disk_uploader
import pdf_generator
import importlib

# На случай, если модуль был закеширован без новой функции
if not hasattr(yandex_disk_uploader, "upload_and_get_link_from_bytes"):
    importlib.reload(yandex_disk_uploader)

# --- Инициализация ---
init(autoreset=True)

# --- Система логирования ---
LOG_MESSAGES = []
LOG_LOCK = threading.Lock()

def log_message(message: str, level: str = "INFO"):
    """Добавляет сообщение в лог."""
    timestamp = dt.datetime.now().strftime("%H:%M:%S")
    with LOG_LOCK:
        LOG_MESSAGES.append(f"[{timestamp}] [{level.upper()}] {message}")

def print_final_log():
    """Выводит весь накопленный лог."""
    print("\n" + "="*80)
    print(f"{Style.BRIGHT}ПОЛНЫЙ ЛОГ ВЫПОЛНЕНИЯ:{Style.RESET_ALL}")
    print("="*80)
    for msg in LOG_MESSAGES:
        print(msg)
    print("="*80)

def log_progress(message: str):
    """Добавляет форматированное сообщение в лог, убирая ANSI-коды."""
    clean_message = re.sub(r'\x1b\[[0-9;]*m', '', message)
    # Убираем только правые пробелы и новые строки, сохраняя отступы слева
    clean_message = clean_message.rstrip()
    if clean_message: # не логируем пустые строки
        log_message(clean_message, level="PROGRESS")


# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
API_TASKS_URL = "https://d5d7ct0sul274igh4vij.aqkd4clz.apigw.yandexcloud.net/manage"
API_CLIENTS_URL = "https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net/manage"
API_EMPLOYEES_URL = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net/employees/edit"
API_GATEWAY_BASE = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net"
API_STORAGE_URL = "https://d5d5vaj6bd49bcvhnfbh.sk0vql13.apigw.yandexcloud.net/manage"

FIRM_ID = "9a33483b-dfad-44a3-a36d-102b498ec0ef"
LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

YADISK_BASE_FOLDER = "balansoved enterprise"
PAGE_SIZE = 100
MAX_RETRIES = 3
RETRY_BASE_DELAY_SEC = 5
SYNC_FREQUENCY_DAYS = 7 # Квота частоты запуска в днях

# --- Символы и кеши ---
TICK, CROSS, INFO, WARN = "✓", "✗", "→", "!"
CLIENT_NAME_CACHE, EMPLOYEE_NAME_CACHE = {}, {}


def populate_employee_cache(auth_headers: dict):
    """Заполняет кеш сотрудников одним запросом к /get-user-data."""
    # 1. Пробуем специальный Gateway-эндпоинт (может вернуть связи фирм/задач)
    try:
        resp = requests.get(f"{API_GATEWAY_BASE}/get-user-data", headers=auth_headers, timeout=20)
        if resp.status_code == 200:
            data = resp.json().get("data") or resp.json()
            employees = []
            for key in ("employees", "users", "team", "staff"):
                if key in data and isinstance(data[key], list):
                    employees = data[key]
                    break
            for emp in employees:
                uid = emp.get("user_id") or emp.get("id") or emp.get("uid")
                name = emp.get("full_name") or emp.get("fullName") or emp.get("name")
                if uid and name:
                    EMPLOYEE_NAME_CACHE[uid] = name
            log_message(f"Кеш сотрудников пополнен из get-user-data: {len(employees)} записей.")
        else:
            log_message(f"get-user-data ответил статус {resp.status_code}.", "WARN")
    except requests.exceptions.RequestException as e:
        log_message(f"Ошибка сети get-user-data: {e}", "WARN")

    # 2. Запрашиваем полный список сотрудников фирмы через /employees/edit GET_INFO
    try:
        payload = {"firm_id": FIRM_ID, "action": "GET_INFO"}
        resp = requests.post(API_EMPLOYEES_URL, json=payload, headers=auth_headers, timeout=20)
        if resp.status_code == 200:
            employees = resp.json().get("data", [])
            if isinstance(employees, list):
                for emp in employees:
                    uid = emp.get("user_id") or emp.get("id") or emp.get("uid")
                    name = emp.get("full_name") or emp.get("fullName") or emp.get("name")
                    if uid and name:
                        EMPLOYEE_NAME_CACHE[uid] = name
                log_message(f"Кеш сотрудников пополнен из employees/edit: {len(employees)} записей.")
            else:
                log_message(f"employees/edit ответил статус {resp.status_code}.", "WARN")
        else:
            log_message(f"employees/edit ответил статус {resp.status_code}.", "WARN")
    except requests.exceptions.RequestException as e:
        log_message(f"Ошибка сети employees/edit: {e}", "WARN")


# =================================================================================
# ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
# =================================================================================

def retry_network_operation(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        for attempt in range(MAX_RETRIES):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.RequestException as e:
                wait_time = RETRY_BASE_DELAY_SEC * (2 ** attempt)
                log_message(f"Попытка {attempt + 1}/{MAX_RETRIES} провалена. Ошибка сети: {e}.", "WARN")
                if attempt + 1 < MAX_RETRIES:
                    log_message(f"Повторная попытка через {wait_time} секунд...", "WARN")
                    time.sleep(wait_time)
                else:
                    log_message(f"Все попытки исчерпаны. Операция не удалась.", "ERROR")
                    return None
        return None

    return wrapper


@retry_network_operation
def make_api_request(url: str, payload: dict, headers: dict):
    return requests.post(url, json=payload, headers=headers, timeout=20)


def run_api_step(title: str, url: str, payload: dict, headers: dict, expected_status: int, increment: bool = True):
    log_line_start = f"► {title}"
    response = make_api_request(url, payload, headers)

    if response is None:
        log_progress(f"{log_line_start} ... {CROSS} (Операция не удалась после {MAX_RETRIES} попыток)")
        return None
    if response.status_code == expected_status:
        log_progress(f"{log_line_start} ... {TICK} (Статус: {response.status_code})")
        return response
    else:
        log_progress(f"{log_line_start} ... {CROSS} (Ожидался статус {expected_status}, получен {response.status_code})")
        log_progress(f"  Текст ошибки: {response.text}")
        return None


@retry_network_operation
def download_file_to_memory(url: str):
    """Скачивает файл и возвращает bytes."""
    with requests.get(url, stream=True) as r:
        r.raise_for_status()
        buffer = BytesIO()
        for chunk in r.iter_content(chunk_size=8192):
            buffer.write(chunk)
        return buffer.getvalue()


def sanitize_filename(name: str) -> str:
    return re.sub(r'[\\/*?:"<>|]', '_', name).strip()


def get_client_name(client_id: str, auth_headers: dict) -> str:
    if client_id in CLIENT_NAME_CACHE: return CLIENT_NAME_CACHE[client_id]
    payload = {"firm_id": FIRM_ID, "action": "GET", "client_id": client_id}
    try:
        response = requests.post(API_CLIENTS_URL, json=payload, headers=auth_headers, timeout=10)
        if response.status_code == 200:
            name = response.json().get('data', {}).get('client_name', client_id)
            CLIENT_NAME_CACHE[client_id] = name
            return name
    except requests.exceptions.RequestException:
        pass
    CLIENT_NAME_CACHE[client_id] = client_id
    return client_id


def get_employee_name(employee_id: str, auth_headers: dict) -> str:
    if employee_id in EMPLOYEE_NAME_CACHE: return EMPLOYEE_NAME_CACHE[employee_id]
    payload = {"firm_id": FIRM_ID, "action": "GET_INFO", "user_id_to_edit": employee_id}
    try:
        response = requests.post(API_EMPLOYEES_URL, json=payload, headers=auth_headers, timeout=10)
        if response.status_code == 200:
            data = response.json().get('data', {})

            # --- Универсальная рекурсивная функция поиска ФИО ---
            def find_name_in_obj(obj):
                if isinstance(obj, str):
                    # Эвристика: строка с пробелом и длиной > 3 — вероятно ФИО
                    if " " in obj and len(obj) > 3:
                        return obj
                elif isinstance(obj, dict):
                    for k, v in obj.items():
                        # Сначала проверяем ключи, которые явно могут содержать имя
                        if k in ("full_name", "fullName", "name", "display_name", "user_name") and isinstance(v, str) and v:
                            return v
                        res = find_name_in_obj(v)
                        if res:
                            return res
                elif isinstance(obj, list):
                    for item in obj:
                        res = find_name_in_obj(item)
                        if res:
                            return res
                return None

            candidate = find_name_in_obj(data)
            if candidate:
                EMPLOYEE_NAME_CACHE[employee_id] = candidate
                return candidate
            # Имя не найдено — возможно нет прав / пустой ответ
            # Оставляем fallback ниже
        elif response.status_code == 403:
            # Недостаточно прав — возвращаем ID, но помечаем в кэше
            EMPLOYEE_NAME_CACHE[employee_id] = employee_id
            return employee_id
    except requests.exceptions.RequestException:
        pass
    EMPLOYEE_NAME_CACHE[employee_id] = employee_id
    return employee_id


def get_client_folder_name(client_ids_json: str, auth_headers: dict) -> str:
    try:
        client_ids = json.loads(client_ids_json or '[]')
        if not client_ids: return "unassigned"
        folder_parts = [f"{sanitize_filename(get_client_name(cid, auth_headers))} ({cid})" for cid in client_ids]
        return "-".join(sorted(folder_parts))
    except (json.JSONDecodeError, TypeError):
        return "invalid_client_id_format"


def count_task_operations(task_data: dict) -> int:
    """Подсчитывает количество операций для одной задачи."""
    # 1 (детали) + N*(1(ссылка)+1(скачать)+1(загрузить)) + 1(pdf) + 1(json)
    attachments_count = len(json.loads(task_data.get('attachments_json', '[]')))
    return 1 + (attachments_count * 3) + 2


# =================================================================================
# ОСНОВНАЯ ЛОГИКА
# =================================================================================

def process_single_task(task_summary: dict, base_path_on_disk: str, auth_headers: dict, yadisk_token: str,
                        temp_dir: str):
    task_id = task_summary['task_id']
    task_title_raw = task_summary.get('title', 'Без_названия')
    task_title_sanitized = sanitize_filename(task_title_raw)
    log_progress(f"--- Обработка задачи: {task_title_raw} ---")

    get_task_payload = {"firm_id": FIRM_ID, "action": "GET", "task_id": task_id}
    task_response = run_api_step(f"Детали: {task_title_raw[:30]}...", API_TASKS_URL, get_task_payload, auth_headers,
                                 200)
    if not task_response: return

    task_data = task_response.json().get('data', {})

    client_folder_name = get_client_folder_name(task_data.get('client_ids_json'), auth_headers)
    task_folder_name = f"{task_title_sanitized} ({task_id})"
    task_folder_on_disk = f"{base_path_on_disk}/{client_folder_name}/{task_folder_name}"

    log_progress(f"  {INFO} Путь на Диске: {task_folder_on_disk}")
    if not yandex_disk_uploader.ensure_path_recursively(yadisk_token, task_folder_on_disk): return

    new_attachments_with_urls = []
    attachments_from_task = json.loads(task_data.get('attachments_json', '[]'))
    if attachments_from_task:
        files_folder_on_disk = f"{task_folder_on_disk}/файлы"
        total_files = len(attachments_from_task)
        log_progress(f"  {INFO} Найдено вложений: {total_files}. Загрузка файлов...")
        # Для подпапки "файлы" достаточно один раз убедиться, что она существует,
        # чтобы избежать повторных проверок каждого уровня пути (что порождает лишние API-запросы).
        if not yandex_disk_uploader.ensure_folder_exists(yadisk_token, files_folder_on_disk):
            return

        counter_lock = threading.Lock()
        completed = {"cnt": 0}

        def update_counter():
            with counter_lock:
                completed["cnt"] += 1
                msg = f"  {INFO} Вложения: {completed['cnt']}/{total_files}"
                log_progress(msg)

        def handle_attachment(attachment: dict):
            raw_key = attachment.get('file_key') or attachment.get('fileKey')
            file_key = str(raw_key) if raw_key else ""
            filename = attachment.get('name', os.path.basename(file_key))

            if not file_key:
                log_progress(f"  {WARN} Пропуск вложения (нет key): {filename}")
                return None

            for attempt in range(1, MAX_RETRIES + 1):
                try:
                    # --- 1. Получаем download URL ---
                    payload = {"firm_id": FIRM_ID, "action": "GET_DOWNLOAD_URL", "file_key": file_key}
                    resp = make_api_request(API_STORAGE_URL, payload, auth_headers)
                    if not resp or resp.status_code != 200:
                        raise RuntimeError(f"Storage API status {resp.status_code if resp else 'N/A'}")
                    download_url = resp.json().get('download_url')
                    if not download_url:
                        raise RuntimeError("download_url отсутствует в ответе")

                    # --- 2. Скачиваем файл в память ---
                    log_message(f"Скачивание: {filename[:25]}...")
                    file_bytes = download_file_to_memory(download_url)
                    if file_bytes is None:
                        raise RuntimeError("Download returned None")

                    # --- 3. Загружаем в Я.Диск ---
                    log_message(f"Загрузка: {filename[:25]}...")
                    target_path_on_disk = f"{files_folder_on_disk}/{filename}"
                    if hasattr(yandex_disk_uploader, "upload_and_get_link_from_bytes"):
                        yadisk_link = yandex_disk_uploader.upload_and_get_link_from_bytes(
                            yadisk_token, file_bytes, target_path_on_disk)
                    else:
                        temp_path = os.path.join(temp_dir, filename)
                        with open(temp_path, "wb") as f:
                            f.write(file_bytes)
                        yadisk_link = yandex_disk_uploader.upload_and_get_link(yadisk_token, temp_path, target_path_on_disk)

                    if not yadisk_link:
                        raise RuntimeError("upload returned None")

                    update_counter()
                    return {"name": filename, "public_url": yadisk_link, "file_key": file_key}

                except Exception as e:
                    log_progress(f"  {WARN} Ошибка обработки '{filename}' (попытка {attempt}/{MAX_RETRIES}): {e}")
                    if attempt == MAX_RETRIES:
                        log_progress(f"  {CROSS} Файл '{filename}' пропущен после {MAX_RETRIES} попыток.")
                        return None

        # Ограничиваем количество потоков, чтобы не создавать слишком много одновременных загрузок
        max_workers = min(4, len(attachments_from_task))
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_map = {executor.submit(handle_attachment, att): att for att in attachments_from_task}
            for future in as_completed(future_map):
                result = future.result()
                if result:
                    new_attachments_with_urls.append(result)
    else:
        log_progress(f"  {INFO} Вложения отсутствуют.")

    log_progress(f"  {INFO} Подготовка данных для PDF...")
    participant_roles = ['assignee_ids_json', 'creator_ids_json', 'observer_ids_json']
    participants_map = {role.split('_')[0]: [f"{get_employee_name(eid, auth_headers)} ({eid})" for eid in
                                             json.loads(task_data.get(role, '[]'))] for role in participant_roles}
    clients_map = [f"{get_client_name(cid, auth_headers)} ({cid})" for cid in
                   json.loads(task_data.get('client_ids_json', '[]'))]

    log_message(f"Создание PDF: {task_title_raw[:25]}...")
    pdf_path = os.path.join(temp_dir, 'task_report.pdf')
    if pdf_generator.generate_task_pdf(pdf_path, task_data, participants_map, clients_map, new_attachments_with_urls):
        final_pdf_path_on_disk = f"{task_folder_on_disk}/Отчет по задаче.pdf"
        yandex_disk_uploader.upload_and_get_link(yadisk_token, pdf_path, final_pdf_path_on_disk)
        log_progress(f"  {TICK} Отчет PDF сохранен.")
    else:
        log_progress(f"  {CROSS} Не удалось сгенерировать PDF.")

    task_data_for_json = task_data.copy()
    task_data_for_json['attachments_json'] = json.dumps(new_attachments_with_urls, ensure_ascii=False)
    task_json_path = os.path.join(temp_dir, 'task_info_raw.json')
    with open(task_json_path, 'w', encoding='utf-8') as f:
        json.dump(task_data_for_json, f, ensure_ascii=False, indent=4)
    final_json_path_on_disk = f"{task_folder_on_disk}/task_info_raw.json"
    yandex_disk_uploader.upload_and_get_link(yadisk_token, task_json_path, final_json_path_on_disk)
    log_progress(f"  {TICK} Файл JSON сохранен.")


def main():
    script_start = time.time()
    log_progress("--- Запуск скрипта синхронизации с Яндекс Диском ---")

    # ---- Проверка режима запуска ----
    is_test_mode_str = os.environ.get('SYNC_TEST_MODE', 'true').lower()
    is_test_mode = is_test_mode_str != 'false'

    if is_test_mode:
        log_message("РЕЖИM ТЕСТИРОВАНИЯ АКТИВЕН: проверка частоты синхронизации будет проигнорирована.", "WARN")
    else:
        log_message("Продакшн-режим: проверка частоты синхронизации активна.", "INFO")

    login_response = run_api_step("Авторизация", API_AUTH_URL, LOGIN_PAYLOAD, DEFAULT_HEADERS, 200)
    if not login_response:
        log_message("Авторизация не удалась", "ERROR")
        return
    auth_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {login_response.json()['token']}"}

    # ---- Кеш всех сотрудников фирмы ----
    populate_employee_cache(auth_headers)

    # ---- Проверка и обновление времени последней синхронизации ----
    integrations_url = "https://d5d5vaj6bd49bcvhnfbh.sk0vql13.apigw.yandexcloud.net/integrations"
    integrations_response = run_api_step("Получение интеграций", integrations_url,
                                         {"firm_id": FIRM_ID, "action": "GET"}, auth_headers, 200)

    if not integrations_response:
        log_message("Не удалось получить данные об интеграциях.", "ERROR")
        return

    integrations = integrations_response.json().get('integrations', {})
    yadisk_data = integrations.get('yandex_disk', {})
    yadisk_token = yadisk_data.get('token')

    if not yadisk_token:
        log_message("Критическая ошибка: токен Яндекс Диска не найден.", "ERROR")
        return
    log_progress(f"{TICK} Токен Яндекс Диска получен.")

    # Проверяем квоту только если это не тестовый режим
    if not is_test_mode:
        last_sync_str = yadisk_data.get('last_sync_utc')
        if last_sync_str:
            try:
                # Нормализуем строку, т.к. fromisoformat() до Python 3.11 не понимает 'Z'
                if last_sync_str.upper().endswith('Z'):
                    parse_str = last_sync_str[:-1] + '+00:00'
                else:
                    parse_str = last_sync_str
                
                last_sync_dt = dt.datetime.fromisoformat(parse_str)

                # Убедимся, что у нас есть часовой пояс для корректного сравнения
                if last_sync_dt.tzinfo is None:
                    last_sync_dt = last_sync_dt.replace(tzinfo=timezone.utc)

                if dt.datetime.now(timezone.utc) - last_sync_dt < timedelta(days=SYNC_FREQUENCY_DAYS):
                    next_run_time = (last_sync_dt + timedelta(days=SYNC_FREQUENCY_DAYS)).strftime('%Y-%m-%d %H:%M UTC')
                    log_message(
                        f"Последняя синхронизация была менее {SYNC_FREQUENCY_DAYS} дней назад. Следующий запуск возможен после {next_run_time}.",
                        "WARN")
                    return
            except ValueError:
                log_message(f"Не удалось распознать формат даты последней синхронизации: '{last_sync_str}'", "WARN")

    # Немедленно обновляем время последней синхронизации
    now_utc_iso = dt.datetime.now(timezone.utc).isoformat()
    yadisk_data['last_sync_utc'] = now_utc_iso
    
    update_payload = {
        "firm_id": FIRM_ID, 
        "action": "UPSERT", 
        "payload": {"yandex_disk": yadisk_data}
    }
    
    update_response = run_api_step("Обновление времени синхронизации", integrations_url, update_payload, auth_headers, 200)
    if not update_response:
        log_message("Не удалось обновить время последней синхронизации. Синхронизация прервана.", "ERROR")
        return
    log_progress(f"{TICK} Время последней синхронизации успешно обновлено.")


    # --- Устанавливаем обработку всех задач по умолчанию ---
    process_timeless = True
    process_dated = True
    log_progress(f"{INFO} Режим работы: обработка бессрочных и датированных задач.")

    tasks_to_process = []
    log_message("Получение списка задач...")
    if process_timeless:
        page = 0
        while True:
            resp = make_api_request(API_TASKS_URL, {"firm_id": FIRM_ID, "action": "GET", "page": page}, auth_headers)
            if not resp or resp.status_code != 200 or not resp.json().get('data'): break
            tasks = resp.json()['data']
            tasks_to_process.extend([{'type': 'timeless', 'data': t} for t in tasks])
            if len(tasks) < PAGE_SIZE: break
            page += 1

    if process_dated:
        current_year = dt.datetime.now().year
        for month in range(1, 13):
            resp = make_api_request(API_TASKS_URL,
                                    {"firm_id": FIRM_ID, "action": "GET", "get_dated_tasks": True, "year": current_year,
                                     "month": month}, auth_headers)
            if resp and resp.status_code == 200 and resp.json().get('data'):
                tasks_to_process.extend(
                    [{'type': 'dated', 'data': t, 'year': current_year, 'month': month} for t in resp.json()['data']])

    if not tasks_to_process:
        log_progress(f"Актуальных задач для обработки не найдено. Завершение работы.")
        return

    log_message("Подготовка к обработке...")

    with tempfile.TemporaryDirectory() as temp_dir:
        log_progress(f"Временная папка: {temp_dir}")
        log_progress(
            f"Всего задач для обработки: {len(tasks_to_process)}.")

        for i, task_info in enumerate(tasks_to_process):
            log_progress(f"\n[ЗАДАЧА {i+1}/{len(tasks_to_process)}]")
            task_data = task_info['data']
            if task_info['type'] == 'timeless':
                base_path = f"{YADISK_BASE_FOLDER}/бессрочные задачи"
            else:
                base_path = f"{YADISK_BASE_FOLDER}/задачи/{task_info['year']}/{task_info['month']:02d}"

            yandex_disk_uploader.ensure_path_recursively(yadisk_token, base_path)
            process_single_task(task_data, base_path, auth_headers, yadisk_token, temp_dir)
            time.sleep(0.2)

    log_progress("--- Синхронизация успешно завершена! ---")
    elapsed = time.time() - script_start
    log_progress(f"Время выполнения скрипта: {elapsed:.2f} сек.")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("Процесс прерван пользователем", "WARN")
    except Exception as e:
        log_message(f"Непредвиденная ошибка: {e}", "ERROR")
        import traceback
        log_message(f"Трассировка: {traceback.format_exc()}", "ERROR")
    finally:
        print_final_log()