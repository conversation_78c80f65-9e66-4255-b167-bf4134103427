import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_local_data_source.dart';
import 'package:flutter/foundation.dart';

import '../../domain/entities/file_upload_entity.dart';
import '../../domain/entities/tariffs_and_storage_entity.dart';
import '../../domain/repositories/tariffs_and_storage_repository.dart';
import '../data_source/tariffs_and_storage_remote_data_source.dart';

class TariffsAndStorageRepositoryImpl implements ITariffsAndStorageRepository {
  final ITariffsAndStorageRemoteDataSource remoteDataSource;
  final IAuthLocalDataSource localAuth;

  TariffsAndStorageRepositoryImpl({
    required this.remoteDataSource,
    required this.localAuth,
  });

  @override
  Future<Either<Failure, TariffsAndStorageEntity>> getTariffsAndStorage(
    String firmId,
  ) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return const Left(
          ConnectionFailure(message: 'Токен авторизации не найден'),
        );
      }

      final result = await remoteDataSource.getTariffsAndStorage(token, firmId);
      return Right(result.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnexpectedFailure(message: 'Неизвестная ошибка: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateJsonFields({
    required String firmId,
    required String targetJsonField,
    required Map<String, dynamic> updates,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return const Left(
          ConnectionFailure(message: 'Токен авторизации не найден'),
        );
      }

      await remoteDataSource.updateJsonFields(
        token: token,
        firmId: firmId,
        targetJsonField: targetJsonField,
        updates: updates,
      );
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnexpectedFailure(message: 'Неизвестная ошибка: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearJsonFields({
    required String firmId,
    required List<String> fieldsToClear,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return const Left(
          ConnectionFailure(message: 'Токен авторизации не найден'),
        );
      }

      await remoteDataSource.clearJsonFields(
        token: token,
        firmId: firmId,
        fieldsToClear: fieldsToClear,
      );
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnexpectedFailure(message: 'Неизвестная ошибка: $e'));
    }
  }

  @override
  Future<Either<Failure, FileUploadEntity>> getUploadUrl({
    required String firmId,
    required String fileName,
    required int fileSize,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return const Left(
          ConnectionFailure(message: 'Токен авторизации не найден'),
        );
      }

      final result = await remoteDataSource.getUploadUrl(
        token: token,
        firmId: firmId,
        fileName: fileName,
        fileSize: fileSize,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnexpectedFailure(message: 'Неизвестная ошибка: $e'));
    }
  }

  @override
  Future<Either<Failure, FileDownloadEntity>> getDownloadUrl({
    required String firmId,
    required String fileKey,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return const Left(
          ConnectionFailure(message: 'Токен авторизации не найден'),
        );
      }

      final result = await remoteDataSource.getDownloadUrl(
        token: token,
        firmId: firmId,
        fileKey: fileKey,
      );
      return Right(result);
    } on ServerException catch (e) {
      debugPrint('❌ [REPO] ServerException в getDownloadUrl: ${e.message}');
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      debugPrint('❌ [REPO] NetworkException в getDownloadUrl: ${e.message}');
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      debugPrint('❌ [REPO] Неизвестная ошибка в getDownloadUrl: $e');
      return Left(UnexpectedFailure(message: 'Неизвестная ошибка: $e'));
    }
  }

  @override
  Stream<FileUploadProgressEntity> uploadFile({
    required String uploadUrl,
    required String fileKey,
    required String firmId,
    required List<int> fileBytes,
    required String fileName,
  }) async* {
    try {
      await for (final progress in remoteDataSource.uploadFileViaHttp(
        uploadUrl: uploadUrl,
        fileBytes: fileBytes,
        fileName: fileName,
      )) {
        yield FileUploadProgressEntity(
          fileKey: fileKey,
          progress: progress.progress,
          status: progress.status,
          errorMessage: progress.errorMessage,
        );
      }
    } catch (e) {
      yield FileUploadProgressEntity(
        fileKey: fileKey,
        progress: 0.0,
        status: FileUploadStatus.failed,
        errorMessage: e.toString(),
      );
    }
  }

  @override
  Future<Either<Failure, void>> confirmUpload({
    required String firmId,
    required String fileKey,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return const Left(
          ConnectionFailure(message: 'Токен авторизации не найден'),
        );
      }

      await remoteDataSource.confirmUpload(
        token: token,
        firmId: firmId,
        fileKey: fileKey,
      );
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnexpectedFailure(message: 'Неизвестная ошибка: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteFile({
    required String firmId,
    required String fileKey,
  }) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return const Left(
          ConnectionFailure(message: 'Токен авторизации не найден'),
        );
      }

      await remoteDataSource.deleteFile(
        token: token,
        firmId: firmId,
        fileKey: fileKey,
      );
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnexpectedFailure(message: 'Неизвестная ошибка: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> checkSpaceAvailable({
    required String firmId,
    required int fileSize,
  }) async {
    try {
      // Получаем текущие данные о тарифах и хранилище
      final tariffsResult = await getTariffsAndStorage(firmId);

      return tariffsResult.fold((failure) => Left(failure), (tariffsData) {
        final hasSpace = tariffsData.hasSpaceForFile(fileSize);
        return Right(hasSpace);
      });
    } catch (e) {
      return Left(
        UnexpectedFailure(message: 'Ошибка проверки доступности места: $e'),
      );
    }
  }
}
