import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/domain/repositories/tasks_repository.dart';

abstract class TasksState extends Equatable {
  const TasksState();

  @override
  List<Object?> get props => [];
}

class TasksInitial extends TasksState {}

class TasksLoading extends TasksState {
  final TaskRequestParams currentParams;

  const TasksLoading({required this.currentParams});

  @override
  List<Object?> get props => [currentParams];
}

class TasksLoaded extends TasksState {
  final TasksResult result;
  final TaskRequestParams currentParams;
  final bool canLoadMore;

  const TasksLoaded({
    required this.result,
    required this.currentParams,
    this.canLoadMore = false,
  });

  List<TaskEntity> get tasks => result.tasks;
  TaskPaginationMeta? get paginationMeta => result.paginationMeta;

  @override
  List<Object?> get props => [result, currentParams, canLoadMore];
}

class TasksLoadingMore extends TasksState {
  final TasksResult currentResult;
  final TaskRequestParams currentParams;

  const TasksLoadingMore({
    required this.currentResult,
    required this.currentParams,
  });

  List<TaskEntity> get tasks => currentResult.tasks;
  TaskPaginationMeta? get paginationMeta => currentResult.paginationMeta;

  @override
  List<Object?> get props => [currentResult, currentParams];
}

class TasksError extends TasksState {
  final String message;

  const TasksError({required this.message});

  @override
  List<Object?> get props => [message];
}

class TasksNoAccess extends TasksState {
  final String message;

  const TasksNoAccess({this.message = 'Недостаточно прав для просмотра задач'});

  @override
  List<Object?> get props => [message];
}

class TaskLoading extends TasksState {
  const TaskLoading();
}

class TaskLoaded extends TasksState {
  final TaskEntity task;

  const TaskLoaded({required this.task});

  @override
  List<Object?> get props => [task];
}

class TaskError extends TasksState {
  final String message;

  const TaskError({required this.message});

  @override
  List<Object?> get props => [message];
}
