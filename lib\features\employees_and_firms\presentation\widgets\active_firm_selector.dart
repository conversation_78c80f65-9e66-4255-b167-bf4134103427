import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import 'package:balansoved_enterprise/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:balansoved_enterprise/injection_container.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/widgets/create_firm_dialog.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/create_firm_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/widgets/invitation_status_widget.dart';

const String _inviteCodeKey = '__INVITE_CODE__';
const String _createFirmKey = '__CREATE_FIRM__';

class ActiveFirmSelector extends StatelessWidget {
  const ActiveFirmSelector({super.key});

  void _showInviteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (ctx) => const _InvitationCodeDialog(),
    );
  }

  void _showCreateFirmDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (ctx) => BlocProvider<CreateFirmCubit>.value(
            value: sl<CreateFirmCubit>(),
            child: const CreateFirmDialog(),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ActiveFirmCubit, ActiveFirmState>(
      listener: (context, state) {
        // Можно добавить обработку ошибок, если нужно
      },
      builder: (context, state) {
        if (state.isLoading) {
          return const SizedBox(
            height: 32,
            width: 140,
            child: LoadingTile(height: 32, width: 140),
          );
        }

        if (state.firms.isEmpty) {
          return Row(
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.add_business),
                label: const Text('Создать фирму'),
                onPressed: () => _showCreateFirmDialog(context),
                style: ElevatedButton.styleFrom(
                  visualDensity: VisualDensity.compact,
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.card_giftcard),
                label: const Text('Ввести код приглашения'),
                onPressed: () => _showInviteDialog(context),
                style: ElevatedButton.styleFrom(
                  visualDensity: VisualDensity.compact,
                ),
              ),
            ],
          );
        }

        return PopupMenuButton<String>(
          initialValue: state.selectedFirm?.id,
          tooltip: 'Выбрать фирму',
          onSelected: (id) {
            if (id == _inviteCodeKey) {
              _showInviteDialog(context);
            } else if (id == _createFirmKey) {
              _showCreateFirmDialog(context);
            } else {
              final firm = state.firms.firstWhere((f) => f.id == id);
              context.read<ActiveFirmCubit>().selectFirm(firm);
            }
          },
          itemBuilder: (ctx) {
            final List<PopupMenuEntry<String>> items =
                state.firms
                    .map(
                      (f) => PopupMenuItem<String>(
                        value: f.id,
                        child: Text(f.name),
                      ),
                    )
                    .toList();

            items.add(
              const PopupMenuItem<String>(
                value: _createFirmKey,
                child: Row(
                  children: [
                    Icon(Icons.add_business, size: 20),
                    SizedBox(width: 8),
                    Text('Создать фирму'),
                  ],
                ),
              ),
            );

            items.add(
              const PopupMenuItem<String>(
                enabled: false,
                height: 8,
                child: Divider(height: 1),
              ),
            );
            items.add(
              const PopupMenuItem<String>(
                value: _inviteCodeKey,
                child: Row(
                  children: [
                    Icon(Icons.card_giftcard, size: 20),
                    SizedBox(width: 8),
                    Text('Ввести код приглашения'),
                  ],
                ),
              ),
            );
            return items;
          },
          child: Row(
            children: [
              const Icon(Icons.business),
              const SizedBox(width: 4),
              Text(state.selectedFirm?.name ?? 'Фирма'),
              const Icon(Icons.arrow_drop_down),
            ],
          ),
        );
      },
    );
  }
}

class _InvitationCodeDialog extends StatefulWidget {
  const _InvitationCodeDialog();

  @override
  State<_InvitationCodeDialog> createState() => _InvitationCodeDialogState();
}

class _InvitationCodeDialogState extends State<_InvitationCodeDialog> {
  final _controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      final code = _controller.text.trim();
      final authCubit = context.read<AuthCubit>();
      final profileCubit = sl<ProfileCubit>();

      // Закрываем диалог перед вызовом
      Navigator.of(context).pop();

      // Показываем индикатор загрузки
      InvitationStatusWidget.showLoading(context, 'Активируем приглашение...');

      try {
        final success = await authCubit.acceptInvitation(code);

        if (mounted) {
          if (success) {
            // Показываем успешное сообщение
            InvitationStatusWidget.showSuccess(
              context, 
              'Приглашение принято! Обновляем данные...',
              icon: Icons.check_circle,
            );
            
            // Небольшая задержка перед обновлением профиля
            await Future.delayed(const Duration(milliseconds: 1500));
            
            // Обновляем профиль, чтобы подтянуть новую фирму
            try {
              await profileCubit.forceReloadProfile();
              
              if (mounted) {
                // Показываем финальное сообщение об успехе
                InvitationStatusWidget.showSuccess(
                  context,
                  'Данные обновлены! Новая фирма добавлена.',
                  icon: Icons.business,
                );
              }
            } catch (e) {
              if (mounted) {
                InvitationStatusWidget.showWarning(
                  context,
                  'Приглашение принято, но не удалось обновить список фирм. Перезагрузите страницу.',
                );
              }
            }
          } else {
            // Показываем ошибку
            InvitationStatusWidget.showError(
              context,
              'Не удалось активировать приглашение. Проверьте код и попробуйте снова.',
            );
          }
        }
      } catch (e) {
        if (mounted) {
          InvitationStatusWidget.showError(
            context,
            'Произошла ошибка: ${e.toString()}',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Ввести код приглашения'),
      content: Form(
        key: _formKey,
        child: TextFormField(
          controller: _controller,
          autofocus: true,
          decoration: const InputDecoration(
            labelText: 'Код приглашения',
            hintText: 'Вставьте код из письма',
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Код не может быть пустым';
            }
            return null;
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        FilledButton(onPressed: _submit, child: const Text('Подтвердить')),
      ],
    );
  }
}
