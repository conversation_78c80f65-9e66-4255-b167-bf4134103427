import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/refresh_token_cubit.dart';
import 'package:balansoved_enterprise/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:get_it/get_it.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/storage_usage_widget.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/backup_integration_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/widgets/backup_toggle_widget.dart';
import 'package:balansoved_enterprise/features/profile/presentation/widgets/clear_sessions_button.dart';

@RoutePage()
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ProfileCubit>(
          create: (_) => GetIt.I<ProfileCubit>()..fetchProfile(),
        ),
        BlocProvider<RefreshTokenCubit>(
          create: (_) => GetIt.I<RefreshTokenCubit>(),
        ),
        BlocProvider<BackupIntegrationCubit>(
          create: (_) => GetIt.I<BackupIntegrationCubit>(),
        ),
      ],
      child: Scaffold(
        appBar: AppBar(title: const Text('Профиль')),
        body: BlocBuilder<ProfileCubit, ProfileState>(
          builder: (context, state) {
            if (state is ProfileLoading || state is ProfileInitial) {
              return const Center(child: CircularProgressIndicator());
            }
            if (state is ProfileError) {
              return Center(child: Text(state.message));
            }
            if (state is ProfileLoaded) {
              final p = state.profile;
              context.read<ActiveFirmCubit>().setFirms(p.firms);
              return Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('ID: ${p.id}'),
                    const SizedBox(height: 8),
                    Text('Email: ${p.email ?? '-'}'),
                    const SizedBox(height: 8),
                    Text('Имя: ${p.userName ?? '-'}'),
                    const SizedBox(height: 8),
                    Text('Фирм: ${p.firmsCount}'),
                    const SizedBox(height: 8),
                    Text('Задач (всего): ${p.tasksCount}'),
                    const SizedBox(height: 24),

                    // Виджет хранилища для активной фирмы
                    BlocBuilder<ActiveFirmCubit, ActiveFirmState>(
                      builder: (context, firmState) {
                        if (firmState.selectedFirm != null) {
                          return Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Хранилище фирмы: ${firmState.selectedFirm!.name}',
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                  const SizedBox(height: 12),
                                  StorageUsageWidget(
                                    firmId: firmState.selectedFirm!.id,
                                    size: 60.0,
                                  ),
                                  const SizedBox(height: 16),
                                  BackupToggleWidget(
                                    firmId: firmState.selectedFirm!.id,
                                  ),
                                ],
                              ),
                            ),
                          );
                        } else {
                          return Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.business,
                                    size: 48,
                                    color:
                                        Theme.of(context).colorScheme.outline,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Фирма не выбрана',
                                    style: TextStyle(
                                      color:
                                          Theme.of(
                                            context,
                                          ).colorScheme.onSurfaceVariant,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Выберите фирму для просмотра хранилища',
                                    style: TextStyle(
                                      color:
                                          Theme.of(context).colorScheme.outline,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }
                      },
                    ),

                    const Spacer(),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
        persistentFooterButtons: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Кнопка "Очистить сеансы аккаунта"
                SizedBox(
                  width: double.infinity,
                  child: const ClearSessionsButton(),
                ),
                
                const SizedBox(height: 16),
                
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      context.read<AuthCubit>().signOut();
                    },
                    icon: const Icon(Icons.logout),
                    label: const Text('Выйти'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
