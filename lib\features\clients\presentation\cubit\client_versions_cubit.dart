import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/system_task_uid_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_client_versions_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/delete_client_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/upsert_client_usecase.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

part 'client_versions_state.dart';

class ClientVersionsCubit extends Cubit<ClientVersionsState> {
  final GetClientVersionsUseCase getClientVersionsUseCase;
  final DeleteClientUseCase deleteClientUseCase;
  final UpsertClientUseCase upsertClientUseCase;

  ClientVersionsCubit({
    required this.getClientVersionsUseCase,
    required this.deleteClientUseCase,
    required this.upsertClientUseCase,
  }) : super(const ClientVersionsState.initial());

  Future<void> fetchVersions(String firmId, String clientName) async {
    emit(state.copyWith(isLoading: true, error: null));
    NetworkLogger.printInfo(
      'ClientVersionsCubit: Starting fetchVersions for firmId: $firmId, clientName: $clientName',
    );

    try {
      final result = await getClientVersionsUseCase.call(firmId, clientName);

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientVersionsCubit: fetchVersions failed with error:',
            failure.message,
          );
          emit(state.copyWith(isLoading: false, error: failure.message));
        },
        (versions) {
          NetworkLogger.printSuccess(
            'ClientVersionsCubit: fetchVersions success, loaded ${versions.length} versions',
          );

          // Сортируем версии: актуальная первая, затем по дате создания (новые первые)
          final sortedVersions = List<ClientEntity>.from(versions);
          sortedVersions.sort((a, b) {
            // Сначала актуальная версия
            if (a.isActual && !b.isActual) return -1;
            if (!a.isActual && b.isActual) return 1;

            // Затем по дате создания версии (новые первые)
            final aDate =
                a.manualCreationDate ?? a.creationDate ?? DateTime(1970);
            final bDate =
                b.manualCreationDate ?? b.creationDate ?? DateTime(1970);
            return bDate.compareTo(aDate);
          });

          emit(
            state.copyWith(
              isLoading: false,
              versions: sortedVersions,
              selectedVersion:
                  sortedVersions.isNotEmpty ? sortedVersions.first : null,
              error: null,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientVersionsCubit: Unexpected error in fetchVersions:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  void selectVersion(ClientEntity version) {
    NetworkLogger.printInfo(
      'ClientVersionsCubit: Selecting version with date: ${version.manualCreationDate ?? version.creationDate}',
    );
    emit(state.copyWith(selectedVersion: version));
  }

  Future<void> deleteVersion(
    String firmId,
    String clientId,
    DateTime creationDate,
  ) async {
    NetworkLogger.printInfo(
      'ClientVersionsCubit: Deleting version with clientId: $clientId, creationDate: $creationDate',
    );

    final versionToDeleteIndex = state.versions.indexWhere(
      (v) =>
          v.id == clientId &&
          (v.manualCreationDate ?? v.creationDate)?.millisecondsSinceEpoch ==
              creationDate.millisecondsSinceEpoch,
    );

    if (versionToDeleteIndex == -1) {
      emit(state.copyWith(error: 'Версия для удаления не найдена'));
      return;
    }

    final versionToDelete = state.versions[versionToDeleteIndex];

    // Если удаляем актуальную версию, переносим ее системные задачи на новую актуальную
    if (versionToDelete.isActual && state.versions.length > 1) {
      // Новой актуальной станет следующая в списке (предполагается, что список отсортирован)
      final newActualVersion = state.versions.firstWhere(
        (v) => v != versionToDelete,
      );
      final uidsToTransfer = versionToDelete.systemTaskUids;

      if (uidsToTransfer.isNotEmpty) {
        final allUids =
            (newActualVersion.systemTaskUids.toSet()..addAll(uidsToTransfer))
                .toList();
        final updateResult = await upsertClientUseCase.call(
          firmId,
          newActualVersion.copyWith(systemTaskUids: allUids, isActual: true),
        );

        if (updateResult.isLeft()) {
          updateResult.fold((failure) {
            emit(
              state.copyWith(
                error:
                    'Не удалось перенести системные задачи: ${failure.message}',
              ),
            );
          }, (_) {});
          return; // Прерываем удаление, если не удалось перенести задачи
        }
      }
    }

    try {
      final result = await deleteClientUseCase.call(
        firmId,
        clientId,
        creationDate,
      );

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientVersionsCubit: deleteVersion failed with error:',
            failure.message,
          );
          emit(state.copyWith(error: failure.message));
        },
        (_) async {
          NetworkLogger.printSuccess(
            'ClientVersionsCubit: deleteVersion success',
          );
          final clientName =
              state.versions.isNotEmpty ? state.versions.first.name : '';
          if (clientName.isNotEmpty) {
            await fetchVersions(firmId, clientName);
            if (state.versions.isNotEmpty) {
              selectVersion(state.versions.first);
            }
          }
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientVersionsCubit: Unexpected error in deleteVersion:',
        e,
        stackTrace,
      );
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> createNewVersion(String firmId, ClientEntity baseVersion) async {
    NetworkLogger.printInfo(
      'ClientVersionsCubit: Creating new actual version based on: ${baseVersion.id}',
    );

    if (!baseVersion.isActual) {
      emit(
        state.copyWith(
          error:
              'Можно создать новую актуальную версию только из текущей актуальной версии.',
        ),
      );
      return;
    }

    try {
      // 1. Переносим системные задачи
      final uidsToTransfer = baseVersion.systemTaskUids;

      // 2. "Понижаем" старую версию до неактуальной
      final demotedResult = await upsertClientUseCase.call(
        firmId,
        baseVersion.copyWith(
          isActual: false,
          systemTaskUids: [],
        ), // Очищаем задачи у старой
      );

      await demotedResult.fold(
        (failure) async {
          NetworkLogger.printError(
            'ClientVersionsCubit: createNewVersion (demoting old) failed:',
            failure.message,
          );
          emit(state.copyWith(error: failure.message));
        },
        (_) async {
          // 3. Создаем новую актуальную версию
          final newVersion = baseVersion.copyWith(
            manualCreationDate: DateTime.now(),
            isActual: true,
            systemTaskUids: uidsToTransfer, // Переносим задачи
          );

          final createResult = await upsertClientUseCase.call(
            firmId,
            newVersion,
          );

          await createResult.fold(
            (failure) async {
              NetworkLogger.printError(
                'ClientVersionsCubit: createNewVersion (creating new) failed:',
                failure.message,
              );
              emit(state.copyWith(error: failure.message));
              // Попытка "откатить" понижение старой версии
              await upsertClientUseCase.call(
                firmId,
                baseVersion.copyWith(isActual: true),
              );
            },
            (createdVersion) async {
              NetworkLogger.printSuccess(
                'ClientVersionsCubit: createNewVersion success',
              );
              await fetchVersions(firmId, baseVersion.name);

              final newVersionDate =
                  createdVersion.manualCreationDate ??
                  createdVersion.creationDate;
              if (newVersionDate != null) {
                final createdVersionInList = state.versions.firstWhere(
                  (v) =>
                      (v.manualCreationDate ?? v.creationDate)
                          ?.millisecondsSinceEpoch ==
                      newVersionDate.millisecondsSinceEpoch,
                  orElse: () => state.versions.first,
                );
                selectVersion(createdVersionInList);
              }
            },
          );
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientVersionsCubit: Unexpected error in createNewVersion:',
        e,
        stackTrace,
      );
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> updateVersionDate(
    String firmId,
    ClientEntity version,
    DateTime newDate,
  ) async {
    NetworkLogger.printInfo(
      'ClientVersionsCubit: Updating version date for client: ${version.id}',
    );

    try {
      // Шаг 1: Создаем копию версии с новой датой
      // КРИТИЧЕСКИ ВАЖНО: Сохраняем client_id для поддержания целостности версионирования
      final newVersionCopy = version.copyWith(
        // НЕ очищаем id - все версии клиента должны иметь один client_id!
        manualCreationDate: newDate,
      );

      final createResult = await upsertClientUseCase.call(
        firmId,
        newVersionCopy,
      );

      createResult.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientVersionsCubit: updateVersionDate (create step) failed with error:',
            failure.message,
          );
          emit(state.copyWith(error: failure.message));
        },
        (createdVersion) async {
          NetworkLogger.printSuccess(
            'ClientVersionsCubit: New version created successfully',
          );

          // Шаг 2: Удаляем старую версию
          final deleteDate = version.manualCreationDate ?? version.creationDate;
          if (deleteDate != null) {
            final deleteResult = await deleteClientUseCase.call(
              firmId,
              version.id,
              deleteDate,
            );

            deleteResult.fold(
              (failure) {
                NetworkLogger.printError(
                  'ClientVersionsCubit: updateVersionDate (delete step) failed with error:',
                  failure.message,
                );
                emit(state.copyWith(error: failure.message));
              },
              (_) async {
                NetworkLogger.printSuccess(
                  'ClientVersionsCubit: Old version deleted successfully',
                );
                // Перезагружаем версии после завершения операции и выбираем обновленную версию
                await fetchVersions(firmId, version.name);

                // Находим и выбираем версию с новой датой
                final updatedVersionInList = state.versions.firstWhere(
                  (v) =>
                      (v.manualCreationDate ?? v.creationDate)
                          ?.millisecondsSinceEpoch ==
                      newDate.millisecondsSinceEpoch,
                  orElse: () => state.versions.first,
                );
                selectVersion(updatedVersionInList);
              },
            );
          } else {
            NetworkLogger.printError(
              'ClientVersionsCubit: Cannot delete version - no creation date found',
              'Missing creation date',
            );
            emit(
              state.copyWith(
                error:
                    'Не удалось найти дату создания для удаления старой версии',
              ),
            );
          }
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientVersionsCubit: Unexpected error in updateVersionDate:',
        e,
        stackTrace,
      );
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> transferSystemTaskMarksToActual(String firmId) async {
    NetworkLogger.printInfo(
      'ClientVersionsCubit: Transferring system task marks to actual version',
    );

    if (state.versions.isEmpty) return;

    // Определяем актуальную версию (если нет, берём первую)
    final actualVersion = state.versions.firstWhere(
      (v) => v.isActual,
      orElse: () => state.versions.first,
    );

    // Собираем все UID задач из всех версий
    final Set<SystemTaskUidEntity> allTaskUids = {};
    for (final v in state.versions) {
      allTaskUids.addAll(v.systemTaskUids);
    }

    // Функция обновления версии
    Future<bool> updateVersion(
      ClientEntity version,
      List<SystemTaskUidEntity> uids,
    ) async {
      final result = await upsertClientUseCase.call(
        firmId,
        version.copyWith(systemTaskUids: uids),
      );
      return result.fold((failure) {
        NetworkLogger.printError(
          'ClientVersionsCubit: transferSystemTaskMarksToActual failed for version:',
          failure.message,
        );
        return false;
      }, (_) => true);
    }

    bool hasError = false;

    // Очищаем исторические версии и наполняем актуальную
    for (final v in state.versions) {
      if (v.id == actualVersion.id) {
        // Обновляем актуальную, если набор UID отличается
        if (v.systemTaskUids.toSet() != allTaskUids) {
          if (!await updateVersion(v, allTaskUids.toList())) hasError = true;
        }
      } else if (v.systemTaskUids.isNotEmpty) {
        if (!await updateVersion(v, const [])) hasError = true;
      }
    }

    if (!hasError) {
      // Перезагружаем версии, чтобы отобразить изменения
      await fetchVersions(firmId, actualVersion.name);
      NetworkLogger.printSuccess(
        'ClientVersionsCubit: transferSystemTaskMarksToActual completed',
      );
    }
  }

  void reset() {
    emit(const ClientVersionsState.initial());
  }
}
