// lib/features/clients/presentation/widgets/edit_card_components/client_edit_header.dart
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/client_versions_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_version_selector.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/injection_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ClientEditHeader extends StatelessWidget {
  final String clientName;
  final ValueChanged<ClientEntity> onVersionSelected;

  const ClientEditHeader({
    super.key,
    required this.clientName,
    required this.onVersionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        const Text(
          'Редактировать клиента',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(width: 24),
        BlocProvider(
          create: (context) => sl<ClientVersionsCubit>(),
          child: Row(
            children: [
              ClientVersionSelector(
                firmId:
                    context.watch<ActiveFirmCubit>().state.selectedFirm?.id ??
                    '',
                clientName: clientName,
                onVersionSelected: onVersionSelected,
              ),
              const SizedBox(width: 8),
              IconButton(
                tooltip: 'Перенести отметки задач в актуальную версию',
                icon: const Icon(Icons.task_alt),
                onPressed: () {
                  final firm =
                      context.read<ActiveFirmCubit>().state.selectedFirm;
                  if (firm == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Сначала выберите фирму')),
                    );
                    return;
                  }
                  context
                      .read<ClientVersionsCubit>()
                      .transferSystemTaskMarksToActual(firm.id);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
