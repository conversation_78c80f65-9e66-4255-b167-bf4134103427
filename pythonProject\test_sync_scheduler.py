import requests
import json
import sys
import datetime
import pytz
from colorama import init, Fore, Style

# Инициализируем colorama
init(autoreset=True)

# --- Конфигурация и константы ---
API_AUTH_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net/login"
API_USER_DATA_URL = "https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net/get-user-data"
API_SCHEDULER_MANAGE_URL = "https://d5dln4usaaktunc0kvia.8wihnuyr.apigw.yandexcloud.net/manage"
API_SCHEDULER_TRIGGER_URL = "https://d5dln4usaaktunc0kvia.8wihnuyr.apigw.yandexcloud.net/trigger"

# ID функции, которую мы будем вызывать по расписанию
SYNC_FUNCTION_ID = "d4e3f6917icsff9hikrh"

LOGIN_PAYLOAD = {"email": "<EMAIL>", "password": "458854Mm"}
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# Символы для статуса
TICK = Fore.GREEN + "✓" + Style.RESET_ALL
CROSS = Fore.RED + "✗" + Style.RESET_ALL
INFO = Fore.CYAN + "→" + Style.RESET_ALL


# --- ИЗМЕНЕНИЕ 1: Добавлен параметр timeout_seconds со значением по умолчанию 30 ---
def run_test_step(title: str, url: str, method: str, payload: dict, headers: dict, expected_status: int, timeout_seconds: int = 30):
    """Выполняет один шаг теста (POST или GET), выводит результат и возвращает ответ в случае успеха."""
    print(f"{Style.BRIGHT}► {title}", end=" ... ")
    try:
        if method.upper() == 'POST':
            # --- ИЗМЕНЕНИЕ 2: Используем новый параметр таймаута ---
            response = requests.post(url, json=payload, headers=headers, timeout=timeout_seconds)
        elif method.upper() == 'GET':
            # --- ИЗМЕНЕНИЕ 3: Используем новый параметр таймаута ---
            response = requests.get(url, headers=headers, timeout=timeout_seconds)
        else:
            raise ValueError("Unsupported HTTP method")

        if response.status_code == expected_status:
            print(f"{TICK} (Статус: {response.status_code})")
            return response
        else:
            print(f"{CROSS} (Ожидался статус {expected_status}, получен {response.status_code})")
            print(Fore.RED + "  Текст ошибки:")
            try:
                print(Fore.RED + json.dumps(response.json(), indent=4, ensure_ascii=False))
            except json.JSONDecodeError:
                print(Fore.RED + response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"{CROSS} (Исключение во время запроса)")
        print(Fore.RED + f"  Текст ошибки: {e}")
        return None


def wait_for_confirmation(phase_name: str):
    """Приостанавливает выполнение, ожидая подтверждения от пользователя (y/n)."""
    while True:
        prompt = f"\n--- Этап '{phase_name}' завершен. Продолжить? (y/n): "
        choice = input(Style.BRIGHT + Fore.YELLOW + prompt + Style.RESET_ALL).lower()
        if choice == 'y':
            print()
            return
        if choice == 'n':
            print(f"\n{CROSS} Тестирование прервано пользователем.")
            sys.exit()


if __name__ == "__main__":
    print("\n--- Начало теста 'Запланированная синхронизация с Яндекс.Диском' ---\n")

    # --- Фаза 1: Авторизация и выбор фирмы ---
    print(f"{Style.BRIGHT}--- Фаза 1: Авторизация и выбор фирмы ---{Style.RESET_ALL}")
    auth_response = run_test_step("Шаг 1.1: Получение JWT токена", API_AUTH_URL, 'POST', LOGIN_PAYLOAD, DEFAULT_HEADERS,
                                  200)
    if not auth_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось войти в систему. Тестирование прервано.")

    jwt_token = auth_response.json().get("token")
    authorized_headers = {**DEFAULT_HEADERS, "Authorization": f"Bearer {jwt_token}"}
    print(f"   {INFO} Успешно получен JWT токен для следующих запросов.")

    user_data_response = run_test_step("Шаг 1.2: Получение данных пользователя и списка фирм", API_USER_DATA_URL, 'GET',
                                       {}, authorized_headers, 200)
    if not user_data_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось получить данные пользователя.")

    all_firms = user_data_response.json().get("firms", [])
    owner_firms = [f for f in all_firms if "OWNER" in f.get("user_roles", [])]

    if not owner_firms:
        sys.exit(f"\n{CROSS} Критическая ошибка: не найдено фирм, где пользователь является владельцем (OWNER).")

    print(f"\n   {INFO} Найдены фирмы, где вы являетесь владельцем:")
    for i, firm in enumerate(owner_firms):
        print(f"     {Style.BRIGHT}[{i + 1}] {firm['firm_name']} (ID: ...{firm['firm_id'][-12:]})")

    selected_firm_id = None
    while not selected_firm_id:
        try:
            choice = int(input(f"\n{Style.BRIGHT}Введите номер фирмы для работы: {Style.RESET_ALL}")) - 1
            if 0 <= choice < len(owner_firms):
                selected_firm_id = owner_firms[choice]['firm_id']
                print(f"   {TICK} Выбрана фирма: {owner_firms[choice]['firm_name']}")
            else:
                print(f"   {CROSS} Неверный номер. Пожалуйста, введите число от 1 до {len(owner_firms)}.")
        except ValueError:
            print(f"   {CROSS} Пожалуйста, введите число.")

    # --- Фаза 2: Создание события ---
    print(f"\n{Style.BRIGHT}--- Фаза 2: Создание запланированного события ---{Style.RESET_ALL}")

    trigger_time = (datetime.datetime.now(pytz.utc) - datetime.timedelta(minutes=10)).isoformat()

    sync_request_body = {
        "firm_id": selected_firm_id,
        "test_mode": True
    }

    create_event_payload = {
        "action": "UPSERT",
        "payload": {
            "function_id": SYNC_FUNCTION_ID,
            "custom_identifier": "auto-test-sync-to-disk",
            "is_annual": False,
            "execution_dates_json": json.dumps([trigger_time]),
            "request_body_json": json.dumps(sync_request_body),
            "is_active": True
        }
    }

    create_response = run_test_step("Шаг 2.1: Создание события на синхронизацию", API_SCHEDULER_MANAGE_URL, 'POST',
                                    create_event_payload, authorized_headers, 201)
    if not create_response:
        sys.exit(f"\n{CROSS} Критическая ошибка: не удалось создать событие. Тестирование прервано.")
    event_id = create_response.json().get("event_id")
    print(f"   {INFO} Создано событие с ID: {event_id}")
    print(
        f"   {INFO} Оно запланировано на {trigger_time.split('T')[1].split('.')[0]} UTC и будет немедленно выполнено триггером.")

    wait_for_confirmation("Создание события")

    # --- Фаза 3: Запуск триггера ---
    print(f"{Style.BRIGHT}--- Фаза 3: Ручной запуск триггера ---{Style.RESET_ALL}")
    print(f"   {INFO} Этот шаг имитирует срабатывание таймера Yandex Cloud.")
    print(f"   {INFO} Так как событие запланировано в прошлом, оно будет гарантированно запущено.")
    print(f"   {INFO} Таймаут для этого шага установлен на 10 минут...")
    # --- ИЗМЕНЕНИЕ 4: Передаем новый таймаут (10 минут = 600 секунд) ---
    run_test_step("Шаг 3.1: Вызов scheduler-trigger", API_SCHEDULER_TRIGGER_URL, 'POST', {}, DEFAULT_HEADERS, 200, timeout_seconds=600)

    wait_for_confirmation("Запуск триггера")

    # --- Фаза 4: Очистка ---
    print(f"{Style.BRIGHT}--- Фаза 4: Очистка тестовых данных ---{Style.RESET_ALL}")
    delete_payload = {"action": "DELETE", "event_id": event_id}
    run_test_step("Шаг 4.1: Удаление тестового события", API_SCHEDULER_MANAGE_URL, 'POST', delete_payload,
                  authorized_headers, 200)

    # --- Фаза 5: Проверка ---
    print(f"\n{Style.BRIGHT}--- Фаза 5: Финальная проверка ---{Style.RESET_ALL}")
    get_payload = {"action": "GET", "event_id": event_id}
    run_test_step("Шаг 5.1: Проверка, что событие удалено (ожидаем 404)", API_SCHEDULER_MANAGE_URL, 'POST', get_payload,
                  authorized_headers, 404)

    print("\n--- Тестирование запланированной синхронизации успешно завершено ---")