import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../cubit/price_calculator_cubit.dart';
import '../../cubit/price_calculator_state.dart';
import 'settings_dialog.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';

// Фиксированная высота одной строки параметров
const double _rowHeight = 64;

class PriceCalculatorBody extends StatelessWidget {
  const PriceCalculatorBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Основное содержимое
        Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: BlocBuilder<PriceCalculatorCubit, PriceCalculatorState>(
                  builder: (context, state) {
                    final cubit = context.read<PriceCalculatorCubit>();
                    return ScreenTypeLayout.builder(
                      mobile:
                          (context) =>
                              _buildMobileLayout(context, state, cubit),
                      desktop:
                          (context) =>
                              _buildDesktopLayout(context, state, cubit),
                    );
                  },
                ),
              ),
            ),
            // отступ, чтобы контент не перекрывался футером
            const SizedBox(height: 100),
          ],
        ),
        // Плашка с итогом
        Positioned(bottom: 16, right: 16, child: const _TotalFooter()),
      ],
    );
  }
}

Widget _buildMobileLayout(
  BuildContext context,
  PriceCalculatorState state,
  PriceCalculatorCubit cubit,
) {
  return Column(
    children: [
      _buildEntitySelector(context, state, cubit),
      const SizedBox(height: 16),
      ..._buildSections(context, state, cubit),
    ],
  );
}

Widget _buildDesktopLayout(
  BuildContext context,
  PriceCalculatorState state,
  PriceCalculatorCubit cubit,
) {
  final sections = _buildSections(context, state, cubit);
  return LayoutBuilder(
    builder: (context, constraints) {
      // Определяем количество колонок так, чтобы ширина одной карточки была ≤600
      final maxWidth = constraints.maxWidth;
      final crossAxisCount = (maxWidth / 620).floor().clamp(1, 6);

      return Column(
        children: [
          _buildEntitySelector(context, state, cubit),
          const SizedBox(height: 16),
          MasonryGridView.extent(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            maxCrossAxisExtent: 400, // карточка ~1.5 раза уже
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            itemCount: sections.length,
            itemBuilder: (_, i) => sections[i],
          ),
        ],
      );
    },
  );
}

Widget _buildEntitySelector(
  BuildContext context,
  PriceCalculatorState state,
  PriceCalculatorCubit cubit,
) {
  return Center(
    child: SegmentedButton<EntityType>(
      segments:
          EntityType.values
              .map(
                (e) =>
                    ButtonSegment<EntityType>(value: e, label: Text(e.label)),
              )
              .toList(),
      selected: {state.entityType},
      onSelectionChanged: (newSelection) {
        cubit.changeEntity(newSelection.first);
      },
    ),
  );
}

List<Widget> _buildSections(
  BuildContext context,
  PriceCalculatorState state,
  PriceCalculatorCubit cubit,
) {
  return [
    _SectionCard(
      title: 'Базовый тариф',
      sectionKey: 'base_rates',
      children: [
        _OptionRow(
          label: 'Вид деятельности',
          control: _BusinessActivitySelector(
            value: state.businessActivityType,
            onChanged: (v) => cubit.updateField('businessActivityType', v),
          ),
        ),
        _OptionRow(
          label: 'Система налогообложения',
          control: _TaxSystemSelector(
            value: state.taxSystem,
            onChanged: (v) => cubit.updateField('taxSystem', v),
          ),
        ),
        _OptionRow(
          label: 'Базовый тариф',
          control: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${state.baseRate} ₽',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ),
      ],
    ),
    _SectionCard(
      title: 'Операции и первичка',
      sectionKey: 'operations',
      children: [
        _OptionRow(
          label: 'Общее количество операций в месяц',
          control: _NumberControl(
            value: state.operationsQty,
            onChanged: (v) => cubit.updateField('operationsQty', v),
          ),
        ),
        _OptionRow(
          label: 'Формирование первички нашей компанией',
          control: _SwitchControl(
            value: state.generatePrimaryDocs,
            onChanged: (v) => cubit.updateField('generatePrimaryDocs', v),
          ),
        ),
      ],
    ),
    _SectionCard(
      title: 'Сотрудники',
      sectionKey: 'employees',
      children: [
        _OptionRow(
          label: 'Количество сотрудников в штате',
          control: _NumberControl(
            value: state.employeesQty,
            onChanged: (v) => cubit.updateField('employeesQty', v),
          ),
        ),
      ],
    ),
    _SectionCard(
      title: 'Банк, касса, ВЭД',
      sectionKey: 'bank',
      children: [
        _OptionRow(
          label: 'Количество платежек в месяц',
          control: _NumberControl(
            value: state.paymentsQty,
            onChanged: (v) => cubit.updateField('paymentsQty', v),
          ),
        ),
        _OptionRowVertical(
          label: 'ВЭД',
          control: _VedControl(
            value: state.vedOption,
            onChanged: (v) => cubit.updateField('vedOption', v),
          ),
        ),
        _OptionRow(
          label: 'Наличие ККМ, БСО',
          control: _SwitchControl(
            value: state.kkmBso,
            onChanged: (v) => cubit.updateField('kkmBso', v),
          ),
        ),
        _OptionRow(
          label: 'Доп. расчетный счет (кол-во)',
          control: _NumberControl(
            value: state.extraAccountQty,
            onChanged: (v) => cubit.updateField('extraAccountQty', v),
          ),
        ),
        _OptionRow(
          label: 'Валютный счет (кол-во)',
          control: _NumberControl(
            value: state.currencyAccountQty,
            onChanged: (v) => cubit.updateField('currencyAccountQty', v),
          ),
        ),
      ],
    ),
    _SectionCard(
      title: 'Прочие особенности',
      sectionKey: 'features',
      children: [
        _OptionRow(
          label: 'Количество номенклатур',
          control: _NumberControl(
            value: state.nomenclatureQty,
            onChanged: (v) => cubit.updateField('nomenclatureQty', v),
          ),
        ),
        _OptionRow(
          label: 'Совмещение систем налогообложения',
          control: _SwitchControl(
            value: state.taxSystemMix,
            onChanged: (v) => cubit.updateField('taxSystemMix', v),
          ),
        ),
        _OptionRow(
          label: 'Разные ставки НДС',
          control: _SwitchControl(
            value: state.ndsRates,
            onChanged: (v) => cubit.updateField('ndsRates', v),
          ),
        ),
        _OptionRow(
          label: 'ПБУ 18 (налог на прибыль)',
          control: _SwitchControl(
            value: state.pbu18,
            onChanged: (v) => cubit.updateField('pbu18', v),
          ),
        ),
        _OptionRow(
          label: 'Основные средства и НМА',
          control: _SwitchControl(
            value: state.osNma,
            onChanged: (v) => cubit.updateField('osNma', v),
          ),
        ),
        _OptionRow(
          label: 'ФТС ЕАЭС',
          control: _SwitchControl(
            value: state.ftsEaes,
            onChanged: (v) => cubit.updateField('ftsEaes', v),
          ),
        ),
        _OptionRow(
          label: 'Торговля акцизными товарами',
          control: _SwitchControl(
            value: state.exciseGoods,
            onChanged: (v) => cubit.updateField('exciseGoods', v),
          ),
        ),
        _OptionRow(
          label: 'Заполнение алкогольной декларации',
          control: _SwitchControl(
            value: state.alcoholDeclaration,
            onChanged: (v) => cubit.updateField('alcoholDeclaration', v),
          ),
        ),
        _OptionRow(
          label: 'Количество обособленных подразделений',
          control: _NumberControl(
            value: state.subdivisionsQty,
            onChanged: (v) => cubit.updateField('subdivisionsQty', v),
          ),
        ),
        _OptionRow(
          label: 'Сложные операции (лизинг, кредиты и т.д.)',
          control: _SwitchControl(
            value: state.complexOperations,
            onChanged: (v) => cubit.updateField('complexOperations', v),
          ),
        ),
      ],
    ),
    _SectionCard(
      title: 'Дополнительные услуги',
      sectionKey: 'services',
      children: [
        _OptionRow(
          label: 'Ведение в нашей 1С',
          control: _SwitchControl(
            value: state.our1C,
            onChanged: (v) => cubit.updateField('our1C', v),
          ),
        ),
        _OptionRow(
          label: 'Отдельный доступ в 1С',
          control: _SwitchControl(
            value: state.separate1CAccess,
            onChanged: (v) => cubit.updateField('separate1CAccess', v),
          ),
        ),
        _OptionRow(
          label: 'VPN',
          control: _SwitchControl(
            value: state.vpn,
            onChanged: (v) => cubit.updateField('vpn', v),
          ),
        ),
        _OptionRow(
          label: 'Повторное подписание документов',
          control: _SwitchControl(
            value: state.documentResigning,
            onChanged: (v) => cubit.updateField('documentResigning', v),
          ),
        ),
        _OptionRow(
          label: 'Оплата взносов курьером',
          control: _SwitchControl(
            value: state.courierPayment,
            onChanged: (v) => cubit.updateField('courierPayment', v),
          ),
        ),
        _OptionRow(
          label: 'Снятие блоков р/с',
          control: _SwitchControl(
            value: state.accountUnblocking,
            onChanged: (v) => cubit.updateField('accountUnblocking', v),
          ),
        ),
      ],
    ),
  ];
}

class _SectionCard extends StatelessWidget {
  final String title;
  final String sectionKey;
  final List<Widget> children;

  const _SectionCard({
    required this.title,
    required this.children,
    required this.sectionKey,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<PriceCalculatorCubit>();

    // Динамическая высота: базовый хедер + высота строки × кол-во полей
    const double header = 96; // включая отступы и заголовок
    const double row = _rowHeight; // фиксированная высота строки
    final double cardHeight = header + children.length * row;

    return SizedBox(
      height: cardHeight,
      child: Card(
        margin: EdgeInsets.zero,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 8, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(title, style: Theme.of(context).textTheme.titleMedium),
                  IconButton(
                    icon: const Icon(Icons.settings_outlined),
                    tooltip: 'Настроить тарифы',
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (_) => BlocProvider.value(
                          value: cubit,
                          child: SettingsDialog(sectionKey: sectionKey),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(children: children),
            ),
          ],
        ),
      ),
    );
  }
}

class _TotalFooter extends StatefulWidget {
  const _TotalFooter();

  @override
  State<_TotalFooter> createState() => _TotalFooterState();
}

class _TotalFooterState extends State<_TotalFooter>
    with SingleTickerProviderStateMixin {
  bool _expanded = false;
  static const double _width = 420;

  @override
  Widget build(BuildContext context) {
    final state = context.watch<PriceCalculatorCubit>().state;
    final numberFormat = NumberFormat.currency(locale: 'ru_RU', symbol: '₽');
    final breakdown = context.read<PriceCalculatorCubit>().breakdown();

    return SizedBox(
      width: _expanded ? 600 : _width,
      child: AnimatedSize(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: Card(
          elevation: 8,
          child: InkWell(
            onTap: () => setState(() => _expanded = !_expanded),
            customBorder: const RoundedRectangleBorder(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child:
                  _expanded
                      ? Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Итоговая стоимость',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              Row(
                                children: [
                                  Text(
                                    numberFormat.format(state.total),
                                    style: Theme.of(
                                      context,
                                    ).textTheme.headlineMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Icon(Icons.expand_more),
                                ],
                              ),
                            ],
                          ),
                          const Divider(),
                          SizedBox(
                            height: 260,
                            child: Scrollbar(
                              child: ListView(
                                padding: EdgeInsets.zero,
                                children:
                                    breakdown.entries.map((e) {
                                      final isNegative = e.value < 0;
                                      final valueText = numberFormat.format(
                                        e.value,
                                      );
                                      final valueStyle = Theme.of(
                                        context,
                                      ).textTheme.bodyMedium!.copyWith(
                                        color:
                                            isNegative
                                                ? Theme.of(
                                                  context,
                                                ).colorScheme.error
                                                : Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                        fontWeight: FontWeight.w600,
                                      );
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 4,
                                          horizontal: 0,
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                e.key,
                                                style:
                                                    Theme.of(
                                                      context,
                                                    ).textTheme.bodyMedium,
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            Text(valueText, style: valueStyle),
                                          ],
                                        ),
                                      );
                                    }).toList(),
                              ),
                            ),
                          ),
                        ],
                      )
                      : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              'Итоговая стоимость в месяц:',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            numberFormat.format(state.total),
                            style: Theme.of(
                              context,
                            ).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(Icons.expand_less),
                        ],
                      ),
            ),
          ),
        ),
      ),
    );
  }
}

class _OptionRow extends StatelessWidget {
  final String label;
  final Widget control;
  const _OptionRow({required this.label, required this.control});
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _rowHeight,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 3,
            child: Text(label, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Expanded(flex: 2, child: control),
        ],
      ),
    );
  }
}

class _OptionRowVertical extends StatelessWidget {
  final String label;
  final Widget control;
  const _OptionRowVertical({required this.label, required this.control});
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _rowHeight,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(label, style: Theme.of(context).textTheme.bodyMedium),
          const SizedBox(height: 4),
          control,
        ],
      ),
    );
  }
}

class _NumberControl extends StatefulWidget {
  final int value;
  final ValueChanged<int> onChanged;
  const _NumberControl({required this.value, required this.onChanged});
  @override
  State<_NumberControl> createState() => _NumberControlState();
}

class _NumberControlState extends State<_NumberControl> {
  late TextEditingController _controller;
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value.toString());
  }

  @override
  void didUpdateWidget(covariant _NumberControl oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      final newText = widget.value.toString();
      if (_controller.text != newText) {
        final previousSelection = _controller.selection;
        _controller.text = newText;
        _controller.selection = previousSelection;
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 120,
      child: UniversalNumberField(
        controller: _controller,
        labelText: '',
        fieldType: NumberFieldType.integer,
        isEditing: true,
        onChanged: (v) {
          final parsed = int.tryParse(v);
          if (parsed != null) {
            widget.onChanged(parsed);
          }
        },
      ),
    );
  }
}

class _SwitchControl extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  const _SwitchControl({required this.value, required this.onChanged});
  @override
  Widget build(BuildContext context) {
    return Switch.adaptive(value: value, onChanged: onChanged);
  }
}

class _VedControl extends StatelessWidget {
  final VedOption value;
  final ValueChanged<VedOption> onChanged;
  const _VedControl({required this.value, required this.onChanged});
  @override
  Widget build(BuildContext context) {
    return SegmentedButton<VedOption>(
      segments:
          VedOption.values
              .map(
                (v) => ButtonSegment<VedOption>(
                  value: v,
                  label: Text(
                    v == VedOption.none
                        ? 'Нет'
                        : v == VedOption.importExport
                        ? 'И/Э'
                        : 'И+Э',
                  ),
                ),
              )
              .toList(),
      selected: {value},
      onSelectionChanged: (s) => onChanged(s.first),
    );
  }
}

class _BusinessActivitySelector extends StatelessWidget {
  final BusinessActivityType value;
  final ValueChanged<BusinessActivityType> onChanged;
  const _BusinessActivitySelector({
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<BusinessActivityType>(
      isExpanded: true,
      value: value,
      items:
          BusinessActivityType.values.map((type) {
            return DropdownMenuItem<BusinessActivityType>(
              value: type,
              child: Text(type.label),
            );
          }).toList(),
      onChanged: (v) => v != null ? onChanged(v) : null,
    );
  }
}

class _TaxSystemSelector extends StatelessWidget {
  final TaxSystem value;
  final ValueChanged<TaxSystem> onChanged;
  const _TaxSystemSelector({required this.value, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return DropdownButton<TaxSystem>(
      isExpanded: true,
      value: value,
      items:
          TaxSystem.values.map((system) {
            return DropdownMenuItem<TaxSystem>(
              value: system,
              child: Text(system.label),
            );
          }).toList(),
      onChanged: (v) => v != null ? onChanged(v) : null,
    );
  }
}
