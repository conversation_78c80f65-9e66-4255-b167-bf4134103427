import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/domain/repositories/tasks_repository.dart';

class GetTaskUseCase {
  final TasksRepository repository;

  GetTaskUseCase(this.repository);

  Future<Either<Failure, TaskEntity>> call(String firmId, String taskId) async {
    return await repository.getTask(firmId, taskId);
  }
}
