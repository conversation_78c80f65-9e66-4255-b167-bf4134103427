
```python
import json
import ydb
from custom_errors import <PERSON><PERSON>rror, NotFoundError

def archive_notice(session, table_name, notice_id):
    if not notice_id:
        raise LogicError("notice_id is required for ARCHIVE action.")
    
    tx = session.transaction(ydb.SerializableReadWrite())

    check_query = session.prepare(f"DECLARE $id AS Utf8; SELECT 1 FROM `{table_name}` WHERE notice_id = $id;")
    print(f"Executing YQL existence check for archive on notice_id: {notice_id}")
    check_res = tx.execute(check_query, {"$id": notice_id})
    if not check_res[0].rows:
        raise NotFoundError(f"Notice with id {notice_id} not found.")

    update_query = session.prepare(f"UPDATE `{table_name}` SET is_archived = true WHERE notice_id = $id;")
    print("Executing YQL update to set is_archived=true")
    tx.execute(update_query, {"$id": notice_id})
    
    tx.commit()
    print(f"Successfully archived notice_id: {notice_id} in table {table_name}")
    
    return {"statusCode": 200, "headers": {"Content-Type": "application/json; charset=utf-8"}, "body": json.dumps({"message": "Notice archived successfully."}, ensure_ascii=False)}
```