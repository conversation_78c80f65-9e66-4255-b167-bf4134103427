
Идентификатор - d4eqfkchno7rtj8rlhak
Описание - 🔗 Управление JSON-интеграциями компании: получить (`GET`), добавить/обновить (`UPSERT`), удалить (`DELETE`).
Точка входа - index.handler
Таймаут - 10 сек

---
### Конвейер работы
На входе:
	-> `Authorization: Bearer <jwt_token>`: JWT любого авторизованного пользователя.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).
	-> `firm_id` (string, **обязательно**): ID фирмы, с которой работаем.
	-> `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
	-> `payload` (object, опц.) — JSON-объект с новыми/обновлёнными интеграциями для `UPSERT`.
	-> `integration_keys` (array<string>, опц.) — список ключей, которые нужно удалить при `DELETE`.

Внутренняя работа:
	-> Установка логирования: logging.basicConfig(level=logging.INFO)
	-> Авторизация:
		-> Получение headers из event.
		-> Поиск auth_header в 'x-forwarded-authorization' или 'authorization'.
		-> Если не начинается с 'Bearer ', raise AuthError("Unauthorized")
		-> Извлечение token, auth_utils.verify_jwt(token), получение user_id. Если не, raise AuthError("Invalid token")
	-> Парсинг тела запроса: request_parser.parse_request_body(event)
		-> Получение firm_id и action. Если не все, raise LogicError("firm_id and action are required")
	-> Подключение к YDB: ydb_utils.get_driver_for_db(os.environ['YDB_ENDPOINT_FIRMS'], os.environ['YDB_DATABASE_FIRMS']), создание ydb.SessionPool(driver)
	-> В транзакции (pool.retry_operation_sync(txn)):
		-> Проверка членства и роли: _check_membership_and_role(session, user_id, firm_id)
			-> Подготовка и выполнение запроса: SELECT roles FROM Users WHERE user_id = $uid AND firm_id = $fid
			-> Если нет rows, return (False, False)
			-> Парсинг roles из json.loads(roles or '[]'), проверка наличия 'OWNER' или 'ADMIN'
			-> Если не член, raise AuthError("User is not a member of the specified firm")
		-> Маршрутизация по action:
			-> Если 'GET':
				-> _get_integrations(session, firm_id):
					-> Подготовка и выполнение: SELECT integrations_json FROM Firms WHERE firm_id = $fid
					-> Если нет rows, raise NotFoundError("Firm not found")
					-> return json.loads(integrations_json or '{}')
				-> return {"statusCode": 200, "body": json.dumps({"integrations": integrations})}
			-> Если 'UPSERT':
				-> Если не is_admin_or_owner, raise AuthError("Admin or Owner rights required for UPSERT")
				-> Получение payload = data.get('payload'), если не isinstance(payload, dict), raise LogicError("payload must be an object for UPSERT")
				-> _upsert_integrations(session, firm_id, payload):
					-> current = _get_integrations(session, firm_id)
					-> _deep_merge_dict(current, payload)  # Рекурсивный мердж словарей
					-> new_json = json.dumps(current)
					-> now = datetime.datetime.now(pytz.utc)
					-> Подготовка и выполнение: UPDATE Firms SET integrations_json = $data, updated_at = $now WHERE firm_id = $fid
				-> return {"statusCode": 200, "body": json.dumps({"message": "Integrations updated"})}
			-> Если 'DELETE':
				-> Если не is_admin_or_owner, raise AuthError("Admin or Owner rights required for DELETE")
				-> Получение keys = data.get('integration_keys') or [], если не isinstance(keys, list), raise LogicError("integration_keys must be a list for DELETE")
				-> _delete_integrations(session, firm_id, keys):
					-> current = _get_integrations(session, firm_id)
					-> for k in keys: current.pop(k, None)
					-> new_json = json.dumps(current)
					-> now = datetime.datetime.now(pytz.utc)
					-> Подготовка и выполнение: UPDATE Firms SET integrations_json = $data, updated_at = $now WHERE firm_id = $fid
				-> return {"statusCode": 200, "body": json.dumps({"message": "Integrations deleted"})}
			-> Иначе: raise LogicError("Invalid action")
	-> Обработка исключений:
		-> AuthError as e: return {"statusCode": 401 if 'Unauthorized' in str(e) else 403, "body": json.dumps({"message": str(e)})}
		-> LogicError as e: return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
		-> NotFoundError as e: return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
		-> Exception as e: logging.error(f"Critical error in edit-integrations: {e}", exc_info=True), return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}

На выходе:
	-> `200 OK` (GET): `{ "integrations": { ... } }`
	-> `200 OK` (UPSERT): `{ "message": "Integrations updated" }`
	-> `200 OK` (DELETE): `{ "message": "Integrations deleted" }`
	-> `400 Bad Request`: Неверные параметры.
	-> `401 Unauthorized`: Невалидный/отсутствует JWT.
	-> `403 Forbidden`: Недостаточно прав.
	-> `404 Not Found`: Фирма не найдена.
	-> `500 Internal Server Error`: Необработанная ошибка сервера.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`

---
#### Код функции (`index.py`)
```python
import json, os, logging, datetime, pytz
import ydb
from utils import auth_utils, ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

# ────────────────────────── HELPERS ──────────────────────────

# Новый helper для глубокого мерджа словарей
def _deep_merge_dict(dst: dict, src: dict):
    """
    Рекурсивно мерджит словарь `src` в `dst`.
    Если по ключу находятся два словаря — выполняем глубокий мердж,
    иначе значение из `src` перезаписывает значение в `dst`.
    """
    for k, v in src.items():
        if isinstance(v, dict) and isinstance(dst.get(k), dict):
            _deep_merge_dict(dst[k], v)
        else:
            dst[k] = v


def _check_membership_and_role(session, user_id, firm_id):
    query = session.prepare("DECLARE $uid AS Utf8; DECLARE $fid AS Utf8; SELECT roles FROM Users WHERE user_id = $uid AND firm_id = $fid;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id, "$fid": firm_id}, commit_tx=True)
    if not res[0].rows:
        return (False, False)
    roles = json.loads(res[0].rows[0].roles or '[]')
    is_admin_or_owner = "OWNER" in roles or "ADMIN" in roles
    return (True, is_admin_or_owner)


def _get_integrations(session, firm_id):
    q = session.prepare("DECLARE $fid AS Utf8; SELECT integrations_json FROM Firms WHERE firm_id = $fid;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(q, {"$fid": firm_id}, commit_tx=True)
    if not res[0].rows:
        raise NotFoundError("Firm not found")
    return json.loads(res[0].rows[0].integrations_json or '{}')


def _upsert_integrations(session, firm_id, new_data: dict):
    current = _get_integrations(session, firm_id)
    _deep_merge_dict(current, new_data)  # глубокий мердж вместо поверхностного update
    new_json = json.dumps(current)
    now = datetime.datetime.now(pytz.utc)
    q = session.prepare("""
        DECLARE $fid AS Utf8; DECLARE $data AS Json; DECLARE $now AS Timestamp;
        UPDATE Firms SET integrations_json = $data, updated_at = $now WHERE firm_id = $fid;
    """)
    session.transaction(ydb.SerializableReadWrite()).execute(q, {"$fid": firm_id, "$data": new_json, "$now": now}, commit_tx=True)


def _delete_integrations(session, firm_id, keys_to_delete):
    current = _get_integrations(session, firm_id)
    for k in keys_to_delete:
        current.pop(k, None)
    new_json = json.dumps(current)
    now = datetime.datetime.now(pytz.utc)
    q = session.prepare("""
        DECLARE $fid AS Utf8; DECLARE $data AS Json; DECLARE $now AS Timestamp;
        UPDATE Firms SET integrations_json = $data, updated_at = $now WHERE firm_id = $fid;
    """)
    session.transaction(ydb.SerializableReadWrite()).execute(q, {"$fid": firm_id, "$data": new_json, "$now": now}, commit_tx=True)

# ────────────────────────── HANDLER ──────────────────────────

def handler(event, context):
    try:
        # 1. Авторизация
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid token")
        user_id = user_payload['user_id']

        # 2. Парсинг тела
        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        if not all([firm_id, action]):
            raise LogicError("firm_id and action are required")

        # 3. Подключаемся к БД фирм
        driver = ydb_utils.get_driver_for_db(os.environ['YDB_ENDPOINT_FIRMS'], os.environ['YDB_DATABASE_FIRMS'])
        pool = ydb.SessionPool(driver)

        def txn(session):
            is_member, is_admin_or_owner = _check_membership_and_role(session, user_id, firm_id)
            if not is_member:
                raise AuthError("User is not a member of the specified firm")

            if action == 'GET':
                integrations = _get_integrations(session, firm_id)
                return {"statusCode": 200, "body": json.dumps({"integrations": integrations})}

            elif action == 'UPSERT':
                if not is_admin_or_owner:
                    raise AuthError("Admin or Owner rights required for UPSERT")
                payload = data.get('payload')
                if not isinstance(payload, dict):
                    raise LogicError("payload must be an object for UPSERT")
                _upsert_integrations(session, firm_id, payload)
                return {"statusCode": 200, "body": json.dumps({"message": "Integrations updated"})}

            elif action == 'DELETE':
                if not is_admin_or_owner:
                    raise AuthError("Admin or Owner rights required for DELETE")
                keys = data.get('integration_keys') or []
                if not isinstance(keys, list):
                    raise LogicError("integration_keys must be a list for DELETE")
                _delete_integrations(session, firm_id, keys)
                return {"statusCode": 200, "body": json.dumps({"message": "Integrations deleted"})}

            else:
                raise LogicError("Invalid action")

        return pool.retry_operation_sync(txn)

    except AuthError as e:
        return {"statusCode": 401 if 'Unauthorized' in str(e) else 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical error in edit-integrations: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```