import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// Универсальный виджет для "умного" ввода дат
/// Поддерживает различные форматы ввода и автоматическое форматирование
class SmartDateInputField extends StatefulWidget {
  /// Контроллер для текстового поля
  final TextEditingController? controller;

  /// FocusNode для управления фокусом
  final FocusNode? focusNode;

  /// Текст подсказки (label)
  final String? labelText;

  /// Текст placeholder
  final String? hintText;

  /// Текст помощи
  final String? helperText;

  /// Иконка префикса
  final IconData? prefixIcon;

  /// Иконка суффикса (по умолчанию календарь)
  final IconData? suffixIcon;

  /// Начальное значение даты
  final DateTime? initialDate;

  /// Формат отображения даты (по умолчанию dd.MM.yyyy)
  final String dateFormat;

  /// Функция валидации
  final String? Function(String?)? validator;

  /// Функция, вызываемая при изменении даты
  final void Function(DateTime?)? onDateChanged;

  /// Функция, вызываемая при изменении текста
  final void Function(String)? onTextChanged;

  /// Функция, вызываемая при клике на иконку календаря
  final Future<DateTime?> Function()? onCalendarTap;

  /// Функция, вызываемая при клике на поле (в режиме только чтения)
  final void Function()? onTap;

  /// Функция, вызываемая при отправке формы (Enter)
  final void Function(String)? onFieldSubmitted;

  /// Функция, вызываемая при изменении фокуса
  final void Function(bool)? onFocusChanged;

  /// Функция для кастомного парсинга даты
  final DateTime? Function(String)? customDateParser;

  /// Функция для кастомного форматирования даты
  final String Function(DateTime)? customDateFormatter;

  /// Является ли поле только для чтения
  final bool readOnly;

  /// Является ли поле обязательным
  final bool required;

  /// Включен ли режим редактирования
  final bool enabled;

  /// Стиль декорации поля
  final InputDecoration? decoration;

  /// Стиль текста
  final TextStyle? style;

  /// Тип клавиатуры
  final TextInputType? keyboardType;

  /// Действие клавиатуры
  final TextInputAction? textInputAction;

  /// Список форматеров ввода
  final List<TextInputFormatter>? inputFormatters;

  /// Максимальная длина текста
  final int? maxLength;

  /// Минимальная дата для выбора в календаре
  final DateTime? firstDate;

  /// Максимальная дата для выбора в календаре
  final DateTime? lastDate;

  /// Автофокус
  final bool autofocus;

  /// Показывать ли курсор
  final bool? showCursor;

  /// Авто валидация
  final AutovalidateMode? autovalidateMode;

  /// Функция копирования в буфер обмена (для режима чтения)
  final void Function(String)? onCopyToClipboard;

  /// Название поля для сообщений (например, при копировании)
  final String? fieldName;

  /// Отключить автоматическое форматирование при потере фокуса / Enter
  final bool disableAutoFormatting;

  const SmartDateInputField({
    super.key,
    this.controller,
    this.focusNode,
    this.labelText,
    this.hintText,
    this.helperText,
    this.prefixIcon,
    this.suffixIcon,
    this.initialDate,
    this.dateFormat = 'dd.MM.yyyy',
    this.validator,
    this.onDateChanged,
    this.onTextChanged,
    this.onCalendarTap,
    this.onTap,
    this.onFieldSubmitted,
    this.onFocusChanged,
    this.customDateParser,
    this.customDateFormatter,
    this.readOnly = false,
    this.required = false,
    this.enabled = true,
    this.decoration,
    this.style,
    this.keyboardType,
    this.textInputAction,
    this.inputFormatters,
    this.maxLength,
    this.firstDate,
    this.lastDate,
    this.autofocus = false,
    this.showCursor,
    this.autovalidateMode,
    this.onCopyToClipboard,
    this.fieldName,
    this.disableAutoFormatting = false,
  });

  @override
  State<SmartDateInputField> createState() => _SmartDateInputFieldState();
}

class _SmartDateInputFieldState extends State<SmartDateInputField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _shouldDispose = false;

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _setupFocusListener();
    _initializeValue();
  }

  @override
  void didUpdateWidget(SmartDateInputField oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Обновляем контроллеры если они изменились
    if (widget.controller != oldWidget.controller) {
      _setupControllers();
      _setupFocusListener();
      _initializeValue();
    }
  }

  @override
  void dispose() {
    if (_shouldDispose) {
      _controller.dispose();
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _setupControllers() {
    if (widget.controller != null) {
      _controller = widget.controller!;
    } else {
      _controller = TextEditingController();
      _shouldDispose = true;
    }

    if (widget.focusNode != null) {
      _focusNode = widget.focusNode!;
    } else {
      _focusNode = FocusNode();
      _shouldDispose = true;
    }
  }

  void _setupFocusListener() {
    _focusNode.addListener(_handleFocusChange);
  }

  void _initializeValue() {
    if (widget.initialDate != null && _controller.text.isEmpty) {
      final formatter =
          widget.customDateFormatter ??
          (date) => DateFormat(widget.dateFormat).format(date);
      _controller.text = formatter(widget.initialDate!);
    }
  }

  void _handleFocusChange() {
    final hasFocus = _focusNode.hasFocus;

    // Уведомляем о изменении фокуса
    widget.onFocusChanged?.call(hasFocus);

    // Если потеряли фокус и автоформатирование не отключено
    if (!hasFocus && !widget.disableAutoFormatting) {
      _parseAndFormatDate();
    }
  }

  void _parseAndFormatDate() {
    final text = _controller.text.trim();
    if (text.isEmpty) {
      widget.onDateChanged?.call(null);
      return;
    }

    DateTime? parsedDate;

    // Используем кастомный парсер, если предоставлен
    if (widget.customDateParser != null) {
      parsedDate = widget.customDateParser!(text);
    } else {
      parsedDate = _tryParseDate(text);
    }

    if (parsedDate != null) {
      // Форматируем дату
      String formattedText;
      if (widget.customDateFormatter != null) {
        formattedText = widget.customDateFormatter!(parsedDate);
      } else {
        formattedText = DateFormat(widget.dateFormat).format(parsedDate);
      }

      // Обновляем текст только если он изменился
      if (_controller.text != formattedText) {
        _controller.text = formattedText;
      }

      // Уведомляем о изменении даты
      widget.onDateChanged?.call(parsedDate);
    }
  }

  DateTime? _tryParseDate(String input) {
    input = input.trim();

    // Обработка чисел без разделителей (ддммгггг, ддммгг)
    if (RegExp(r'^\d{8}$').hasMatch(input)) {
      final d = int.parse(input.substring(0, 2));
      final m = int.parse(input.substring(2, 4));
      final y = int.parse(input.substring(4, 8));
      if (_isValidDayMonth(d, m)) return DateTime(y, m, d);
    }

    if (RegExp(r'^\d{6}$').hasMatch(input)) {
      final d = int.parse(input.substring(0, 2));
      final m = int.parse(input.substring(2, 4));
      final yy = int.parse(input.substring(4, 6));
      final y = yy < 50 ? 2000 + yy : 1900 + yy;
      if (_isValidDayMonth(d, m)) return DateTime(y, m, d);
    }

    // Стандартные форматы
    const formats = [
      'dd.MM.yyyy',
      'dd/MM/yyyy',
      'dd-MM-yyyy',
      'dd.MM.yy',
      'dd/MM/yy',
      'dd-MM-yy',
      'dd.MM',
      'dd/MM',
      'dd-MM',
    ];

    for (final format in formats) {
      try {
        DateTime dt = DateFormat(format).parseStrict(input);

        // Если год не указан, используем текущий
        if (format.length <= 5) {
          dt = DateTime(DateTime.now().year, dt.month, dt.day);
        }
        // Обрабатываем двузначные года
        else if (format.contains('yy') && !format.contains('yyyy')) {
          if (dt.year < 50) {
            dt = DateTime(dt.year + 2000, dt.month, dt.day);
          } else if (dt.year < 100) {
            dt = DateTime(dt.year + 1900, dt.month, dt.day);
          }
        }

        return dt;
      } catch (_) {}
    }

    // Извлечение чисел из произвольного текста
    final numbers =
        RegExp(
          r'\d+',
        ).allMatches(input).map((m) => int.parse(m.group(0)!)).toList();

    if (numbers.length >= 2) {
      final day = numbers[0];
      final month = numbers[1];
      final year = numbers.length >= 3 ? numbers[2] : DateTime.now().year;

      int finalYear = year;
      if (year < 100) {
        finalYear = year < 50 ? 2000 + year : 1900 + year;
      }

      if (_isValidDayMonth(day, month)) {
        return DateTime(finalYear, month, day);
      }
    }

    return null;
  }

  bool _isValidDayMonth(int day, int month) =>
      day >= 1 && day <= 31 && month >= 1 && month <= 12;

  void _handleCalendarTap() async {
    DateTime? selectedDate;

    if (widget.onCalendarTap != null) {
      selectedDate = await widget.onCalendarTap!();
    } else {
      // Дефолтная реализация showDatePicker
      final currentDate = _tryParseDate(_controller.text) ?? DateTime.now();
      selectedDate = await showDatePicker(
        context: context,
        initialDate: currentDate,
        firstDate: widget.firstDate ?? DateTime(1900),
        lastDate: widget.lastDate ?? DateTime(2100),
      );
    }

    if (selectedDate != null) {
      String formattedText;
      if (widget.customDateFormatter != null) {
        formattedText = widget.customDateFormatter!(selectedDate);
      } else {
        formattedText = DateFormat(widget.dateFormat).format(selectedDate);
      }

      _controller.text = formattedText;
      widget.onDateChanged?.call(selectedDate);
    }
  }

  void _handleTap() {
    if (widget.readOnly && widget.onCopyToClipboard != null) {
      widget.onCopyToClipboard!(_controller.text);

      // Показываем снэкбар о копировании
      if (widget.fieldName != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${widget.fieldName} скопировано в буфер обмена'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    }
    widget.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    final decoration =
        widget.decoration ??
        InputDecoration(
          labelText: widget.labelText,
          hintText: widget.hintText,
          helperText: widget.helperText,
          prefixIcon:
              widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
          suffixIcon: GestureDetector(
            onTap:
                widget.enabled && !widget.readOnly ? _handleCalendarTap : null,
            child: Icon(
              widget.suffixIcon ??
                  (widget.readOnly ? Icons.copy : Icons.calendar_today),
            ),
          ),
          border: const OutlineInputBorder(),
        );

    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      decoration: decoration,
      style: widget.style,
      keyboardType: widget.keyboardType ?? TextInputType.text,
      textInputAction: widget.textInputAction,
      inputFormatters: widget.inputFormatters,
      maxLength: widget.maxLength,
      readOnly: widget.readOnly,
      enabled: widget.enabled,
      autofocus: widget.autofocus,
      showCursor: widget.showCursor,
      autovalidateMode: widget.autovalidateMode,
      validator: widget.validator,
      onTap: _handleTap,
      onChanged: (value) {
        widget.onTextChanged?.call(value);
      },
      onFieldSubmitted: (value) {
        if (!widget.disableAutoFormatting) {
          _parseAndFormatDate();
        }
        widget.onFieldSubmitted?.call(value);
      },
    );
  }
}
