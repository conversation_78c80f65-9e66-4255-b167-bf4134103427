import requests
import random
import json

# --- Ваши данные ---
# ВАЖНО: Никогда не храните API ключ в коде в реальных проектах.
# Используйте переменные окружения или другие безопасные хранилища.
API_KEY = '6t6s6yzpk4s1knaaj8gem7hkmazc5h4m67b1bcmy'
SENDER_EMAIL = '<EMAIL>'
SENDER_NAME = 'Slava Morozov Development'
RECIPIENT_EMAIL = '<EMAIL>'

# ID списка контактов в Unisender. Обязательный параметр. [5]
# Его можно получить через API методом getLists или посмотреть в интерфейсе Unisender.
# Если контакт будет новым, он автоматически добавится в этот список.
LIST_ID = 1 # Замените на ID вашего списка

# --- Генерация кода и HTML-тела письма ---
confirmation_code = random.randint(100000, 999999)
email_subject = f'Код подтверждения: {confirmation_code}'

html_body = f"""
<html>
<head>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f4f4f4; }}
        .container {{ background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); text-align: center; }}
        .code {{ font-size: 24px; font-weight: bold; color: #333; letter-spacing: 5px; margin: 20px 0; padding: 15px; background-color: #eef1f3; border-radius: 5px; }}
        p {{ color: #555; }}
    </style>
</head>
<body>
    <div class="container">
        <h2>Подтверждение аккаунта</h2>
        <p>Здравствуйте!</p>
        <p>Для завершения регистрации, пожалуйста, используйте этот проверочный код:</p>
        <div class="code">{confirmation_code}</div>
        <p>Если вы не запрашивали этот код, просто проигнорируйте это письмо.</p>
    </div>
</body>
</html>
"""

# --- Параметры для запроса к API ---
api_url = 'https://api.unisender.com/ru/api/sendEmail'

params = {
    'api_key': API_KEY,
    'format': 'json',
    'email': RECIPIENT_EMAIL,
    'sender_name': SENDER_NAME,
    'sender_email': SENDER_EMAIL,
    'subject': email_subject,
    'body': html_body,
    'list_id': LIST_ID,
}

# --- Отправка запроса и вывод результата ---
print("--- Отправка запроса на Unisender API ---")
print(f"URL: {api_url}")
print(f"Параметры (кроме body): {{k: v for k, v in params.items() if k != 'body'}}")

try:
    # Рекомендуется передавать API-ключ и другие данные в теле POST-запроса для безопасности [7]
    response = requests.post(api_url, data=params)
    response.raise_for_status()  # Проверка на HTTP ошибки (4xx или 5xx)

    print("\n--- Сырой ответ от сервера (статус код) ---")
    print(response.status_code)

    print("\n--- Сырой ответ от сервера (тело) ---")
    # Используем json.dumps для красивого вывода с отступами
    print(json.dumps(response.json(), indent=4, ensure_ascii=False))

except requests.exceptions.HTTPError as http_err:
    print(f"\n--- HTTP ошибка ---: {http_err}")
    print(f"Тело ответа: {response.text}")
except requests.exceptions.RequestException as req_err:
    print(f"\n--- Ошибка запроса ---: {req_err}")
except json.JSONDecodeError:
    print("\n--- Ошибка декодирования JSON ---")
    print(f"Не удалось прочитать ответ сервера как JSON. Ответ: {response.text}")