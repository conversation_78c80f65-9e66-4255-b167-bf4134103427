import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Универсальный виджет для числовых полей
/// Поддерживает различные типы числовых данных:
/// - Целые числа (ИНН, КПП, номера)
/// - Десятичные числа (суммы, проценты)
/// - Дни месяца (1-31)
/// - Числа с ограничением длины
class UniversalNumberField extends StatefulWidget {
  /// Контроллер для управления текстом
  final TextEditingController? controller;

  /// Начальное значение (используется если controller не задан)
  final String? initialValue;

  /// Текст метки поля
  final String labelText;

  /// Иконка префикса
  final IconData? prefixIcon;

  /// Тип числового поля
  final NumberFieldType fieldType;

  /// Максимальная длина ввода
  final int? maxLength;

  /// Минимальное значение (для валидации)
  final num? minValue;

  /// Максимальное значение (для валидации)
  final num? maxValue;

  /// Количество десятичных знаков (для decimal типа)
  final int? decimalPlaces;

  /// Обязательное ли поле
  final bool isRequired;

  /// Режим редактирования
  final bool isEditing;

  /// Только для чтения
  final bool readOnly;

  /// Функция обратного вызова при изменении
  final ValueChanged<String>? onChanged;

  /// Функция обратного вызова при нажатии (для копирования)
  final VoidCallback? onTap;

  /// Кастомная функция валидации
  final String? Function(String?)? validator;

  /// Узел фокуса
  final FocusNode? focusNode;

  /// Текст подсказки
  final String? helperText;

  /// Суффикс текста (например, "₽", "%")
  final String? suffixText;

  /// Максимальное количество строк
  final int? maxLines;

  const UniversalNumberField({
    super.key,
    this.controller,
    this.initialValue,
    required this.labelText,
    this.prefixIcon,
    this.fieldType = NumberFieldType.integer,
    this.maxLength,
    this.minValue,
    this.maxValue,
    this.decimalPlaces,
    this.isRequired = false,
    this.isEditing = true,
    this.readOnly = false,
    this.onChanged,
    this.onTap,
    this.validator,
    this.focusNode,
    this.helperText,
    this.suffixText,
    this.maxLines = 1,
  });

  @override
  State<UniversalNumberField> createState() => _UniversalNumberFieldState();
}

class _UniversalNumberFieldState extends State<UniversalNumberField> {
  late final TextEditingController _controller;
  late FocusNode _focusNode;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _controller =
        widget.controller ?? TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
    });

    if (_focusNode.hasFocus) {
      // Выделяем весь текст при каждом получении фокуса
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_focusNode.hasFocus && _controller.text.isNotEmpty) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
    }
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode.dispose();
    }
    // Если контроллер был создан внутри, его нужно утилизировать
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  /// Получает отображаемое значение (форматированное или исходное)
  String get _displayValue {
    if (_hasFocus || _controller.text.isEmpty) {
      return _controller.text;
    }
    return _formatDisplayValue(_controller.text);
  }

  /// Форматирует значение для отображения в расфокусированном состоянии
  String _formatDisplayValue(String value) {
    if (value.isEmpty) return value;

    // Только для числовых типов (integer и decimal)
    if (widget.fieldType != NumberFieldType.integer &&
        widget.fieldType != NumberFieldType.decimal) {
      return value;
    }

    // Парсим число
    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '');
    if (cleanValue.isEmpty) return value;

    // Заменяем запятую на точку для парсинга
    final normalizedValue = cleanValue.replaceAll(',', '.');
    final number = double.tryParse(normalizedValue);
    if (number == null) return value;

    // Разделяем на целую и дробную части
    final parts = normalizedValue.split('.');
    final integerPart = parts[0];
    final decimalPart = parts.length > 1 ? parts[1] : '';

    // Форматируем целую часть с пробелами
    String formattedInteger = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        formattedInteger += ' ';
      }
      formattedInteger += integerPart[i];
    }

    // Добавляем дробную часть если есть
    if (decimalPart.isNotEmpty) {
      return '$formattedInteger,$decimalPart';
    } else {
      return formattedInteger;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Основное поле ввода
        TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          decoration: InputDecoration(
            labelText:
                widget.isRequired ? '${widget.labelText} *' : widget.labelText,
            prefixIcon:
                widget.prefixIcon != null
                    ? Icon(widget.prefixIcon, size: 20)
                    : null,
            helperText: widget.helperText,
            suffixText: widget.suffixText,
          ),
          keyboardType: _getKeyboardType(),
          inputFormatters: _getInputFormatters(),
          maxLength: widget.maxLength,
          buildCounter: (
            context, {
            required currentLength,
            required isFocused,
            maxLength,
          }) {
            if (maxLength == null) return null;

            // Всегда используем длину исходного значения из контроллера
            final actualLength = _controller.text.length;

            return Text(
              '$actualLength/$maxLength',
              style: Theme.of(context).textTheme.bodySmall,
              semanticsLabel: 'счетчик символов',
            );
          },
          maxLines: widget.maxLines,
          readOnly: !widget.isEditing || widget.readOnly,
          onTap: (!widget.isEditing || widget.readOnly) ? widget.onTap : null,
          onChanged:
              widget.isEditing && !widget.readOnly
                  ? (value) {
                    // Передаем исходное значение в callback
                    widget.onChanged?.call(value);
                    // Обновляем отображение
                    setState(() {});
                  }
                  : null,
          validator: widget.validator ?? _getDefaultValidator(),
          style: _hasFocus ? null : const TextStyle(color: Colors.transparent),
        ),
        // Отформатированное отображение (только когда поле не в фокусе)
        if (!_hasFocus && _controller.text.isNotEmpty)
          Positioned.fill(
            child: IgnorePointer(
              child: Container(
                padding: EdgeInsets.only(
                  left:
                      widget.prefixIcon != null
                          ? 48.0
                          : 12.0, // Учитываем иконку
                  top: 16.0,
                  right: 12.0,
                  bottom: 8.0,
                ),
                alignment: Alignment.centerLeft,
                child: Text(
                  _displayValue,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Определяет тип клавиатуры в зависимости от типа поля
  TextInputType _getKeyboardType() {
    if (!widget.isEditing) return TextInputType.none;

    switch (widget.fieldType) {
      case NumberFieldType.integer:
      case NumberFieldType.dayOfMonth:
        return TextInputType.number;
      case NumberFieldType.decimal:
        return const TextInputType.numberWithOptions(decimal: true);
    }
  }

  /// Определяет форматеры ввода в зависимости от типа поля
  List<TextInputFormatter> _getInputFormatters() {
    if (!widget.isEditing) return [];

    final formatters = <TextInputFormatter>[];

    switch (widget.fieldType) {
      case NumberFieldType.integer:
      case NumberFieldType.dayOfMonth:
        formatters.add(FilteringTextInputFormatter.digitsOnly);
        break;
      case NumberFieldType.decimal:
        formatters.add(
          FilteringTextInputFormatter.allow(RegExp(r'^\d*[.,]?\d*')),
        );
        break;
    }

    if (widget.maxLength != null) {
      formatters.add(LengthLimitingTextInputFormatter(widget.maxLength!));
    }

    return formatters;
  }

  /// Возвращает функцию валидации по умолчанию
  String? Function(String?)? _getDefaultValidator() {
    return (value) {
      // Всегда используем значение из контроллера для валидации
      final valueToValidate = value ?? _controller.text;

      // Проверка на обязательность
      if (widget.isRequired && (valueToValidate.isEmpty)) {
        return 'Введите ${widget.labelText}';
      }

      // Если поле пустое и не обязательное, валидация пройдена
      if (valueToValidate.isEmpty) {
        return null;
      }

      // Валидация в зависимости от типа поля
      switch (widget.fieldType) {
        case NumberFieldType.integer:
          return _validateInteger(valueToValidate);
        case NumberFieldType.decimal:
          return _validateDecimal(valueToValidate);
        case NumberFieldType.dayOfMonth:
          return _validateDayOfMonth(valueToValidate);
      }
    };
  }

  /// Валидация целых чисел
  String? _validateInteger(String value) {
    final number = int.tryParse(value);
    if (number == null) {
      return 'Введите корректное число';
    }

    if (widget.minValue != null && number < widget.minValue!) {
      return 'Минимальное значение: ${widget.minValue!.toInt()}';
    }

    if (widget.maxValue != null && number > widget.maxValue!) {
      return 'Максимальное значение: ${widget.maxValue!.toInt()}';
    }

    return null;
  }

  /// Валидация десятичных чисел
  String? _validateDecimal(String value) {
    // Заменяем запятую на точку для парсинга
    final normalizedValue = value.replaceAll(',', '.');
    final number = double.tryParse(normalizedValue);
    if (number == null) {
      return 'Введите корректное число';
    }

    if (widget.minValue != null && number < widget.minValue!) {
      return 'Минимальное значение: ${widget.minValue}';
    }

    if (widget.maxValue != null && number > widget.maxValue!) {
      return 'Максимальное значение: ${widget.maxValue}';
    }

    // Проверка количества десятичных знаков (используем запятую или точку)
    if (widget.decimalPlaces != null) {
      final parts = value.contains(',') ? value.split(',') : value.split('.');
      if (parts.length > 1 && parts[1].length > widget.decimalPlaces!) {
        return 'Максимум ${widget.decimalPlaces} знаков после запятой';
      }
    }

    return null;
  }

  /// Валидация дней месяца (1-31)
  String? _validateDayOfMonth(String value) {
    // Извлекаем число из текста (например, "25 число" -> 25)
    final match = RegExp(r'\d+').firstMatch(value);
    if (match == null) {
      return '1-31';
    }

    final day = int.tryParse(match.group(0)!);
    if (day == null || day < 1 || day > 31) {
      return '1-31';
    }

    return null;
  }
}

/// Типы числовых полей
enum NumberFieldType {
  /// Целые числа (ИНН, КПП, номера)
  integer,

  /// Десятичные числа (суммы, проценты)
  decimal,

  /// Дни месяца (1-31)
  dayOfMonth,
}
