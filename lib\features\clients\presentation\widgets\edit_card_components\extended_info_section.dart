import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';
import 'package:intl/intl.dart';
import 'custom_text_field.dart';
import 'kpp_info_section.dart';

class ExtendedInfoSection extends StatefulWidget {
  final bool isEditing;
  final String? ownershipForm;
  final List<FnsInfo> fnsInfo;
  final List<KppInfo> kppInfo;
  final SfrInfo? sfrInfo;
  final String? okpo;
  final String? ogrn;
  final String? legalAddress;
  final String? actualAddress;
  final bool actualAddressSameAsLegal;
  final Function(List<FnsInfo>) onFnsInfoChanged;
  final Function(List<KppInfo>) onKppInfoChanged;
  final Function(SfrInfo?) onSfrInfoChanged;
  final Function(String?) onOkpoChanged;
  final Function(String?) onOgrnChanged;
  final Function(String?) onLegalAddressChanged;
  final Function(String?) onActualAddressChanged;
  final Function(bool) onActualAddressSameAsLegalChanged;
  final Function(String, String) copyToClipboard;

  const ExtendedInfoSection({
    super.key,
    required this.isEditing,
    this.ownershipForm,
    required this.fnsInfo,
    required this.kppInfo,
    required this.sfrInfo,
    required this.okpo,
    required this.ogrn,
    required this.legalAddress,
    required this.actualAddress,
    required this.actualAddressSameAsLegal,
    required this.onFnsInfoChanged,
    required this.onKppInfoChanged,
    required this.onSfrInfoChanged,
    required this.onOkpoChanged,
    required this.onOgrnChanged,
    required this.onLegalAddressChanged,
    required this.onActualAddressChanged,
    required this.onActualAddressSameAsLegalChanged,
    required this.copyToClipboard,
  });

  @override
  State<ExtendedInfoSection> createState() => _ExtendedInfoSectionState();
}

class _ExtendedInfoSectionState extends State<ExtendedInfoSection> {
  String? _previousOwnershipForm;

  @override
  void initState() {
    super.initState();
    _previousOwnershipForm = widget.ownershipForm;
  }

  @override
  void didUpdateWidget(ExtendedInfoSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Если изменился тип собственности, очищаем поле ОГРН/ОГРНИП
    if (oldWidget.ownershipForm != widget.ownershipForm) {
      _previousOwnershipForm = widget.ownershipForm;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onOgrnChanged('');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final isOoo = widget.ownershipForm == 'ООО' || widget.ownershipForm == 'АО';
    final isIp = widget.ownershipForm == 'ИП';
    final isKfh = widget.ownershipForm == 'КФХ' || widget.ownershipForm == 'КХ';

    final ogrnLabel = isOoo ? 'ОГРН' : 'ОГРНИП';
    final isOgrnEnabled = isOoo || isIp || isKfh;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.business_center_outlined,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            const Text(
              'Расширенная информация',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.smallSpacing),

        // ОКПО и ОГРН
        Row(
          children: [
            Expanded(
              child: UniversalNumberField(
                initialValue: widget.okpo,
                labelText: 'ОКПО',
                prefixIcon: Icons.numbers_outlined,
                fieldType: NumberFieldType.integer,
                isEditing: widget.isEditing,
                onChanged: widget.onOkpoChanged,
                onTap: () => widget.copyToClipboard(widget.okpo ?? '', 'ОКПО'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: UniversalNumberField(
                initialValue:
                    _previousOwnershipForm != widget.ownershipForm
                        ? ''
                        : widget.ogrn,
                labelText: ogrnLabel,
                prefixIcon: Icons.badge_outlined,
                fieldType: NumberFieldType.integer,
                readOnly: !isOgrnEnabled,
                isRequired: isOgrnEnabled,
                isEditing: widget.isEditing,
                onChanged: widget.onOgrnChanged,
                onTap:
                    () => widget.copyToClipboard(widget.ogrn ?? '', ogrnLabel),
              ),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),

        // Юридический адрес
        CustomTextField(
          initialValue: widget.legalAddress,
          labelText: 'Юридический адрес',
          prefixIcon: const Icon(Icons.location_on_outlined, size: 20),
          maxLines: 2,
          isEditing: widget.isEditing,
          onChanged: widget.onLegalAddressChanged,
          onTap:
              () => widget.copyToClipboard(
                widget.legalAddress ?? '',
                'Юридический адрес',
              ),
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),

        // Чекбокс "Фактический адрес совпадает с юридическим"
        if (widget.isEditing)
          CheckboxListTile(
            title: const Text('Фактический адрес совпадает с юридическим'),
            value: widget.actualAddressSameAsLegal,
            onChanged:
                (value) =>
                    widget.onActualAddressSameAsLegalChanged(value ?? false),
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          )
        else
          GestureDetector(
            onTap:
                () => widget.copyToClipboard(
                  widget.actualAddressSameAsLegal ? 'Да' : 'Нет',
                  'Фактический адрес совпадает с юридическим',
                ),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Icon(
                    widget.actualAddressSameAsLegal
                        ? Icons.check_box
                        : Icons.check_box_outline_blank,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  const Text('Фактический адрес совпадает с юридическим'),
                  const Spacer(),
                  const Icon(Icons.copy, size: 16, color: Colors.grey),
                ],
              ),
            ),
          ),

        // Фактический адрес (если не совпадает с юридическим)
        if (!widget.actualAddressSameAsLegal) ...[
          const SizedBox(height: ClientConstants.fieldSpacing),
          CustomTextField(
            initialValue: widget.actualAddress,
            labelText: 'Фактический адрес',
            prefixIcon: const Icon(Icons.home_outlined, size: 20),
            maxLines: 2,
            isEditing: widget.isEditing,
            onChanged: widget.onActualAddressChanged,
            onTap:
                () => widget.copyToClipboard(
                  widget.actualAddress ?? '',
                  'Фактический адрес',
                ),
          ),
        ],

        const SizedBox(height: ClientConstants.fieldSpacing),

        // СФР информация
        _buildSfrSection(context),

        const SizedBox(height: ClientConstants.fieldSpacing),

        // ФНС информация
        _buildFnsSection(context),

        const SizedBox(height: ClientConstants.fieldSpacing),

        // КПП информация
        KppInfoSection(
          isEditing: widget.isEditing,
          kppInfo: widget.kppInfo,
          onKppInfoChanged: widget.onKppInfoChanged,
          copyToClipboard: widget.copyToClipboard,
        ),
      ],
    );
  }

  Widget _buildSfrSection(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.account_balance_outlined,
              size: 18,
              color: theme.colorScheme.secondary,
            ),
            const SizedBox(width: 8),
            const Text(
              'Информация о СФР',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: UniversalNumberField(
                initialValue: widget.sfrInfo?.number,
                labelText: 'Номер СФР (10 цифр)',
                prefixIcon: Icons.confirmation_number_outlined,
                fieldType: NumberFieldType.integer,
                maxLength: 10,
                isEditing: widget.isEditing,
                onChanged: (value) {
                  final newSfrInfo =
                      widget.sfrInfo?.copyWith(number: value) ??
                      SfrInfo(number: value, subordinationCode: '');
                  widget.onSfrInfoChanged(newSfrInfo);
                },
                onTap:
                    () => widget.copyToClipboard(
                      widget.sfrInfo?.number ?? '',
                      'Номер СФР',
                    ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: UniversalNumberField(
                initialValue: widget.sfrInfo?.subordinationCode,
                labelText: 'Код подчинённости (5 цифр)',
                prefixIcon: Icons.code_outlined,
                fieldType: NumberFieldType.integer,
                maxLength: 5,
                isEditing: widget.isEditing,
                onChanged: (value) {
                  final newSfrInfo =
                      widget.sfrInfo?.copyWith(subordinationCode: value) ??
                      SfrInfo(number: '', subordinationCode: value);
                  widget.onSfrInfoChanged(newSfrInfo);
                },
                onTap:
                    () => widget.copyToClipboard(
                      widget.sfrInfo?.subordinationCode ?? '',
                      'Код подчинённости',
                    ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFnsSection(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 18,
              color: theme.colorScheme.secondary,
            ),
            const SizedBox(width: 8),
            const Text(
              'Информация о ФНС',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            if (widget.isEditing) ...[
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.add, size: 20),
                onPressed: () {
                  final newFnsInfo = List<FnsInfo>.from(widget.fnsInfo)..add(
                    FnsInfo(
                      code: '',
                      name: '',
                      date: DateTime.now(),
                      oktmo: '',
                    ),
                  );
                  widget.onFnsInfoChanged(newFnsInfo);
                },
                tooltip: 'Добавить ФНС',
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        if (widget.fnsInfo.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                'Информация о ФНС не добавлена',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...widget.fnsInfo.asMap().entries.map((entry) {
            final index = entry.key;
            final fns = entry.value;
            return _buildFnsInfoCard(context, fns, index);
          }),
      ],
    );
  }

  Widget _buildFnsInfoCard(BuildContext context, FnsInfo fns, int index) {
    return _FnsInfoCard(
      fns: fns,
      index: index,
      isEditing: widget.isEditing,
      onFnsInfoChanged: widget.onFnsInfoChanged,
      fnsInfo: widget.fnsInfo,
      copyToClipboard: widget.copyToClipboard,
    );
  }
}

class _FnsInfoCard extends StatefulWidget {
  final FnsInfo fns;
  final int index;
  final bool isEditing;
  final Function(List<FnsInfo>) onFnsInfoChanged;
  final List<FnsInfo> fnsInfo;
  final Function(String, String) copyToClipboard;

  const _FnsInfoCard({
    required this.fns,
    required this.index,
    required this.isEditing,
    required this.onFnsInfoChanged,
    required this.fnsInfo,
    required this.copyToClipboard,
  });

  @override
  State<_FnsInfoCard> createState() => _FnsInfoCardState();
}

class _FnsInfoCardState extends State<_FnsInfoCard> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  'ФНС ${widget.index + 1}',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                if (widget.isEditing)
                  IconButton(
                    icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                    onPressed: () {
                      final newFnsInfo = List<FnsInfo>.from(widget.fnsInfo)
                        ..removeAt(widget.index);
                      widget.onFnsInfoChanged(newFnsInfo);
                    },
                    tooltip: 'Удалить',
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: UniversalNumberField(
                    initialValue: widget.fns.code,
                    labelText: 'Код (4 цифры)',
                    prefixIcon: Icons.tag_outlined,
                    fieldType: NumberFieldType.integer,
                    maxLength: 4,
                    isEditing: widget.isEditing,
                    onChanged: (value) {
                      final newFnsInfo = List<FnsInfo>.from(widget.fnsInfo);
                      newFnsInfo[widget.index] = widget.fns.copyWith(
                        code: value,
                      );
                      widget.onFnsInfoChanged(newFnsInfo);
                    },
                    onTap:
                        () =>
                            widget.copyToClipboard(widget.fns.code, 'Код ФНС'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: UniversalNumberField(
                    initialValue: widget.fns.oktmo,
                    labelText: 'ОКТМО (8 цифр)',
                    prefixIcon: Icons.location_city_outlined,
                    fieldType: NumberFieldType.integer,
                    maxLength: 8,
                    isEditing: widget.isEditing,
                    onChanged: (value) {
                      final newFnsInfo = List<FnsInfo>.from(widget.fnsInfo);
                      newFnsInfo[widget.index] = widget.fns.copyWith(
                        oktmo: value,
                      );
                      widget.onFnsInfoChanged(newFnsInfo);
                    },
                    onTap:
                        () => widget.copyToClipboard(widget.fns.oktmo, 'ОКТМО'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            CustomTextField(
              initialValue: widget.fns.name,
              labelText: 'Название',
              prefixIcon: const Icon(Icons.business_outlined, size: 20),
              isEditing: widget.isEditing,
              onChanged: (value) {
                final newFnsInfo = List<FnsInfo>.from(widget.fnsInfo);
                newFnsInfo[widget.index] = widget.fns.copyWith(name: value);
                widget.onFnsInfoChanged(newFnsInfo);
              },
              onTap:
                  () => widget.copyToClipboard(widget.fns.name, 'Название ФНС'),
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap:
                  widget.isEditing
                      ? () async {
                        final date = await SmartDatePickerDialog.show(
                          context: context,
                          initialDate: widget.fns.date,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                          helpText: 'Выберите дату',
                          allowClear: false,
                        );
                        if (date != null) {
                          final newFnsInfo = List<FnsInfo>.from(widget.fnsInfo);
                          newFnsInfo[widget.index] = widget.fns.copyWith(
                            date: date,
                          );
                          widget.onFnsInfoChanged(newFnsInfo);
                        }
                      }
                      : () => widget.copyToClipboard(
                        DateFormat('dd.MM.yyyy').format(widget.fns.date),
                        'Дата',
                      ),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 12,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 20,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        DateFormat('dd.MM.yyyy').format(widget.fns.date),
                      ),
                    ),
                    Icon(
                      widget.isEditing ? Icons.edit : Icons.copy,
                      size: 16,
                      color: Colors.grey,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
