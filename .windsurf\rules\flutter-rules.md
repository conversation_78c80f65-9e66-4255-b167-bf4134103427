---
trigger: always_on
---

Общее ->
1. Ты находишься в проекте FLUTTER для WEB. Твоя задача генерация Flutter кода.
2. Не запукай "flutter run" самостоятельно. Скорее всего процесс уже запущен. При желании пользователя дай ему команду для запуска - "flutter run -d chrome --web-port=8080", НЕ БОЛЕЕ.
3. Не запукай "flutter build" самостоятельно.
4. Проект разростается? Тебе НЕЛЬЗЯ УДАЛЯТЬ/МЕНЯТЬ/ЗАМЕНЯТЬ/ПЕРЕМЕЩАТЬ контент (например, набор ТЕГОВ в карточке параметров, или набор ПОЛЕЙ для карточки редактирования, и т.д.), без ЛИЧНОГО ПРЯМОГО одобрения юзера! Тебе нельзя УДАЛЯТЬ/МЕНЯТЬ/ЗАМЕНЯТЬ/ПЕРЕМЕЩАТЬ ТО что КАСАЕТСЯ ФОРМАТА ДАННЫХ/КОНТЕНТА.

---
Требования к коду ->
1. Код нашего приложения разбит на "lib/features/...". Каждая feature имеет структуру, эталонной "lib/features/_empty". А именно, data/data_source, data/models, data/repositories, domain/entities, domain/repositories, domain/usecases, presentation/cubit/имя_кубита, presentation/pages, presentation/widgets.
2. Слой domain должен использовать структуры "Future<Either<Failure, ...>>" для обработки ошибок и данных между data и presentation (использовать dartz).
3. Слой domain должен содержать Equatable классы в "entities".
4. Слой data должен наследовать классы domain. Но расширить их добавив, toEntity, fromEntity.
5. Хорошей парктикой будет, использовать цвет theme-of-context, нежели РУЧНЫЕ значения.
6. Хорошей парктикой будет, ВЫНЕСТИ БЗИНЕС ЛОГИКУ и СОСТОЯНИЯ в cubit-ы.

---
Разделение кода интрфейса ->
1. Код КАРТОЧКИ/СТРАНИЦЫ, всегда должен быть РАЗБИТ, но МНОГОРАЗОВЫЕ НЕЗАВИСИМЫЕ УНИВЕРСАЛЬНЫЕ виджеты, для этого feature.
2. Фактически dart файлы страниц/карточек/форм, будут лишь сборочным пунктом для разбитых на файлы виджетов.
3. Хорошей практикой будет расширение виджета, для повторного использования в рамках того-же feature.

---
Особенные виджеты / файлы ->
1. "lib/presentation/widgets/loading_tile.dart" - используется вместо самого виджета пока данные для него загружаются. например вместо имени юзер, пока данные о нём подгружаются с сервера.
2. "lib/core/constants/constants.dart" - используется как хранилище для ссылок, api ключей и других констант.
3. "lib/core/error/failure.dart" - используется для дропа ошибок из data -в-> domain + presentation.

---
База знаний по проекту ->
1. В "/pythonProject" находятся питон скрипты, которые ПОКАЗЫВАЮТ, КАК РАБОТАТЬ с АПИ нашего приложения. Тебе НЕЛЬЗЯ ИХ ТРОГАТЬ ИЛИ МЕНЯТЬ, можно только ЧИТАТЬ и ОПИРАТЬСЯ. Как на ПРИМЕРЫ.
2. В "/obsidian_balansoved" находится развернутая база знаний обсидиан, по YandexCloud проекту нашего приложения, а именно вся серверная составляющая. Она НЕ ПРЕДСТАВЛЯЕТ для тебя ИНТЕРЕСА. Её НЕЛЬЗЯ ТРОГАТЬ.