
```python
import json
import os
import logging
import ydb
from utils import ydb_utils, request_parser

from get import get_events
from upsert import upsert_event
from delete import delete_event
from custom_errors import LogicError, NotFoundError

# Настраиваем подробное логирование
logging.getLogger().setLevel(logging.DEBUG)

def _log_event_context(event, context):
    """Выводит в лог подробную информацию о входящем событии и контексте вызова."""
    try:
        logging.debug("RAW EVENT: %s", json.dumps(event, default=str)[:10000])
    except Exception as e:
        logging.debug("RAW EVENT (non-json serialisable): %s", event)
    if context is not None:
        logging.debug(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s",
            getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None),
        )

def handler(event, context):
    _log_event_context(event, context)
    try:
        # Аутентификация пользователя здесь не требуется, т.к. функция внутренняя
        data = request_parser.parse_request_body(event)
        action = data.get('action')
        
        if not action:
            raise LogicError("Action is a required parameter.")

        driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_SCHEDULER"],
            os.environ["YDB_DATABASE_SCHEDULER"]
        )
        pool = ydb.SessionPool(driver)
        table_name = "ScheduledEvents"

        def transaction_router(session):
            event_id = data.get('event_id')
            custom_identifier = data.get('custom_identifier')
            payload = data.get('payload', {})

            if action == "GET":
                return get_events(session, table_name, event_id, custom_identifier)
            
            elif action == "UPSERT":
                # Если клиент не передал request_headers_json, копируем Authorization из вызова
                if 'request_headers_json' not in payload:
                    logging.debug("'request_headers_json' not in payload. Checking event headers.")
                    hdrs = event.get('headers', {}) or {}
                    auth_val = hdrs.get('Authorization') or hdrs.get('authorization') or hdrs.get('X-Forwarded-Authorization') or hdrs.get('x-forwarded-authorization')
                    if auth_val:
                        logging.debug("Found 'Authorization' header. Copying to payload.")
                        payload['request_headers_json'] = {"Authorization": auth_val}
                    else:
                        logging.warning("'Authorization' header not found in event. 'request_headers_json' will be empty.")
                else:
                    logging.debug("'request_headers_json' already present in payload.")
                return upsert_event(session, table_name, payload, event_id)
            
            elif action == "DELETE":
                return delete_event(session, table_name, event_id)
            
            else:
                raise LogicError(f"Invalid action '{action}'.")

        return pool.retry_operation_sync(transaction_router)

    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing scheduled event request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```