**1. `hash_password(password: str) -> str`**
- **На входе**:
	-> `password`: Строка с паролем, который нужно хешировать.
- **Внутренняя работа**:
	1. Генерирует случайную "соль" с помощью `bcrypt.gensalt()`.
	2. Объединяет пароль и соль с помощью криптографического алгоритма `bcrypt`.
	3. Кодирует результат в строку формата UTF-8, готовую для сохранения в базе данных.
- **На выходе**:
	-> Строка, содержащая хеш пароля, включающий в себя соль.

**2. `verify_password(plain_password: str, hashed_password: str) -> bool`**
- **На входе**:
	-> `plain_password`: Текстовый пароль для проверки.
	-> `hashed_password`: Хеш из базы данных для сравнения.
- **Внутренняя работа**:
	1. Использует функцию `bcrypt.checkpw`, которая автоматически извлекает соль из `hashed_password`.
	2. Хеширует `plain_password` с использованием извлеченной соли.
	3. Сравнивает полученный хеш с `hashed_password` из базы данных.
- **На выходе**:
	-> `True`, если пароль совпадает с хешем, иначе `False`.

**3. `generate_jwt(user_id: str, email: str) -> str`**
- **На входе**:
	-> `user_id`: Уникальный идентификатор пользователя.
	-> `email`: Email пользователя.
- **Внутренняя работа**:
	1. Формирует `payload` (полезную нагрузку) токена в виде словаря, включая `user_id`, `email` и время создания (`iat`).
	2. Подписывает `payload` с помощью секретного ключа (`JWT_SECRET`) и алгоритма `HS256`. Поле `exp` (срок жизни) намеренно не указывается для создания "бессрочного" токена.
- **На выходе**:
	-> Строка с подписанным JWT-токеном.

**4. `verify_jwt(token: str) -> dict | None`**
- **На входе**:
	-> `token`: Строка с JWT-токеном для проверки.
- **Внутренняя работа**:
	1. Пытается декодировать токен с помощью `jwt.decode`, используя тот же секретный ключ и алгоритм.
	2. Использует опцию `options={"verify_exp": False}`, чтобы не проверять срок истечения токена.
	3. В случае ошибки подписи или формата токена перехватывает исключение `jwt.PyJWTError`.
- **На выходе**:
	-> Словарь (`payload`) с данными пользователя, если токен валиден.
	-> `None`, если токен невалиден.

---

```python
# auth_utils.py

import jwt
import bcrypt
import os
import datetime

JWT_SECRET = os.environ.get("JWT_SECRET", "your-super-secret-key-that-is-long-and-secure")
JWT_ALGORITHM = "HS256"

def hash_password(password: str) -> str:
    """Хеширует пароль с использованием bcrypt."""
    salt = bcrypt.gensalt()
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed_password.decode('utf-8')

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Проверяет соответствие пароля его хешу."""
    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))

def generate_jwt(user_id: str, email: str) -> str:
    """Генерирует "бесконечный" JWT токен (без срока истечения)."""
    payload = {
        "user_id": user_id,
        "email": email,
        "iat": datetime.datetime.now(datetime.timezone.utc)
    }
    # Мы не устанавливаем 'exp', чтобы токен был бессрочным
    token = jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)
    return token

def verify_jwt(token: str) -> dict | None:
    """Проверяет JWT токен и возвращает его payload в случае успеха."""
    try:
        # Для поддержки бессрочных токенов отключаем проверку 'exp'
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM], options={"verify_exp": False})
        return payload
    except jwt.PyJWTError:
        return None
```