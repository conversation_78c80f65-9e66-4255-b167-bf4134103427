import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import '../cubit/backup_integration_cubit.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

class BackupToggleWidget extends StatefulWidget {
  final String firmId;

  const BackupToggleWidget({super.key, required this.firmId});

  @override
  State<BackupToggleWidget> createState() => _BackupToggleWidgetState();
}

class _BackupToggleWidgetState extends State<BackupToggleWidget> {
  // Формат даты – храним в состоянии, т.к. Stateless -> Stateful
  final DateFormat _dateFormat = DateFormat.yMMMMd('ru').add_Hm();

  @override
  void initState() {
    super.initState();
    // Используем addPostFrameCallback, чтобы дождаться полной инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BackupIntegrationCubit>().loadStatus(widget.firmId);
    });
  }

  @override
  void didUpdateWidget(covariant BackupToggleWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.firmId != widget.firmId) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<BackupIntegrationCubit>().loadStatus(widget.firmId);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BackupIntegrationCubit, BackupIntegrationState>(
      builder: (context, state) {
        if (state is BackupIntegrationLoading ||
            state is BackupIntegrationInitial) {
          return const LoadingTile(height: 60, width: 250);
        }

        if (state is BackupIntegrationError) {
          return Row(
            children: [
              Icon(Icons.error, color: Theme.of(context).colorScheme.error),
              const SizedBox(width: 8),
              Flexible(child: Text(state.message)),
            ],
          );
        }

        if (state is BackupIntegrationLoaded) {
          final entity = state.entity;
          final enabled = entity.enabled;

          String subtitle;
          if (!enabled) {
            subtitle = 'Резервное копирование выключено';
          } else {
            final last = entity.lastSyncUtc;
            final next = entity.nextSyncUtc;
            final now = DateTime.now().toUtc();
            if (last != null && now.isBefore(next!)) {
              subtitle =
                  'Последнее копирование: ${_dateFormat.format(last.toLocal())}';
            } else if (next != null) {
              subtitle =
                  'Следующее копирование: ${_dateFormat.format(next.toLocal())}';
            } else {
              subtitle = 'Резервное копирование включено';
            }
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Резервное копирование на Я.Диск',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  Switch.adaptive(
                    value: enabled,
                    onChanged:
                        (val) async =>
                            _handleToggle(context, val, entity.token),
                  ),
                ],
              ),
            ],
          );
        }
        // fallback
        return const SizedBox.shrink();
      },
    );
  }

  Future<void> _handleToggle(
    BuildContext context,
    bool enable,
    String? currentToken,
  ) async {
    if (enable) {
      final GlobalKey<_EnableBackupDialogContentState> dialogKey = GlobalKey();
      final confirmed = await showDialog<bool>(
        context: context,
        builder:
            (ctx) => AlertDialog(
              title: const Text('Включение резервного копирования'),
              content: _EnableBackupDialogContent(
                key: dialogKey,
                initialToken: currentToken,
                onConfirm: (token) {
                  Navigator.of(ctx).pop(true); // Закрываем диалог с успехом
                  context
                      .read<BackupIntegrationCubit>()
                      .enableBackupAndSetToken(widget.firmId, token);
                },
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(ctx).pop(false),
                  child: const Text('Отмена'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Вызываем метод submit из дочернего виджета
                    dialogKey.currentState?.submit();
                  },
                  child: const Text('Включить'),
                ),
              ],
            ),
      );
    } else {
      // Логика для выключения
      final confirmed =
          await showDialog<bool>(
            context: context,
            builder:
                (ctx) => AlertDialog(
                  title: const Text('Подтверждение'),
                  content: const Text(
                    'Вы уверены, что хотите выключить резервное копирование?',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(ctx).pop(false),
                      child: const Text('Отмена'),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(ctx).pop(true),
                      child: const Text('Да'),
                    ),
                  ],
                ),
          ) ??
          false;

      if (confirmed) {
        context.read<BackupIntegrationCubit>().toggleBackup(
          widget.firmId,
          false,
        );
      }
    }
  }
}

class _EnableBackupDialogContent extends StatefulWidget {
  final ValueChanged<String> onConfirm;
  final String? initialToken;
  const _EnableBackupDialogContent({
    super.key,
    required this.onConfirm,
    this.initialToken,
  });

  @override
  State<_EnableBackupDialogContent> createState() =>
      _EnableBackupDialogContentState();
}

class _EnableBackupDialogContentState
    extends State<_EnableBackupDialogContent> {
  final _tokenController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _tokenController.text = widget.initialToken ?? '';
  }

  @override
  void dispose() {
    _tokenController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Чтобы включить резервное копирование, укажите OAuth-токен для Яндекс.Диска.',
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _tokenController,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            decoration: const InputDecoration(
              labelText: 'Токен Я.Диска',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Токен не может быть пустым';
              }
              return null;
            },
            inputFormatters: [FilteringTextInputFormatter.deny(RegExp(r'\s'))],
          ),
          const SizedBox(height: 6),
          InkWell(
            onTap:
                () => launchUrl(
                  Uri.parse('https://yandex.ru/dev/disk/poligon/'),
                  mode: LaunchMode.externalApplication,
                ),
            child: Text(
              'Где взять токен? (откроется в новой вкладке)',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                decoration: TextDecoration.underline,
                decorationColor: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void submit() {
    if (_formKey.currentState?.validate() ?? false) {
      widget.onConfirm(_tokenController.text.trim());
    }
  }
}
