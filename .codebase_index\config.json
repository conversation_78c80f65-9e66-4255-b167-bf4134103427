{"file_extensions": [".rb", ".py", ".swift", ".go", ".js", ".dart", ".h", ".c", ".kt", ".md", ".java", ".cpp", ".ts", ".php", ".rs"], "max_file_size_mb": 1000, "max_files_count": 999, "max_lines_per_file": 10000, "encoding": "utf-8", "ignore_patterns": ["*.tmp", "*.log", "__pycache__/*", ".git/*", "node_modules/*"], "max_results": 100, "context_lines": 2, "highlight_matches": true, "auto_update_index": true, "check_file_changes": true, "parallel_processing": true, "max_workers": 4, "whitelist": [".swift", ".kt", ".rb", ".h", ".py", ".go", ".md", ".php", ".c", ".cpp", ".ts", ".js", ".rs", ".dart", ".java"], "last_indexed_path": "c:\\FlutterProjects\\balansoved_enterprise\\lib\\features\\tasks", "last_index_time": 1754660630.4744298}