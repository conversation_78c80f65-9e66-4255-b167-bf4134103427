import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'price_calculator_state.dart';

/// Кубит для калькулятора нулевой отчетности
class ZeroReportingCubit extends Cubit<ZeroReportingState> {
  ZeroReportingCubit()
    : super(const ZeroReportingState(selectedType: ZeroReportingType.oooUsn)) {
    _loadCustomPrices();
  }

  static const _prefsKey = 'zero_reporting_custom_prices_v1';

  /// Пользовательские расценки
  Map<ZeroReportingType, int> _customPrices = {};

  /// Выбрать тип нулевой отчетности
  void selectType(ZeroReportingType type) {
    emit(state.copyWith(selectedType: type));
  }

  /// Получить цену за квартал (с учетом пользовательских настроек)
  int get quarterlyPrice => getCustomPrice(state.selectedType);

  /// Получить цену для конкретного типа (с учетом пользовательских настроек)
  int getCustomPrice(ZeroReportingType type) {
    return _customPrices[type] ?? type.price;
  }

  /// Обновить пользовательские расценки
  void updateCustomPrices(Map<ZeroReportingType, int> newPrices) {
    _customPrices = Map.from(newPrices);
    emit(state.copyWith(selectedType: state.selectedType)); // Перестроить UI
    unawaited(_saveCustomPrices());
  }

  /// Загрузить пользовательские расценки из SharedPreferences
  Future<void> _loadCustomPrices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_prefsKey);
      if (jsonString != null && jsonString.isNotEmpty) {
        final Map<String, dynamic> data = jsonDecode(jsonString);
        _customPrices = {};

        for (final entry in data.entries) {
          final type = ZeroReportingType.values.firstWhere(
            (t) => t.name == entry.key,
            orElse: () => ZeroReportingType.oooUsn,
          );
          _customPrices[type] = entry.value as int;
        }

        // Обновляем UI после загрузки
        emit(state.copyWith(selectedType: state.selectedType));
      }
    } catch (e) {
      // Игнорируем ошибки загрузки
    }
  }

  /// Сохранить пользовательские расценки в SharedPreferences
  Future<void> _saveCustomPrices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final Map<String, int> data = {};

      for (final entry in _customPrices.entries) {
        data[entry.key.name] = entry.value;
      }

      await prefs.setString(_prefsKey, jsonEncode(data));
    } catch (e) {
      // Игнорируем ошибки сохранения
    }
  }

  /// Сбросить расценки к значениям по умолчанию
  void resetToDefaults() {
    _customPrices.clear();
    emit(state.copyWith(selectedType: state.selectedType));
    unawaited(_saveCustomPrices());
  }
}
