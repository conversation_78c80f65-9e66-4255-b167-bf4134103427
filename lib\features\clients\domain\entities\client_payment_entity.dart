import 'package:equatable/equatable.dart';

/// Сущность платежа клиента.
///
/// [actualAmountPaid] и [tariffAnnualAmount] хранятся в рублях.
class ClientPaymentEntity extends Equatable {
  final String clientId;
  final DateTime period; // всегда первый день месяца
  final double? actualAmountPaid;
  final double? tariffAnnualAmount;
  final DateTime? paymentDate; // точная дата оплаты
  final DateTime createdAt;
  final DateTime updatedAt;

  const ClientPaymentEntity({
    required this.clientId,
    required this.period,
    required this.actualAmountPaid,
    required this.tariffAnnualAmount,
    this.paymentDate,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
    clientId,
    period,
    actualAmountPaid,
    tariffAnnualAmount,
    paymentDate,
    createdAt,
    updatedAt,
  ];
}
