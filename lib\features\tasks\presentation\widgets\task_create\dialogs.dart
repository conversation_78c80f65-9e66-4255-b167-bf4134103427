import 'package:flutter/material.dart';
import 'models.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';

class AttachmentDialog extends StatefulWidget {
  final Function(AttachmentItem) onAdd;

  const AttachmentDialog({super.key, required this.onAdd});

  @override
  State<AttachmentDialog> createState() => _AttachmentDialogState();
}

class _AttachmentDialogState extends State<AttachmentDialog> {
  final _nameController = TextEditingController();
  final _urlController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Добавить файл'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(labelText: 'Название файла'),
          ),
          const Si<PERSON><PERSON><PERSON>(height: 16),
          TextField(
            controller: _urlController,
            decoration: const InputDecoration(labelText: 'URL файла'),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_nameController.text.isNotEmpty &&
                _urlController.text.isNotEmpty) {
              widget.onAdd(
                AttachmentItem(
                  name: _nameController.text,
                  url: _urlController.text,
                ),
              );
            }
          },
          child: const Text('Добавить'),
        ),
      ],
    );
  }
}

class ChecklistItemDialog extends StatefulWidget {
  final Function(ChecklistItem) onAdd;

  const ChecklistItemDialog({super.key, required this.onAdd});

  @override
  State<ChecklistItemDialog> createState() => _ChecklistItemDialogState();
}

class _ChecklistItemDialogState extends State<ChecklistItemDialog> {
  final _textController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Добавить пункт чек-листа'),
      content: TextField(
        controller: _textController,
        decoration: const InputDecoration(labelText: 'Текст пункта'),
        maxLines: 3,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_textController.text.isNotEmpty) {
              widget.onAdd(ChecklistItem(text: _textController.text));
            }
          },
          child: const Text('Добавить'),
        ),
      ],
    );
  }
}

class ReminderDialog extends StatefulWidget {
  final Function(ReminderItem) onAdd;

  const ReminderDialog({super.key, required this.onAdd});

  @override
  State<ReminderDialog> createState() => _ReminderDialogState();
}

class _ReminderDialogState extends State<ReminderDialog> {
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  String _selectedRole = 'assignee';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Добавить напоминание'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Дата'),
            subtitle: Text(
              '${_selectedDate.day}.${_selectedDate.month}.${_selectedDate.year}',
            ),
            onTap: _selectDate,
          ),
          ListTile(
            title: const Text('Время'),
            subtitle: Text('${_selectedTime.hour}:${_selectedTime.minute}'),
            onTap: _selectTime,
          ),
          DropdownButtonFormField<String>(
            value: _selectedRole,
            decoration: const InputDecoration(labelText: 'Роль'),
            items: const [
              DropdownMenuItem(value: 'assignee', child: Text('Исполнитель')),
              DropdownMenuItem(value: 'observer', child: Text('Наблюдатель')),
              DropdownMenuItem(value: 'creator', child: Text('Постановщик')),
            ],
            onChanged: (value) => setState(() => _selectedRole = value!),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        ElevatedButton(
          onPressed: () {
            final dateTime = DateTime(
              _selectedDate.year,
              _selectedDate.month,
              _selectedDate.day,
              _selectedTime.hour,
              _selectedTime.minute,
            );
            widget.onAdd(ReminderItem(dateTime: dateTime, role: _selectedRole));
          },
          child: const Text('Добавить'),
        ),
      ],
    );
  }

  void _selectDate() async {
    final date = await SmartDatePickerDialog.show(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: 'Выберите дату напоминания',
      allowClear: false,
    );
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  void _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }
}
