// lib/features/clients/presentation/widgets/edit_card_components/yearly_tasks_calendar.dart
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

class YearlyTasksCalendar extends StatelessWidget {
  final ClientEntity client;
  final int selectedYear;
  final ValueChanged<int> onYearChanged;
  final Function(String taskId)? onTaskTap;
  final bool isEditing;

  const YearlyTasksCalendar({
    super.key,
    required this.client,
    required this.selectedYear,
    required this.onYearChanged,
    this.onTaskTap,
    this.isEditing = false,
  });

  @override
  Widget build(BuildContext context) {
    final monthNames = [
      'Янв',
      'Фев',
      'Мар',
      'Апр',
      'Май',
      'Июн',
      'Июл',
      'Авг',
      'Сен',
      'Окт',
      'Ноя',
      'Дек',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Системные задачи',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        const SizedBox(height: 12),
        Row(
          children: List.generate(
            4,
            (i) => _buildMonthCalendar(context, monthNames[i], i),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(
            4,
            (i) => _buildMonthCalendar(context, monthNames[i + 4], i + 4),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(
            4,
            (i) => _buildMonthCalendar(context, monthNames[i + 8], i + 8),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              onPressed: () => onYearChanged(selectedYear - 1),
              icon: const Icon(Icons.chevron_left),
              tooltip: 'Предыдущий год',
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$selectedYear',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            IconButton(
              onPressed: () => onYearChanged(selectedYear + 1),
              icon: const Icon(Icons.chevron_right),
              tooltip: 'Следующий год',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMonthCalendar(
    BuildContext context,
    String name,
    int monthIndex,
  ) {
    final tasksForSelectedYear =
        client.systemTaskUids
            .where((task) => task.dueDate?.year == selectedYear)
            .toList();

    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              width: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Text(
                name,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(4),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  childAspectRatio: 1,
                  crossAxisSpacing: 2,
                  mainAxisSpacing: 2,
                ),
                itemCount: 31,
                itemBuilder: (ctx, i) {
                  final day = i + 1;
                  final taskOnDate = tasksForSelectedYear.firstWhereOrNull(
                    (uidInfo) =>
                        uidInfo.dueDate?.month == monthIndex + 1 &&
                        uidInfo.dueDate?.day == day,
                  );
                  final selected = taskOnDate != null;

                  final tooltipMessage =
                      selected
                          ? [
                            if (taskOnDate.name.isNotEmpty) taskOnDate.name,
                            if (taskOnDate.description.isNotEmpty)
                              taskOnDate.description,
                            'UID: ${taskOnDate.uid}',
                          ].join('\n')
                          : '';

                  return Tooltip(
                    message: tooltipMessage,
                    child: GestureDetector(
                      onTap: isEditing && selected && taskOnDate.uid.isNotEmpty
                          ? () => onTaskTap?.call(taskOnDate.uid)
                          : null,
                      child: Container(
                        decoration: BoxDecoration(
                          color: selected ? Theme.of(context).primaryColor : null,
                          borderRadius: BorderRadius.circular(4),
                          border: isEditing && selected
                              ? Border.all(color: Colors.orange, width: 1)
                              : null,
                        ),
                        child: Center(
                          child: Text(
                            '$day',
                            style: TextStyle(
                              fontSize: 10,
                              color: selected ? Colors.white : null,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
