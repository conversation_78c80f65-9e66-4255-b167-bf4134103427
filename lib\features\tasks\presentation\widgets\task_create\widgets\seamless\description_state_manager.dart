import 'package:flutter/material.dart';

import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';
import './description_models.dart';
import './description_parser.dart';

class DescriptionStateManager {
  final List<Block> blocks = [];
  final VoidCallback onUpdate;
  final Function(String) onContentChanged;
  final List<FileAttachmentItem> attachments;

  int _lastFocusedBlockIndex = -1;
  int get lastFocusedBlockIndex => _lastFocusedBlockIndex;

  int _lastCursorPosition = -1;
  int get lastCursorPosition => _lastCursorPosition;

  DescriptionStateManager({
    required String? initialText,
    required this.attachments,
    required this.onUpdate,
    required this.onContentChanged,
  }) {
    blocks.addAll(parseDescription(initialText, attachments));
    _setupAllTextListeners();
  }

  void dispose() {
    for (final block in blocks) {
      block.dispose();
    }
  }

  void _setupAllTextListeners() {
    for (int i = 0; i < blocks.length; i++) {
      if (blocks[i].type == BlockType.text) {
        _setupTextListenersForBlock(i);
      }
    }
    _notifyContentChanged();
  }

  void _setupTextListenersForBlock(int blockIndex) {
    final block = blocks[blockIndex];
    if (block.type != BlockType.text) return;

    block.controller?.addListener(() {
      if (block.focusNode?.hasFocus == true) {
        _lastFocusedBlockIndex = blockIndex;
        _lastCursorPosition = block.controller!.selection.baseOffset;
      }
      _notifyContentChanged();
    });

    block.focusNode?.addListener(() {
      if (block.focusNode?.hasFocus == true) {
        _lastFocusedBlockIndex = blockIndex;
        _lastCursorPosition = block.controller!.selection.baseOffset;
      }
    });
  }

  void _notifyContentChanged() {
    final combined =
        blocks.map((b) {
          if (b.type == BlockType.text) return b.controller!.text;
          return '[[${b.fileKey}|${b.width}x${b.height}]]';
        }).join();
    onContentChanged(combined);
  }

  void handleBackspaceAtStart(int textBlockIndex) {
    if (textBlockIndex == 0) return;

    final int prevBlockIndex = textBlockIndex - 1;
    final Block prevBlock = blocks[prevBlockIndex];

    if (prevBlock.type == BlockType.image) {
      blocks[prevBlockIndex].dispose();
      blocks.removeAt(prevBlockIndex);

      if (prevBlockIndex > 0 &&
          blocks[prevBlockIndex - 1].type == BlockType.text) {
        final Block textBlockToMergeInto = blocks[prevBlockIndex - 1];
        final Block currentTextBlock = blocks[prevBlockIndex];
        final int cursorPosition = textBlockToMergeInto.controller!.text.length;

        textBlockToMergeInto.controller!.text +=
            '\n${currentTextBlock.controller!.text}';

        currentTextBlock.dispose();
        blocks.removeAt(prevBlockIndex);
        _scheduleFocus(prevBlockIndex - 1, cursorPosition + 1);
      } else {
        _scheduleFocus(prevBlockIndex, 0);
      }
    } else if (prevBlock.type == BlockType.text) {
      final Block currentTextBlock = blocks[textBlockIndex];
      final int cursorPosition = prevBlock.controller!.text.length;

      prevBlock.controller!.text += '\n${currentTextBlock.controller!.text}';

      currentTextBlock.dispose();
      blocks.removeAt(textBlockIndex);
      _scheduleFocus(prevBlockIndex, cursorPosition + 1);
    }
    onUpdate();
    _notifyContentChanged();
  }

  void removeImageBlock(int index) {
    blocks[index].dispose();
    blocks.removeAt(index);

    if (index - 1 >= 0 &&
        index < blocks.length &&
        blocks[index - 1].type == BlockType.text &&
        blocks[index].type == BlockType.text) {
      final prevText = blocks[index - 1];
      final nextText = blocks[index];
      final cursorPos = prevText.controller!.text.length;

      prevText.controller!.text += '\n${nextText.controller!.text}';
      nextText.dispose();
      blocks.removeAt(index);
      _scheduleFocus(index - 1, cursorPos + 1);
    }
    onUpdate();
    _notifyContentChanged();
  }

  void insertImageBlock(String fileKey, String fileName, int w, int h) {
    final imageBlock = Block.image(
      fileKey: fileKey,
      fileName: fileName,
      width: w,
      height: h,
    );

    _insertBlockAtCursor(imageBlock);
    onUpdate();
    _notifyContentChanged();
  }

  void insertImageFromAttachment(FileAttachmentItem attachment) {
    final imageBlock = Block.image(
      fileKey: attachment.fileKey!,
      fileName: attachment.name,
      width: 300,
      height: 200,
    );
    _insertBlockAtCursor(imageBlock);
    onUpdate();
    _notifyContentChanged();
  }

  void _insertBlockAtCursor(Block blockToInsert) {
    int focusedBlockIndex = _lastFocusedBlockIndex;
    int cursorPosition = _lastCursorPosition;

    if (focusedBlockIndex == -1) {
      focusedBlockIndex = blocks.lastIndexWhere(
        (b) => b.type == BlockType.text,
      );
      if (focusedBlockIndex == -1) {
        blocks.insert(0, Block.text(''));
        focusedBlockIndex = 0;
      }
      cursorPosition = blocks[focusedBlockIndex].controller!.text.length;
    }

    final blockToInsertIn = blocks[focusedBlockIndex];
    final controller = blockToInsertIn.controller!;

    if (cursorPosition >= 0 && cursorPosition < controller.text.length) {
      final textAfterCursor = controller.text.substring(cursorPosition);
      final textBeforeCursor = controller.text.substring(0, cursorPosition);

      controller.text = textBeforeCursor;

      final newTextBlock = Block.text(textAfterCursor);

      blocks.insert(focusedBlockIndex + 1, blockToInsert);
      blocks.insert(focusedBlockIndex + 2, newTextBlock);
      _setupTextListenersForBlock(focusedBlockIndex + 2);

      _scheduleFocus(focusedBlockIndex, cursorPosition);
    } else {
      blocks.insert(focusedBlockIndex + 1, blockToInsert);
      final newTextBlock = Block.text('');
      blocks.insert(focusedBlockIndex + 2, newTextBlock);
      _setupTextListenersForBlock(focusedBlockIndex + 2);

      _scheduleFocus(focusedBlockIndex + 2, 0);
    }
  }

  void moveFocusVertically(int currentIndex, {required bool directionUp}) {
    int? targetIndex;
    if (directionUp) {
      for (int i = currentIndex - 1; i >= 0; i--) {
        if (blocks[i].type == BlockType.text) {
          targetIndex = i;
          break;
        }
      }
    } else {
      for (int i = currentIndex + 1; i < blocks.length; i++) {
        if (blocks[i].type == BlockType.text) {
          targetIndex = i;
          break;
        }
      }
    }

    if (targetIndex != null) {
      final tgt = blocks[targetIndex];
      WidgetsBinding.instance.addPostFrameCallback((_) {
        tgt.focusNode?.requestFocus();
        tgt.controller?.selection =
            directionUp
                ? TextSelection.collapsed(offset: tgt.controller!.text.length)
                : const TextSelection.collapsed(offset: 0);
      });
    }
  }

  void _scheduleFocus(int index, int position) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (index >= blocks.length) return;
      final block = blocks[index];
      if (block.type == BlockType.text) {
        block.focusNode?.requestFocus();
        block.controller?.selection = TextSelection.collapsed(offset: position);
      }
    });
  }

  void cacheFocus() {
    _lastFocusedBlockIndex = -1;
    _lastCursorPosition = -1;
    for (int i = 0; i < blocks.length; i++) {
      final block = blocks[i];
      if (block.type == BlockType.text && block.focusNode?.hasFocus == true) {
        _lastFocusedBlockIndex = i;
        _lastCursorPosition = block.controller!.selection.baseOffset;
        return;
      }
    }
  }
}
