import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/file_upload_entity.dart';
import '../repositories/tariffs_and_storage_repository.dart';

/// Use case для скачивания файлов
class DownloadFileUseCase {
  final ITariffsAndStorageRepository repository;

  const DownloadFileUseCase({required this.repository});

  /// Получить URL для скачивания файла по ключу
  Future<Either<Failure, FileDownloadEntity>> call({
    required String firmId,
    required String fileKey,
  }) async {
    return await repository.getDownloadUrl(firmId: firmId, fileKey: fileKey);
  }
}
