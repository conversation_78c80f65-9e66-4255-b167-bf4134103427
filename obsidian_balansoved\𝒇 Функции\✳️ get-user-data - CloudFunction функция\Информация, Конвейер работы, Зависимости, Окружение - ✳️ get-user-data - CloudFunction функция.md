
Идентификатор - d4e4c3r7udrhh476a309
Описание - 📥 Получить все данные пользователя (инфо, фирмы, задачи) по JWT токену.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен аутентифицированного пользователя.
	-> `X-Forwarded-Authorization: Bearer <jwt_token>` (служебный вход, передается внутренними сервисами).

Внутренняя работа:
	-> **Авторизация**: Проверяется JWT токен с помощью [[📄 utils - auth_utils.md]], извлекается `user_id`. Логируется начало процесса авторизации.
	-> **Сбор информации о пользователе**: Выполняется запрос к [[💾 jwt-database - База данных YandexDatabase.md]] для получения основной информации о пользователе (`user_id`, `email`, `user_name`). Логируется email обрабатываемого пользователя.
	-> **Сбор информации о фирмах**:
		-> Выполняется запрос к [[💾 firms-database - База данных YandexDatabase.md]] для поиска всех **АКТИВНЫХ** записей в таблице `Users` (`is_active = true`), где `user_id` совпадает с `user_id` из токена.
		-> Из полученных записей формируется список `firm_ids`.
		-> Выполняется второй запрос к таблице `Firms` для получения полной информации по каждому `firm_id` из списка.
		-> Логируются найденные фирмы и их данные.
	-> **Сбор информации о задачах**:
		-> Для каждой найденной фирмы (`firm_id`) динамически определяется имя таблицы задач (`tasks_{firm_id}`) в [[💾 tasks-database - База данных YandexDatabase.md]].
		-> Выполняется запрос к каждой такой таблице, который извлекает все задачи и фильтрует их в коде, проверяя наличие `user_id` в полях `assignee_ids_json`, `observer_ids_json`, `creator_ids_json`.
		-> Логируется обработка каждой фирмы и количество найденных задач.
	-> **Агрегация и логирование**: Все полученные данные (информация о пользователе, список фирм, список задач с полными данными) собираются в единый JSON-объект. Логируются финальные выходные данные.
	-> **Отключение кеширования**: Все ответы содержат заголовки `Cache-Control`, `Pragma` и `Expires` для предотвращения кеширования.

На выходе:
	-> `200 OK`: `{"user_info": {"user_id": "...", "email": "...", "user_name": "..."}, "firms": [{"firm_id": "...", "firm_name": "...", "owner_user_id": "...", "integrations": {...}, "user_roles": ["OWNER", "ADMIN"]}], "tasks": [{"task_id": "...", "title": "...", "description": "...", "firm_id": "...", /* и другие поля задачи */}]}`
	-> `401 Unauthorized`: Если токен невалиден.
	-> `404 Not Found`: Если пользователь из токена не найден в `jwt-database`.
	-> `500 Internal Server Error`: В случае ошибок при работе с базами данных.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT`, `YDB_DATABASE` ([[💾 jwt-database - База данных YandexDatabase.md]])
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase.md]])
	- `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS` ([[💾 tasks-database - База данных YandexDatabase.md]])
	- `SA_KEY_FILE`
	- `JWT_SECRET`

