import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/firm_entity.dart';

class ProfileEntity extends Equatable {
  final String id;
  final String? email;
  final String? userName;
  final List<FirmEntity> firms;
  int get firmsCount => firms.length;
  final int tasksCount;

  const ProfileEntity({
    required this.id,
    this.email,
    this.userName,
    required this.firms,
    required this.tasksCount,
  });

  @override
  List<Object?> get props => [id, email, userName, firms, tasksCount];
}
