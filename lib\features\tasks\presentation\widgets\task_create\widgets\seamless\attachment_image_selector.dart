import 'package:flutter/material.dart';

import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/widgets/file_attachments/models/file_attachment_models.dart';

Future<FileAttachmentItem?> showAttachmentImageSelector(
  BuildContext context,
  List<FileAttachmentItem> attachments,
) {
  final imageAttachments =
      attachments.where((a) {
        if (a.fileKey == null) return false;
        final ext = a.name.split('.').last.toLowerCase();
        return [
          'jpg',
          'jpeg',
          'png',
          'webp',
          'jpe',
          'gif',
          'bmp',
        ].contains(ext);
      }).toList();

  if (imageAttachments.isEmpty) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Нет доступных изображений во вложениях')),
    );
    return Future.value(null);
  }

  return showDialog<FileAttachmentItem>(
    context: context,
    builder:
        (ctx) => AlertDialog(
          title: const Text('Выберите изображение'),
          content: SizedBox(
            width: 400,
            height: 300,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: imageAttachments.length,
              itemBuilder: (context, index) {
                final attachment = imageAttachments[index];
                return GestureDetector(
                  onTap: () {
                    Navigator.pop(ctx, attachment);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.image,
                          size: 40,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          attachment.name,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(fontSize: 10),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(ctx),
              child: const Text('Отмена'),
            ),
          ],
        ),
  );
}
