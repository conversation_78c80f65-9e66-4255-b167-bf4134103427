import 'package:flutter/material.dart';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';

import 'calendar_cells/header_cells.dart';
import 'calendar_cells/content_cells.dart';

class CalendarTable extends StatelessWidget {
  final List<TaskEntity> tasks;
  final List<ClientEntity> clients;
  final DateTime selectedDate;
  final ScrollController? scrollController;

  const CalendarTable({
    super.key,
    required this.tasks,
    required this.clients,
    required this.selectedDate,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    // Получаем количество дней в месяце
    final daysInMonth =
        DateTime(selectedDate.year, selectedDate.month + 1, 0).day;

    // Показываем всех клиентов, а не только тех, у которых есть задачи
    final allClients = clients;

    if (allClients.isEmpty) {
      return const Center(child: Text('Нет клиентов для отображения'));
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Фиксированный заголовок
          _buildHeader(context, allClients),
          // Прокручиваемое содержимое
          Expanded(
            child: _buildScrollableContent(context, allClients, daysInMonth),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, List<ClientEntity> allClients) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(bottom: BorderSide(color: theme.dividerColor)),
      ),
      child: Row(
        children: [
          // Фиксированная колонка с датами
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              border: Border(right: BorderSide(color: theme.dividerColor)),
            ),
            child: TableHeaderCell(text: 'Дата', theme: theme),
          ),
          // Прокручиваемые заголовки клиентов
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children:
                    allClients.map((client) {
                      return Container(
                        width: 120,
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: theme.dividerColor),
                          ),
                        ),
                        child: RotatedHeaderCell(
                          fullName: client.name,
                          shortName: client.shortName,
                          theme: theme,
                        ),
                      );
                    }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScrollableContent(
    BuildContext context,
    List<ClientEntity> allClients,
    int daysInMonth,
  ) {
    final theme = Theme.of(context);
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          controller: scrollController,
          child: ConstrainedBox(
            constraints: BoxConstraints(minHeight: constraints.maxHeight),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Фиксированная колонка с датами
                Container(
                  width: 80,
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(color: theme.dividerColor),
                    ),
                  ),
                  child: Column(
                    children: List.generate(daysInMonth, (dayIndex) {
                      final day = dayIndex + 1;
                      final currentDate = DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        day,
                      );
                      final isToday = _isToday(currentDate);

                      return Container(
                        height: 50,
                        decoration: BoxDecoration(
                          color:
                              isToday
                                  ? theme.colorScheme.primaryContainer
                                      .withValues(alpha: 0.3)
                                  : null,
                          border:
                              dayIndex < daysInMonth - 1
                                  ? Border(
                                    bottom: BorderSide(
                                      color: theme.dividerColor,
                                    ),
                                  )
                                  : null,
                        ),
                        child: DateCell(date: currentDate, theme: theme),
                      );
                    }),
                  ),
                ),
                // Прокручиваемое содержимое
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: List.generate(daysInMonth, (dayIndex) {
                        final day = dayIndex + 1;
                        final currentDate = DateTime(
                          selectedDate.year,
                          selectedDate.month,
                          day,
                        );
                        final isToday = _isToday(currentDate);

                        return Container(
                          height: 50,
                          decoration: BoxDecoration(
                            color:
                                isToday
                                    ? theme.colorScheme.primaryContainer
                                        .withValues(alpha: 0.3)
                                    : null,
                            border:
                                dayIndex < daysInMonth - 1
                                    ? Border(
                                      bottom: BorderSide(
                                        color: theme.dividerColor,
                                      ),
                                    )
                                    : null,
                          ),
                          child: Row(
                            children:
                                allClients.map((client) {
                                  final clientTasks = _getTasksForClientAndDate(
                                    client.id,
                                    currentDate,
                                  );
                                  return Container(
                                    width: 120,
                                    decoration: BoxDecoration(
                                      border: Border(
                                        right: BorderSide(
                                          color: theme.dividerColor,
                                        ),
                                      ),
                                    ),
                                    child: TaskCell(
                                      tasks: clientTasks,
                                      theme: theme,
                                      date: currentDate,
                                      clientId: client.id,
                                    ),
                                  );
                                }).toList(),
                          ),
                        );
                      }),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<TaskEntity> _getTasksForClientAndDate(String clientId, DateTime date) {
    return tasks.where((task) {
      if (!task.clientIds.contains(clientId)) return false;
      if (task.dueDate == null) return false;

      final taskDate = task.dueDate!;
      return taskDate.year == date.year &&
          taskDate.month == date.month &&
          taskDate.day == date.day;
    }).toList();
  }

  bool _isToday(DateTime date) {
    final today = DateTime.now();
    return date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;
  }
}
