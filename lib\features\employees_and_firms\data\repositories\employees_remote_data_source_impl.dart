import 'dart:convert';

import 'package:balansoved_enterprise/core/constants/constants.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/data_source/employees_remote_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/models/employee_model.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/employee_entity.dart';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

class EmployeesRemoteDataSourceImpl implements IEmployeesRemoteDataSource {
  final Dio? dio;
  final http.Client? httpClient;

  EmployeesRemoteDataSourceImpl({this.dio, this.httpClient})
    : assert(
        dio != null || httpClient != null,
        'Either dio or httpClient must be provided',
      );

  @override
  Future<List<EmployeeEntity>> fetchEmployees(String token, String firmId) {
    if (dio != null) {
      return _fetchEmployeesDio(token, firmId);
    } else {
      return _fetchEmployeesHttp(token, firmId);
    }
  }

  Future<List<EmployeeEntity>> _fetchEmployeesDio(
    String token,
    String firmId,
  ) async {
    final url = ManagementApiUrls.editEmployees();
    try {
      debugPrint('🔵 [DIO] Получение списка сотрудников');
      final response = await dio!.post(
        url,
        data: {"firm_id": firmId, "action": "GET_INFO"},
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );
      debugPrint('✅ [DIO] Ответ получен: ${response.statusCode}');
      debugPrint('📦 [DIO] Raw JSON: ${response.data}');
      if (response.statusCode == 200) {
        final dynamic raw = response.data;
        final iterable =
            raw is List
                ? raw
                : raw is Map && raw['data'] is List
                ? raw['data']
                : [];
        final list =
            iterable
                .map<EmployeeEntity>(
                  (e) => EmployeeModel.fromJson(e).toEntity(),
                )
                .toList();
        return list;
      }
      throw ServerException(
        message: 'Ошибка получения списка сотрудников',
        statusCode: response.statusCode,
        requestUrl: url,
        responseBody: response.data.toString(),
      );
    } on DioException catch (e) {
      throw NetworkException(
        message: e.message ?? 'Dio error',
        originalError: e.toString(),
        requestUrl: url,
      );
    }
  }

  Future<List<EmployeeEntity>> _fetchEmployeesHttp(
    String token,
    String firmId,
  ) async {
    final url = Uri.parse(ManagementApiUrls.editEmployees());
    try {
      final response = await httpClient!.post(
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({"firm_id": firmId, "action": "GET_INFO"}),
      );
      debugPrint('HTTP status: ${response.statusCode}');
      debugPrint('📦 Raw JSON: ${response.body}');
      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        final iterable =
            decoded is List
                ? decoded
                : decoded is Map && decoded['data'] is List
                ? decoded['data']
                : [];
        return iterable
            .map<EmployeeEntity>((e) => EmployeeModel.fromJson(e).toEntity())
            .toList();
      }
      throw ServerException(
        message: 'Ошибка получения списка сотрудников',
        statusCode: response.statusCode,
        requestUrl: url.toString(),
        responseBody: response.body,
      );
    } catch (e) {
      throw NetworkException(message: e.toString(), requestUrl: url.toString());
    }
  }

  @override
  Future<void> addEmployee(String token, String firmId, String email) {
    if (dio != null) {
      return _addEmployeeDio(token, firmId, email);
    } else {
      return _addEmployeeHttp(token, firmId, email);
    }
  }

  Future<void> _addEmployeeDio(
    String token,
    String firmId,
    String email,
  ) async {
    final url = ManagementApiUrls.createEmployee();
    try {
      debugPrint('🔵 [DIO] Добавление сотрудника по email');
      final response = await dio!.post(
        url,
        data: {"firm_id": firmId, "email": email},
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );
      debugPrint('✅ [DIO] Ответ: ${response.statusCode}');
      if (response.statusCode == 201) {
        return;
      }
      throw ServerException(
        message: 'Ошибка добавления сотрудника',
        statusCode: response.statusCode,
        requestUrl: url,
        responseBody: response.data.toString(),
      );
    } on DioException catch (e) {
      throw NetworkException(
        message: e.message ?? 'Dio error',
        originalError: e.toString(),
        requestUrl: url,
      );
    }
  }

  Future<void> _addEmployeeHttp(
    String token,
    String firmId,
    String email,
  ) async {
    final url = Uri.parse(ManagementApiUrls.createEmployee());
    try {
      final response = await httpClient!.post(
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({"firm_id": firmId, "email": email}),
      );
      debugPrint('HTTP status: ${response.statusCode}');
      if (response.statusCode == 201) {
        return;
      }
      throw ServerException(
        message: 'Ошибка добавления сотрудника',
        statusCode: response.statusCode,
        requestUrl: url.toString(),
        responseBody: response.body,
      );
    } catch (e) {
      throw NetworkException(message: e.toString(), requestUrl: url.toString());
    }
  }

  @override
  Future<void> inviteEmployee(String token, String firmId, String email) {
    if (dio != null) {
      return _inviteEmployeeDio(token, firmId, email);
    } else {
      return _inviteEmployeeHttp(token, firmId, email);
    }
  }

  Future<void> _inviteEmployeeDio(
    String token,
    String firmId,
    String email,
  ) async {
    final url = ManagementApiUrls.inviteEmployee();
    try {
      debugPrint('🔵 [DIO] Приглашение сотрудника по email');
      NetworkLogger.printInfo('DIO INVITE: Preparing request');
      NetworkLogger.logDioRequest(
        RequestOptions(
          path: url,
          method: 'POST',
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
          data: {"firm_id": firmId, "email": email},
        ),
      );
      final response = await dio!.post(
        url,
        data: {"firm_id": firmId, "email": email},
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );
      NetworkLogger.logDioResponse(response);
      debugPrint('✅ [DIO] Ответ: ${response.statusCode}');
      if (response.statusCode == 201) {
        return;
      }
      throw ServerException(
        message: 'Ошибка приглашения сотрудника',
        statusCode: response.statusCode,
        requestUrl: url,
        responseBody: response.data.toString(),
      );
    } on DioException catch (e) {
      throw NetworkException(
        message: e.message ?? 'Dio error',
        originalError: e.toString(),
        requestUrl: url,
      );
    }
  }

  Future<void> _inviteEmployeeHttp(
    String token,
    String firmId,
    String email,
  ) async {
    final url = Uri.parse(ManagementApiUrls.inviteEmployee());
    try {
      NetworkLogger.printInfo('HTTP INVITE: Preparing request');
      NetworkLogger.logHttpRequest(
        method: 'POST',
        url: url.toString(),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({"firm_id": firmId, "email": email}),
      );
      final response = await httpClient!.post(
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({"firm_id": firmId, "email": email}),
      );
      NetworkLogger.logHttpResponse(
        statusCode: response.statusCode,
        headers: response.headers,
        body: response.body,
      );
      debugPrint('HTTP status: ${response.statusCode}');
      if (response.statusCode == 201) {
        return;
      }
      throw ServerException(
        message: 'Ошибка приглашения сотрудника',
        statusCode: response.statusCode,
        requestUrl: url.toString(),
        responseBody: response.body,
      );
    } catch (e) {
      throw NetworkException(message: e.toString(), requestUrl: url.toString());
    }
  }

  @override
  Future<void> addRole(
    String token,
    String firmId,
    String userId,
    String role,
  ) {
    return _editRole(token, firmId, userId, role, 'ADD_ROLE');
  }

  @override
  Future<void> removeRole(
    String token,
    String firmId,
    String userId,
    String role,
  ) {
    return _editRole(token, firmId, userId, role, 'REMOVE_ROLE');
  }

  Future<void> _editRole(
    String token,
    String firmId,
    String userId,
    String role,
    String action,
  ) async {
    final url = ManagementApiUrls.editEmployees();
    if (dio != null) {
      try {
        debugPrint('🔵 [DIO] $action for user=$userId role=$role');
        final response = await dio!.post(
          url,
          data: {
            'firm_id': firmId,
            'action': action,
            'user_id_to_edit': userId,
            'role': role,
          },
          options: Options(
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
            },
          ),
        );
        if (response.statusCode == 200) return;
        throw ServerException(
          message: 'Ошибка изменения роли',
          statusCode: response.statusCode,
          requestUrl: url,
          responseBody: response.data.toString(),
        );
      } on DioException catch (e) {
        throw NetworkException(
          message: e.message ?? 'Dio',
          originalError: e.toString(),
          requestUrl: url,
        );
      }
    } else {
      final uri = Uri.parse(url);
      try {
        final response = await httpClient!.post(
          uri,
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
          body: jsonEncode({
            'firm_id': firmId,
            'action': action,
            'user_id_to_edit': userId,
            'role': role,
          }),
        );
        if (response.statusCode == 200) return;
        throw ServerException(
          message: 'Ошибка изменения роли',
          statusCode: response.statusCode,
          requestUrl: uri.toString(),
          responseBody: response.body,
        );
      } catch (e) {
        throw NetworkException(
          message: e.toString(),
          requestUrl: uri.toString(),
        );
      }
    }
  }

  @override
  Future<void> deleteEmployee(
    String token,
    String firmId,
    String userId,
  ) async {
    final urlStr = ManagementApiUrls.deleteEmployee();
    if (dio != null) {
      try {
        debugPrint('🔵 [DIO] Delete employee $userId');
        final response = await dio!.post(
          urlStr,
          data: {'firm_id': firmId, 'user_id_to_delete': userId},
          options: Options(
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
            },
          ),
        );
        if (response.statusCode == 200) return;
        throw ServerException(
          message: 'Ошибка удаления сотрудника',
          statusCode: response.statusCode,
          requestUrl: urlStr,
          responseBody: response.data.toString(),
        );
      } on DioException catch (e) {
        throw NetworkException(
          message: e.message ?? 'Dio',
          originalError: e.toString(),
          requestUrl: urlStr,
        );
      }
    } else {
      final uri = Uri.parse(urlStr);
      try {
        final response = await httpClient!.post(
          uri,
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
          body: jsonEncode({'firm_id': firmId, 'user_id_to_delete': userId}),
        );
        if (response.statusCode == 200) return;
        throw ServerException(
          message: 'Ошибка удаления сотрудника',
          statusCode: response.statusCode,
          requestUrl: uri.toString(),
          responseBody: response.body,
        );
      } catch (e) {
        throw NetworkException(
          message: e.toString(),
          requestUrl: uri.toString(),
        );
      }
    }
  }

  // === Create Firm ===
  @override
  Future<void> createFirm(String token, String firmName) {
    if (dio != null) {
      return _createFirmDio(token, firmName);
    } else {
      return _createFirmHttp(token, firmName);
    }
  }

  Future<void> _createFirmDio(String token, String firmName) async {
    final url = ManagementApiUrls.createFirm();
    try {
      debugPrint('🔵 [DIO] Создание фирмы');
      final response = await dio!.post(
        url,
        data: {"firm_name": firmName},
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );
      debugPrint('✅ [DIO] Ответ: ${response.statusCode}');
      if (response.statusCode == 201) return;

      throw ServerException(
        message: 'Ошибка создания фирмы',
        statusCode: response.statusCode,
        requestUrl: url,
        responseBody: response.data.toString(),
      );
    } on DioException catch (e) {
      throw NetworkException(
        message: e.message ?? 'Dio error',
        originalError: e.toString(),
        requestUrl: url,
      );
    }
  }

  Future<void> _createFirmHttp(String token, String firmName) async {
    final url = Uri.parse(ManagementApiUrls.createFirm());
    try {
      final response = await httpClient!.post(
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({"firm_name": firmName}),
      );
      debugPrint('HTTP status: ${response.statusCode}');
      if (response.statusCode == 201) return;

      throw ServerException(
        message: 'Ошибка создания фирмы',
        statusCode: response.statusCode,
        requestUrl: url.toString(),
        responseBody: response.body,
      );
    } catch (e) {
      throw NetworkException(message: e.toString(), requestUrl: url.toString());
    }
  }
}
