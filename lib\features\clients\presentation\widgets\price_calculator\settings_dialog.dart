import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';

import '../../cubit/price_calculator_cubit.dart';
import '../../cubit/price_calculator_state.dart';

class SettingsDialog extends StatefulWidget {
  final String sectionKey;
  const SettingsDialog({super.key, required this.sectionKey});

  @override
  State<SettingsDialog> createState() => _SettingsDialogState();
}

class _SettingsDialogState extends State<SettingsDialog> {
  late final PriceCalculatorCubit _cubit;
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _cubit = context.read<PriceCalculatorCubit>();
    if (widget.sectionKey != 'base_rates') {
      final relevantSettings = settingsSchema.where(
        (s) => s['section'] == widget.sectionKey,
      );

      for (var setting in relevantSettings) {
        final key = setting['key'] as String;
        final label = setting['label'] as String;
        final currentValue = _cubit.state.rates[key] ?? 0.0;

        String displayValue;
        if (label.contains('%')) {
          final v = currentValue * 100;
          displayValue = v % 1 == 0 ? v.toStringAsFixed(0) : v.toString();
        } else {
          displayValue = currentValue % 1 == 0
              ? currentValue.toStringAsFixed(0)
              : currentValue.toString();
        }
        _controllers[key] = TextEditingController(text: displayValue);
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _save() {
    final Map<String, double> newRates = {};
    for (var entry in _controllers.entries) {
      final key = entry.key;
      final controller = entry.value;
      final schema = settingsSchema.firstWhere((s) => s['key'] == key);
      final isPercent = (schema['label'] as String).contains('%');

      double value =
          double.tryParse(controller.text.replaceAll(',', '.')) ?? 0.0;
      if (isPercent) {
        value /= 100;
      }
      newRates[key] = value;
    }
    _cubit.updateRates(newRates);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    const double dialogWidth = 600;
    const double dialogHeight = 480;

    final bool isBaseRates = widget.sectionKey == 'base_rates';

    return AlertDialog(
      title: Text(
        'Настройки: ${_sectionNames[widget.sectionKey] ?? widget.sectionKey}',
      ),
      content: SizedBox(
        width: dialogWidth,
        height: dialogHeight,
        child: isBaseRates
            ? _BaseRatesSettings(cubit: _cubit)
            : Scrollbar(
                child: MasonryGridView.count(
                  crossAxisCount: 2,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  itemCount: _controllers.length,
                  itemBuilder: (context, index) {
                    final entry = _controllers.entries.elementAt(index);
                    final key = entry.key;
                    final controller = entry.value;
                    final setting =
                        settingsSchema.firstWhere((s) => s['key'] == key);

                    final originalLabel = setting['label'] as String;
                    final displayLabel = originalLabel.replaceFirst(
                      RegExp(r'^(Тариф|Ставка|Наценка)\s+'),
                      '',
                    );

                    return UniversalNumberField(
                      controller: controller,
                      labelText: displayLabel,
                      fieldType: NumberFieldType.decimal,
                      isEditing: true,
                    );
                  },
                ),
              ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        if (!isBaseRates)
          FilledButton(onPressed: _save, child: const Text('Сохранить')),
      ],
    );
  }
}

// Русские названия секций для заголовка диалога
const Map<String, String> _sectionNames = {
  'operations': 'Операции и первичка',
  'employees': 'Сотрудники',
  'bank': 'Банк, касса, ВЭД',
  'features': 'Прочие особенности',
  'services': 'Дополнительные услуги',
  'base_rates': 'Базовые тарифы',
};

class _BaseRatesSettings extends StatefulWidget {
  final PriceCalculatorCubit cubit;
  const _BaseRatesSettings({required this.cubit});

  @override
  State<_BaseRatesSettings> createState() => _BaseRatesSettingsState();
}

class _BaseRatesSettingsState extends State<_BaseRatesSettings> {
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    final baseTariffs = PriceCalculatorCubit.getBaseTariffs();
    for (var activityEntry in baseTariffs.entries) {
      for (var taxEntry in activityEntry.value.entries) {
        final key = '${activityEntry.key.name}_${taxEntry.key.name}';
        final value = taxEntry.value.toString();
        _controllers[key] = TextEditingController(text: value);
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _save() {
    final newTariffs = <BusinessActivityType, Map<TaxSystem, int>>{};
    for (var controllerEntry in _controllers.entries) {
      final parts = controllerEntry.key.split('_');
      final activity = BusinessActivityType.fromString(parts[0]);
      final taxSystem = TaxSystem.fromString(parts[1]);
      final value = int.tryParse(controllerEntry.value.text) ?? 0;

      if (newTariffs.containsKey(activity)) {
        newTariffs[activity]![taxSystem] = value;
      } else {
        newTariffs[activity] = {taxSystem: value};
      }
    }
    widget.cubit.updateBaseTariffs(newTariffs);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final baseTariffs = PriceCalculatorCubit.getBaseTariffs();
    return Column(
      children: [
        Expanded(
          child: Scrollbar(
            child: ListView(
              children: [
                for (var activityEntry in baseTariffs.entries)
                  _buildActivitySection(activityEntry.key, activityEntry.value),
              ],
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Отмена'),
            ),
            FilledButton(onPressed: _save, child: const Text('Сохранить')),
          ],
        )
      ],
    );
  }

  Widget _buildActivitySection(
    BusinessActivityType activity, Map<TaxSystem, int> tariffs) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(activity.label, style: Theme.of(context).textTheme.titleMedium),
          const Divider(),
          for (var tariffEntry in tariffs.entries)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: UniversalNumberField(
                controller: _controllers['${activity.name}_${tariffEntry.key.name}']!,
                labelText: tariffEntry.key.label,
                fieldType: NumberFieldType.integer,
                isEditing: true,
              ),
            ),
        ],
      ),
    );
  }
}

const List<Map<String, Object>> settingsSchema = [
  {
    'key': 'operations_t1',
    'label': 'Наценка 51-100 опер. (%)',
    'section': 'operations',
  },
  {
    'key': 'operations_t2',
    'label': 'Наценка 101-150 опер. (%)',
    'section': 'operations',
  },
  {
    'key': 'operations_t3',
    'label': 'Наценка 151-200 опер. (%)',
    'section': 'operations',
  },
  {
    'key': 'operations_t4',
    'label': 'Ставка >200 опер. (руб.)',
    'section': 'operations',
  },
  {
    'key': 'primary_docs_t1',
    'label': 'Первичка до 50 опер. (%)',
    'section': 'operations',
  },
  {
    'key': 'primary_docs_t2',
    'label': 'Первичка 51-100 опер. (%)',
    'section': 'operations',
  },
  {
    'key': 'primary_docs_t3',
    'label': 'Первичка 101-200 опер. (%)',
    'section': 'operations',
  },
  {
    'key': 'primary_docs_t4',
    'label': 'Первичка >200 опер. (руб.)',
    'section': 'operations',
  },
  {
    'key': 'employees_cost',
    'label': 'Стоимость за сотрудника (руб.)',
    'section': 'employees',
  },
  {
    'key': 'employees_free',
    'label': 'Кол-во бесплатных сотрудников',
    'section': 'employees',
  },
  {
    'key': 'employees_discount',
    'label': 'Авто-скидка без работников (%)',
    'section': 'employees',
  },
  {'key': 'bank_p1', 'label': 'Тариф 1-20 платежек (руб.)', 'section': 'bank'},
  {'key': 'bank_p2', 'label': 'Тариф 21-40 платежек (руб.)', 'section': 'bank'},
  {'key': 'bank_p3', 'label': 'Тариф 41-60 платежек (руб.)', 'section': 'bank'},
  {'key': 'bank_p4', 'label': 'Тариф 61-80 платежек (руб.)', 'section': 'bank'},
  {
    'key': 'bank_p5',
    'label': 'Тариф 81-100 платежек (руб.)',
    'section': 'bank',
  },
  {
    'key': 'bank_p6',
    'label': 'Ставка >100 платежек (руб./шт)',
    'section': 'bank',
  },
  {
    'key': 'bank_p7',
    'label': 'Ставка >500 платежек (руб./шт)',
    'section': 'bank',
  },
  {'key': 'bank_ved_ie', 'label': 'Наценка ВЭД И/Э (%)', 'section': 'bank'},
  {'key': 'bank_ved_both', 'label': 'Наценка ВЭД И+Э (%)', 'section': 'bank'},
  {'key': 'bank_kkm', 'label': 'Наценка за ККМ/БСО (%)', 'section': 'bank'},
  {
    'key': 'bank_extra_acc',
    'label': 'Стоимость доп. р/с (руб.)',
    'section': 'bank',
  },
  {
    'key': 'bank_curr_acc',
    'label': 'Стоимость валютного счета (руб.)',
    'section': 'bank',
  },
  {
    'key': 'features_nom_t1',
    'label': 'Наценка 51-100 номенкл. (%)',
    'section': 'features',
  },
  {
    'key': 'features_nom_t2',
    'label': 'Наценка 101-250 номенкл. (%)',
    'section': 'features',
  },
  {
    'key': 'features_nom_t3',
    'label': 'Наценка >250 номенкл. (%)',
    'section': 'features',
  },
  {
    'key': 'features_tax_mix',
    'label': 'Наценка за совмещение СНО (%)',
    'section': 'features',
  },
  {
    'key': 'features_nds_rates',
    'label': 'Наценка за разные ставки НДС (%)',
    'section': 'features',
  },
  {
    'key': 'features_pbu18',
    'label': 'Наценка за ПБУ 18 (%)',
    'section': 'features',
  },
  {
    'key': 'features_os_nma',
    'label': 'Наценка за ОС/НМА (%)',
    'section': 'features',
  },
  {
    'key': 'features_fts_eaes',
    'label': 'Наценка за ФТС ЕАЭС (%)',
    'section': 'features',
  },
  {
    'key': 'features_excise_goods',
    'label': 'Наценка за торговлю акцизными товарами (%)',
    'section': 'features',
  },
  {
    'key': 'features_alcohol_declaration',
    'label': 'Наценка за алкогольную декларацию (%)',
    'section': 'features',
  },
  {
    'key': 'features_subdivision_cost',
    'label': 'Стоимость обособленного подразделения (руб.)',
    'section': 'features',
  },
  {
    'key': 'features_complex_ops',
    'label': 'Наценка за сложные операции (%)',
    'section': 'features',
  },
  {
    'key': 'services_our_1c',
    'label': 'Ведение в нашей 1С (руб.)',
    'section': 'services',
  },
  {
    'key': 'services_separate_1c_access',
    'label': 'Отдельный доступ в 1С (руб.)',
    'section': 'services',
  },
  {'key': 'services_vpn', 'label': 'VPN (руб.)', 'section': 'services'},
  {
    'key': 'services_document_resigning',
    'label': 'Повторное подписание документов (руб.)',
    'section': 'services',
  },
  {
    'key': 'services_courier_payment',
    'label': 'Оплата взносов курьером (руб.)',
    'section': 'services',
  },
  {
    'key': 'services_account_unblocking',
    'label': 'Снятие блоков р/с (руб.)',
    'section': 'services',
  },
];
