import tkinter as tk
from tkinter import ttk
import threading
import queue
import math

class TkinterProgressWindow:
    """
    Создает и управляет окном прогресса на базе tkinter, работающим в отдельном потоке.
    Окно отображается поверх всех остальных в правом верхнем углу.
    Эта версия исправляет ошибки с отображением "None" и некорректным завершением потока.
    """
    def __init__(self):
        self.root = None
        self.progress_bar = None
        self.label = None
        self.thread = None
        self.queue = queue.Queue()
        # Счётчики операций
        self._total_ops = 0
        self._completed_ops = 0
        self.is_running = False
        # Переменная для хранения последнего действительного описания
        self.current_description = "Инициализация..."

        self.thread = threading.Thread(target=self._run_gui, daemon=True)
        self.thread.start()

    def _run_gui(self):
        """Метод, выполняющийся в потоке GUI."""
        self.root = tk.Tk()
        self.root.title("Прогресс выполнения")
        self.root.attributes("-topmost", True)
        self.root.protocol("WM_DELETE_WINDOW", self._on_close)

        window_width = 400
        window_height = 80
        screen_width = self.root.winfo_screenwidth()
        x_position = screen_width - window_width - 20
        y_position = 40
        self.root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
        self.root.resizable(False, False)

        style = ttk.Style(self.root)
        style.theme_use('clam')
        style.configure("green.Horizontal.TProgressbar", background='#4CAF50', troughcolor='#E0E0E0', bordercolor='#E0E0E0', lightcolor='#4CAF50', darkcolor='#4CAF50')

        self.label = tk.Label(self.root, text=self.current_description, anchor='w', justify='left')
        self.label.pack(pady=(10, 0), padx=10, fill='x')

        self.progress_bar = ttk.Progressbar(self.root, orient='horizontal', mode='determinate', length=380, style="green.Horizontal.TProgressbar")
        self.progress_bar.pack(pady=5, padx=10, fill='x', expand=True)

        self.is_running = True
        self._process_queue()
        # Запускаем главный цикл tkinter. Он завершится после вызова root.quit().
        self.root.mainloop()

        # После выхода из цикла безопасно уничтожаем окно.
        if self.root is not None:
            try:
                self.root.destroy()
            except Exception:
                pass  # На случай, если окно уже уничтожено

        self.is_running = False

    def _update_label_text(self):
        """Вспомогательный метод для обновления текста метки."""
        if not self.is_running:
            return
        if self.progress_bar is None or self.label is None:
            return
        current = self.progress_bar['value']
        total = self.progress_bar['maximum']
        # Используем math.ceil для округления вверх, чтобы избежать "0/0" при инициализации
        self.label.config(text=f"{self.current_description} ({math.ceil(current)}/{math.ceil(total)})")


    def _process_queue(self):
        """Обрабатывает команды из очереди для обновления GUI."""
        try:
            while not self.queue.empty():
                command, args = self.queue.get_nowait()

                # Утверждаем существование элементов GUI для статического анализатора
                assert self.progress_bar is not None and self.label is not None

                if command == "initialize":
                    self.current_description = args['description']
                    # Устанавливаем общее количество операций
                    self._total_ops = args['total']
                    self._completed_ops = 0
                    self.progress_bar.config(maximum=100)  # Фиксируем максимум в 100%
                    self.progress_bar['value'] = 0
                elif command == "update":
                    self._completed_ops += args.get('amount', 1)
                    # Если фактическое количество операций превысило изначальную оценку,
                    # просто расширяем total, чтобы прогресс никогда не откатывался назад.
                    if self._completed_ops > self._total_ops:
                        self._total_ops = self._completed_ops

                    # Обновляем value и maximum напрямую (линейно)
                    self.progress_bar.config(maximum=self._total_ops)  # type: ignore[attr-defined]
                    self.progress_bar['value'] = self._completed_ops
                    # Если с обновлением пришло новое описание, запоминаем его
                    if args.get('description') is not None:
                        self.current_description = args['description']
                elif command == "add_total":
                     self._total_ops += args['amount']
                     # Пересчитываем процент после увеличения общего количества
                     if self._total_ops:
                         self.progress_bar.config(maximum=self._total_ops)  # type: ignore[attr-defined]
                         self.progress_bar['value'] = self._completed_ops
                elif command == "set_description":
                    self.current_description = args['description']
                elif command == "close":
                    self._on_close()
                    return

                # Обновляем текст метки после любого изменения
                self._update_label_text()

            if self.is_running and self.root is not None:
                self.root.after(100, self._process_queue)
        except Exception:
            pass

    def _on_close(self):
        """Безопасно закрывает окно tkinter."""
        if self.is_running and self.root is not None:
            # Помечаем окно к закрытию и выходим из mainloop
            self.is_running = False
            try:
                self.root.quit()
            except Exception:
                pass  # Игнорируем возможные ошибки при завершении

    def initialize(self, initial_total=1, description="Обработка"):
        self.queue.put(("initialize", {"total": initial_total, "description": description}))

    def add_to_total(self, amount):
        self.queue.put(("add_total", {"amount": amount}))

    def increment(self, amount=1, description=None):
        self.queue.put(("update", {"amount": amount, "description": description}))

    def set_description(self, description):
        self.queue.put(("set_description", {"description": description}))

    def close(self):
        """Отправляет команду на закрытие и ждет завершения потока."""
        if self.is_running:
            self.queue.put(("close", {}))
            # Убираем таймаут! Основной поток должен дождаться завершения GUI.
            if self.thread and self.thread.is_alive():
                self.thread.join()

    # ------------------------------------------------------------------
    # Вспомогательные методы расчёта прогресса
    # ------------------------------------------------------------------

