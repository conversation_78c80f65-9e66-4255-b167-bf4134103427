
```python
import json
import logging
import ydb
import invoke_utils
from custom_errors import AuthError, PreconditionFailedError
from utils import ydb_utils

def _check_owner_permissions(session, user_id, firm_id):
    """Проверяет, что пользователь является владельцем фирмы."""
    logging.info("Checking owner permissions...")
    query = session.prepare("DECLARE $uid AS Utf8; DECLARE $fid AS Utf8; SELECT roles FROM Users WHERE user_id = $uid AND firm_id = $fid;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id, "$fid": firm_id}, commit_tx=True)
    
    if not res[0].rows:
        raise AuthError("User is not a member of the specified firm.")
    
    roles = json.loads(res[0].rows[0].roles or '[]')
    if roles != ["OWNER"]:
        raise AuthError("Access denied. Only the firm owner can perform this operation.")
    logging.info("Owner permissions confirmed.")

def _check_integrations(session, firm_id):
    """Проверяет, что нет активных интеграций."""
    logging.info("Checking for active integrations...")
    query = session.prepare("DECLARE $fid AS Utf8; SELECT integrations_json FROM Firms WHERE firm_id = $fid;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$fid": firm_id}, commit_tx=True)
    
    if not res[0].rows: return # Фирма уже удалена, проверка пройдена
    
    integrations = json.loads(res[0].rows[0].integrations_json or '{}')
    for name, details in integrations.items():
        if isinstance(details, dict) and details.get("enabled") is True:
            raise PreconditionFailedError(f"Cannot delete firm: active integration '{name}' found.")
    logging.info("No active integrations found.")

def _check_task_attachments(session, firm_id):
    """Проверяет, что ни у одной задачи нет вложений."""
    logging.info("Checking for task attachments...")
    table_name = f"tasks_{firm_id}"
    
    # ИСПРАВЛЕНО: Правильное сравнение для JSON-поля
    # Проверяем, что attachments_json не NULL И что он не равен пустому JSON-массиву.
    # Если столбец Optional<Json>, то сравнение его с CAST('[]' AS Json)
    # будет работать, даже если значение NULL.
    # Более надежный способ: CAST в строку и проверка, что не содержит "[{", что означает не пустой массив объектов
    query_text = f"""
        SELECT COUNT(task_id) AS count FROM `{table_name}`
        WHERE attachments_json IS NOT NULL AND String::Contains(CAST(attachments_json AS String), '[{{');
    """
    
    try:
        res = session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(query_text), commit_tx=True)
        if res[0].rows and res[0].rows[0].count > 0:
            raise PreconditionFailedError(f"Cannot delete firm: found {res[0].rows[0].count} tasks with attachments.")
    except ydb.SchemeError: # <-- ИСПРАВЛЕНО: заменено ydb.BadQuery на ydb.SchemeError
        logging.warning(f"Table {table_name} not found. Assuming no tasks with attachments.")
        return # Если таблицы нет, то и задач с вложениями нет
    except Exception as e: # Отлавливаем другие ошибки YQL, которые не являются SchemeError
        # Для отладки можем логировать полную ошибку YDB
        if isinstance(e, ydb.issues.GenericError):
             logging.error(f"YDB Query Error in _check_task_attachments: {e.issues}", exc_info=True)
        else:
            logging.error(f"Unexpected error in _check_task_attachments: {e}", exc_info=True)
        raise # Пробрасываем ошибку дальше, так как это не ожидаемое отсутствие таблицы
    logging.info("No tasks with attachments found.")

def _check_client_attachments(user_jwt, firm_id):
    """Проверяет, что ни у одного клиента нет вложений."""
    logging.info("Checking for client attachments...")
    clients_response = invoke_utils.invoke_function(
        invoke_utils.FUNCTION_ID_EDIT_CLIENT,
        {"firm_id": firm_id, "action": "GET"},
        user_jwt
    )
    if not clients_response or 'data' not in clients_response:
        logging.warning("Could not retrieve client list to check for attachments.")
        return

    clients = clients_response['data']
    for client in clients:
        legal_info = client.get('tax_and_legal_info_json', '{}')
        if isinstance(legal_info, str): legal_info = json.loads(legal_info)
        
        # Проверяем наличие файла по ключу 'attached_files' и что он не пустой список
        if legal_info.get("attached_files") and len(legal_info["attached_files"]) > 0:
            raise PreconditionFailedError(f"Cannot delete firm: client '{client.get('client_name')}' has attached files.")
    logging.info("No clients with attachments found.")

def run_all_checks(pool, user_jwt, user_id, firm_id):
    """Запускает все проверки предусловий."""
    # Проверки, требующие доступ к firms-database
    pool.retry_operation_sync(lambda s: _check_owner_permissions(s, user_id, firm_id))
    pool.retry_operation_sync(lambda s: _check_integrations(s, firm_id))
    
    # Проверки, требующие доступ к другим БД или функциям
    tasks_driver = ydb_utils.get_driver_for_db(invoke_utils.YDB_ENDPOINT_TASKS, invoke_utils.YDB_DATABASE_TASKS)
    tasks_pool = ydb.SessionPool(tasks_driver)
    tasks_pool.retry_operation_sync(lambda s: _check_task_attachments(s, firm_id))

    _check_client_attachments(user_jwt, firm_id)
```