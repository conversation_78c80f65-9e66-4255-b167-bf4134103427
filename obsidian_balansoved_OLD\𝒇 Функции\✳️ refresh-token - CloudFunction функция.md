Идентификатор - d4elom5l31834a68a8r7
Описание - Принудительно обновить JWT токен по реквизитам доступа.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `email`: Email пользователя.
	-> `password`: Пар<PERSON>ль пользователя.
Внутренняя работа:
	-> **Использует утилиту `utils.request_parser` для безопасного извлечения и парсинга тела запроса.**
	-> Извлекает `email` и `password` из parsed data.
	-> Проверяет наличие `email` и `password`, возвращая 400 если отсутствуют.
	-> Получает YDB driver и session pool.
	-> В транзакции:
	  -> Выполняет SELECT для user_id и password_hash активного пользователя по `email`.
	  -> Если не найдено, rollback и return None.
	  -> Проверяет хеш пароля с помощью `auth_utils.verify_password`.
	  -> Если неверно, rollback и return None.
	  -> Генерирует новый JWT токен с помощью `auth_utils.generate_jwt`.
	  -> Получает текущее время в UTC.
	  -> Пытается исправить `user_id` в `firms-database` (не критично, логирует warning при ошибке):
	    -> Получает отдельный driver и pool для firms-database.
	    -> В nested транзакции:
	      -> Находит записи где email совпадает, но user_id != correct.
	      -> Для каждой:
	        -> UPSERT новую запись с correct user_id и всеми данными старой.
	        -> DELETE старую запись.
	    -> Commit nested транзакции.
	  -> Выполняет UPDATE для last_login_at и jwt_token в users таблице.
	  -> Commit главной транзакции и возвращает новый токен.
	-> Если токен получен, возвращает 200 с {"token": "<jwt_token>"}, иначе 401.
	-> При критических ошибках возвращает 500.
На выходе:
	-> `200 OK`: {"token": "<jwt_token>"}
	-> `400 Bad Request`: В случае проблем с телом запроса или отсутствием email/password.
	-> `401 Unauthorized`: {"message": "Invalid credentials."}
	-> `500 Internal Server Error`: При критических ошибках.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
    - `YDB_ENDPOINT` - Эндпоинт для [[🗄️ Базы данных/💾 jwt-database - База данных YandexDatabase.md|jwt-database]]
    - `YDB_DATABASE` - Путь к [[🗄️ Базы данных/💾 jwt-database - База данных YandexDatabase.md|jwt-database]]
    - `YDB_ENDPOINT_FIRMS` - Эндпоинт для [[🗄️ Базы данных/💾 firms-database - База данных YandexDatabase.md|firms-database]] (опционально, по умолчанию = `YDB_ENDPOINT`)
    - `YDB_DATABASE_FIRMS` - Путь к [[🗄️ Базы данных/💾 firms-database - База данных YandexDatabase.md|firms-database]] (опционально, по умолчанию = `YDB_DATABASE/firms`)
    - `SA_KEY_FILE` - Путь к файлу ключа сервисного аккаунта [[🗝️ Ключи доступа/🗝️ auth-service-acc - Статический ключ доступа.md]]
    - `JWT_SECRET` - Секретный ключ для JWT

---

```python
import json
import os
import datetime
import pytz
import logging
import ydb
from utils import ydb_utils, auth_utils, request_parser

logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    logging.info("--- NEW INVOCATION ---")
    logging.info(f"RAW EVENT: {event}")

    try:
        data = request_parser.parse_request_body(event)
    except ValueError as e:
        logging.error(f"Request body processing error: {e}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}

    email = data.get('email')
    password = data.get('password')

    if not all([email, password]):
        logging.error("Email or password not provided in the parsed data.")
        return {"statusCode": 400, "body": json.dumps({"message": "Email and password are required."})}

    driver = ydb_utils.get_ydb_driver()
    pool = ydb.SessionPool(driver)

    def transaction(session):
        tx = session.transaction(ydb.SerializableReadWrite())

        select_query_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8;
            SELECT user_id, password_hash FROM users WHERE email = $email AND is_active = true;
        """
        prepared_select = session.prepare(select_query_text)
        result_sets = tx.execute(prepared_select, {'$email': email})
        
        if not result_sets[0].rows:
            tx.rollback()
            return None

        user_data = result_sets[0].rows[0]

        if not auth_utils.verify_password(password, user_data.password_hash):
            tx.rollback()
            return None

        logging.info(f"Force refreshing token for user {email}")
        new_token = auth_utils.generate_jwt(user_data.user_id, email)
        now = datetime.datetime.now(pytz.utc)
        
        # ИСПРАВЛЕННАЯ ЛОГИКА: Вместо UPDATE используется CREATE COPY + DELETE, т.к. PK нельзя обновить
        def fix_firms_user_id(firm_session):
            update_tx = firm_session.transaction(ydb.SerializableReadWrite())
            
            # 1. Находим все поля некорректных записей
            find_query = """
                DECLARE $email AS Utf8; DECLARE $correct_user_id AS Utf8;
                SELECT * FROM Users WHERE email = $email AND user_id != $correct_user_id;
            """
            result = update_tx.execute(
                firm_session.prepare(find_query),
                {'$email': email, '$correct_user_id': user_data.user_id}
            )
            
            if result[0].rows:
                logging.info(f"Found {len(result[0].rows)} incorrect records in firms-database for {email}. Fixing...")
                
                # Готовим запросы для создания копии и удаления старой записи
                upsert_query = firm_session.prepare("""
                    DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; DECLARE $email AS Utf8;
                    DECLARE $password_hash AS Utf8?; DECLARE $full_name AS Utf8?; DECLARE $roles AS Json?;
                    DECLARE $is_active AS Bool?; DECLARE $created_at AS Timestamp?; DECLARE $invitation_key AS Utf8?;
                    DECLARE $invitation_sent_at AS Timestamp?;
                    UPSERT INTO Users (user_id, firm_id, email, password_hash, full_name, roles, is_active, created_at, invitation_key, invitation_sent_at)
                    VALUES ($user_id, $firm_id, $email, $password_hash, $full_name, $roles, $is_active, $created_at, $invitation_key, $invitation_sent_at);
                """)
                
                delete_query = firm_session.prepare("""
                    DECLARE $old_user_id AS Utf8; DECLARE $firm_id AS Utf8;
                    DELETE FROM Users WHERE user_id = $old_user_id AND firm_id = $firm_id;
                """)

                for old_record in result[0].rows:
                    # 2. Создаем новую запись с правильным user_id и всеми старыми данными
                    update_tx.execute(
                        upsert_query,
                        {
                            '$user_id': user_data.user_id, # <-- Правильный ID
                            '$firm_id': old_record.firm_id,
                            '$email': old_record.email,
                            '$password_hash': old_record.password_hash,
                            '$full_name': old_record.full_name,
                            '$roles': old_record.roles,
                            '$is_active': old_record.is_active,
                            '$created_at': old_record.created_at,
                            '$invitation_key': old_record.invitation_key,
                            '$invitation_sent_at': old_record.invitation_sent_at
                        }
                    )
                    
                    # 3. Удаляем старую запись с некорректным user_id
                    update_tx.execute(
                        delete_query,
                        {'$old_user_id': old_record.user_id, '$firm_id': old_record.firm_id}
                    )
                    logging.info(f"  - Fixed record for firm {old_record.firm_id}: {old_record.user_id} -> {user_data.user_id}")

            update_tx.commit()
        
        # Пытаемся обновить firms-database (не критично для refresh-token)
        try:
            firms_driver = ydb_utils.get_driver_for_db(
                os.environ.get("YDB_ENDPOINT_FIRMS", os.environ["YDB_ENDPOINT"]),
                os.environ.get("YDB_DATABASE_FIRMS", os.environ["YDB_DATABASE"] + "/firms")
            )
            firms_pool = ydb.SessionPool(firms_driver)
            firms_pool.retry_operation_sync(fix_firms_user_id)
        except Exception as e:
            logging.warning(f"Failed to update firms-database during token refresh for {email}: {e}")

        update_query_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); 
            DECLARE $user_id AS Utf8; DECLARE $now AS Timestamp; DECLARE $token AS Utf8;
            UPDATE users SET last_login_at = $now, jwt_token = $token WHERE user_id = $user_id;
        """
        prepared_update = session.prepare(update_query_text)
        tx.execute(
            prepared_update,
            {'$user_id': user_data.user_id, '$now': now, '$token': new_token}
        )
        
        tx.commit()
        return new_token

    try:
        token = pool.retry_operation_sync(transaction)
        if token:
            logging.info(f"Token refreshed successfully for user {email}.")
            return {"statusCode": 200, "body": json.dumps({"token": token})}
        else:
            logging.warning(f"Invalid credentials on token refresh attempt for user {email}.")
            return {"statusCode": 401, "body": json.dumps({"message": "Invalid credentials."})}
    except Exception as e:
        logging.error(f"Critical error during token refresh for user {email}: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```