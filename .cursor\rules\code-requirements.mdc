---
alwaysApply: true
---
Требования к коду ->
1. Код нашего приложения разбит на "lib/features/...". Каждая feature имеет структуру, эталонной "lib/features/_empty". А именно, data/data_source, data/models, data/repositories, domain/entities, domain/repositories, domain/usecases, presentation/cubit/имя_кубита, presentation/pages, presentation/widgets.
2. Слой domain должен использовать структуры "Future<Either<Failure, ...>>" для обработки ошибок и данных между data и presentation (использовать dartz).
3. Слой domain должен содержать Equatable классы в "entities".
4. Слой data должен наследовать классы domain. Но расширить их добавив, toEntity, fromEntity.
5. Хорошей парктикой будет, использовать цвет theme-of-context, нежели РУЧНЫЕ значения.
6. Хорошей парктикой будет, ВЫНЕСТИ БЗИНЕС ЛОГИКУ и СОСТОЯНИЯ в cubit-ы. Не брезгуй создать кубиты, для интерфейсов с состояниями.