import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/notification_entity.dart';
import '../../domain/usecases/get_notifications.dart';
import '../../domain/usecases/mark_as_delivered.dart';
import '../../../profile/presentation/cubit/profile_cubit.dart';

part 'notifications_state.dart';

class NotificationsCubit extends Cubit<NotificationsState> {
  final GetNotifications getNotifications;
  final MarkAsDelivered markAsDelivered;
  final ProfileCubit profileCubit;
  StreamSubscription<ProfileState>? _profileSubscription;

  NotificationsCubit({
    required this.getNotifications,
    required this.markAsDelivered,
    required this.profileCubit,
  }) : super(NotificationsInitial()) {
    _listenToProfileChanges();
  }

  List<NotificationEntity> _allNotifications = [];
  int _currentPage = 0;
  bool _hasMoreNotifications = true;
  int _undeliveredCount = 0;

  int get undeliveredCount => _undeliveredCount;
  List<NotificationEntity> get notifications => _allNotifications;
  bool get hasMoreNotifications => _hasMoreNotifications;

  void _listenToProfileChanges() {
    // Проверяем текущее состояние профиля
    if (profileCubit.state is ProfileLoaded && state is NotificationsInitial) {
      // Если профиль уже загружен, загружаем уведомления сразу
      loadNotifications();
    }
    
    // Слушаем изменения состояния профиля
    _profileSubscription = profileCubit.stream.listen((profileState) {
      if (profileState is ProfileLoaded && state is NotificationsInitial) {
        // Автоматически загружаем уведомления когда профиль загружен
        loadNotifications();
      }
    });
  }

  @override
  Future<void> close() {
    _profileSubscription?.cancel();
    return super.close();
  }

  Future<void> loadNotifications({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 0;
      _allNotifications.clear();
      _hasMoreNotifications = true;
    }

    if (!_hasMoreNotifications && !refresh) {
      return;
    }

    if (_currentPage == 0) {
      emit(NotificationsLoading());
    } else {
      emit(NotificationsLoadingMore(_allNotifications, _undeliveredCount));
    }

    final result = await getNotifications(
      page: _currentPage,
      getArchived: false,
    );

    result.fold(
      (failure) => emit(NotificationsError(failure.message)),
      (notifications) {
        if (notifications.length < 100) {
          _hasMoreNotifications = false;
        }

        _allNotifications.addAll(notifications);
        _currentPage++;

        // Подсчитываем неотправленные уведомления только с первой страницы
        if (_currentPage == 1) {
          _undeliveredCount = notifications
              .where((notification) => !notification.isDelivered)
              .length;
        }

        emit(NotificationsLoaded(_allNotifications, _undeliveredCount));
      },
    );
  }

  Future<void> loadMoreNotifications() async {
    if (_hasMoreNotifications && state is! NotificationsLoadingMore) {
      await loadNotifications();
    }
  }

  Future<void> markNotificationsAsDelivered(List<String> noticeIds) async {
    final result = await markAsDelivered(noticeIds);
    
    result.fold(
      (failure) => emit(NotificationsError(failure.message)),
      (_) {
        // Обновляем локальное состояние
        _allNotifications = _allNotifications.map((notification) {
          if (noticeIds.contains(notification.id)) {
            return NotificationEntity(
              id: notification.id,
              title: notification.title,
              body: notification.body,
              icon: notification.icon,
              createdAt: notification.createdAt,
              isDelivered: true,
              isArchived: notification.isArchived,
            );
          }
          return notification;
        }).toList();

        // Пересчитываем количество неотправленных
        _undeliveredCount = _allNotifications
            .where((notification) => !notification.isDelivered)
            .length;

        emit(NotificationsLoaded(_allNotifications, _undeliveredCount));
      },
    );
  }

  Future<void> markAllUndeliveredAsDelivered() async {
    // Получаем ID всех неотправленных уведомлений
    final undeliveredIds = _allNotifications
        .where((notification) => !notification.isDelivered)
        .map((notification) => notification.id)
        .toList();
    
    if (undeliveredIds.isNotEmpty) {
      await markNotificationsAsDelivered(undeliveredIds);
    }
  }
}