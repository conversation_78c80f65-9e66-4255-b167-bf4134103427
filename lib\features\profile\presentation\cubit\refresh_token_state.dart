part of 'refresh_token_cubit.dart';

abstract class RefreshTokenState extends Equatable {
  const RefreshTokenState();
  
  @override
  List<Object?> get props => [];
}

class RefreshTokenInitial extends RefreshTokenState {}

class RefreshTokenLoading extends RefreshTokenState {}

class RefreshTokenSuc<PERSON> extends RefreshTokenState {}

class RefreshTokenError extends RefreshTokenState {
  final String message;
  
  const RefreshTokenError(this.message);
  
  @override
  List<Object?> get props => [message];
}