
```python
# utils/invoke_utils.py

import os
import json
import logging
import time
import urllib.request
import urllib.error

# Настраиваем логирование, чтобы видеть информационные сообщения
logging.basicConfig(level=logging.INFO)

IAM_TOKEN_CACHE = {"token": None, "expires_at": 0}
METADATA_URL = "http://169.254.169.254/computeMetadata/v1/instance/service-accounts/default/token"

def _get_iam_token() -> str:
    """Возвращает актуальный IAM-токен, кешируя его."""
    now_ts = time.time()
    if IAM_TOKEN_CACHE["token"] and now_ts < IAM_TOKEN_CACHE["expires_at"]:
        return IAM_TOKEN_CACHE["token"]
    try:
        req = urllib.request.Request(METADATA_URL, headers={"Metadata-Flavor": "Google"})
        with urllib.request.urlopen(req, timeout=2) as resp:
            data = json.loads(resp.read())
            IAM_TOKEN_CACHE["token"] = data["access_token"]
            IAM_TOKEN_CACHE["expires_at"] = now_ts + data.get("expires_in", 300) - 60
            return IAM_TOKEN_CACHE["token"]
    except Exception as e:
        logging.error(f"Не удалось получить IAM-токен: {e}", exc_info=True)
        return ""

class FunctionInvokeError(Exception):
    """Исключение для ошибок при вызове облачных функций."""
    def __init__(self, message, status_code=None, details=None):
        super().__init__(message)
        self.status_code = status_code
        self.details = details

def invoke_function(function_id: str, payload: dict, user_jwt: str) -> dict:
    """
    Вызывает облачную функцию и возвращает ее ответ в виде словаря,
    имитирующего стандартный ответ Yandex Cloud Function.
    """
    if not function_id:
        raise FunctionInvokeError("ID функции не указан для вызова")

    function_url = f"https://functions.yandexcloud.net/{function_id}"
    iam_token = _get_iam_token()
    if not iam_token:
        raise FunctionInvokeError("Не удалось получить IAM-токен для вызова функции")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {iam_token}",
        "X-Forwarded-Authorization": f"Bearer {user_jwt}"
    }

    req_data = json.dumps(payload).encode('utf-8')
    req = urllib.request.Request(function_url, data=req_data, headers=headers, method="POST")

    try:
        with urllib.request.urlopen(req, timeout=15) as resp:
            # Читаем тело ответа и декодируем его в строку
            resp_body_str = resp.read().decode('utf-8')
            
            # --- ФИНАЛЬНОЕ ИСПРАВЛЕНИЕ ---
            # Собираем словарь, который имитирует стандартный ответ функции.
            # Теперь вызывающий код получит именно то, что ожидает.
            response_dict = {
                "statusCode": resp.status,
                "body": resp_body_str
            }
            # --- КОНЕЦ ИСПРАВЛЕНИЯ ---
            
            logging.info(f"Successfully invoked function {function_id}, status: {resp.status}")
            return response_dict

    except urllib.error.HTTPError as e:
        details = e.read().decode(errors='ignore')
        logging.error(f"Ошибка вызова {function_id}. Статус: {e.code}. Детали: {details[:500]}")
        raise FunctionInvokeError(f"Ошибка вызова функции {function_id}", status_code=e.code, details=details)
    except Exception as e:
        logging.error(f"Исключение при вызове {function_id}: {e}", exc_info=True)
        raise FunctionInvokeError(f"Непредвиденное исключение при вызове {function_id}: {e}")
```