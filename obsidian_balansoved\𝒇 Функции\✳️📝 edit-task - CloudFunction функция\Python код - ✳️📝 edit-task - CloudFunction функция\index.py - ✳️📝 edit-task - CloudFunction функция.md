
```python
import json, os, logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

from get import get_task
from upsert import upsert_task
from delete import delete_task
from custom_errors import AuthError, LogicError, NotFoundError

logging.basicConfig(level=logging.INFO)

def _log_event_context(event, context):
    """Выводит в лог подробную информацию о входящем событии и контексте вызова."""
    try:
        print("RAW EVENT: %s" % json.dumps(event, default=str, ensure_ascii=False)[:10000])
    except Exception:
        print("RAW EVENT (non-json serialisable): %s" % event)
    
    if context is not None:
        print(
            "CONTEXT: request_id=%s | function=%s | memory_limit=%s" %
            (getattr(context, "request_id", None),
            getattr(context, "function_name", None),
            getattr(context, "memory_limit_in_mb", None))
        )

def check_membership_and_role(session, user_id, firm_id):
    query_text = "DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;"
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$user_id": user_id, "$firm_id": firm_id}, commit_tx=True)
    if not result[0].rows:
        return (False, False)
    roles = json.loads(result[0].rows[0].roles)
    is_admin_or_owner = "OWNER" in roles or "ADMIN" in roles
    return (True, is_admin_or_owner)

def handler(event, context):
    _log_event_context(event, context)
    
    user_id = "unknown"
    action = "unknown"
    firm_id = "unknown"
    try:
        # 1. Авторизация
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing Bearer token")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload: 
            raise AuthError("Invalid or expired token")
        requesting_user_id = user_payload['user_id']
        user_id = requesting_user_id
        print(f"Authorized request for user_id: {user_id}")

        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        
        if not all([firm_id, action]): 
            raise LogicError("firm_id and action are required.")
        
        print(f"Processing action: {action} for user_id: {user_id}, firm_id: {firm_id}")
        print(f"Parsed request data: {data}")

        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        print(f"Connected to firms database for firm_id: {firm_id}")
        firms_pool = ydb.SessionPool(firms_driver)
        print(f"Checking membership and role for user_id: {user_id}, firm_id: {firm_id}")
        is_member, is_admin_or_owner = firms_pool.retry_operation_sync(
            lambda s: check_membership_and_role(s, requesting_user_id, firm_id)
        )

        if not is_member: 
            print(f"Access denied: User {user_id} is not a member of firm {firm_id}")
            raise AuthError("User is not a member of the specified firm.")
        
        print(f"User {user_id} membership verified. Admin/Owner: {is_admin_or_owner}")

        tasks_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_TASKS"], os.environ["YDB_DATABASE_TASKS"])
        print(f"Connected to tasks database")
        tasks_pool = ydb.SessionPool(tasks_driver)
        table_name = f"tasks_{firm_id}"

        def task_transaction_router(session):
            task_id = data.get('task_id')
            payload = data.get('payload', {})
            page = data.get('page')
            get_dated_tasks = data.get('get_dated_tasks', False)
            month = data.get('month')
            year = data.get('year')
            client_id = data.get('client_id') # Новый параметр

            print(f"Routing to action handler: {action}")
            if action == "GET":
                print(f"Executing GET action for user_id: {user_id}")
                # Передаем client_id в функцию get_task
                result = get_task(session, table_name, task_id, page, get_dated_tasks, month, year, client_id)
                print(f"GET action completed successfully for user_id: {user_id}")
                return result
            
            elif action == "UPSERT":
                print(f"Executing UPSERT action for user_id: {user_id}")
                # ИЗМЕНЕНИЕ: Передаем firm_id в функцию upsert_task
                result = upsert_task(session, table_name, payload, task_id, requesting_user_id, is_admin_or_owner, token, firm_id)
                print(f"UPSERT action completed successfully for user_id: {user_id}")
                return result
            
            elif action == "DELETE":
                print(f"Executing DELETE action for user_id: {user_id}")
                result = delete_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner, token)
                print(f"DELETE action completed successfully for user_id: {user_id}")
                return result
            
            else:
                raise LogicError(f"Invalid action.")

        return tasks_pool.retry_operation_sync(task_transaction_router)

    except AuthError as e:
        print(f"AUTH ERROR for user_id: {user_id}, action: {action}, firm_id: {firm_id} - {str(e)}")
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        print(f"LOGIC ERROR for user_id: {user_id}, action: {action}, firm_id: {firm_id} - {str(e)}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        print(f"NOT FOUND ERROR for user_id: {user_id}, action: {action}, firm_id: {firm_id} - {str(e)}")
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        print(f"UNEXPECTED ERROR for user_id: {user_id}, action: {action}, firm_id: {firm_id} - {str(e)}")
        logging.error(f"Error processing task request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```