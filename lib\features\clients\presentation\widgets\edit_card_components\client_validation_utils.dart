import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_utils.dart';

/// Утилиты для валидации клиентов
class ClientValidationUtils {
  /// Прокручивает к первому невалидному полю
  static void scrollToFirstInvalidField(List<GlobalKey?> keysToScroll) {
    for (final key in keysToScroll) {
      if (key?.currentContext != null) {
        Scrollable.ensureVisible(
          key!.currentContext!,
          duration: const Duration(milliseconds: 300),
          alignment: 0.5,
        );
        return; // Stop at the first visible error
      }
    }
  }

  /// Возвращает ключ первого невалидного графика платежей
  static GlobalKey? getInvalidPaymentScheduleKey(
    PaymentSchedule? salaryPayment,
    PaymentSchedule? advancePayment,
    PaymentSchedule? ndflPayment,
    GlobalKey salaryPaymentKey,
    GlobalKey advancePaymentKey,
    GlobalKey ndflPaymentKey,
  ) {
    if (salaryPayment != null &&
        !ClientUtils.isValidDay(salaryPayment.paymentDate)) {
      return salaryPaymentKey;
    }
    if (advancePayment != null &&
        !ClientUtils.isValidDay(advancePayment.paymentDate)) {
      return advancePaymentKey;
    }
    if (ndflPayment != null &&
        (!ClientUtils.isValidDay(ndflPayment.paymentDate) ||
            !ClientUtils.isValidDay(ndflPayment.transferDate))) {
      return ndflPaymentKey;
    }
    return null;
  }
}
