import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';

/// Виджет для редактирования статуса задачи прямо в таблице
class StatusDropdownCell extends StatelessWidget {
  final TaskEntity task;

  const StatusDropdownCell({super.key, required this.task});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: InlineStatusDropdown(task: task),
    );
  }
}

/// Встроенный выпадающий список для редактирования статуса
class InlineStatusDropdown extends StatefulWidget {
  final TaskEntity task;

  const InlineStatusDropdown({super.key, required this.task});

  @override
  State<InlineStatusDropdown> createState() => _InlineStatusDropdownState();
}

class _InlineStatusDropdownState extends State<InlineStatusDropdown> {
  late String _currentStatus;
  bool _isUpdating = false;

  // Доступные статусы с переводом (только нужные)
  static const Map<String, String> _statuses = {
    'in_progress': 'Активна',
    'completed': 'Завершена',
    'cancelled': 'Отменена',
  };

  @override
  void initState() {
    super.initState();
    _currentStatus = _normalizeStatus(widget.task.status);
  }

  @override
  void didUpdateWidget(covariant InlineStatusDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.task.status != widget.task.status) {
      _currentStatus = _normalizeStatus(widget.task.status);
    }
  }

  String _normalizeStatus(String status) {
    if (_statuses.containsKey(status)) return status;

    if (status.contains('progress') || status.contains('ongoing')) {
      return 'in_progress';
    } else if (status.contains('complet') || status.contains('done')) {
      return 'completed';
    } else if (status.contains('cancel')) {
      return 'cancelled';
    } else {
      return 'in_progress'; // По умолчанию
    }
  }

  String _translateStatus(String status) {
    return _statuses[status] ?? status;
  }

  @override
  Widget build(BuildContext context) {
    if (_isUpdating) {
      return SizedBox(
        width: 120,
        height: 32,
        child: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _translateStatus(_currentStatus),
                style: Theme.of(context).textTheme.bodyMedium,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    return DropdownButtonFormField<String>(
      value: _currentStatus,
      isDense: true,
      isExpanded: true,
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
      items:
          _statuses.entries
              .map(
                (e) => DropdownMenuItem<String>(
                  value: e.key,
                  child: Text(
                    e.value,
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              )
              .toList(),
      onChanged: (newStatus) async {
        if (newStatus == null || newStatus == _currentStatus) return;

        setState(() => _isUpdating = true);

        final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
        if (firm == null) {
          setState(() => _isUpdating = false);
          return;
        }

        try {
          await context.read<TasksCubit>().updateTaskStatus(
            firm.id,
            widget.task.id,
            newStatus,
          );

          if (mounted) {
            setState(() {
              _currentStatus = newStatus;
              _isUpdating = false;
            });
          }
        } catch (e) {
          if (mounted) {
            setState(() => _isUpdating = false);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Ошибка обновления статуса: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
    );
  }
}
