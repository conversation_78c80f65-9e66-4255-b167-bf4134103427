import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';

class TaskStatusDropdown extends StatefulWidget {
  final TaskEntity task;
  const TaskStatusDropdown({super.key, required this.task});

  @override
  State<TaskStatusDropdown> createState() => _TaskStatusDropdownState();
}

class _TaskStatusDropdownState extends State<TaskStatusDropdown> {
  late String _current;
  bool _updating = false;

  // Доступные для изменения статусы
  static const Map<String, String> _statuses = {
    'in_progress': 'Активно',
    'cancelled': 'Отменено',
    'completed': 'Завершено',
  };

  @override
  void initState() {
    super.initState();
    _current = widget.task.status;
  }

  @override
  void didUpdateWidget(covariant TaskStatusDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.task.status != widget.task.status) {
      _current = widget.task.status;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_updating) {
      return const LoadingTile(height: 32, width: 96);
    }

    // Сформировать список пунктов, добавив текущий неизвестный статус при необходимости
    final Map<String, String> itemsMap = {..._statuses};
    if (!itemsMap.containsKey(_current)) {
      itemsMap[_current] = _current; // отображаем как есть
    }

    return DropdownButton<String>(
      value: _current,
      items:
          itemsMap.entries
              .map(
                (e) => DropdownMenuItem<String>(
                  value: e.key,
                  child: Text(e.value),
                ),
              )
              .toList(),
      onChanged: (val) async {
        if (val == null || val == _current) return;
        setState(() => _updating = true);

        final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
        if (firm == null) {
          setState(() => _updating = false);
          return;
        }

        // create updated task copy
        final updatedTask = widget.task.copyWith(status: val);

        final ok = await context.read<TasksCubit>().saveTask(
          firm.id,
          updatedTask,
        );

        if (mounted) {
          setState(() {
            _updating = false;
            if (ok) {
              _current = val;
            } else {
              // В случае ошибки возвращаем старое значение
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Недостаточно прав для изменения статуса'),
                ),
              );
            }
          });
        }
      },
      underline: const SizedBox(),
    );
  }
}
