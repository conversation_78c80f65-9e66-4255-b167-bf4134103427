
---
Идентификатор - etnugqopettga2m2a6uv
Путь к базе - /ru-central1/b1g2bgg0i9r8beucbthc/etnugqopettga2m2a6uv
Эндпоинт - grpcs://ydb.serverless.yandexcloud.net:2135/?database=/ru-central1/b1g2bgg0i9r8beucbthc/etnugqopettga2m2a6uv

---
# Таблицы
#### Таблица: `users`

| #   | Имя                 | Ключ | Тип         | Описание                                           |
| --- | ------------------- | ---- | ----------- | -------------------------------------------------- |
| 0   | `user_id`           | PK   | `Utf8`      | Уникальный идентификатор пользователя (UUID).      |
| 1   | `email`             |      | `Utf8`      | Email пользователя (должен быть уникальным).       |
| 2   | `password_hash`     |      | `Utf8`      | Хеш пароля пользователя.                           |
| 3   | `user_name`         |      | `Utf8`      | Имя пользователя.                                  |
| 4   | `created_at`        |      | `Timestamp` | Время создания записи.                             |
| 5   | `last_login_at`     |      | `Timestamp` | Время последнего входа.                            |
| 6   | `verification_code` |      | `Utf8`      | Временный код для подтверждения (6-значное число). |
| 7   | `code_expires_at`   |      | `Timestamp` | Время истечения срока действия кода.               |
| 8   | `is_active`         |      | `Bool`      | Флаг, указывающий, подтвержден ли аккаунт.         |
