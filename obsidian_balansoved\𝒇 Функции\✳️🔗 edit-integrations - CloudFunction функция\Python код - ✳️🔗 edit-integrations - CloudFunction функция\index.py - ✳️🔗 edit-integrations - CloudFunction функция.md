
```python
import json, os, logging, datetime, pytz
import ydb
from utils import auth_utils, ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

# ────────────────────────── HELPERS ──────────────────────────

# Новый helper для глубокого мерджа словарей
def _deep_merge_dict(dst: dict, src: dict):
    """
    Рекурсивно мерджит словарь `src` в `dst`.
    Если по ключу находятся два словаря — выполняем глубокий мердж,
    иначе значение из `src` перезаписывает значение в `dst`.
    """
    for k, v in src.items():
        if isinstance(v, dict) and isinstance(dst.get(k), dict):
            _deep_merge_dict(dst[k], v)
        else:
            dst[k] = v


def _check_membership_and_role(session, user_id, firm_id):
    query = session.prepare("DECLARE $uid AS Utf8; DECLARE $fid AS Utf8; SELECT roles FROM Users WHERE user_id = $uid AND firm_id = $fid;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$uid": user_id, "$fid": firm_id}, commit_tx=True)
    if not res[0].rows:
        return (False, False)
    roles = json.loads(res[0].rows[0].roles or '[]')
    is_admin_or_owner = "OWNER" in roles or "ADMIN" in roles
    return (True, is_admin_or_owner)


def _get_integrations(session, firm_id):
    q = session.prepare("DECLARE $fid AS Utf8; SELECT integrations_json FROM Firms WHERE firm_id = $fid;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(q, {"$fid": firm_id}, commit_tx=True)
    if not res[0].rows:
        raise NotFoundError("Firm not found")
    return json.loads(res[0].rows[0].integrations_json or '{}')


def _upsert_integrations(session, firm_id, new_data: dict):
    current = _get_integrations(session, firm_id)
    _deep_merge_dict(current, new_data)  # глубокий мердж вместо поверхностного update
    new_json = json.dumps(current)
    now = datetime.datetime.now(pytz.utc)
    q = session.prepare("""
        DECLARE $fid AS Utf8; DECLARE $data AS Json; DECLARE $now AS Timestamp;
        UPDATE Firms SET integrations_json = $data, updated_at = $now WHERE firm_id = $fid;
    """)
    session.transaction(ydb.SerializableReadWrite()).execute(q, {"$fid": firm_id, "$data": new_json, "$now": now}, commit_tx=True)


def _delete_integrations(session, firm_id, keys_to_delete):
    current = _get_integrations(session, firm_id)
    for k in keys_to_delete:
        current.pop(k, None)
    new_json = json.dumps(current)
    now = datetime.datetime.now(pytz.utc)
    q = session.prepare("""
        DECLARE $fid AS Utf8; DECLARE $data AS Json; DECLARE $now AS Timestamp;
        UPDATE Firms SET integrations_json = $data, updated_at = $now WHERE firm_id = $fid;
    """)
    session.transaction(ydb.SerializableReadWrite()).execute(q, {"$fid": firm_id, "$data": new_json, "$now": now}, commit_tx=True)

# ────────────────────────── HANDLER ──────────────────────────

def handler(event, context):
    try:
        # 1. Авторизация
        headers = event.get('headers', {}) or {}
        auth_header = headers.get('x-forwarded-authorization', headers.get('X-Forwarded-Authorization'))
        if not auth_header:
            auth_header = headers.get('authorization', headers.get('Authorization', ''))
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid token")
        user_id = user_payload['user_id']

        # 2. Парсинг тела
        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        if not all([firm_id, action]):
            raise LogicError("firm_id and action are required")

        # 3. Подключаемся к БД фирм
        driver = ydb_utils.get_driver_for_db(os.environ['YDB_ENDPOINT_FIRMS'], os.environ['YDB_DATABASE_FIRMS'])
        pool = ydb.SessionPool(driver)

        def txn(session):
            is_member, is_admin_or_owner = _check_membership_and_role(session, user_id, firm_id)
            if not is_member:
                raise AuthError("User is not a member of the specified firm")

            if action == 'GET':
                integrations = _get_integrations(session, firm_id)
                return {"statusCode": 200, "body": json.dumps({"integrations": integrations})}

            elif action == 'UPSERT':
                if not is_admin_or_owner:
                    raise AuthError("Admin or Owner rights required for UPSERT")
                payload = data.get('payload')
                if not isinstance(payload, dict):
                    raise LogicError("payload must be an object for UPSERT")
                _upsert_integrations(session, firm_id, payload)
                return {"statusCode": 200, "body": json.dumps({"message": "Integrations updated"})}

            elif action == 'DELETE':
                if not is_admin_or_owner:
                    raise AuthError("Admin or Owner rights required for DELETE")
                keys = data.get('integration_keys') or []
                if not isinstance(keys, list):
                    raise LogicError("integration_keys must be a list for DELETE")
                _delete_integrations(session, firm_id, keys)
                return {"statusCode": 200, "body": json.dumps({"message": "Integrations deleted"})}

            else:
                raise LogicError("Invalid action")

        return pool.retry_operation_sync(txn)

    except AuthError as e:
        return {"statusCode": 401 if 'Unauthorized' in str(e) else 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical error in edit-integrations: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```