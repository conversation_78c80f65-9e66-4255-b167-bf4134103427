import json
import logging
from custom_errors import NotFoundError

def _format_row(row, columns):
    """Преобразует строку YDB в словарь, парсит JSON-поля."""
    data = {}
    for c in columns:
        value = row[c.name]
        # Парсим JSON-поля, если они не пустые
        if 'json' in c.name and value:
            data[c.name] = json.loads(value)
        else:
            data[c.name] = value
    return data

def get_events(session, table_name, event_id=None):
    """Получает одно или все запланированные события."""
    tx = session.transaction(ydb.SerializableReadWrite())

    if event_id:
        query_text = f"DECLARE $event_id AS Utf8; SELECT * FROM `{table_name}` WHERE event_id = $event_id;"
        res = tx.execute(session.prepare(query_text), {"$event_id": event_id})
        if not res.rows:
            raise NotFoundError(f"Scheduled event with id {event_id} not found.")
        
        data = _format_row(res.rows, res.columns)
    else:
        # Возвращаем все события, если ID не указан
        query_text = f"SELECT * FROM `{table_name}` WHERE is_active = true;"
        res = tx.execute(session.prepare(query_text))
        data = [_format_row(row, res.columns) for row in res.rows]

    tx.commit()
    return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}