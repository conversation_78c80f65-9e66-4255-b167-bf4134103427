import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:translit/translit.dart';
import 'package:mime/mime.dart';

import 'package:balansoved_enterprise/core/constants/constants.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';
import '../data_source/tariffs_and_storage_remote_data_source.dart';
import '../models/tariffs_and_storage_model.dart';
import '../../domain/entities/file_upload_entity.dart';

class TariffsAndStorageRemoteDataSourceImpl
    implements ITariffsAndStorageRemoteDataSource {
  final Dio dio;
  DateTime? _lastProgressTime;
  double _lastProgressValue = 0.0;

  TariffsAndStorageRemoteDataSourceImpl({required this.dio});

  Map<String, String> _getHeaders(String token) {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  @override
  Future<TariffsAndStorageModel> getTariffsAndStorage(
    String token,
    String firmId,
  ) async {
    NetworkLogger.printInfo(
      'TARIFFS DATA SOURCE: Получение данных для firmId: $firmId',
    );

    final url = TariffsAndStorageApiUrls.manage();
    final headers = _getHeaders(token);
    final requestData = {'firm_id': firmId, 'action': 'GET_RECORD'};
    final body = jsonEncode(requestData);

    try {
      final response = await dio.post(
        url,
        options: Options(headers: headers),
        data: body,
      );

      dynamic responseData = response.data;
      if (responseData is String) {
        try {
          responseData = jsonDecode(responseData);
        } catch (e) {
          throw ServerException(message: 'Failed to decode JSON response: $e');
        }
      }

      NetworkLogger.printJson(
        'TARIFFS DATA SOURCE: DIO Response data:',
        responseData,
      );

      if (response.statusCode == 200) {
        final data = responseData['data'];
        return TariffsAndStorageModel.fromJson(data);
      } else {
        throw ServerException(
          message: 'DIO ${response.statusCode}: ${response.data}',
        );
      }
    } on DioException catch (e) {
      _handleDioException(e, 'getTariffsAndStorage');
      rethrow; // _handleDioException throws a specific exception
    } catch (e) {
      NetworkLogger.printError(
        'TARIFFS DATA SOURCE: Exception in getTariffsAndStorage:',
        e,
      );
      throw NetworkException(
        message: 'Ошибка сети при получении данных о тарифах: $e',
      );
    }
  }

  @override
  Future<FileUploadEntity> getUploadUrl({
    required String token,
    required String firmId,
    required String fileName,
    required int fileSize,
  }) async {
    final url = TariffsAndStorageApiUrls.manage();
    final headers = _getHeaders(token);
    final safeFileName = _transliterateAndClean(fileName);

    final requestData = {
      'firm_id': firmId,
      'action': 'GET_UPLOAD_URL',
      'filename': safeFileName,
      'filesize': fileSize,
    };
    final body = jsonEncode(requestData);

    try {
      final response = await dio.post(
        url,
        options: Options(headers: headers),
        data: body,
      );
      if (response.statusCode == 200) {
        dynamic responseData = response.data;

        // Если данные пришли в виде строки, декодируем JSON
        if (responseData is String) {
          try {
            responseData = jsonDecode(responseData);
          } catch (e) {
            throw ServerException(
              message: 'Failed to decode JSON response: $e',
            );
          }
        }

        // Проверяем, что у нас есть Map
        if (responseData is! Map<String, dynamic>) {
          throw ServerException(
            message: 'Unexpected response format: ${responseData.runtimeType}',
          );
        }

        final uploadUrl = responseData['upload_url'];
        final fileKey = responseData['file_key'];

        // Проверяем типы
        if (uploadUrl is! String) {
          throw ServerException(
            message:
                'Expected "upload_url" to be a String, but got ${uploadUrl.runtimeType}',
          );
        }
        if (fileKey is! String) {
          throw ServerException(
            message:
                'Expected "file_key" to be a String, but got ${fileKey.runtimeType}',
          );
        }

        return FileUploadEntity(
          uploadUrl: uploadUrl,
          fileKey: fileKey,
          fileName: safeFileName,
          fileSize: fileSize,
        );
      } else {
        throw ServerException(
          message: 'DIO ${response.statusCode}: ${response.data}',
        );
      }
    } on DioException catch (e) {
      _handleDioException(e, 'getUploadUrl');
      rethrow;
    } catch (e) {
      NetworkLogger.printError(
        'TARIFFS DATA SOURCE: Exception in getUploadUrl:',
        e,
      );
      throw NetworkException(
        message: 'Ошибка сети при получении URL для загрузки: $e',
      );
    }
  }

  @override
  Stream<FileUploadProgressEntity> uploadFileViaHttp({
    required String uploadUrl,
    required List<int> fileBytes,
    required String fileName,
  }) {
    final controller = StreamController<FileUploadProgressEntity>();
    final mimeType = lookupMimeType(fileName) ?? 'application/octet-stream';

    controller.add(
      const FileUploadProgressEntity(
        fileKey: '',
        progress: 0.0,
        status: FileUploadStatus.uploading,
      ),
    );

    final data = kIsWeb ? fileBytes : Stream.fromIterable([fileBytes]);

    dio
        .put(
          uploadUrl,
          data: data,
          options: Options(
            headers: {
              'Content-Type': mimeType,
              'Content-Length': fileBytes.length.toString(),
            },
            sendTimeout: const Duration(minutes: 10),
            receiveTimeout: const Duration(minutes: 10),
          ),
          onSendProgress: (sent, total) {
            if (total <= 0) return;

            final progress = sent / total;

            // Throttle: отправляем не чаще 250 мс и при изменении >0.5 %
            final now = DateTime.now();
            if (_lastProgressTime == null ||
                now.difference(_lastProgressTime!) >
                    const Duration(milliseconds: 250) ||
                (progress - _lastProgressValue).abs() > 0.005) {
              _lastProgressTime = now;
              _lastProgressValue = progress;

              controller.add(
                FileUploadProgressEntity(
                  fileKey: '',
                  progress: progress,
                  status: FileUploadStatus.uploading,
                ),
              );
            }
          },
        )
        .then((response) {
          if (response.statusCode != null &&
              response.statusCode! >= 200 &&
              response.statusCode! < 300) {
            controller.add(
              const FileUploadProgressEntity(
                fileKey: '',
                progress: 1.0,
                status: FileUploadStatus.completed,
              ),
            );
          } else {
            final errorMessage =
                'Upload failed with status ${response.statusCode}: ${response.statusMessage}';
            NetworkLogger.printError(
              'TARIFFS DATA SOURCE: Dio Upload Error',
              errorMessage,
            );
            controller.add(
              FileUploadProgressEntity(
                fileKey: '',
                progress: 0.0,
                status: FileUploadStatus.failed,
                errorMessage: errorMessage,
              ),
            );
          }
          controller.close();
        })
        .catchError((error) {
          String errorMessage = 'Unknown error during upload.';
          if (error is DioException) {
            final dioError = error;
            final details =
                'Type: ${dioError.type}\n'
                'Message: ${dioError.message}\n'
                'Response Status: ${dioError.response?.statusCode}\n'
                'Response Data: ${dioError.response?.data}';
            errorMessage = 'DioException during upload:\n$details';
            NetworkLogger.printError(
              'TARIFFS DATA SOURCE: DioException during upload',
              details,
              dioError.stackTrace,
            );
          } else {
            errorMessage = 'Generic error during upload: ${error.toString()}';
            NetworkLogger.printError(
              'TARIFFS DATA SOURCE: Generic error during upload',
              errorMessage,
            );
          }
          controller.add(
            FileUploadProgressEntity(
              fileKey: '',
              progress: 0.0,
              status: FileUploadStatus.failed,
              errorMessage: errorMessage,
            ),
          );
          controller.close();
        });

    return controller.stream;
  }

  @override
  Future<void> confirmUpload({
    required String token,
    required String firmId,
    required String fileKey,
  }) async {
    final url = TariffsAndStorageApiUrls.manage();
    final headers = _getHeaders(token);
    final requestData = {
      'firm_id': firmId,
      'action': 'CONFIRM_UPLOAD',
      'file_key': fileKey,
    };
    await _makeRequest(url, headers, requestData, 'confirmUpload');
  }

  Future<void> _makeRequest(
    String url,
    Map<String, String> headers,
    Map<String, dynamic> requestData,
    String operationName,
  ) async {
    final body = jsonEncode(requestData);
    NetworkLogger.printInfo('TARIFFS DATA SOURCE: $operationName');
    NetworkLogger.printJson('TARIFFS DATA SOURCE: Request:', requestData);

    try {
      final response = await dio.post(
        url,
        options: Options(headers: headers),
        data: body,
      );
      if (response.statusCode != 200) {
        throw ServerException(
          message: 'DIO ${response.statusCode}: ${response.data}',
        );
      }
    } on DioException catch (e) {
      _handleDioException(e, operationName);
      rethrow;
    } catch (e) {
      NetworkLogger.printError(
        'TARIFFS DATA SOURCE: Exception in $operationName:',
        e,
      );
      throw NetworkException(
        message: 'Ошибка сети в операции $operationName: $e',
      );
    }
  }

  String _transliterateAndClean(String input) {
    final transliterated = Translit().toTranslit(source: input);
    final safeFileName = transliterated
        .replaceAll(' ', '_')
        .replaceAll(RegExp(r'[^\w\._-]'), '');
    return safeFileName;
  }

  void _handleDioException(DioException e, String operationName) {
    final details =
        'Operation: $operationName\n'
        'Type: ${e.type}\n'
        'Message: ${e.message}\n'
        'URL: ${e.requestOptions.uri}\n'
        'Response Status: ${e.response?.statusCode}\n'
        'Response Data: ${e.response?.data}';

    NetworkLogger.printError(
      'TARIFFS DATA SOURCE: DioException',
      details,
      e.stackTrace,
    );

    // Специальная обработка ошибки квоты (413)
    if (e.response?.statusCode == 413) {
      String userFriendlyMessage =
          'Недостаточно места в хранилище для загрузки файла';

      // Пытаемся извлечь детали о квоте из ответа сервера
      try {
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic> &&
            responseData['message'] != null) {
          final serverMessage = responseData['message'] as String;

          // Извлекаем числовые данные из сообщения сервера
          final usedMatch = RegExp(r'Used: (\d+)').firstMatch(serverMessage);
          final fileMatch = RegExp(r'File: (\d+)').firstMatch(serverMessage);
          final quotaMatch = RegExp(r'Quota: (\d+)').firstMatch(serverMessage);

          if (usedMatch != null && fileMatch != null && quotaMatch != null) {
            final usedBytes = int.parse(usedMatch.group(1)!);
            final fileBytes = int.parse(fileMatch.group(1)!);
            final quotaBytes = int.parse(quotaMatch.group(1)!);

            final usedMB = (usedBytes / (1024 * 1024)).toStringAsFixed(1);
            final fileMB = (fileBytes / (1024 * 1024)).toStringAsFixed(1);
            final quotaMB = (quotaBytes / (1024 * 1024)).toStringAsFixed(1);
            final availableMB = ((quotaBytes - usedBytes) / (1024 * 1024))
                .toStringAsFixed(1);

            userFriendlyMessage =
                'Недостаточно места в хранилище.\n'
                'Размер файла: $fileMB МБ\n'
                'Доступно: $availableMB МБ\n'
                'Использовано: $usedMB МБ из $quotaMB МБ';
          }
        }
      } catch (parseError) {
        // Если не удалось распарсить, используем базовое сообщение
        NetworkLogger.printError(
          'TARIFFS DATA SOURCE: Error parsing quota details',
          parseError.toString(),
        );
      }

      throw ServerException(
        message: userFriendlyMessage,
        statusCode: 413,
        details: {'quota_exceeded': true, 'original_message': e.response?.data},
      );
    }

    // Обычная обработка для других ошибок
    throw ServerException(message: 'Ошибка сервера: $details');
  }

  @override
  Future<FileDownloadEntity> getDownloadUrl({
    required String token,
    required String firmId,
    required String fileKey,
  }) async {
    NetworkLogger.printInfo(
      'TARIFFS DATA SOURCE: Запрос URL для скачивания файла: $fileKey для фирмы: $firmId',
    );

    final url = TariffsAndStorageApiUrls.manage();
    final headers = _getHeaders(token);
    final requestData = {
      'firm_id': firmId,
      'action': 'GET_DOWNLOAD_URL',
      'file_key': fileKey,
    };
    final body = jsonEncode(requestData);

    NetworkLogger.printJson('TARIFFS DATA SOURCE: Request data:', requestData);

    try {
      final response = await dio.post(
        url,
        options: Options(headers: headers),
        data: body,
      );

      NetworkLogger.printInfo(
        'TARIFFS DATA SOURCE: Получен ответ со статусом: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        dynamic responseData = response.data;

        // Если ответ пришел как строка, парсим JSON
        if (responseData is String) {
          try {
            responseData = jsonDecode(responseData);
          } catch (e) {
            throw ServerException(
              message: 'Failed to decode JSON response: $e',
            );
          }
        }

        NetworkLogger.printJson(
          'TARIFFS DATA SOURCE: Response data:',
          responseData,
        );

        // Явно приводим к строке
        final downloadUrl = responseData['download_url'] as String?;
        if (downloadUrl == null || downloadUrl.isEmpty) {
          throw ServerException(message: 'Сервер вернул пустой download_url');
        }

        NetworkLogger.printInfo(
          'TARIFFS DATA SOURCE: Успешно получен URL для скачивания',
        );

        return FileDownloadEntity(downloadUrl: downloadUrl, fileKey: fileKey);
      } else {
        throw ServerException(
          message: 'DIO ${response.statusCode}: ${response.data}',
        );
      }
    } on DioException catch (e) {
      NetworkLogger.printError(
        'TARIFFS DATA SOURCE: DioException в getDownloadUrl',
        'URL: $url, FileKey: $fileKey, Error: ${e.message}',
        e.stackTrace,
      );
      _handleDioException(e, 'getDownloadUrl');
      rethrow;
    } catch (e) {
      NetworkLogger.printError(
        'TARIFFS DATA SOURCE: Exception в getDownloadUrl',
        'FileKey: $fileKey, Error: $e',
      );
      throw NetworkException(
        message: 'Ошибка сети при получении URL для скачивания: $e',
      );
    }
  }

  @override
  Future<void> updateJsonFields({
    required String token,
    required String firmId,
    required String targetJsonField,
    required Map<String, dynamic> updates,
  }) async {
    final url = TariffsAndStorageApiUrls.manage();
    final requestData = {
      'firm_id': firmId,
      'action': 'UPDATE_JSON',
      'target_json_field': targetJsonField,
      'updates': updates,
    };
    await _makeRequest(
      url,
      _getHeaders(token),
      requestData,
      'updateJsonFields',
    );
  }

  @override
  Future<void> clearJsonFields({
    required String token,
    required String firmId,
    required List<String> fieldsToClear,
  }) async {
    final url = TariffsAndStorageApiUrls.manage();
    final requestData = {
      'firm_id': firmId,
      'action': 'CLEAR_JSON',
      'fields_to_clear': fieldsToClear,
    };
    await _makeRequest(url, _getHeaders(token), requestData, 'clearJsonFields');
  }

  @override
  Future<void> deleteFile({
    required String token,
    required String firmId,
    required String fileKey,
  }) async {
    final url = TariffsAndStorageApiUrls.manage();
    final requestData = {
      'firm_id': firmId,
      'action': 'DELETE_FILE',
      'file_key': fileKey,
    };
    await _makeRequest(url, _getHeaders(token), requestData, 'deleteFile');
  }
}
