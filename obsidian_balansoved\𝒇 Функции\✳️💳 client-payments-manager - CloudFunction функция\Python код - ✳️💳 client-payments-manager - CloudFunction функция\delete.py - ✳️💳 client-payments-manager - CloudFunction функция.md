
```python
import json
import datetime
import ydb
from custom_errors import LogicError

def delete_payment(session, table_name, client_id, period):
    if not all([client_id, period]):
        raise LogicError("`client_id` and `period` are required for DELETE action.")

    try:
        period_date = datetime.datetime.strptime(period, '%Y-%m').date().replace(day=1)
    except ValueError:
        raise LogicError("Invalid `period` format. Use 'YYYY-MM'.")

    query = session.prepare(f"""
        DECLARE $client_id AS Utf8;
        DECLARE $period AS Date;
        DELETE FROM `{table_name}`
        WHERE client_id = $client_id AND period_start_date = $period;
    """)
    
    tx = session.transaction(ydb.SerializableReadWrite())
    tx.execute(query, {"$client_id": client_id, "$period": period_date})
    tx.commit()

    return {"statusCode": 200, "body": json.dumps({"message": "Payment record deleted"})}
```