# auth_utils.py

import jwt
import bcrypt
import os
import datetime

JWT_SECRET = os.environ.get("JWT_SECRET", "your-super-secret-key-that-is-long-and-secure")
JWT_ALGORITHM = "HS256"

def hash_password(password: str) -> str:
    """Хеширует пароль с использованием bcrypt."""
    salt = bcrypt.gensalt()
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed_password.decode('utf-8')

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Проверяет соответствие пароля его хешу."""
    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))

def generate_jwt(user_id: str, email: str) -> str:
    """Генерирует "бесконечный" JWT токен (без срока истечения)."""
    payload = {
        "user_id": user_id,
        "email": email,
        "iat": datetime.datetime.now(datetime.timezone.utc)
    }
    # Мы не устанавливаем 'exp', чтобы токен был бессрочным
    token = jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)
    return token

def verify_jwt(token: str) -> dict | None:
    """Проверяет JWT токен и возвращает его payload в случае успеха."""
    try:
        # Для поддержки бессрочных токенов отключаем проверку 'exp'
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM], options={"verify_exp": False})
        return payload
    except jwt.PyJWTError:
        return None