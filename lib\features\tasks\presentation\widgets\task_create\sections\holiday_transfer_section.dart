import 'package:flutter/material.dart';

// ------------------ Перенос при праздниках ------------------
class HolidayTransferSection extends StatelessWidget {
  final String holidayTransferRule;
  final Function(String) onHolidayTransferRuleChanged;

  const HolidayTransferSection({
    super.key,
    required this.holidayTransferRule,
    required this.onHolidayTransferRuleChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Перенос на случай праздника/выходного',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        DropdownButtonFormField<String>(
          value: holidayTransferRule,
          decoration: const InputDecoration(
            labelText: 'Правило переноса',
            border: OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem(
              value: 'next_workday',
              child: Text('Следующий рабочий день'),
            ),
            DropdownMenuItem(
              value: 'previous_workday',
              child: Text('Предыдущий рабочий день'),
            ),
            DropdownMenuItem(
              value: 'no_transfer',
              child: Text('Не переносить'),
            ),
          ],
          onChanged:
              (value) => onHolidayTransferRuleChanged(value ?? 'next_workday'),
        ),
      ],
    );
  }
}
