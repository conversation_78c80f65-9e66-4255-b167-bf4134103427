import 'package:equatable/equatable.dart';

class SystemTaskUidEntity extends Equatable {
  final String uid;
  final String name;
  final String description;
  final DateTime? dueDate;

  const SystemTaskUidEntity({
    required this.uid,
    required this.name,
    required this.description,
    this.dueDate,
  });

  @override
  List<Object?> get props => [uid, name, description, dueDate];

  SystemTaskUidEntity copyWith({
    String? uid,
    String? name,
    String? description,
    DateTime? dueDate,
  }) {
    return SystemTaskUidEntity(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      description: description ?? this.description,
      dueDate: dueDate ?? this.dueDate,
    );
  }
}