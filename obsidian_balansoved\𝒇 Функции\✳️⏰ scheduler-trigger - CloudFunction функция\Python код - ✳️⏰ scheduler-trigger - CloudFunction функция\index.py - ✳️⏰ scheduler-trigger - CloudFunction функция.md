
```python
# ✳️⏰ scheduler-trigger/index.py

import json
import os
import logging
import datetime
import pytz
import ydb
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from utils import ydb_utils

# Глобальная переменная для кеширования IAM-токена
IAM_TOKEN_CACHE = {
    "token": None,
    "expires_at": datetime.datetime.min.replace(tzinfo=pytz.utc)
}

# Настраиваем подробное логирование
logging.getLogger().setLevel(logging.INFO)

METADATA_URL = "http://169.254.169.254/computeMetadata/v1/instance/service-accounts/default/token"

def get_iam_token() -> str:
    """
    Получает IAM-токен для сервисного аккаунта функции из метаданных.
    Кеширует токен, чтобы избежать лишних запросов.
    """
    now = datetime.datetime.now(pytz.utc)
    if IAM_TOKEN_CACHE["token"] and now < IAM_TOKEN_CACHE["expires_at"]:
        logging.info("✅ Using cached IAM token")
        return IAM_TOKEN_CACHE["token"]

    logging.info("🔄 Requesting new IAM token from metadata service...")
    try:
        response = requests.get(METADATA_URL, headers={"Metadata-Flavor": "Google"}, timeout=2)
        response.raise_for_status()
        token_data = response.json()
        
        expires_in_seconds = token_data['expires_in']
        IAM_TOKEN_CACHE["token"] = token_data['access_token']
        IAM_TOKEN_CACHE["expires_at"] = now + datetime.timedelta(seconds=expires_in_seconds - 60)
        
        logging.info(f"✅ Successfully received and cached new IAM token (expires in {expires_in_seconds}s)")
        return IAM_TOKEN_CACHE["token"]
    except requests.exceptions.RequestException as e:
        logging.error(f"❌ Failed to get IAM token: {e}", exc_info=True)
        raise RuntimeError("Could not get IAM token for function invocation.") from e

def _to_datetime(value) -> datetime.datetime:
    """Безопасно конвертирует значение Timestamp из YDB в datetime объект (UTC)."""
    if isinstance(value, datetime.datetime):
        return value.replace(tzinfo=pytz.utc) if value.tzinfo is None else value
    if isinstance(value, int):
        return datetime.datetime.fromtimestamp(value / 1_000_000, tz=pytz.utc)
    return datetime.datetime.min.replace(tzinfo=pytz.utc) # Возвращаем минимальную дату если None

# --- NEW helper ---
def _parse_iso_utc(date_str: str) -> datetime.datetime:
    """Парсит ISO-дату (с 'Z' или без) и возвращает TZ-aware объект в UTC.

    Если часовой пояс отсутствует, считается, что время указано в локальном смещении,
    заданном переменной окружения EXECUTION_DATES_DEFAULT_OFFSET (пример '+05:00').
    По умолчанию используется '+00:00'.
    """
    date_str_norm = date_str.strip()
    if date_str_norm.endswith('Z'):
        date_str_norm = date_str_norm.replace('Z', '+00:00')

    try:
        dt = datetime.datetime.fromisoformat(date_str_norm)
    except ValueError:
        # Попробуем без миллисекунд
        dt = datetime.datetime.fromisoformat(date_str_norm.split('.')[0])

    if dt.tzinfo is None:
        offset_str = os.environ.get("EXECUTION_DATES_DEFAULT_OFFSET", "+00:00")
        try:
            sign = 1 if offset_str.startswith('+') else -1
            hours, minutes = map(int, offset_str[1:].split(':'))
            offset = datetime.timedelta(hours=hours, minutes=minutes)
            tz = datetime.timezone(sign * offset)
        except Exception:
            tz = pytz.utc
        dt = dt.replace(tzinfo=tz)

    return dt.astimezone(pytz.utc)

def invoke_function(event_data):
    """
    Вызывает функцию для одного события и возвращает результат.
    Выполняется в отдельном потоке.
    """
    event_row, selected_due_date, iam_token, now_utc = event_data
    event_id = event_row.event_id
    
    logging.info(f"🎯 [EVENT {event_id}] Starting function invocation")
    logging.info(f"📅 [EVENT {event_id}] Scheduled time: {selected_due_date.isoformat()}")
    logging.info(f"🏷️  [EVENT {event_id}] Event details: is_annual={event_row.is_annual}, function_id={event_row.function_id}")
    
    try:
        function_url = f"https://functions.yandexcloud.net/{event_row.function_id}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {iam_token}"
        }
        
        if getattr(event_row, 'request_headers_json', None):
            try:
                extra_headers = json.loads(event_row.request_headers_json)
                user_auth = extra_headers.pop('Authorization', extra_headers.pop('authorization', None))
                if user_auth:
                    headers['X-Forwarded-Authorization'] = user_auth
                headers.update(extra_headers)
            except Exception as e:
                logging.warning(f"⚠️  [EVENT {event_id}] Could not parse request_headers_json: {e}")

        payload_str = event_row.request_body_json or "{}"
        
        start_time = datetime.datetime.now(pytz.utc)
        response = requests.post(
            function_url,
            data=payload_str,
            headers=headers,
            timeout=300
        )
        end_time = datetime.datetime.now(pytz.utc)
        duration_ms = int((end_time - start_time).total_seconds() * 1000)
        
        status_emoji = "✅" if 200 <= response.status_code < 300 else "⚠️" if response.status_code < 500 else "❌"
        logging.info(f"{status_emoji} [EVENT {event_id}] Function call completed: HTTP {response.status_code} in {duration_ms}ms")
        
        return {
            "event_id": event_id,
            "status": "success",
            "http_code": response.status_code,
            "selected_due_date": selected_due_date,
            "is_annual": event_row.is_annual,
            "execution_dates_json": event_row.execution_dates_json # Передаем для логики деактивации
        }
        
    except requests.exceptions.Timeout:
        logging.error(f"⏰❌ [EVENT {event_id}] TIMEOUT: Function call exceeded 300s timeout")
        return { "event_id": event_id, "status": "timeout", "selected_due_date": selected_due_date }
    except Exception as e:
        logging.error(f"💥❌ [EVENT {event_id}] UNEXPECTED ERROR: {e}", exc_info=True)
        return { "event_id": event_id, "status": "error", "error": str(e) }

def handler(event, context):
    now_utc = datetime.datetime.now(pytz.utc)
    logging.info(f"🚀 Scheduler trigger started at {now_utc.isoformat()}")

    processed_events_count = 0
    
    try:
        driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_SCHEDULER"],
            os.environ["YDB_DATABASE_SCHEDULER"]
        )
        pool = ydb.SessionPool(driver)

        invocation_tasks = []
        events_to_update = []

        def collect_events_transaction(session):
            """Собирает события для обработки с новой логикой"""
            tx = session.transaction(ydb.SerializableReadWrite())
            result_set = tx.execute(session.prepare("SELECT * FROM `ScheduledEvents` WHERE is_active = true;"))
            
            if not result_set[0].rows:
                tx.commit()
                return []
            
            events_to_process = result_set[0].rows
            logging.info(f"📊 Found {len(events_to_process)} active events in database")
            iam_token = get_iam_token()
            tasks_data = []

            for event_row in events_to_process:
                event_id = event_row.event_id
                try:
                    execution_dates_str = event_row.execution_dates_json
                    execution_dates_list = json.loads(execution_dates_str) if execution_dates_str else []
                    
                    if not execution_dates_list:
                        continue
                    
                    last_invoked_ts = _to_datetime(event_row.last_invoked_at)
                    due_dates = []

                    # --- НОВАЯ ЛОГИКА ---
                    if event_row.is_annual:
                        # Игнорируем год, используем даты как шаблон
                        logging.info(f"🌿 [EVENT {event_id}] Annual event. Checking templates.")
                        for date_template_str in execution_dates_list:
                            template_dt = datetime.datetime.fromisoformat(date_template_str.replace('Z', '+00:00'))
                            
                            # Проверяем для текущего и прошлого года, чтобы не пропустить недавние события
                            for year_to_check in [now_utc.year, now_utc.year - 1]:
                                candidate_date = template_dt.replace(year=year_to_check, tzinfo=pytz.utc)
                                if last_invoked_ts < candidate_date <= now_utc:
                                    due_dates.append(candidate_date)

                    else: # is_annual = false
                        # Старая логика: просто проверяем даты из списка
                        for date_str in execution_dates_list:
                            dd = _parse_iso_utc(date_str)
                            if last_invoked_ts < dd <= now_utc:
                                due_dates.append(dd)
                    # --- КОНЕЦ НОВОЙ ЛОГИКИ ---

                    if not due_dates:
                        continue
                    
                    due_dates.sort()
                    selected_due_date = due_dates[0]
                    
                    logging.info(f"🎯 [EVENT {event_id}] Selected for execution: {selected_due_date.isoformat()}")
                    tasks_data.append((event_row, selected_due_date, iam_token, now_utc))
                    
                except Exception as e:
                    cid = getattr(event_row, 'custom_identifier', None)
                    logging.error(
                        f"💥❌ [EVENT {event_id}] Failed to prepare event (custom_identifier={cid}): {e}\n"
                        f"➡️ RAW execution_dates_json: {event_row.execution_dates_json}",
                        exc_info=True
                    )
            
            tx.commit()
            logging.info(f"🎯 Events scheduled for execution: {len(tasks_data)}")
            return tasks_data

        invocation_tasks = pool.retry_operation_sync(collect_events_transaction)
        
        if not invocation_tasks:
            logging.info("📭 No events to process - scheduler run complete")
            return {"statusCode": 200, "body": json.dumps({"message": "No events to process."})}

        successful_invocations = 0
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_event = {executor.submit(invoke_function, task_data): task_data[0] for task_data in invocation_tasks}
            for future in as_completed(future_to_event):
                try:
                    result = future.result()
                    if result.get("status") in ["success", "timeout"]:
                        successful_invocations += 1
                        events_to_update.append(result)
                        processed_events_count += 1
                except Exception as e:
                    logging.error(f"💥❌ Exception in thread: {e}")

        if events_to_update:
            logging.info(f"💾 Starting database update for {len(events_to_update)} events...")
            
            def update_events_transaction(session):
                updated_count = 0
                for update_info in events_to_update:
                    event_id = update_info["event_id"]
                    is_annual = update_info.get("is_annual", False)
                    
                    logging.info(f"💾 [EVENT {event_id}] Updating database record (is_annual={is_annual})...")
                    
                    try:
                        # --- НОВАЯ ЛОГИКА ОБНОВЛЕНИЯ ---
                        if is_annual:
                            # Для ежегодных просто обновляем last_invoked_at
                            query = """
                                DECLARE $event_id AS Utf8;
                                DECLARE $now_ts AS Timestamp;
                                UPDATE `ScheduledEvents`
                                SET updated_at = $now_ts, last_invoked_at = $now_ts
                                WHERE event_id = $event_id;
                            """
                            tx = session.transaction().begin()
                            tx.execute(
                                session.prepare(query),
                                {
                                    "$event_id": event_id,
                                    "$now_ts": int(now_utc.timestamp() * 1_000_000)
                                }
                            )
                            tx.commit()
                            logging.info(f"✅ [EVENT {event_id}] Annual event timestamps updated.")

                        else: # is_annual = false
                            # Проверяем, нужно ли деактивировать событие
                            dates_list = json.loads(update_info.get("execution_dates_json", "[]"))
                            if not dates_list:
                                continue

                            dates_list.sort()
                            last_date_in_schedule = _parse_iso_utc(dates_list[-1])
                            selected_date = update_info["selected_due_date"]
                            
                            should_deactivate = (selected_date >= last_date_in_schedule)
                            
                            if should_deactivate:
                                logging.info(f"🏁 [EVENT {event_id}] This was the last run. Deactivating event.")
                                query = """
                                    DECLARE $event_id AS Utf8;
                                    DECLARE $now_ts AS Timestamp;
                                    UPDATE `ScheduledEvents`
                                    SET updated_at = $now_ts, last_invoked_at = $now_ts, is_active = false
                                    WHERE event_id = $event_id;
                                """
                            else:
                                query = """
                                    DECLARE $event_id AS Utf8;
                                    DECLARE $now_ts AS Timestamp;
                                    UPDATE `ScheduledEvents`
                                    SET updated_at = $now_ts, last_invoked_at = $now_ts
                                    WHERE event_id = $event_id;
                                """
                            
                            tx = session.transaction().begin()
                            tx.execute(
                                session.prepare(query),
                                {
                                    "$event_id": event_id,
                                    "$now_ts": int(now_utc.timestamp() * 1_000_000)
                                }
                            )
                            tx.commit()
                            logging.info(f"✅ [EVENT {event_id}] Non-annual event updated (deactivated={should_deactivate}).")
                        # --- КОНЕЦ НОВОЙ ЛОГИКИ ОБНОВЛЕНИЯ ---
                        updated_count += 1
                    except Exception as e:
                        logging.error(f"💾❌ [EVENT {event_id}] Failed to update database: {e}", exc_info=True)
                
                logging.info(f"💾✅ Transaction committed - {updated_count} events updated in database")

            pool.retry_operation_sync(update_events_transaction)
        
        final_message = f"Processed {processed_events_count} events."
        logging.info(f"🏁 Scheduler trigger finished: {final_message}")
        
        return {"statusCode": 200, "body": json.dumps({"message": final_message})}

    except Exception as e:
        logging.error(f"💥❌ Critical error in scheduler trigger: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```