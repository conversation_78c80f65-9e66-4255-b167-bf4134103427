import json
import ydb
from custom_errors import LogicError

def delete_event(session, table_name, event_id):
    if not event_id:
        raise LogicError("event_id is required for DELETE action.")

    tx = session.transaction(ydb.SerializableReadWrite())
    query = session.prepare(f"DECLARE $event_id AS Utf8; DELETE FROM `{table_name}` WHERE event_id = $event_id;")
    tx.execute(query, {"$event_id": event_id})
    tx.commit()

    return {"statusCode": 200, "body": json.dumps({"message": "Scheduled event deleted"})}