import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/employees_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/status_dropdown_cell.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/tasks_table_helpers.dart';
import 'package:balansoved_enterprise/presentation/widgets/resizable_data_table.dart';

class TasksTableSection extends StatefulWidget {
  final String Function() filterProvider;
  final int sortColumnIndex;
  final bool sortAsc;
  final void Function(int, bool) onSort;
  final void Function(TaskEntity) onView;
  final void Function(TaskEntity) onEdit;
  final bool showOnlyMy;
  final TaskRequestParams currentParams;

  const TasksTableSection({
    super.key,
    required this.filterProvider,
    required this.sortColumnIndex,
    required this.sortAsc,
    required this.onSort,
    required this.onView,
    required this.onEdit,
    required this.showOnlyMy,
    required this.currentParams,
  });

  @override
  State<TasksTableSection> createState() => _TasksTableSectionState();
}

class _TasksTableSectionState extends State<TasksTableSection> {

  @override
  void initState() {
    super.initState();
    // Убираем вызов fetchTasks отсюда, чтобы избежать дублирования
    // Загрузка будет происходить в BlocListener при изменении состояния фирмы
  }



  @override
  Widget build(BuildContext context) {
    return BlocListener<ActiveFirmCubit, ActiveFirmState>(
      listener: (context, firmState) {
        // Убираем дублирующий вызов fetchTasks отсюда
        // Загрузка будет происходить только в BlocBuilder
      },
      child: BlocBuilder<ActiveFirmCubit, ActiveFirmState>(
        builder: (context, firmState) {
          if (firmState.isLoading || firmState.selectedFirm == null) {
            return const Center(child: CircularProgressIndicator());
          }
          return BlocBuilder<EmployeesCubit, EmployeesState>(
            builder: (context, employeesState) {
              if (employeesState.employees.isEmpty &&
                  !employeesState.isLoading &&
                  employeesState.error == null) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  context.read<EmployeesCubit>().fetchEmployees(
                    firmState.selectedFirm!.id,
                  );
                });
              }
              return BlocBuilder<ClientsCubit, ClientsState>(
                builder: (context, clientsState) {
                  if (clientsState.clients.isEmpty &&
                      !clientsState.isLoading &&
                      clientsState.error == null) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      context.read<ClientsCubit>().fetchClients(
                        firmState.selectedFirm!.id,
                      );
                      // Также загружаем информацию о хранилище для работы с файлами
                      context.read<TariffsAndStorageCubit>().loadStorageInfo(
                        firmState.selectedFirm!.id,
                      );
                    });
                  }
                  return BlocListener<TasksCubit, TasksState>(
                    listener: (context, state) {
                      if (state is TasksError) {
                        ScaffoldMessenger.of(
                          context,
                        ).showSnackBar(SnackBar(content: Text(state.message)));
                      }
                    },
                    child: BlocBuilder<TasksCubit, TasksState>(
                      builder: (context, tasksState) {
                        // Загружаем задачи только один раз при инициализации
                        if (tasksState is TasksInitial) {
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            final firm =
                                context
                                    .read<ActiveFirmCubit>()
                                    .state
                                    .selectedFirm;
                            if (firm != null && mounted) {
                              context.read<TasksCubit>().fetchTasks(
                                firm.id,
                                widget.currentParams,
                              );
                            }
                          });
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        if (tasksState is TasksLoading ||
                            tasksState is TasksInitial ||
                            employeesState.isLoading ||
                            clientsState.isLoading) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }
                        if (tasksState is TasksNoAccess) {
                          return const Center(
                            child: Text(
                              'Недостаточно прав для просмотра задач',
                            ),
                          );
                        }

                        if (tasksState is! TasksLoaded &&
                            tasksState is! TasksLoadingMore) {
                          return const Center(
                            child: Text('Нет данных о задачах'),
                          );
                        }

                        final allTasks =
                            tasksState is TasksLoaded
                                ? tasksState.tasks
                                : tasksState is TasksLoadingMore
                                ? tasksState.tasks
                                : <TaskEntity>[];

                        final search = widget.filterProvider().toLowerCase();
                        List<TaskEntity> data =
                            allTasks.where((t) {
                              final concat =
                                  '${t.title}${t.description ?? ''}${TasksTableHelpers.translateStatus(t.status)}${TasksTableHelpers.translatePriority(t.priority)}'
                                      .toLowerCase();
                              return search.isEmpty || concat.contains(search);
                            }).toList();

                        if (widget.showOnlyMy) {
                          final authState = context.watch<AuthCubit>().state;
                          if (authState is AuthInitial) {
                            context.read<AuthCubit>().checkAuth();
                          }
                          final profileState =
                              context.watch<ProfileCubit>().state;
                          if (profileState is ProfileInitial) {
                            context.read<ProfileCubit>().fetchProfile();
                          }
                          if (authState is! AuthAuthenticated ||
                              profileState is! ProfileLoaded) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          }
                          final employees = employeesState.employees;
                          final myEmployeeIds =
                              employees
                                  .where(
                                    (e) =>
                                        e.id == profileState.profile.id ||
                                        e.email == profileState.profile.email,
                                  )
                                  .map((e) => e.id)
                                  .toSet();
                          data =
                              data
                                  .where(
                                    (t) =>
                                        t.assigneeIds.any(
                                          myEmployeeIds.contains,
                                        ) ||
                                        t.creatorIds.any(
                                          myEmployeeIds.contains,
                                        ) ||
                                        t.observerIds.any(
                                          myEmployeeIds.contains,
                                        ),
                                  )
                                  .toList();
                        }

                        data.sort((a, b) {
                          int res;
                          switch (widget.sortColumnIndex) {
                            case 0:
                              res = a.title.compareTo(b.title);
                              break;
                            case 1:
                              res = TasksTableHelpers.translateStatus(
                                a.status,
                              ).compareTo(
                                TasksTableHelpers.translateStatus(b.status),
                              );
                              break;
                            case 2:
                              res = TasksTableHelpers.translatePriority(
                                a.priority,
                              ).compareTo(
                                TasksTableHelpers.translatePriority(b.priority),
                              );
                              break;
                            case 3:
                              final aDate = a.dueDate ?? DateTime(2099);
                              final bDate = b.dueDate ?? DateTime(2099);
                              res = aDate.compareTo(bDate);
                              break;
                            default:
                              res = 0;
                          }

                          if (res == 0) {
                            // Стабильная сортировка по ID, чтобы элементы с одинаковыми
                            // значениями не меняли своего относительного порядка
                            res = a.id.compareTo(b.id);
                          }

                          return widget.sortAsc ? res : -res;
                        });

                        if (data.isEmpty) {
                          return const Center(child: Text('Нет задач'));
                        }

                        return Column(
                          children: [
                            Expanded(
                              child: ResizableDataTable(
                                prefsKey: 'tasks_table_section_columns',
                                initialColumnWidths: const [200, 150, 120, 100, 120],
                                showCheckboxColumn: false,
                                sortColumnIndex: widget.sortColumnIndex,
                                sortAscending: widget.sortAsc,
                                columns: [
                                  DataColumn(
                                    label: const Text('Название'),
                                    onSort: (i, asc) => widget.onSort(i, asc),
                                  ),
                                  DataColumn(
                                    label: const Text('Статус'),
                                    onSort: (i, asc) => widget.onSort(i, asc),
                                  ),
                                  DataColumn(
                                    label: const Text('Приоритет'),
                                    onSort: (i, asc) => widget.onSort(i, asc),
                                  ),
                                  DataColumn(
                                    label: const Text('Срок'),
                                    onSort: (i, asc) => widget.onSort(i, asc),
                                  ),
                                  const DataColumn(
                                    label: Text('Действия'),
                                  ),
                                ],
                                rows: data
                                    .map(
                                      (t) => DataRow(
                                        onSelectChanged: (_) => widget.onView(t),
                                        cells: [
                                          DataCell(
                                            Text(
                                              t.title,
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 2,
                                            ),
                                          ),
                                          DataCell(
                                            StatusDropdownCell(
                                              task: t,
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              TasksTableHelpers.translatePriority(
                                                t.priority,
                                              ),
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              t.dueDate
                                                      ?.toString()
                                                      .substring(0, 10) ??
                                                  '–',
                                            ),
                                          ),
                                          DataCell(
                                            Row(
                                              children: [
                                                IconButton(
                                                  icon: const Icon(Icons.edit),
                                                  onPressed: () => widget.onEdit(t),
                                                ),
                                                IconButton(
                                                  icon: const Icon(Icons.visibility),
                                                  onPressed: () => widget.onView(t),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                    .toList(),
                              ),
                            ),
                            if (widget.currentParams.viewType ==
                                TaskViewType.timeless)
                              BlocBuilder<TasksCubit, TasksState>(
                                builder: (context, state) {
                                  final cubit = context.read<TasksCubit>();
                                  if (!cubit.canLoadMore &&
                                      !cubit.isLoadingMore) {
                                    return const SizedBox.shrink();
                                  }
                                  return Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton.icon(
                                        onPressed:
                                            cubit.isLoadingMore
                                                ? null
                                                : () {
                                                  context
                                                      .read<TasksCubit>()
                                                      .loadMoreTasks(
                                                        context
                                                            .read<
                                                              ActiveFirmCubit
                                                            >()
                                                            .state
                                                            .selectedFirm!
                                                            .id,
                                                      );
                                                },
                                        icon:
                                            cubit.isLoadingMore
                                                ? const SizedBox(
                                                  width: 16,
                                                  height: 16,
                                                  child:
                                                      CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                      ),
                                                )
                                                : const Icon(Icons.expand_more),
                                        label: Text(
                                          cubit.isLoadingMore
                                              ? 'Загружаем...'
                                              : 'Загрузить ещё задач',
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                          ],
                        );
                      },
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
