
Идентификатор - d4evkcnph6jfho1qlcfa
Описание - 💳 Управляет записями об оплатах услуг от клиентов фирмы.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя с правами `OWNER` или `ADMIN`.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы, в контексте которой выполняется операция.
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
		- **Для `GET`**:
			- `year` (integer, **обязательно**): Год, за который запрашиваются данные.
			- `client_id` (string, необязательно): Если указан, вернутся данные только по этому клиенту.
		- **Для `UPSERT`**:
			- `payload` (object, **обязательно**): Объект с данными для записи. **Суммы передаются в рублях.**
                ```json
                {
                  "client_id": "...",
                  "period": "2025-07",
                  "actual_amount_paid": 5800.50,
                  "tariff_annual_amount": 70000.00
                }
                ```
		- **Для `DELETE`**:
			- `client_id` (string, **обязательно**): ID клиента.
			- `period` (string, **обязательно**): Период в формате "YYYY-MM".

Внутренняя работа:
    -> Авторизация:
        -> Извлечение и проверка JWT-токена.
        -> Верификация и получение user_id.
    -> Парсинг тела запроса.
    -> Проверка обязательных параметров: firm_id и action.
    -> Проверка прав: OWNER или ADMIN в firms-database.
    -> Инициализация подключения к client-payments-database.
    -> Обеспечение существования таблицы client_payments_{firm_id}:
        -> Если таблицы нет, создание с колонками (client_id, period_start_date, actual_amount_kopeks, tariff_annual_amount_kopeks, created_at, updated_at).
    -> Маршрутизация по action в транзакции:
        -> GET:
            -> Проверка года, опционально client_id.
            -> Запрос записей за год, конвертация копеек в рубли (float).
        -> UPSERT:
            -> Валидация payload (client_id, period, суммы).
            -> Конвертация рублей в копейки (Int64).
            -> Проверка существования записи для сохранения created_at.
            -> UPSERT с обновлением updated_at.
        -> DELETE:
            -> Валидация client_id и period.
            -> Удаление записи.
    -> Обработка исключений и возврат статусов.

На выходе:
    -> 200 OK (GET): {"data": [{"client_id": "...", "period": "YYYY-MM", "actual_amount_paid": float, "tariff_annual_amount": float, "created_at": str, "updated_at": str}]}
    -> 200/201 OK (UPSERT): {"message": "Payment record updated"} или {"message": "Payment record created"} (201 для новой записи)
    -> 200 OK (DELETE): {"message": "Payment record deleted"}
    -> 400 Bad Request, 403 Forbidden, 404 Not Found, 500 Internal Server Error.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
	- `YDB_ENDPOINT_CLIENT_PAYMENTS`, `YDB_DATABASE_CLIENT_PAYMENTS` ([[💾 client-payments-database - База данных YandexDatabase]])
	- `SA_KEY_FILE` ([[ydb_sa_key.json]])
	- `JWT_SECRET`

---
#### index.py
```python
import json
import os
import logging
import sys
import ydb

from utils import auth_utils, ydb_utils, request_parser
from get import get_payments
from upsert import upsert_payment
from delete import delete_payment
from custom_errors import AuthError, LogicError, NotFoundError

logging.getLogger().setLevel(logging.INFO)

def check_permissions(session, user_id, firm_id):
    """Проверяет, является ли пользователь OWNER или ADMIN в фирме."""
    query = session.prepare("""
        DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8;
        SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;
    """)
    result = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$user_id": user_id, "$firm_id": firm_id}, commit_tx=True)
    if not result[0].rows:
        raise AuthError("User is not a member of the specified firm.")
    
    roles = json.loads(result[0].rows[0].roles or '[]')
    if "OWNER" not in roles and "ADMIN" not in roles:
        raise AuthError("Insufficient permissions. Owner or Admin role required.")
    
    logging.info(f"User {user_id} has required permissions for firm {firm_id}.")
    return True

def _ensure_table_exists(driver, database_path, table_name):
    """Проверяет существование таблицы и создает ее при необходимости."""
    full_table_path = os.path.join(database_path, table_name)
    logging.info(f"Checking for existence of table at full path: {full_table_path}")
    try:
        session = driver.table_client.session().create()
        session.describe_table(full_table_path)
        logging.info(f"Table {table_name} already exists.")
    except ydb.SchemeError:
        logging.warning(f"Table {table_name} does not exist. Creating...")
        session.create_table(
            full_table_path,
            ydb.TableDescription().with_primary_keys("client_id", "period_start_date")
            .with_columns(
                ydb.Column("client_id", ydb.PrimitiveType.Utf8),
                ydb.Column("period_start_date", ydb.PrimitiveType.Date),
                ydb.Column("actual_amount_kopeks", ydb.OptionalType(ydb.PrimitiveType.Int64)),
                ydb.Column("tariff_annual_amount_kopeks", ydb.OptionalType(ydb.PrimitiveType.Int64)),
                ydb.Column("created_at", ydb.PrimitiveType.Timestamp),
                ydb.Column("updated_at", ydb.PrimitiveType.Timestamp),
            )
        )
        logging.info(f"Table {table_name} created successfully.")
    except Exception as e:
        logging.error(f"Failed to check or create table {table_name}: {e}", exc_info=True)
        raise RuntimeError(f"Could not ensure table {table_name} exists.")


def handler(event, context):
    try:
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '): raise AuthError("Unauthorized")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload: raise AuthError("Invalid token")
        requesting_user_id = user_payload['user_id']

        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        if not all([firm_id, action]):
            raise LogicError("firm_id and action are required.")

        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        firms_pool.retry_operation_sync(lambda s: check_permissions(s, requesting_user_id, firm_id))

        payments_db_path = os.environ["YDB_DATABASE_CLIENT_PAYMENTS"]
        payments_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_CLIENT_PAYMENTS"], payments_db_path)
        payments_pool = ydb.SessionPool(payments_driver)
        table_name = f"client_payments_{firm_id}"
        
        _ensure_table_exists(payments_driver, payments_db_path, table_name)

        def payments_transaction_router(session):
            if action == "GET":
                year = data.get('year')
                client_id = data.get('client_id')
                return get_payments(session, table_name, year, client_id)
            
            elif action == "UPSERT":
                payload = data.get('payload')
                return upsert_payment(session, table_name, payload)
            
            elif action == "DELETE":
                client_id = data.get('client_id')
                period = data.get('period')
                return delete_payment(session, table_name, client_id, period)
            
            else:
                raise LogicError(f"Invalid action specified: '{action}'.")

        return payments_pool.retry_operation_sync(payments_transaction_router)

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing payment request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```
#### get.py
```python
import json
import datetime
import ydb
from custom_errors import LogicError

def get_payments(session, table_name, year, client_id=None):
    if not isinstance(year, int):
        raise LogicError("`year` parameter must be an integer.")

    start_date_obj = datetime.date(year, 1, 1)
    end_date_obj = datetime.date(year, 12, 31)

    params = {"$start_date": start_date_obj, "$end_date": end_date_obj}
    declare_clauses = ["DECLARE $start_date AS Date;", "DECLARE $end_date AS Date;"]
    where_clauses = ["period_start_date >= $start_date", "period_start_date <= $end_date"]

    if client_id:
        params["$client_id"] = client_id
        declare_clauses.append("DECLARE $client_id AS Utf8;")
        where_clauses.append("client_id = $client_id")

    query_text = f"""
        {" ".join(declare_clauses)}
        SELECT * FROM `{table_name}`
        WHERE {" AND ".join(where_clauses)}
        ORDER BY period_start_date ASC;
    """
    
    tx = session.transaction(ydb.SerializableReadWrite())
    res = tx.execute(session.prepare(query_text), params)
    tx.commit()
    
    data = []
    for row in res[0].rows:
        period_val = row.period_start_date
        period_str = ""
        
        # ИСПРАВЛЕНИЕ: Проверяем тип данных, полученных из YDB
        if isinstance(period_val, int):
            # Если это int, считаем его количеством дней с эпохи Unix
            period_date = datetime.date(1970, 1, 1) + datetime.timedelta(days=period_val)
            period_str = period_date.strftime('%Y-%m')
        elif isinstance(period_val, datetime.date):
            # Если это уже объект date, просто форматируем его
            period_str = period_val.strftime('%Y-%m')
        else:
            # Запасной вариант на случай другого формата
            period_str = str(period_val)

        item = {
            "client_id": row.client_id,
            "period": period_str,
            "actual_amount_paid": (float(row.actual_amount_kopeks) / 100.0) if row.actual_amount_kopeks is not None else None,
            "tariff_annual_amount": (float(row.tariff_annual_amount_kopeks) / 100.0) if row.tariff_annual_amount_kopeks is not None else None,
            "created_at": str(row.created_at),
            "updated_at": str(row.updated_at),
        }
        data.append(item)

    return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}
```
#### upsert.py
```python
import json
import datetime
import pytz
import ydb
from decimal import Decimal, InvalidOperation
from custom_errors import LogicError

def upsert_payment(session, table_name, payload):
    if not isinstance(payload, dict):
        raise LogicError("`payload` must be a JSON object.")
    
    client_id = payload.get('client_id')
    period_str = payload.get('period')
    
    if not all([client_id, period_str]):
        raise LogicError("`client_id` and `period` are required in payload.")

    try:
        period_date = datetime.datetime.strptime(period_str, '%Y-%m').date().replace(day=1)
    except ValueError:
        raise LogicError("Invalid `period` format. Use 'YYYY-MM'.")

    now = datetime.datetime.now(pytz.utc)

    try:
        actual_amount_kopeks = None
        if 'actual_amount_paid' in payload and payload['actual_amount_paid'] is not None:
            actual_amount_kopeks = int(Decimal(str(payload['actual_amount_paid'])) * 100)

        tariff_annual_amount_kopeks = None
        if 'tariff_annual_amount' in payload and payload['tariff_annual_amount'] is not None:
            tariff_annual_amount_kopeks = int(Decimal(str(payload['tariff_annual_amount'])) * 100)
    except (InvalidOperation, TypeError):
        raise LogicError("Invalid number format for amount fields.")

    tx = session.transaction(ydb.SerializableReadWrite())

    # Шаг 1: Проверяем наличие записи, чтобы сохранить исходную дату создания (created_at)
    check_query = session.prepare(f"""
        DECLARE $client_id AS Utf8;
        DECLARE $period AS Date;
        SELECT created_at FROM `{table_name}` WHERE client_id = $client_id AND period_start_date = $period;
    """)
    result = tx.execute(check_query, {
        "$client_id": client_id,
        "$period": period_date,
    })

    is_new_record = not result[0].rows
    created_at_to_use = now if is_new_record else result[0].rows[0].created_at
    
    # Шаг 2: Выполняем простой и надежный UPSERT
    upsert_query = session.prepare(f"""
        DECLARE $client_id AS Utf8;
        DECLARE $period AS Date;
        DECLARE $actual_amount AS Optional<Int64>;
        DECLARE $tariff_amount AS Optional<Int64>;
        DECLARE $created_at AS Timestamp;
        DECLARE $updated_at AS Timestamp;

        UPSERT INTO `{table_name}` (client_id, period_start_date, actual_amount_kopeks, tariff_annual_amount_kopeks, created_at, updated_at)
        VALUES (
            $client_id,
            $period,
            $actual_amount,
            $tariff_amount,
            $created_at,
            $updated_at
        );
    """)

    tx.execute(
        upsert_query,
        {
            "$client_id": client_id,
            "$period": period_date,
            "$actual_amount": actual_amount_kopeks,
            "$tariff_amount": tariff_annual_amount_kopeks,
            "$created_at": created_at_to_use,
            "$updated_at": now,
        }
    )

    tx.commit()
    
    # Возвращаем разные коды статуса для создания и обновления, как ожидает тест
    status_code = 201 if is_new_record else 200
    message = "Payment record created" if is_new_record else "Payment record updated"
    
    return {"statusCode": status_code, "body": json.dumps({"message": message})}
```
#### delete.py
```python
import json
import datetime
import ydb
from custom_errors import LogicError

def delete_payment(session, table_name, client_id, period):
    if not all([client_id, period]):
        raise LogicError("`client_id` and `period` are required for DELETE action.")

    try:
        period_date = datetime.datetime.strptime(period, '%Y-%m').date().replace(day=1)
    except ValueError:
        raise LogicError("Invalid `period` format. Use 'YYYY-MM'.")

    query = session.prepare(f"""
        DECLARE $client_id AS Utf8;
        DECLARE $period AS Date;
        DELETE FROM `{table_name}`
        WHERE client_id = $client_id AND period_start_date = $period;
    """)
    
    tx = session.transaction(ydb.SerializableReadWrite())
    tx.execute(query, {"$client_id": client_id, "$period": period_date})
    tx.commit()

    return {"statusCode": 200, "body": json.dumps({"message": "Payment record deleted"})}
```
#### custom_errors.py
```python
class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass
```