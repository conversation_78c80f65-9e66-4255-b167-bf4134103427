
Идентификатор - d4er1guc7vbu00j2lksd
Описание - ✉️ Пригласить нового или существующего пользователя в фирму по email.
Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя с правами `OWNER` или `ADMIN`.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы, в которую отправляется приглашение.
		- `email` (string, **обязательно**): Email приглашаемого пользователя.

 Внутренняя работа:
-> Авторизация и парсинг запроса:
  -> Проверяет наличие и формат заголовка Authorization (Bearer token).
  -> Верифицирует JWT, извлекает admin_user_id; при ошибке - AuthError (403).
  -> Парсит тело запроса для firm_id и invitee_email (в нижний регистр); при отсутствии - LogicError (400).

-> Поиск кандидата в глобальной базе (auth-data):
  -> Инициализирует YDB driver и pool для auth-database.
  -> Выполняет запрос на user_id и user_name по email; сохраняет candidate_data.

-> Основная транзакция в firms-database:
  -> Инициализирует YDB driver и pool для firms-database.
  -> В транзакции:
    -> Проверяет права администратора: SELECT roles из Users по admin_user_id и firm_id; если нет или роли не OWNER/ADMIN - AuthError (403).
    -> Получает firm_name из Firms по firm_id; если не найдено - LogicError (400).
    -> Проверяет существование пользователя в Users по email и firm_id: SELECT user_id, is_active, invitation_sent_at.
    -> Генерирует invitation_key.
    -> Если пользователь существует:
      -> Если is_active=True - LogicError (409, уже активен).
      -> Если is_active=False: проверяет время с invitation_sent_at; если <3 мин - LogicError (409); иначе UPDATE invitation_key и invitation_sent_at.
    -> Если не существует:
      -> Создает новую запись: UPSERT в Users с user_id (из candidate_data или новый), email, full_name, roles=["EMPLOYEE"], is_active=False, invitation_key, created_at, invitation_sent_at.
  -> Коммитит транзакцию, возвращает invitation_key и firm_name.

-> Отправка email:
  -> Получает admin_name из payload.
  -> Вызывает email_utils.send_invitation с invitee_email, invitation_key, firm_name, admin_name.
  -> Если не удалось - логирует ошибку и возвращает 500.
  -> Если успешно - логирует и возвращает 201.

-> Обработка исключений:
  -> AuthError -> 403.
  -> LogicError -> 409 (или 400 для некоторых случаев).
  -> Другие ошибки -> 500.

На выходе:
	-> `201 Created`: {"message": "Invitation sent successfully."}
	-> `400 Bad Request`: Ошибка в теле запроса.
	-> `403 Forbidden`: Недостаточно прав для выполнения операции.
	-> `409 Conflict`: Пользователь уже является активным участником фирмы, или повторная отправка происходит слишком часто.
	-> `500 Internal Server Error`: Ошибка отправки email или работы с БД.

---
#### Зависимости и окружение
- **Необходимые утилиты**: [[𝒇 Функции/utils/auth_utils - утилита.md|auth_utils.py]], [[𝒇 Функции/utils/ydb_utils - утилита.md|ydb_utils.py]], [[𝒇 Функции/utils/request_parser - утилита.md|request_parser.py]], [[𝒇 Функции/utils/email_utils - утилита.md|email_utils.py]]
- **Переменные окружения**:
	- `YDB_ENDPOINT`, `YDB_DATABASE` ([[💾 jwt-database - База данных YandexDatabase]])
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
	- SA_KEY_FILE ([[ydb_sa_key.json]])
	- `JWT_SECRET`
    - `UNISENDER_API_KEY`, `UNISENDER_SENDER_EMAIL`, `UNISENDER_SENDER_NAME`, `UNISENDER_LIST_ID` (для отправки email)

---
#### Код функции (ИСПРАВЛЕНА ЛОГИКА ТРАНЗАКЦИИ)

```python
import json
import os
import uuid
import logging
import ydb
import datetime
from utils import auth_utils, ydb_utils, request_parser, email_utils

logging.basicConfig(level=logging.INFO)

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

def handler(event, context):
    try:
        # 1. Авторизация и парсинг
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '): 
            raise AuthError("Unauthorized: Missing Bearer token.")
        
        parts = auth_header.split(' ')
        if len(parts) != 2:
            raise AuthError("Unauthorized: Invalid Bearer token format.")
        token = parts[1]
        
        admin_payload = auth_utils.verify_jwt(token)
        if not admin_payload or 'user_id' not in admin_payload: 
            raise AuthError("Invalid or expired token.")
        
        admin_user_id = admin_payload['user_id']

        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        invitee_email = data.get('email', '').lower()

        if not all([firm_id, invitee_email]):
            raise LogicError("firm_id and email are required.")

        # 2. Поиск кандидата в глобальной базе auth-data
        auth_driver = ydb_utils.get_ydb_driver()
        auth_pool = ydb.SessionPool(auth_driver)
        
        def find_global_user(session):
            query = session.prepare("DECLARE $email AS Utf8; SELECT user_id, user_name FROM users WHERE email = $email;")
            result = session.transaction(ydb.SerializableReadWrite()).execute(query, {'$email': invitee_email}, commit_tx=True)
            return result[0].rows if result[0].rows else None

        candidate_data = auth_pool.retry_operation_sync(find_global_user)

        # 3. Основная транзакция в firms-database
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)

        def invite_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())

            # --- ИСПРАВЛЕННАЯ И УПРОЩЕННАЯ ЛОГИКА ПРОВЕРКИ ПРАВ ---
            # Шаг 3.1: Сначала проверяем права администратора
            admin_query = session.prepare("""
                DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8;
                SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;
            """)
            admin_res = tx.execute(admin_query, {'$user_id': admin_user_id, '$firm_id': firm_id})
            
            if not admin_res[0].rows:
                raise AuthError("Requesting user is not a member of the specified firm.")
            
            admin_roles = json.loads(admin_res[0].rows[0].roles)
            if "ADMIN" not in admin_roles and "OWNER" not in admin_roles:
                raise AuthError("Insufficient permissions. Owner or Admin role required.")

            # Шаг 3.2: Отдельно получаем название фирмы (этот запрос можно вынести из транзакции, если нужно)
            firm_query = session.prepare("DECLARE $firm_id AS Utf8; SELECT firm_name FROM Firms WHERE firm_id = $firm_id;")
            firm_res = tx.execute(firm_query, {'$firm_id': firm_id})
            if not firm_res[0].rows:
                raise LogicError("The specified firm does not exist.")
            firm_name = firm_res[0].rows[0].firm_name
            # --- КОНЕЦ ИСПРАВЛЕННОЙ ЛОГИКИ ---

            # --- НОВАЯ ЛОГИКА ПРОВЕРКИ СУЩЕСТВУЮЩЕГО ПОЛЬЗОВАТЕЛЯ И ПОВТОРНОЙ ОТПРАВКИ ---
            # Шаг 3.3: Проверяем, существует ли уже пользователь с таким email в фирме
            check_query = session.prepare("""
                DECLARE $email AS Utf8; DECLARE $firm_id AS Utf8;
                SELECT user_id, is_active, invitation_sent_at FROM Users WHERE email = $email AND firm_id = $firm_id;
            """)
            check_res = tx.execute(check_query, {'$email': invitee_email, '$firm_id': firm_id})
            
            invitation_key = uuid.uuid4().hex
            
            if check_res[0].rows:
                existing_user = check_res[0].rows[0]
                if existing_user.is_active:
                    raise LogicError("A user with this email is already an active member of this firm.")
                
весь код ->                # Пользователь есть, но не активен -> проверяем возможность повторной отправки
                last_sent_time_raw = existing_user.invitation_sent_at
                if last_sent_time_raw:
                    # YDB возвращает timestamp как int (микросекунды), его нужно конвертировать
                    if isinstance(last_sent_time_raw, int):
                        last_sent_time = datetime.datetime.fromtimestamp(last_sent_time_raw / 1_000_000, tz=datetime.timezone.utc)
                    else: # На случай, если формат изменится
                        last_sent_time = last_sent_time_raw

                    if (datetime.datetime.now(datetime.timezone.utc) - last_sent_time) < datetime.timedelta(minutes=3):
                        raise LogicError("An invitation has been sent recently. Please wait 3 minutes before trying again.")
                
                # Обновляем ключ и время отправки для существующего неактивного пользователя
                update_query = session.prepare("""
                    DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; 
                    DECLARE $invitation_key AS Utf8;
                    UPDATE Users SET invitation_key = $invitation_key, invitation_sent_at = CurrentUtcTimestamp()
                    WHERE user_id = $user_id AND firm_id = $firm_id;
                """)
                tx.execute(update_query, {
                    '$user_id': existing_user.user_id,
                    '$firm_id': firm_id,
                    '$invitation_key': invitation_key
                })

            else:
                # Пользователя нет -> создаем новую запись
                user_to_insert = {
                    'firm_id': firm_id,
                    'email': invitee_email,
                    'roles': json.dumps(["EMPLOYEE"]),
                    'is_active': False,
                    'invitation_key': invitation_key
                }
                
                if candidate_data:
                    user_to_insert['user_id'] = candidate_data[0].user_id
                    user_to_insert['full_name'] = candidate_data[0].user_name or invitee_email.split('@')[0]
                else:
                    user_to_insert['user_id'] = str(uuid.uuid4())
                    user_to_insert['full_name'] = invitee_email.split('@')[0]

                insert_query = session.prepare("""
                    DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; DECLARE $email AS Utf8;
                    DECLARE $full_name AS Utf8; DECLARE $roles AS Json; DECLARE $is_active AS Bool; DECLARE $invitation_key AS Utf8;
                    UPSERT INTO Users (user_id, firm_id, email, full_name, roles, is_active, invitation_key, created_at, invitation_sent_at)
                    VALUES ($user_id, $firm_id, $email, $full_name, $roles, $is_active, $invitation_key, CurrentUtcTimestamp(), CurrentUtcTimestamp());
                """)
                tx.execute(insert_query, {'$' + k: v for k, v in user_to_insert.items()})
            
            tx.commit()
            return invitation_key, firm_name

        inv_key, f_name = firms_pool.retry_operation_sync(invite_transaction)

        # 4. Отправка email (после успешной транзакции)
        admin_name = admin_payload.get('email')
        
        email_sent = email_utils.send_invitation(
            recipient_email=invitee_email, 
            invitation_key=inv_key, 
            firm_name=f_name, 
            admin_name=admin_name
        )
        
        if not email_sent:
            logging.critical(f"DB record for invitation created for {invitee_email} in firm {firm_id}, BUT EMAIL SENDING FAILED. Invitation key: {inv_key}")
            return {"statusCode": 500, "body": json.dumps({"message": "Invitation record was created, but failed to send the email. Please contact support."})}
        
        logging.info(f"Invitation with key {inv_key} sent to {invitee_email} for firm {firm_id}.")
        return {"statusCode": 201, "body": json.dumps({"message": "Invitation sent successfully."})}

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 409, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical error during invitation process: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```