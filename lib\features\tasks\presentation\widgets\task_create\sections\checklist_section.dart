import 'package:flutter/material.dart';
import '../models.dart';

// ------------------ Чек-лист ------------------
class ChecklistSection extends StatefulWidget {
  final List<ChecklistItem> checklist;
  final VoidCallback onAddItem;
  final Function(ChecklistItem) onRemoveItem;
  final Function(ChecklistItem, bool) onToggleItem;
  final Function(int, int) onReorder;

  const ChecklistSection({
    super.key,
    required this.checklist,
    required this.onAddItem,
    required this.onRemoveItem,
    required this.onToggleItem,
    required this.onReorder,
  });

  @override
  State<ChecklistSection> createState() => _ChecklistSectionState();
}

class _ChecklistSectionState extends State<ChecklistSection> {
  late List<ChecklistItem> _checklist;

  @override
  void initState() {
    super.initState();
    _checklist = List.from(widget.checklist);
  }

  @override
  void didUpdateWidget(ChecklistSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.checklist != oldWidget.checklist) {
      _checklist = List.from(widget.checklist);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Чек-лист',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: widget.onAddItem,
              icon: const Icon(Icons.add),
              label: const Text('Добавить'),
            ),
          ],
        ),
        if (_checklist.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text('Чек-лист пуст', style: TextStyle(color: Colors.grey)),
          )
        else
          SizedBox(
            height:
                _checklist.length * 72.0, // Примерная высота каждого элемента
            child: ReorderableListView.builder(
              itemCount: _checklist.length,
              buildDefaultDragHandles: false,
              onReorder: widget.onReorder,
              itemBuilder: (context, index) {
                final item = _checklist[index];
                return Card(
                  key: ValueKey(
                    item.text + index.toString(),
                  ), // Уникальный ключ
                  child: ListTile(
                    leading: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ReorderableDragStartListener(
                          index: index,
                          child: const Icon(
                            Icons.drag_handle,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Checkbox(
                          value: item.completed,
                          onChanged: (value) {
                            setState(() {
                              item.completed = value ?? false;
                            });
                            widget.onToggleItem(item, value ?? false);
                          },
                        ),
                      ],
                    ),
                    title: Text(
                      item.text,
                      style:
                          item.completed
                              ? const TextStyle(
                                decoration: TextDecoration.lineThrough,
                              )
                              : null,
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () => widget.onRemoveItem(item),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}
