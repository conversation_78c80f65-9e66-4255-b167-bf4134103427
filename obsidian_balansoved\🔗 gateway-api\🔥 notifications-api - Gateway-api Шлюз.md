Идентификатор - d5dcdq2hqt509q04gjkc
Имя - notifications-api
Служебный домен - https://d5dcdq2hqt509q04gjkc.bixf7e87.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Notifications API
  version: 1.2.0
  description: |
    Единый API для управления push-подписками и уведомлениями.
    
    **Ключевые изменения в версии 1.2.0:**
    - Метод DELETE /subscriptions теперь работает по `push_token` в теле запроса, а не по ARN в пути.
    - Удален неиспользуемый эндпоинт GET /subscriptions/{endpointArn}.

servers:
  - url: https://d5dcdq2hqt509q04gjkc.bixf7e87.apigw.yandexcloud.net

x-yc-apigateway:
  cors:
    origin: "*"
    methods:
      - "GET"
      - "POST"
      - "DELETE"
      - "OPTIONS"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    allowCredentials: true
    maxAge: 3600

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - bearerAuth: []

paths:
  # --- Ресурсы для управления подписками (Subscriptions) ---
  /subscriptions:
    get:
      summary: Получить список всех подписок пользователя
      operationId: getSubscriptionsList
      tags: [Subscriptions]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4euafv5ijaums363e84 # -> endpoints-manager
        service_account_id: ajeqgf1b9i412b1cqngu
        # ИЗМЕНЕНО: payload_format_version удален
        context:
          action: GET_LIST
        caching: 
          enabled: false
    post:
      summary: Добавить новую подписку на уведомления
      operationId: addSubscription
      tags: [Subscriptions]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                push_token:
                  type: string
                  description: "JSON-строка для Web Push или токен устройства для RuStore."
                device_info:
                  type: object
                  description: "Необязательная информация об устройстве."
              required: [push_token]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4euafv5ijaums363e84 # -> endpoints-manager
        service_account_id: ajeqgf1b9i412b1cqngu
        # ИЗМЕНЕНО: payload_format_version удален
        context:
          action: ADD
    delete:
      summary: Удалить подписку по push-токену
      operationId: deleteSubscriptionByToken
      tags: [Subscriptions]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                push_token:
                  type: string
                  description: "Токен подписки, которую необходимо удалить."
              required: [push_token]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4euafv5ijaums363e84 # -> endpoints-manager
        service_account_id: ajeqgf1b9i412b1cqngu
        # ИЗМЕНЕНО: payload_format_version удален
        context:
          action: DELETE
  
  # --- Ресурсы для управления уведомлениями (Notices) ---
  /notices:
    get:
      summary: Получить список уведомлений (новых или архивных)
      operationId: getNoticesList
      tags: [Notices]
      parameters:
        - name: page
          in: query
          schema: { type: integer, default: 0 }
        - name: get_archived
          in: query
          schema: { type: boolean, default: false }
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ea448kvn3t4tceoj7i # -> notices-manager
        service_account_id: ajeqgf1b9i412b1cqngu
        # ИЗМЕНЕНО: payload_format_version удален
        context:
          action: GET
        caching: 
          enabled: false

  /notices/{noticeId}:
    parameters:
      - name: noticeId
        in: path
        required: true
        schema: { type: string }
    get:
      summary: Получить одно уведомление по ID
      operationId: getNoticeById
      tags: [Notices]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ea448kvn3t4tceoj7i # -> notices-manager
        service_account_id: ajeqgf1b9i412b1cqngu
        # ИЗМЕНЕНО: payload_format_version удален
        context:
          action: GET
          
  /notices/archive:
    post:
      summary: Архивировать одно уведомление
      operationId: archiveNotice
      tags: [Notices]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties: { notice_id: { type: string } }
              required: [notice_id]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ea448kvn3t4tceoj7i # -> notices-manager
        service_account_id: ajeqgf1b9i412b1cqngu
        # ИЗМЕНЕНО: payload_format_version удален
        context:
          action: ARCHIVE

  /notices/mark-as-delivered:
    post:
      summary: Пометить уведомления как доставленные
      operationId: markAsDelivered
      tags: [Notices]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                notice_ids: { type: array, items: { type: string } }
              required: [notice_ids]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ea448kvn3t4tceoj7i # -> notices-manager
        service_account_id: ajeqgf1b9i412b1cqngu
        # ИЗМЕНЕНО: payload_format_version удален
        context:
          action: MARK_AS_DELIVERED

  # --- Действие по отправке уведомлений ---
  /send-notification:
    post:
      summary: Отправить уведомление пользователю
      description: |
        Отправляет push-уведомление на все активные устройства указанного пользователя.
        **Предназначен для вызова внутренними сервисами сервера, а не конечным пользователем.**
      operationId: sendNotification
      tags: [Actions]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id_to_notify: { type: string }
                payload:
                  type: object
                  properties:
                    title: { type: string }
                    body: { type: string }
                    icon: { type: string }
                  required: [title, body]
              required: [user_id_to_notify, payload]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4euafv5ijaums363e84 # -> endpoints-manager
        service_account_id: ajeqgf1b9i412b1cqngu
        # ИЗМЕНЕНО: payload_format_version удален
        context:
          action: SEND
```