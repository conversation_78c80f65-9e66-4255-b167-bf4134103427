import 'package:equatable/equatable.dart';
import '../../domain/entities/task_entity.dart';
import '../widgets/task_create/models.dart';

abstract class TaskCreateState extends Equatable {
  const TaskCreateState();

  @override
  List<Object?> get props => [];
}

class TaskCreateInitial extends TaskCreateState {}

class TaskCreateLoading extends TaskCreateState {}

class TaskCreateLoaded extends TaskCreateState {
  final String? id;
  final String title;
  final String description;
  final List<String> selectedClientIds;
  final List<String> selectedAssigneeIds;
  final List<String> selectedCoAssigneeIds;
  final List<String> selectedObserverIds;
  final List<String> selectedCreatorIds;
  final DateTime? dueDate;
  final String priority;
  final bool allowAssigneeToChangeDueDate;
  final List<ChecklistItem> checklist;
  final List<ReminderItem> reminders;
  final List<FileAttachmentItem> cloudFiles;
  final String recurrenceType;
  final String recurrenceInterval;
  final List<DateTime> recurrenceExecutionDates;
  final bool recurrenceIsAnnual;
  final String holidayTransferRule;
  final bool wasAutoCreated;
  final bool autoRemindersCreated;
  final bool remindersModifiedByUser;
  final bool hasActiveFileOperations;
  final bool isFormValid;

  const TaskCreateLoaded({
    this.id,
    this.title = '',
    this.description = '',
    this.selectedClientIds = const [],
    this.selectedAssigneeIds = const [],
    this.selectedCoAssigneeIds = const [],
    this.selectedObserverIds = const [],
    this.selectedCreatorIds = const [],
    this.dueDate,
    this.priority = 'medium',
    this.allowAssigneeToChangeDueDate = false,
    this.checklist = const [],
    this.reminders = const [],
    this.cloudFiles = const [],
    this.recurrenceType = 'none',
    this.recurrenceInterval = 'monthly',
    this.recurrenceExecutionDates = const [],
    this.recurrenceIsAnnual = false,
    this.holidayTransferRule = 'next_workday',
    this.wasAutoCreated = false,
    this.autoRemindersCreated = false,
    this.remindersModifiedByUser = false,
    this.hasActiveFileOperations = false,
    this.isFormValid = false,
  });

  TaskCreateLoaded copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? selectedClientIds,
    List<String>? selectedAssigneeIds,
    List<String>? selectedCoAssigneeIds,
    List<String>? selectedObserverIds,
    List<String>? selectedCreatorIds,
    DateTime? dueDate,
    bool clearDueDate = false,
    String? priority,
    bool? allowAssigneeToChangeDueDate,
    List<ChecklistItem>? checklist,
    List<ReminderItem>? reminders,
    List<FileAttachmentItem>? cloudFiles,
    String? recurrenceType,
    String? recurrenceInterval,
    List<DateTime>? recurrenceExecutionDates,
    bool? recurrenceIsAnnual,
    String? holidayTransferRule,
    bool? wasAutoCreated,
    bool? autoRemindersCreated,
    bool? remindersModifiedByUser,
    bool? hasActiveFileOperations,
    bool? isFormValid,
  }) {
    return TaskCreateLoaded(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      selectedClientIds: selectedClientIds ?? this.selectedClientIds,
      selectedAssigneeIds: selectedAssigneeIds ?? this.selectedAssigneeIds,
      selectedCoAssigneeIds:
          selectedCoAssigneeIds ?? this.selectedCoAssigneeIds,
      selectedObserverIds: selectedObserverIds ?? this.selectedObserverIds,
      selectedCreatorIds: selectedCreatorIds ?? this.selectedCreatorIds,
      dueDate: clearDueDate ? null : (dueDate ?? this.dueDate),
      priority: priority ?? this.priority,
      allowAssigneeToChangeDueDate:
          allowAssigneeToChangeDueDate ?? this.allowAssigneeToChangeDueDate,
      checklist: checklist ?? this.checklist,
      reminders: reminders ?? this.reminders,
      cloudFiles: cloudFiles ?? this.cloudFiles,
      recurrenceType: recurrenceType ?? this.recurrenceType,
      recurrenceInterval: recurrenceInterval ?? this.recurrenceInterval,
      recurrenceExecutionDates: recurrenceExecutionDates ?? this.recurrenceExecutionDates,
      recurrenceIsAnnual: recurrenceIsAnnual ?? this.recurrenceIsAnnual,
      holidayTransferRule: holidayTransferRule ?? this.holidayTransferRule,
      wasAutoCreated: wasAutoCreated ?? this.wasAutoCreated,
      autoRemindersCreated: autoRemindersCreated ?? this.autoRemindersCreated,
      remindersModifiedByUser:
          remindersModifiedByUser ?? this.remindersModifiedByUser,
      hasActiveFileOperations:
          hasActiveFileOperations ?? this.hasActiveFileOperations,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }

  @override
  List<Object?> get props => [
    title,
    description,
    selectedClientIds,
    selectedAssigneeIds,
    selectedCoAssigneeIds,
    selectedObserverIds,
    selectedCreatorIds,
    dueDate,
    priority,
    allowAssigneeToChangeDueDate,
    checklist,
    reminders,
    cloudFiles,
    recurrenceType,
    recurrenceInterval,
    recurrenceExecutionDates,
    recurrenceIsAnnual,
    holidayTransferRule,
    wasAutoCreated,
    autoRemindersCreated,
    remindersModifiedByUser,
    hasActiveFileOperations,
    isFormValid,
  ];
}

class TaskCreateSaving extends TaskCreateState {
  final TaskCreateLoaded currentState;

  const TaskCreateSaving({required this.currentState});

  @override
  List<Object?> get props => [currentState];
}

class TaskCreateSaved extends TaskCreateState {
  final TaskEntity savedTask;
  final TaskCreateLoaded currentState;

  const TaskCreateSaved({required this.savedTask, required this.currentState});

  @override
  List<Object?> get props => [savedTask, currentState];
}

class TaskCreateError extends TaskCreateState {
  final String message;
  final TaskCreateLoaded? currentState;

  const TaskCreateError({required this.message, this.currentState});

  @override
  List<Object?> get props => [message, currentState];
}
