import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dropzone/flutter_dropzone.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:html' as html;

class FileUploadZone extends StatefulWidget {
  final Function(List<FileUploadItem>) onFilesSelected;
  final String? hintText;
  final double height;
  final List<String>? allowedExtensions;
  final int? maxFileSize; // в байтах
  final Widget? child;

  const FileUploadZone({
    super.key,
    required this.onFilesSelected,
    this.hintText,
    this.height = 120,
    this.allowedExtensions,
    this.maxFileSize,
    this.child,
  });

  @override
  State<FileUploadZone> createState() => FileUploadZoneState();
}

class FileUploadZoneState extends State<FileUploadZone> {
  DropzoneViewController? _dropzoneController;
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final containerHeight = widget.child == null ? widget.height : null;

    return Container(
      width: double.infinity,
      height: containerHeight,
      decoration: BoxDecoration(
        border: Border.all(
          color: _isHovering ? theme.colorScheme.primary : theme.dividerColor,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(8),
        color:
            _isHovering
                ? theme.colorScheme.primary.withOpacity(0.1)
                : theme.scaffoldBackgroundColor.withAlpha(100),
      ),
      child: Stack(
        children: [
          // Интерактивный слой (клик для выбора файлов) теперь всегда активен и находится под контентом
          Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: pickFiles,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),

          // Drag&Drop зона (поверх всей области, но не влияет на размер)
          if (kIsWeb)
            Positioned.fill(
              child: DropzoneView(
                onCreated: (ctrl) => _dropzoneController = ctrl,
                onHover: () => setState(() => _isHovering = true),
                onLeave: () => setState(() => _isHovering = false),
                onError: (error) {
                  if (kDebugMode) {
                    print('Dropzone error: $error');
                  }
                },
                onDrop: _onSingleFileDropped,
                onDropMultiple: _onMultipleFilesDropped,
              ),
            ),

          // Контент, задающий высоту контейнера
          if (widget.child != null)
            Padding(padding: const EdgeInsets.all(8.0), child: widget.child)
          else
            _buildPlaceholder(theme),
        ],
      ),
    );
  }

  Future<void> pickFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: widget.allowedExtensions,
      );

      if (result != null) {
        final files = <FileUploadItem>[];

        for (final file in result.files) {
          if (_validateFile(file.name, file.size)) {
            files.add(
              FileUploadItem(
                name: file.name,
                bytes: file.bytes,
                size: file.size,
              ),
            );
          }
        }

        if (files.isNotEmpty) {
          widget.onFilesSelected(files);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка выбора файлов: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onSingleFileDropped(dynamic event) async {
    if (!kIsWeb) return;

    final files = <FileUploadItem>[];
    try {
      if (event is html.File) {
        final htmlFile = event;
        final reader = html.FileReader();
        reader.readAsArrayBuffer(htmlFile);
        await reader.onLoadEnd.first;
        final fileBytes = reader.result as Uint8List;

        if (_validateFile(htmlFile.name, fileBytes.length)) {
          files.add(
            FileUploadItem(
              name: htmlFile.name,
              bytes: fileBytes,
              size: fileBytes.length,
            ),
          );
        }
      }

      if (files.isNotEmpty) {
        widget.onFilesSelected(files);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка обработки файла: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      setState(() => _isHovering = false);
    }
  }

  Future<void> _onMultipleFilesDropped(List<dynamic>? events) async {
    if (!kIsWeb || events == null) return;

    final files = <FileUploadItem>[];
    try {
      for (final event in events) {
        if (event is html.File) {
          final htmlFile = event;
          final reader = html.FileReader();
          reader.readAsArrayBuffer(htmlFile);
          await reader.onLoadEnd.first;
          final fileBytes = reader.result as Uint8List;

          if (_validateFile(htmlFile.name, fileBytes.length)) {
            files.add(
              FileUploadItem(
                name: htmlFile.name,
                bytes: fileBytes,
                size: fileBytes.length,
              ),
            );
          }
        }
      }

      if (files.isNotEmpty) {
        widget.onFilesSelected(files);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка обработки файлов: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      setState(() => _isHovering = false);
    }
  }

  bool _validateFile(String fileName, int? fileSize) {
    // Проверка расширения
    if (widget.allowedExtensions != null) {
      final extension = fileName.split('.').last.toLowerCase();
      if (!widget.allowedExtensions!.contains(extension)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Файл $fileName имеет неподдерживаемый формат'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
        return false;
      }
    }

    // Проверка размера
    if (widget.maxFileSize != null &&
        fileSize != null &&
        fileSize > widget.maxFileSize!) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Файл $fileName превышает максимальный размер ${_formatFileSize(widget.maxFileSize!)}',
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      return false;
    }

    return true;
  }

  String _formatFileSize(int bytes) {
    if (bytes == 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    int unitIndex = 0;
    double size = bytes.toDouble();

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(unitIndex == 0 ? 0 : 1)} ${units[unitIndex]}';
  }

  Widget _buildPlaceholder(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.cloud_upload_outlined, size: 32, color: theme.hintColor),
          const SizedBox(height: 8),
          Text(
            widget.hintText ??
                (kIsWeb
                    ? 'Перетащите файлы сюда или нажмите для выбора'
                    : 'Нажмите для выбора файлов'),
            style: TextStyle(color: theme.hintColor, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          if (widget.allowedExtensions != null) ...[
            const SizedBox(height: 4),
            Text(
              'Поддерживаемые форматы: ${widget.allowedExtensions!.join(', ')}',
              style: TextStyle(
                color: theme.hintColor.withOpacity(0.7),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          if (widget.maxFileSize != null) ...[
            const SizedBox(height: 2),
            Text(
              'Максимальный размер: ${_formatFileSize(widget.maxFileSize!)}',
              style: TextStyle(
                color: theme.hintColor.withOpacity(0.7),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Модель для загружаемого файла
class FileUploadItem {
  final String name;
  final Uint8List? bytes;
  final int? size;

  FileUploadItem({required this.name, this.bytes, this.size});

  @override
  String toString() {
    return 'FileUploadItem(name: $name, size: $size)';
  }
}
