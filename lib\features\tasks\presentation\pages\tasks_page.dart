import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/router.dart';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';

import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_filter_widget.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/tasks_table_widget.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/tasks_create_button.dart';

@RoutePage()
class TasksPage extends StatefulWidget {
  final TaskRequestParams? initialParams;
  final String? initialTaskId;
  final bool resetFilters;

  const TasksPage({
    super.key,
    this.initialParams,
    this.initialTaskId,
    this.resetFilters = false,
  });

  @override
  State<TasksPage> createState() => _TasksPageState();
}

class _TasksPageState extends State<TasksPage> {
  final TextEditingController _searchController = TextEditingController();
  String _filter = '';

  int _sortColumnIndex = 0;
  bool _sortAsc = true;
  bool _onlyMy = false;

  late TaskRequestParams _currentParams;
  String? _initialTaskId;
  int _refreshCounter = 0;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() => _filter = _searchController.text);
    });

    _currentParams =
        _createParamsFromUrl() ??
        widget.initialParams ??
        TaskRequestParams.timeless();
    _initialTaskId = widget.initialTaskId;

    // Сброс фильтров при необходимости
    if (widget.resetFilters) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _resetFiltersAndRefresh();
      });
    }
  }

  TaskRequestParams? _createParamsFromUrl() {
    final queryParams = context.routeData.queryParams;
    final viewTypeName = queryParams.optString('viewType');

    if (viewTypeName == null) {
      // Если в URL нет viewType, это может быть корневой маршрут /tasks
      // Не создаем параметры, чтобы initState мог использовать initialParams или дефолтные
      return null;
    }

    final viewType = TaskViewType.values.firstWhere(
      (e) => e.name == viewTypeName,
      orElse: () => TaskViewType.timeless, // Фоллбэк
    );

    if (viewType == TaskViewType.dated) {
      return TaskRequestParams.dated(
        year: queryParams.optInt('year') ?? DateTime.now().year,
        month: queryParams.optInt('month') ?? DateTime.now().month,
        clientId: queryParams.optString('clientId'),
        filterByMonth: queryParams.optBool('filterByMonth') ?? true, // По умолчанию включаем фильтр для dated задач
        force: true, // При навигации по URL всегда принудительно обновляем
      );
    } else {
      return TaskRequestParams.timeless(
        page: queryParams.optInt('page') ?? 0,
        clientId: queryParams.optString('clientId'),
        filterByMonth: queryParams.optBool('filterByMonth') ?? false,
        force: true, // При навигации по URL всегда принудительно обновляем
      );
    }
  }

  @override
  @override
  void didUpdateWidget(covariant TasksPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Логика сброса фильтров
    if (widget.resetFilters && !oldWidget.resetFilters) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _resetFiltersAndRefresh();
      });
      return; // Выходим, чтобы избежать двойного обновления
    }

    // Логика для прямого указания параметров (например, из другого места приложения)
    if (widget.initialParams != null && widget.initialParams != oldWidget.initialParams) {
       _navigateWithParams(widget.initialParams!); 
       return;
    }

    // Логика для открытия конкретной задачи
    if (widget.initialTaskId != null && widget.initialTaskId != oldWidget.initialTaskId) {
       setState(() {
         _initialTaskId = widget.initialTaskId;
       });
       return;
    }

    // Основная логика синхронизации состояния с URL
    final newParams = _createParamsFromUrl();
    if (newParams != null && newParams != _currentParams) {
      setState(() {
        _currentParams = newParams;
      });
      final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
      if (firm != null) {
        context.read<TasksCubit>().fetchTasks(firm.id, newParams);
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }





  void _navigateWithParams(TaskRequestParams params) {
    // Обновляем состояние, чтобы UI (включая TaskFilterWidget) перерисовался
    setState(() {
      _currentParams = params;
    });

    // Выполняем навигацию
    context.router.replace(TasksRoute(initialParams: params));

    // Загружаем данные с новыми параметрами
    final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (firm != null) {
      context.read<TasksCubit>().fetchTasks(firm.id, params);
    }
  }

  void _resetFiltersAndRefresh() {
    // Сброс всех фильтров
    _searchController.clear();
    setState(() {
      _filter = '';
      _onlyMy = false;
      _sortColumnIndex = 0;
      _sortAsc = true;
      _currentParams = TaskRequestParams.timeless();
      _refreshCounter++;
    });

    // Навигация на чистый URL без параметров фильтров
    context.router.navigate(TasksRoute());

    // Обновление задач
    final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (firm != null) {
      context.read<TasksCubit>().fetchTasks(firm.id, _currentParams);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_initialTaskId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Открываем TasksViewPage напрямую, чтобы избежать бесконечного цикла
        context.router.navigate(TasksViewRoute(taskId: _initialTaskId!));
        // Сбрасываем initialTaskId после навигации
        setState(() {
          _initialTaskId = null;
        });
      });
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              // Если ширина контейнера меньше 600px, располагаем элементы в колонку
              final isNarrow = constraints.maxWidth < 600;

              final searchField = TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  prefixIcon: Icon(Icons.search),
                  hintText: 'Поиск задач',
                ),
              );

              final onlyMyCheckbox = Builder(
                builder: (context) {
                  final tasksState = context.watch<TasksCubit>().state;
                  final firmState = context.watch<ActiveFirmCubit>().state;
                  final loading =
                      tasksState is TasksLoading || firmState.isLoading;
                  if (loading) {
                    return const LoadingTile(height: 24, width: 160);
                  }
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Checkbox(
                        value: _onlyMy,
                        onChanged:
                            (val) => setState(() => _onlyMy = val ?? false),
                      ),
                      const Text('Только мои'),
                    ],
                  );
                },
              );

              if (isNarrow) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    searchField,
                    const SizedBox(height: 12),
                    onlyMyCheckbox,
                    const SizedBox(height: 12),
                    TasksCreateButton(
                      onPressed:
                          () =>
                              context.router.navigate(const TasksCreateRoute()),
                    ),
                  ],
                );
              } else {
                return Row(
                  children: [
                    Expanded(child: searchField),
                    const SizedBox(width: 16),
                    onlyMyCheckbox,
                    const SizedBox(width: 16),
                    TasksCreateButton(
                      onPressed:
                          () =>
                              context.router.navigate(const TasksCreateRoute()),
                    ),
                  ],
                );
              }
            },
          ),
          const SizedBox(height: 16),
          TaskFilterWidget(
            currentParams: _currentParams,
            paginationMeta: context.watch<TasksCubit>().paginationMeta,
            onParamsChanged: _navigateWithParams,
            onNextPage:
                () => context.read<TasksCubit>().nextPage(
                  context.read<ActiveFirmCubit>().state.selectedFirm!.id,
                ),
            onPreviousPage:
                () => context.read<TasksCubit>().previousPage(
                  context.read<ActiveFirmCubit>().state.selectedFirm!.id,
                ),
          ),
          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return TasksTableWidget(
      key: ValueKey([_currentParams, _refreshCounter]),
      filterProvider: () => _filter,
      sortColumnIndex: _sortColumnIndex,
      sortAsc: _sortAsc,
      onSort:
          (i, asc) => setState(() {
            _sortColumnIndex = i;
            _sortAsc = asc;
          }),
      onView: (task) {
        context.router.navigate(TasksViewRoute(taskId: task.id));
      },
      onEdit: (task) {
        context.router.navigate(TasksEditRoute(taskId: task.id));
      },
      showOnlyMy: _onlyMy,
      currentParams: _currentParams,
    );
  }
}

// Удален класс _TasksTableSection - перенесен в TasksTableWidget
// Удален класс _TasksTableSectionState - перенесен в TasksTableWidget
// Удалены классы _StatusDropdownCell и _InlineStatusDropdown - перенесены в TasksTableWidget
