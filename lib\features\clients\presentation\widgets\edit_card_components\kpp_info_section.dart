import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/presentation/widgets/universal_number_field.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';

class KppInfoSection extends StatelessWidget {
  final bool isEditing;
  final List<KppInfo> kppInfo;
  final Function(List<KppInfo>) onKppInfoChanged;
  final Function(String, String) copyToClipboard;

  const KppInfoSection({
    super.key,
    required this.isEditing,
    required this.kppInfo,
    required this.onKppInfoChanged,
    required this.copyToClipboard,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.credit_card_outlined,
              size: 18,
              color: theme.colorScheme.secondary,
            ),
            const SizedBox(width: 8),
            const Text(
              'Информация о КПП',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            if (isEditing) ...[
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.add, size: 20),
                onPressed: () {
                  final newKppInfo = List<KppInfo>.from(kppInfo)..add(
                    KppInfo(
                      number: '',
                      date: DateTime.now(),
                    ),
                  );
                  onKppInfoChanged(newKppInfo);
                },
                tooltip: 'Добавить КПП',
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        // Поле для отображения текущего КПП (первого в списке)
        if (kppInfo.isNotEmpty) ...[
          _buildCurrentKppDisplay(context, theme),
          const SizedBox(height: 12),
        ],
        if (kppInfo.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                'Информация о КПП не добавлена',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...kppInfo.asMap().entries.map((entry) {
            final index = entry.key;
            final kpp = entry.value;
            return _buildKppInfoCard(context, kpp, index);
          }),
      ],
    );
  }

  Widget _buildCurrentKppDisplay(BuildContext context, ThemeData theme) {
    final currentKpp = kppInfo.first;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        border: Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                size: 16,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Text(
                'Текущий КПП',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Номер: ${currentKpp.number.isEmpty ? "Не указан" : currentKpp.number}',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'Дата: ${DateFormat('dd.MM.yyyy').format(currentKpp.date)}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKppInfoCard(BuildContext context, KppInfo kpp, int index) {
    return _KppInfoCard(
      kpp: kpp,
      index: index,
      isEditing: isEditing,
      onKppInfoChanged: onKppInfoChanged,
      kppInfo: kppInfo,
      copyToClipboard: copyToClipboard,
      onMoveUp: index > 0 ? () => _moveKppUp(index) : null,
      onMoveDown: index < kppInfo.length - 1 ? () => _moveKppDown(index) : null,
    );
  }

  void _moveKppUp(int index) {
    if (index > 0) {
      final newKppInfo = List<KppInfo>.from(kppInfo);
      final item = newKppInfo.removeAt(index);
      newKppInfo.insert(index - 1, item);
      onKppInfoChanged(newKppInfo);
    }
  }

  void _moveKppDown(int index) {
    if (index < kppInfo.length - 1) {
      final newKppInfo = List<KppInfo>.from(kppInfo);
      final item = newKppInfo.removeAt(index);
      newKppInfo.insert(index + 1, item);
      onKppInfoChanged(newKppInfo);
    }
  }
}

class _KppInfoCard extends StatefulWidget {
  final KppInfo kpp;
  final int index;
  final bool isEditing;
  final Function(List<KppInfo>) onKppInfoChanged;
  final List<KppInfo> kppInfo;
  final Function(String, String) copyToClipboard;
  final VoidCallback? onMoveUp;
  final VoidCallback? onMoveDown;

  const _KppInfoCard({
    required this.kpp,
    required this.index,
    required this.isEditing,
    required this.onKppInfoChanged,
    required this.kppInfo,
    required this.copyToClipboard,
    this.onMoveUp,
    this.onMoveDown,
  });

  @override
  State<_KppInfoCard> createState() => _KppInfoCardState();
}

class _KppInfoCardState extends State<_KppInfoCard> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  'КПП ${widget.index + 1}',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                if (widget.isEditing) ...[
                  // Кнопки сортировки
                  if (widget.onMoveUp != null)
                    IconButton(
                      icon: const Icon(Icons.keyboard_arrow_up, size: 20),
                      onPressed: widget.onMoveUp,
                      tooltip: 'Переместить вверх',
                    ),
                  if (widget.onMoveDown != null)
                    IconButton(
                      icon: const Icon(Icons.keyboard_arrow_down, size: 20),
                      onPressed: widget.onMoveDown,
                      tooltip: 'Переместить вниз',
                    ),
                  // Кнопка удаления
                  IconButton(
                    icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                    onPressed: () {
                      final newKppInfo = List<KppInfo>.from(widget.kppInfo)
                        ..removeAt(widget.index);
                      widget.onKppInfoChanged(newKppInfo);
                    },
                    tooltip: 'Удалить',
                  ),
                ],
              ],
            ),
            const SizedBox(height: 8),
            UniversalNumberField(
              initialValue: widget.kpp.number,
              labelText: 'Номер КПП (9 цифр)',
              prefixIcon: Icons.credit_card_outlined,
              fieldType: NumberFieldType.integer,
              maxLength: 9,
              isEditing: widget.isEditing,
              onChanged: (value) {
                final newKppInfo = List<KppInfo>.from(widget.kppInfo);
                newKppInfo[widget.index] = widget.kpp.copyWith(
                  number: value,
                );
                widget.onKppInfoChanged(newKppInfo);
              },
              onTap:
                  () =>
                      widget.copyToClipboard(widget.kpp.number, 'Номер КПП'),
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap:
                  widget.isEditing
                      ? () async {
                        final date = await SmartDatePickerDialog.show(
                          context: context,
                          initialDate: widget.kpp.date,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                          helpText: 'Выберите дату',
                          allowClear: false,
                        );
                        if (date != null) {
                          final newKppInfo = List<KppInfo>.from(widget.kppInfo);
                          newKppInfo[widget.index] = widget.kpp.copyWith(
                            date: date,
                          );
                          widget.onKppInfoChanged(newKppInfo);
                        }
                      }
                      : () => widget.copyToClipboard(
                        DateFormat('dd.MM.yyyy').format(widget.kpp.date),
                        'Дата КПП',
                      ),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 12,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 20,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        DateFormat('dd.MM.yyyy').format(widget.kpp.date),
                      ),
                    ),
                    Icon(
                      widget.isEditing ? Icons.edit : Icons.copy,
                      size: 16,
                      color: Colors.grey,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}