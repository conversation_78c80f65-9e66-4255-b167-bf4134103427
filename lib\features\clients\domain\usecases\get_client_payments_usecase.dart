import 'package:balansoved_enterprise/features/clients/domain/entities/client_payment_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/client_payments_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';

class GetClientPaymentsUseCase {
  final IClientPaymentsRepository repository;

  GetClientPaymentsUseCase({required this.repository});

  Future<Either<Failure, List<ClientPaymentEntity>>> call(
    String firmId,
    int year, {
    String? clientId,
  }) {
    return repository.getPayments(firmId, year, clientId: clientId);
  }
}
